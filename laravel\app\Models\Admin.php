<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Admin extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * 批量赋值白名单
     */
    protected $fillable = [
        'name',
        'username',
        'password',
    ];

    /**
     * 隐藏字段
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 设置密码时自动加密
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = bcrypt($value);
    }
}
