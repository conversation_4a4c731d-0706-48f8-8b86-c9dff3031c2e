{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue", "mtime": 1749724641760}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GameLayout", "getVipInfo", "claimVipReward", "claimHistoryReward", "claimDailyReward", "claimWeeklyReward", "claimMonthlyReward", "logger", "name", "components", "data", "vipLevel", "vipExp", "vipNextExp", "isClaiming", "canClaimReward", "vipPrivileges", "vipRewardHistory", "canClaimDaily", "canClaimWeekly", "canClaimMonthly", "selectedVipLevel", "loading", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "currentRewardTab", "currentMainTab", "computed", "vipIcon", "vipPercent", "Math", "min", "round", "methods", "goBack", "$router", "push", "getVipIcon", "level", "fetchVipData", "exp", "next_exp", "can_claim_reward", "privileges", "reward_history", "can_claim_daily", "can_claim_weekly", "can_claim_monthly", "info", "e", "$toast", "error", "retryLoading", "showVipDetails", "levelInfo", "vipLevels", "find", "item", "join", "mounted", "Promise", "all"], "sources": ["src/views/game/subpages/Vip.vue"], "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"vip-page\">\r\n      <!-- 返回按钮 -->\r\n      <div class=\"back-button-container\">\r\n        <div class=\"back-button\" @click=\"goBack\">\r\n          <img src=\"/static/game/UI/anniu/fhui_.png\" alt=\"返回\" />\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 加载状态显示 -->\r\n      <div class=\"loading-container\" v-if=\"loading\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <div class=\"loading-text\">加载中...</div>\r\n      </div>\r\n      \r\n      <!-- 错误状态显示 -->\r\n      <div class=\"error-container\" v-else-if=\"hasError\">\r\n        <div class=\"error-icon\">!</div>\r\n        <div class=\"error-text\">{{ errorMessage }}</div>\r\n        <button class=\"pixel-button gold\" @click=\"retryLoading\">重新加载</button>\r\n      </div>\r\n      \r\n      <div v-else>\r\n        <!-- 主标签导航 -->\r\n        <div class=\"main-tabs\">\r\n          <div \r\n            class=\"main-tab\" \r\n            :class=\"{ active: currentMainTab === 'vip' }\"\r\n            @click=\"currentMainTab = 'vip'\"\r\n          >\r\n            <span>VIP</span>\r\n          </div>\r\n          <div \r\n            class=\"main-tab\" \r\n            :class=\"{ active: currentMainTab === 'benefits' }\"\r\n            @click=\"currentMainTab = 'benefits'\"\r\n          >\r\n            <span>VIP福利</span>\r\n          </div>\r\n          <div \r\n            class=\"main-tab\" \r\n            :class=\"{ active: currentMainTab === 'privileges' }\"\r\n            @click=\"currentMainTab = 'privileges'\"\r\n          >\r\n            <span>VIP特权</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- VIP信息标签内容 -->\r\n        <div v-if=\"currentMainTab === 'vip'\" class=\"tab-content\">\r\n          <div class=\"vip-info pixel-border-box\">\r\n            <div class=\"vip-info-header\">\r\n              <img :src=\"vipIcon\" class=\"vip-icon\" :alt=\"'VIP' + vipLevel\" />\r\n            </div>\r\n            \r\n            <div class=\"vip-exp-section\">\r\n              <div class=\"vip-exp\">\r\n                当前经验：{{ vipExp }} / {{ vipNextExp }}\r\n                <span v-if=\"vipLevel < 20\" class=\"vip-next-exp\">\r\n                  ，还需 <span class=\"highlight-text\">{{ vipNextExp - vipExp }}</span> 经验升级到 VIP{{ vipLevel + 1 }}\r\n                </span>\r\n                <span v-else class=\"vip-next-exp\">（已满级）</span>\r\n              </div>\r\n              <div class=\"vip-progress-container\">\r\n                <div class=\"vip-progress-bar\">\r\n                  <div class=\"vip-progress\" :style=\"{width: vipPercent + '%'}\"></div>\r\n                  <div class=\"progress-stars\">\r\n                    <span class=\"progress-star\" v-for=\"n in 5\" :key=\"n\" :style=\"{left: (n * 20 - 10) + '%'}\">✦</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"vip-progress-text\">{{ vipPercent }}%</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <button v-if=\"canClaimReward\" class=\"pixel-button gold shine-effect\" @click=\"claimVipReward\" :disabled=\"isClaiming\">\r\n              <span v-if=\"isClaiming\">领取中...</span>\r\n              <span v-else>领取VIP{{ vipLevel }}奖励</span>\r\n            </button>\r\n          </div>\r\n          \r\n          <div class=\"vip-reward-history pixel-border-box\" v-if=\"vipRewardHistory.length\">\r\n            <div class=\"section-header\">\r\n              <div class=\"vip-title-with-icon\">\r\n                <img :src=\"vipIcon\" class=\"vip-small-icon\" alt=\"VIP图标\" />\r\n                <h3>VIP成长奖励历史</h3>\r\n              </div>\r\n              <div class=\"section-decor\"></div>\r\n            </div>\r\n            <div class=\"history-list\">\r\n              <div v-for=\"(reward, idx) in vipRewardHistory\" :key=\"idx\" class=\"history-item\">\r\n                <div class=\"history-info\">\r\n                  <div class=\"history-vip-badge\">\r\n                    <img :src=\"getVipIcon(reward.level)\" class=\"history-vip-icon\" :alt=\"'VIP' + reward.level\" />\r\n                  </div>\r\n                  <span class=\"history-date\">{{ reward.date || '未领取' }}</span>\r\n                  <span class=\"history-desc\">{{ reward.desc }}</span>\r\n                </div>\r\n                <button \r\n                  v-if=\"!reward.date\" \r\n                  class=\"pixel-button small gold shine-effect\" \r\n                  @click=\"claimHistoryReward(reward.level)\" \r\n                  :disabled=\"isClaiming\"\r\n                >\r\n                  补领\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- VIP福利标签内容 -->\r\n        <div v-else-if=\"currentMainTab === 'benefits'\" class=\"tab-content\">\r\n          <div class=\"vip-daily-reward pixel-border-box\">\r\n            <div class=\"section-header\">\r\n              <div class=\"vip-title-with-icon\">\r\n                <img :src=\"vipIcon\" class=\"vip-small-icon\" alt=\"VIP图标\" />\r\n                <h3>VIP福利</h3>\r\n              </div>\r\n              <div class=\"section-decor\"></div>\r\n            </div>\r\n            \r\n            <!-- 添加横向选择标签 -->\r\n            <div class=\"reward-tabs\">\r\n              <div \r\n                class=\"reward-tab\" \r\n                :class=\"{ active: currentRewardTab === 'daily' }\"\r\n                @click=\"currentRewardTab = 'daily'\"\r\n              >\r\n                每日福利\r\n              </div>\r\n              <div \r\n                class=\"reward-tab\" \r\n                :class=\"{ active: currentRewardTab === 'weekly' }\"\r\n                @click=\"currentRewardTab = 'weekly'\"\r\n              >\r\n                每周福利\r\n              </div>\r\n              <div \r\n                class=\"reward-tab\" \r\n                :class=\"{ active: currentRewardTab === 'monthly' }\"\r\n                @click=\"currentRewardTab = 'monthly'\"\r\n              >\r\n                每月福利\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 每日福利内容 -->\r\n            <div class=\"daily-reward-content\" v-if=\"currentRewardTab === 'daily'\">\r\n              <div class=\"reward-info-card\">\r\n                <ul class=\"reward-list\">\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">金砖</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 10 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">银两</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 1000 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">体力</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 5 }}</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <button \r\n                class=\"pixel-button\" \r\n                :class=\"canClaimDaily ? 'gold shine-effect' : 'disabled'\"\r\n                @click=\"claimDailyReward\" \r\n                :disabled=\"!canClaimDaily || isClaiming\"\r\n              >\r\n                <span v-if=\"isClaiming\">领取中...</span>\r\n                <span v-else-if=\"canClaimDaily\">领取每日福利</span>\r\n                <span v-else>今日已领取</span>\r\n              </button>\r\n            </div>\r\n            \r\n            <!-- 每周福利内容 -->\r\n            <div class=\"daily-reward-content\" v-else-if=\"currentRewardTab === 'weekly'\">\r\n              <div class=\"reward-info-card\">\r\n                <ul class=\"reward-list\">\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">金砖</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 30 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">银两</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 3000 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">高级装备箱</span>\r\n                    <span class=\"reward-amount\">x{{ Math.max(1, Math.floor(vipLevel/2)) }}</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <button \r\n                class=\"pixel-button\" \r\n                :class=\"canClaimWeekly ? 'gold shine-effect' : 'disabled'\"\r\n                @click=\"claimWeeklyReward\" \r\n                :disabled=\"!canClaimWeekly || isClaiming\"\r\n              >\r\n                <span v-if=\"isClaiming\">领取中...</span>\r\n                <span v-else-if=\"canClaimWeekly\">领取每周福利</span>\r\n                <span v-else>本周已领取</span>\r\n              </button>\r\n            </div>\r\n            \r\n            <!-- 每月福利内容 -->\r\n            <div class=\"daily-reward-content\" v-else>\r\n              <div class=\"reward-info-card\">\r\n                <ul class=\"reward-list\">\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">金砖</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 100 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">银两</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 10000 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">稀有装备箱</span>\r\n                    <span class=\"reward-amount\">x{{ Math.max(1, Math.floor(vipLevel/3)) }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">仙将碎片</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 5 }}</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <button \r\n                class=\"pixel-button\" \r\n                :class=\"canClaimMonthly ? 'gold shine-effect' : 'disabled'\"\r\n                @click=\"claimMonthlyReward\" \r\n                :disabled=\"!canClaimMonthly || isClaiming\"\r\n              >\r\n                <span v-if=\"isClaiming\">领取中...</span>\r\n                <span v-else-if=\"canClaimMonthly\">领取每月福利</span>\r\n                <span v-else>本月已领取</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- VIP特权标签内容 -->\r\n        <div v-else class=\"tab-content\">\r\n          <div class=\"vip-privileges pixel-border-box\">\r\n            <div class=\"section-header\">\r\n              <div class=\"vip-title-with-icon\">\r\n                <img :src=\"vipIcon\" class=\"vip-small-icon\" alt=\"VIP图标\" />\r\n                <h3>VIP{{ vipLevel }}特权说明</h3>\r\n              </div>\r\n              <div class=\"section-decor\"></div>\r\n            </div>\r\n            <ul class=\"privilege-list\">\r\n              <li v-for=\"(desc, idx) in vipPrivileges\" :key=\"idx\" class=\"privilege-item\">\r\n                <i class=\"privilege-icon\">✓</i>\r\n                <span>{{ desc }}</span>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue'\r\nimport { getVipInfo, claimVipReward, claimHistoryReward, claimDailyReward, claimWeeklyReward, claimMonthlyReward } from '@/api/services/vipService'\r\nimport logger from '@/utils/logger'\r\n\r\nexport default {\r\n  name: 'Vip',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      vipLevel: 0,\r\n      vipExp: 0,\r\n      vipNextExp: 100,\r\n      isClaiming: false,\r\n      canClaimReward: false,\r\n      vipPrivileges: [],\r\n      vipRewardHistory: [],\r\n      canClaimDaily: false,\r\n      canClaimWeekly: false,\r\n      canClaimMonthly: false,\r\n      selectedVipLevel: null,\r\n      loading: true,\r\n      hasError: false,\r\n      errorMessage: '加载数据失败，请稍后重试',\r\n      currentRewardTab: 'daily',\r\n      currentMainTab: 'vip'\r\n    }\r\n  },\r\n  computed: {\r\n    vipIcon() {\r\n      return `/static/game/UI/vip/${this.vipLevel}.png`\r\n    },\r\n    vipPercent() {\r\n      return Math.min(100, Math.round((this.vipExp / this.vipNextExp) * 100))\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      this.$router.push('/game/main');\r\n    },\r\n    getVipIcon(level) {\r\n      return `/static/game/UI/vip/${level}.png`\r\n    },\r\n    async fetchVipData() {\r\n      this.loading = true\r\n      this.hasError = false\r\n      \r\n      try {\r\n        const data = await getVipInfo()\r\n        this.vipLevel = data.level\r\n        this.vipExp = data.exp\r\n        this.vipNextExp = data.next_exp\r\n        this.canClaimReward = data.can_claim_reward\r\n        this.vipPrivileges = data.privileges || []\r\n        this.vipRewardHistory = data.reward_history || []\r\n        this.canClaimDaily = data.can_claim_daily\r\n        this.canClaimWeekly = data.can_claim_weekly\r\n        this.canClaimMonthly = data.can_claim_monthly\r\n        logger.info('VIP信息加载成功', { level: this.vipLevel, exp: this.vipExp })\r\n      } catch (e) {\r\n        this.$toast && this.$toast('获取VIP信息失败')\r\n        logger.error('获取VIP信息失败', e)\r\n        this.hasError = true\r\n        this.errorMessage = '获取VIP信息失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    async retryLoading() {\r\n      this.hasError = false\r\n      this.loading = true\r\n      \r\n      try {\r\n        // 不再需要并行加载VIP等级数据\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        logger.error('VIP页面重新加载失败', e)\r\n        this.hasError = true\r\n        this.errorMessage = '加载数据失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    async claimVipReward() {\r\n      if (this.isClaiming) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimVipReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取VIP奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimHistoryReward(level) {\r\n      if (this.isClaiming) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimHistoryReward(level)\r\n        this.$toast && this.$toast('补领成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('补领失败')\r\n        logger.error('补领历史奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimDailyReward() {\r\n      if (this.isClaiming || !this.canClaimDaily) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimDailyReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取每日奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimWeeklyReward() {\r\n      if (this.isClaiming || !this.canClaimWeekly) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimWeeklyReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取每周奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimMonthlyReward() {\r\n      if (this.isClaiming || !this.canClaimMonthly) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimMonthlyReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取每月奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    showVipDetails(level) {\r\n      this.selectedVipLevel = level\r\n      // 找到对应等级的VIP信息\r\n      const levelInfo = this.vipLevels.find(item => item.level === level)\r\n      if (levelInfo) {\r\n        this.$toast && this.$toast(`VIP${level}特权: ${levelInfo.privileges.join('、')}`, 'info')\r\n      } else {\r\n        this.$toast && this.$toast(`VIP${level}特权详情`, 'info')\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    try {\r\n      // 并行加载数据以提高性能\r\n      await Promise.all([\r\n        this.fetchVipData()\r\n      ])\r\n    } catch (e) {\r\n      logger.error('VIP页面初始化失败', e)\r\n      // 设置错误状态\r\n      this.hasError = true\r\n      this.errorMessage = '加载数据失败，请稍后重试'\r\n      // 确保无论如何都结束加载状态\r\n      this.loading = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vip-page {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n  color: #ffd700;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.back-button-container {\r\n  position: fixed;\r\n  bottom: 40px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 10;\r\n  width: 100%;\r\n  max-width: 800px;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 0 20px;\r\n}\r\n\r\n.back-button {\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  width: 140px;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));\r\n  \r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: contain;\r\n  }\r\n  \r\n  &:hover {\r\n    transform: scale(1.05);\r\n    filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n// 添加设备适配变量\r\n:root {\r\n  --main-tab-padding: 12px 20px;\r\n  --main-tab-margin: 0 10px;\r\n  --main-tab-min-width: 100px;\r\n  --main-tab-font-size: 16px;\r\n  --section-padding: 20px;\r\n  --vip-icon-size: 120px;\r\n  --reward-tab-padding: 10px 20px;\r\n  --reward-tab-min-width: 90px;\r\n}\r\n\r\n.main-tabs {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 25px;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n  border: 2px solid #444;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.main-tab {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: var(--main-tab-padding);\r\n  margin: var(--main-tab-margin);\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border: 2px solid #555;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: var(--main-tab-min-width);\r\n  position: relative;\r\n  \r\n  span {\r\n    font-weight: bold;\r\n    font-size: var(--main-tab-font-size);\r\n    color: #ccc;\r\n    transition: color 0.3s ease;\r\n  }\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.5);\r\n    transform: translateY(-3px);\r\n    \r\n    span {\r\n      color: #fff;\r\n    }\r\n  }\r\n  \r\n  &.active {\r\n    background: linear-gradient(to bottom, rgba(255, 215, 0, 0.2), rgba(184, 134, 11, 0.2));\r\n    border-color: #ffd700;\r\n    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -2px;\r\n      left: 25%;\r\n      right: 25%;\r\n      height: 3px;\r\n      background: #ffd700;\r\n      border-radius: 3px;\r\n    }\r\n    \r\n    span {\r\n      color: #ffd700;\r\n    }\r\n  }\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.pixel-border-box {\r\n  border: 2px solid #ffd700;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  border-radius: 4px;\r\n  padding: var(--section-padding);\r\n  margin-bottom: 25px;\r\n  position: relative;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -2px;\r\n    left: -2px;\r\n    right: -2px;\r\n    bottom: -2px;\r\n    border: 1px solid #ff8800;\r\n    border-radius: 5px;\r\n    pointer-events: none;\r\n    z-index: 1;\r\n  }\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  text-align: center;\r\n  \r\n  .vip-title-with-icon {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 10px;\r\n    \r\n    .vip-small-icon {\r\n      width: 30px;\r\n      height: 30px;\r\n    }\r\n  }\r\n  \r\n  h3 {\r\n    color: #ffd700;\r\n    font-size: 20px;\r\n    margin: 0;\r\n    padding-bottom: 10px;\r\n    display: inline-block;\r\n  }\r\n  \r\n  .section-decor {\r\n    height: 2px;\r\n    background: linear-gradient(to right, transparent, #ffd700, transparent);\r\n    margin-top: 5px;\r\n  }\r\n}\r\n\r\n.vip-info {\r\n  text-align: center;\r\n  \r\n  .vip-info-header {\r\n    margin-bottom: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n  }\r\n  \r\n  .vip-icon {\r\n    width: var(--vip-icon-size);\r\n    height: var(--vip-icon-size);\r\n    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));\r\n    transition: transform 0.3s, filter 0.3s;\r\n    \r\n    &:hover {\r\n      transform: scale(1.05);\r\n      filter: drop-shadow(0 0 15px rgba(255, 215, 0, 1));\r\n    }\r\n  }\r\n}\r\n\r\n.vip-exp-section {\r\n  margin: 20px 0;\r\n}\r\n\r\n.vip-exp {\r\n  color: #ccc;\r\n  margin-bottom: 15px;\r\n  font-size: 15px;\r\n  \r\n  .vip-next-exp {\r\n    color: #ff8800;\r\n  }\r\n  \r\n  .highlight-text {\r\n    color: #ffd700;\r\n    font-weight: bold;\r\n    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);\r\n  }\r\n}\r\n\r\n.vip-progress-container {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  \r\n  .vip-progress-bar {\r\n    flex: 1;\r\n    height: 18px;\r\n    background: #222;\r\n    border: 1px solid #444;\r\n    border-radius: 9px;\r\n    overflow: hidden;\r\n    margin-right: 10px;\r\n    position: relative;\r\n    box-shadow: inset 0 2px 5px rgba(0,0,0,0.5);\r\n  }\r\n  \r\n  .vip-progress {\r\n    height: 100%;\r\n    background: linear-gradient(90deg, #ffd700, #ff8800);\r\n    border-radius: 9px;\r\n    transition: width 0.8s cubic-bezier(0.22, 1, 0.36, 1);\r\n    box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\r\n  }\r\n  \r\n  .progress-stars {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    pointer-events: none;\r\n  }\r\n  \r\n  .progress-star {\r\n    position: absolute;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    color: rgba(255, 255, 255, 0.6);\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .vip-progress-text {\r\n    width: 45px;\r\n    text-align: right;\r\n    font-size: 16px;\r\n    color: #ffd700;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.privilege-list {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style: none;\r\n}\r\n\r\n.privilege-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 15px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #4caf50;\r\n  transition: transform 0.2s, background 0.2s;\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.4);\r\n    transform: translateX(5px);\r\n  }\r\n  \r\n  .privilege-icon {\r\n    color: #4caf50;\r\n    margin-right: 12px;\r\n    font-style: normal;\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    width: 20px;\r\n    height: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: rgba(76, 175, 80, 0.2);\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.reward-tabs {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 20px;\r\n  padding: 5px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 6px;\r\n  border: 1px solid #444;\r\n}\r\n\r\n.reward-tab {\r\n  padding: var(--reward-tab-padding);\r\n  cursor: pointer;\r\n  background: #222;\r\n  border: 2px solid #444;\r\n  margin: 0 5px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  min-width: var(--reward-tab-min-width);\r\n  border-radius: 4px;\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -2px;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: transparent;\r\n  }\r\n  \r\n  &:hover {\r\n    border-color: #666;\r\n    transform: translateY(-2px);\r\n  }\r\n  \r\n  &.active {\r\n    background: linear-gradient(to bottom, #ffd700, #b8860b);\r\n    color: #000;\r\n    border-color: #ffd700;\r\n    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\r\n    \r\n    &::after {\r\n      background: #ffd700;\r\n    }\r\n  }\r\n}\r\n\r\n.daily-reward-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.reward-info-card {\r\n  width: 100%;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border: 1px solid #444;\r\n}\r\n\r\n.reward-list {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style: none;\r\n}\r\n\r\n.reward-list-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  margin-bottom: 8px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 4px;\r\n  transition: background 0.2s;\r\n  border-left: 3px solid #ffd700;\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.4);\r\n  }\r\n  \r\n  .reward-name {\r\n    font-weight: bold;\r\n    color: #ffd700;\r\n  }\r\n  \r\n  .reward-amount {\r\n    color: #ccc;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.history-list {\r\n  max-height: 250px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n  \r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n  \r\n  &::-webkit-scrollbar-track {\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 3px;\r\n  }\r\n  \r\n  &::-webkit-scrollbar-thumb {\r\n    background: #555;\r\n    border-radius: 3px;\r\n    \r\n    &:hover {\r\n      background: #666;\r\n    }\r\n  }\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 4px;\r\n  transition: background 0.2s;\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.4);\r\n  }\r\n  \r\n  .history-info {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n    \r\n    .history-vip-badge {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .history-vip-icon {\r\n        width: 30px;\r\n        height: 30px;\r\n        filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));\r\n      }\r\n    }\r\n    \r\n    .history-date {\r\n      color: #888;\r\n    }\r\n    \r\n    .history-desc {\r\n      flex-basis: 100%;\r\n      color: #ccc;\r\n      margin-top: 5px;\r\n    }\r\n  }\r\n}\r\n\r\n.pixel-button {\r\n  background: #333;\r\n  color: #fff;\r\n  border: 2px solid #555;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n  overflow: hidden;\r\n  \r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n  \r\n  &:active {\r\n    transform: translateY(1px);\r\n  }\r\n  \r\n  &.gold {\r\n    background: linear-gradient(to bottom, #ffd700, #b8860b);\r\n    border-color: #ffd700;\r\n    color: #000;\r\n    \r\n    &:hover {\r\n      box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);\r\n    }\r\n  }\r\n  \r\n  &.disabled {\r\n    background: #444;\r\n    border-color: #555;\r\n    color: #888;\r\n    cursor: not-allowed;\r\n    \r\n    &:hover {\r\n      transform: none;\r\n      box-shadow: none;\r\n    }\r\n  }\r\n  \r\n  &.small {\r\n    padding: 5px 12px;\r\n    font-size: 13px;\r\n  }\r\n  \r\n  &.shine-effect::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: -50%;\r\n    left: -50%;\r\n    width: 200%;\r\n    height: 200%;\r\n    background: linear-gradient(\r\n      to right,\r\n      rgba(255, 255, 255, 0) 0%,\r\n      rgba(255, 255, 255, 0.3) 50%,\r\n      rgba(255, 255, 255, 0) 100%\r\n    );\r\n    transform: rotate(45deg);\r\n    animation: shine 3s infinite;\r\n  }\r\n}\r\n\r\n@keyframes shine {\r\n  0% {\r\n    left: -150%;\r\n  }\r\n  100% {\r\n    left: 150%;\r\n  }\r\n}\r\n\r\n// 加载和错误状态样式\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 0;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 5px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 50%;\r\n  border-top-color: #ffd700;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.loading-text {\r\n  color: #ffd700;\r\n  font-size: 18px;\r\n  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 0;\r\n  \r\n  .error-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n    border-radius: 50%;\r\n    background: rgba(255, 0, 0, 0.2);\r\n    border: 3px solid #ff6b6b;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 40px;\r\n    font-weight: bold;\r\n    color: #ff6b6b;\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .error-text {\r\n    color: #ff6b6b;\r\n    font-size: 18px;\r\n    margin-bottom: 25px;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 大屏幕设备 (1200px以上)\r\n@media (min-width: 1201px) {\r\n  .vip-page {\r\n    max-width: 900px;\r\n    padding: 30px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 15px 30px;\r\n    min-width: 120px;\r\n    \r\n    span {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 150px;\r\n    height: 150px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 25px;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 平板设备 (768px - 1200px)\r\n@media (min-width: 768px) and (max-width: 1200px) {\r\n  .vip-page {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 12px 25px;\r\n    min-width: 110px;\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 100px;\r\n    height: 100px;\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 手机设备 (600px - 767px)\r\n@media (min-width: 601px) and (max-width: 767px) {\r\n  .vip-page {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 10px 20px;\r\n    min-width: 90px;\r\n    margin: 0 5px;\r\n    \r\n    span {\r\n      font-size: 15px;\r\n    }\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 90px;\r\n    height: 90px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 19px;\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 18px;\r\n  }\r\n  \r\n  .reward-tab {\r\n    padding: 8px 15px;\r\n    min-width: 85px;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 小屏幕手机设备 (600px以下)\r\n@media (max-width: 600px) {\r\n  .vip-page {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    flex-wrap: wrap;\r\n    padding: 8px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 10px;\r\n    margin: 5px;\r\n    min-width: 80px;\r\n    \r\n    span {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 15px;\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n  \r\n  .reward-tabs {\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .reward-tab {\r\n    padding: 8px 15px;\r\n    margin: 3px;\r\n    min-width: 80px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .reward-list-item {\r\n    padding: 8px 12px;\r\n    \r\n    .reward-name, .reward-amount {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .privilege-item {\r\n    font-size: 14px;\r\n    padding: 8px 12px;\r\n  }\r\n  \r\n  .vip-exp {\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .vip-progress-container {\r\n    .vip-progress-bar {\r\n      height: 15px;\r\n    }\r\n    \r\n    .vip-progress-text {\r\n      width: 40px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .history-item {\r\n    padding: 10px;\r\n    \r\n    .history-info {\r\n      gap: 8px;\r\n      \r\n      .history-vip-icon {\r\n        width: 25px;\r\n        height: 25px;\r\n      }\r\n      \r\n      .history-date, .history-desc {\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .back-button-container {\r\n    bottom: 30px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 120px;\r\n    height: 45px;\r\n  }\r\n}\r\n\r\n// 极小屏幕设备 (375px以下)\r\n@media (max-width: 375px) {\r\n  .vip-page {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 5px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 8px;\r\n    margin: 3px;\r\n    min-width: 70px;\r\n    \r\n    span {\r\n      font-size: 13px;\r\n    }\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .reward-tab {\r\n    padding: 6px 10px;\r\n    min-width: 70px;\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .pixel-button {\r\n    padding: 8px 15px;\r\n    font-size: 13px;\r\n    \r\n    &.small {\r\n      padding: 4px 10px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n  \r\n  .back-button-container {\r\n    bottom: 25px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 110px;\r\n    height: 40px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;AA2QA,OAAAA,UAAA;AACA,SAAAC,UAAA,EAAAC,cAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,kBAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAT;EAAA;EACAU,KAAA;IACA;MACAC,QAAA;MACAC,MAAA;MACAC,UAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,cAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,OAAA;MACAC,QAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACAC,QAAA;MACA,mCAAAjB,QAAA;IACA;IACAkB,WAAA;MACA,OAAAC,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,KAAA,MAAApB,MAAA,QAAAC,UAAA;IACA;EACA;EACAoB,OAAA;IACAC,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,WAAAC,KAAA;MACA,8BAAAA,KAAA;IACA;IACA,MAAAC,aAAA;MACA,KAAAjB,OAAA;MACA,KAAAC,QAAA;MAEA;QACA,MAAAb,IAAA,SAAAT,UAAA;QACA,KAAAU,QAAA,GAAAD,IAAA,CAAA4B,KAAA;QACA,KAAA1B,MAAA,GAAAF,IAAA,CAAA8B,GAAA;QACA,KAAA3B,UAAA,GAAAH,IAAA,CAAA+B,QAAA;QACA,KAAA1B,cAAA,GAAAL,IAAA,CAAAgC,gBAAA;QACA,KAAA1B,aAAA,GAAAN,IAAA,CAAAiC,UAAA;QACA,KAAA1B,gBAAA,GAAAP,IAAA,CAAAkC,cAAA;QACA,KAAA1B,aAAA,GAAAR,IAAA,CAAAmC,eAAA;QACA,KAAA1B,cAAA,GAAAT,IAAA,CAAAoC,gBAAA;QACA,KAAA1B,eAAA,GAAAV,IAAA,CAAAqC,iBAAA;QACAxC,MAAA,CAAAyC,IAAA;UAAAV,KAAA,OAAA3B,QAAA;UAAA6B,GAAA,OAAA5B;QAAA;MACA,SAAAqC,CAAA;QACA,KAAAC,MAAA,SAAAA,MAAA;QACA3C,MAAA,CAAA4C,KAAA,cAAAF,CAAA;QACA,KAAA1B,QAAA;QACA,KAAAC,YAAA;MACA;QACA,KAAAF,OAAA;MACA;IACA;IACA,MAAA8B,aAAA;MACA,KAAA7B,QAAA;MACA,KAAAD,OAAA;MAEA;QACA;QACA,WAAAiB,YAAA;MACA,SAAAU,CAAA;QACA1C,MAAA,CAAA4C,KAAA,gBAAAF,CAAA;QACA,KAAA1B,QAAA;QACA,KAAAC,YAAA;MACA;QACA,KAAAF,OAAA;MACA;IACA;IACA,MAAApB,eAAA;MACA,SAAAY,UAAA;MAEA,KAAAA,UAAA;MACA;QACA,MAAAZ,cAAA;QACA,KAAAgD,MAAA,SAAAA,MAAA;QACA,WAAAX,YAAA;MACA,SAAAU,CAAA;QACA,KAAAC,MAAA,SAAAA,MAAA;QACA3C,MAAA,CAAA4C,KAAA,cAAAF,CAAA;MACA;QACA,KAAAnC,UAAA;MACA;IACA;IACA,MAAAX,mBAAAmC,KAAA;MACA,SAAAxB,UAAA;MAEA,KAAAA,UAAA;MACA;QACA,MAAAX,kBAAA,CAAAmC,KAAA;QACA,KAAAY,MAAA,SAAAA,MAAA;QACA,WAAAX,YAAA;MACA,SAAAU,CAAA;QACA,KAAAC,MAAA,SAAAA,MAAA;QACA3C,MAAA,CAAA4C,KAAA,aAAAF,CAAA;MACA;QACA,KAAAnC,UAAA;MACA;IACA;IACA,MAAAV,iBAAA;MACA,SAAAU,UAAA,UAAAI,aAAA;MAEA,KAAAJ,UAAA;MACA;QACA,MAAAV,gBAAA;QACA,KAAA8C,MAAA,SAAAA,MAAA;QACA,WAAAX,YAAA;MACA,SAAAU,CAAA;QACA,KAAAC,MAAA,SAAAA,MAAA;QACA3C,MAAA,CAAA4C,KAAA,aAAAF,CAAA;MACA;QACA,KAAAnC,UAAA;MACA;IACA;IACA,MAAAT,kBAAA;MACA,SAAAS,UAAA,UAAAK,cAAA;MAEA,KAAAL,UAAA;MACA;QACA,MAAAT,iBAAA;QACA,KAAA6C,MAAA,SAAAA,MAAA;QACA,WAAAX,YAAA;MACA,SAAAU,CAAA;QACA,KAAAC,MAAA,SAAAA,MAAA;QACA3C,MAAA,CAAA4C,KAAA,aAAAF,CAAA;MACA;QACA,KAAAnC,UAAA;MACA;IACA;IACA,MAAAR,mBAAA;MACA,SAAAQ,UAAA,UAAAM,eAAA;MAEA,KAAAN,UAAA;MACA;QACA,MAAAR,kBAAA;QACA,KAAA4C,MAAA,SAAAA,MAAA;QACA,WAAAX,YAAA;MACA,SAAAU,CAAA;QACA,KAAAC,MAAA,SAAAA,MAAA;QACA3C,MAAA,CAAA4C,KAAA,aAAAF,CAAA;MACA;QACA,KAAAnC,UAAA;MACA;IACA;IACAuC,eAAAf,KAAA;MACA,KAAAjB,gBAAA,GAAAiB,KAAA;MACA;MACA,MAAAgB,SAAA,QAAAC,SAAA,CAAAC,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAnB,KAAA,KAAAA,KAAA;MACA,IAAAgB,SAAA;QACA,KAAAJ,MAAA,SAAAA,MAAA,OAAAZ,KAAA,OAAAgB,SAAA,CAAAX,UAAA,CAAAe,IAAA;MACA;QACA,KAAAR,MAAA,SAAAA,MAAA,OAAAZ,KAAA;MACA;IACA;EACA;EACA,MAAAqB,QAAA;IACA;MACA;MACA,MAAAC,OAAA,CAAAC,GAAA,EACA,KAAAtB,YAAA,GACA;IACA,SAAAU,CAAA;MACA1C,MAAA,CAAA4C,KAAA,eAAAF,CAAA;MACA;MACA,KAAA1B,QAAA;MACA,KAAAC,YAAA;MACA;MACA,KAAAF,OAAA;IACA;EACA;AACA", "ignoreList": []}]}