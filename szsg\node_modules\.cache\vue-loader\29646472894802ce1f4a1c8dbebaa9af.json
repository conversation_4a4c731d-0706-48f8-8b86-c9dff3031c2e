{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\RegionSelect.vue?vue&type=style&index=0&id=f8b01bc8&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\RegionSelect.vue", "mtime": 1750328969952}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yZWdpb24tc2VsZWN0LWNvbnRhaW5lciB7CiAgcGFkZGluZzogMzBweCAyMHB4OwogIG1heC13aWR0aDogMTQwMHB4OwogIG1hcmdpbjogMCBhdXRvOwogIG1pbi1oZWlnaHQ6IDEwMHZoOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KCi5yZWdpb24tc2VsZWN0LWNvbnRhaW5lcjo6YmVmb3JlIHsKICBjb250ZW50OiAnJzsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiAwOwogIGxlZnQ6IDA7CiAgcmlnaHQ6IDA7CiAgYm90dG9tOiAwOwogIGJhY2tncm91bmQ6CiAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSAyMCUsIHJnYmEoMjEyLCAxNzUsIDU1LCAwLjEpIDAlLCB0cmFuc3BhcmVudCA1MCUpLAogICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgODAlLCByZ2JhKDIxMiwgMTc1LCA1NSwgMC4wNSkgMCUsIHRyYW5zcGFyZW50IDUwJSk7CiAgcG9pbnRlci1ldmVudHM6IG5vbmU7CiAgei1pbmRleDogLTE7Cn0KCi5wYWdlLWhlYWRlciB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDUwcHg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgoudGl0bGUtY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDIwcHg7Cn0KCi50aXRsZS1iYWNrZ3JvdW5kIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMjBweDsKfQoKLnRpdGxlLWltYWdlIHsKICBtYXgtd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiBhdXRvOwogIGRpc3BsYXk6IGJsb2NrOwp9CgoucGFnZS1zdWJ0aXRsZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGNvbG9yOiAjY2NjOwogIG1hcmdpbjogMDsKICBmb250LXN0eWxlOiBpdGFsaWM7Cn0KCi5sb2FkaW5nLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgZmxleDogMTsKICBtaW4taGVpZ2h0OiAzMDBweDsKfQoKLmxvYWRpbmctc3Bpbm5lciB7CiAgd2lkdGg6IDQwcHg7CiAgaGVpZ2h0OiA0MHB4OwogIGJvcmRlcjogNHB4IHNvbGlkICMzMzM7CiAgYm9yZGVyLXRvcDogNHB4IHNvbGlkICNkNGFmMzc7CiAgYm9yZGVyLXJhZGl1czogNTAlOwogIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKQGtleWZyYW1lcyBzcGluIHsKICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9CiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH0KfQoKLmxvYWRpbmctdGV4dCB7CiAgY29sb3I6ICNjY2M7CiAgZm9udC1zaXplOiAxNnB4Owp9CgouZXJyb3ItY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZmxleDogMTsKICBtaW4taGVpZ2h0OiAzMDBweDsKfQoKLmVycm9yLW1lc3NhZ2UgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogI2ZmNmI2YjsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMTA3LCAxMDcsIDAuMSk7CiAgcGFkZGluZzogMzBweDsKICBib3JkZXItcmFkaXVzOiAxMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAxMDcsIDEwNywgMC4zKTsKfQoKLmVycm9yLWljb24gewogIGZvbnQtc2l6ZTogNDhweDsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIGRpc3BsYXk6IGJsb2NrOwp9CgouZXJyb3ItYWN0aW9ucyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEwcHg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogMTVweDsKfQoKLnJldHJ5LWJ0biB7CiAgcGFkZGluZzogOHB4IDE2cHg7CiAgYmFja2dyb3VuZDogI2Q0YWYzNzsKICBjb2xvcjogIzAwMDsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5yZXRyeS1idG46aG92ZXIgewogIGJhY2tncm91bmQ6ICNiODk0MWY7Cn0KCi5jbGVhbnVwLWJ0biB7CiAgcGFkZGluZzogOHB4IDE2cHg7CiAgYmFja2dyb3VuZDogI2ZmNmI2YjsKICBjb2xvcjogd2hpdGU7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgZm9udC1zaXplOiAxNHB4Owp9CgouY2xlYW51cC1idG46aG92ZXIgewogIGJhY2tncm91bmQ6ICNmZjUyNTI7Cn0KCi5yZWdpb25zLWNvbnRhaW5lciB7CiAgZmxleDogMTsKfQoKLnJlZ2lvbnMtaGVhZGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogNDBweDsKfQoKLnJlZ2lvbnMtdGl0bGUgewogIGZvbnQtc2l6ZTogMjhweDsKICBjb2xvcjogI2Q0YWYzNzsKICBtYXJnaW46IDAgMCAxMHB4IDA7CiAgdGV4dC1zaGFkb3c6IDAgMCA4cHggcmdiYSgyMTIsIDE3NSwgNTUsIDAuNCk7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5yZWdpb25zLXN1YnRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgY29sb3I6ICNjY2M7CiAgZm9udC1zdHlsZTogaXRhbGljOwp9CgoucmVnaW9ucy1saXN0IHsKICBtYXgtd2lkdGg6IDYwMHB4OwogIG1hcmdpbjogMCBhdXRvIDMwcHggYXV0bzsKfQoKLnJlZ2lvbi1pdGVtIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiA2MHB4OwogIG1hcmdpbi1ib3R0b206IDEycHg7CiAgYm9yZGVyOiAzcHggc29saWQgIzRhNTU2ODsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIG92ZXJmbG93OiBoaWRkZW47CiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5yZWdpb24taXRlbTpob3ZlciB7CiAgYm9yZGVyLWNvbG9yOiAjZDRhZjM3Owp9CgoucmVnaW9uLWl0ZW0uc2VsZWN0ZWQgewogIGJvcmRlci1jb2xvcjogI2Q0YWYzNzsKICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDIxMiwgMTc1LCA1NSwgMC41KTsKfQoKLnJlZ2lvbi1pdGVtLm1haW50ZW5hbmNlIHsKICBvcGFjaXR5OiAwLjY7CiAgYm9yZGVyLWNvbG9yOiAjNjY2Owp9CgoucmVnaW9uLWl0ZW0uYnVzeSB7CiAgYm9yZGVyLWNvbG9yOiAjZmY5ODAwOwp9CgoucmVnaW9uLW5hbWUtc2VjdGlvbiB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIHotaW5kZXg6IDM7CiAgcGFkZGluZzogMCAxNnB4OwogIGZsZXg6IDE7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuOCk7CiAgaGVpZ2h0OiAxMDAlOwogIGJvcmRlci1yYWRpdXM6IDVweDsKfQoKLnJlZ2lvbi1uYW1lIHsKICBmb250LXNpemU6IDIwcHg7CiAgY29sb3I6ICNkNGFmMzc7CiAgbWFyZ2luOiAwOwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIHRleHQtc2hhZG93OiAycHggMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuOCk7Cn0KCi5yZWdpb24tc3RhdHVzIHsKICBmb250LXNpemU6IDEycHg7CiAgcGFkZGluZzogM3B4IDZweDsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5yZWdpb24tc3RhdHVzLm9ubGluZSB7CiAgYmFja2dyb3VuZDogIzRjYWY1MDsKICBjb2xvcjogd2hpdGU7Cn0KCi5yZWdpb24tc3RhdHVzLmJ1c3kgewogIGJhY2tncm91bmQ6ICNmZjk4MDA7CiAgY29sb3I6IHdoaXRlOwp9CgoucmVnaW9uLXN0YXR1cy5tYWludGVuYW5jZSB7CiAgYmFja2dyb3VuZDogI2Y0NDMzNjsKICBjb2xvcjogd2hpdGU7Cn0KCi5yZWdpb24tc3RhdHVzLm9mZmxpbmUgewogIGJhY2tncm91bmQ6ICM2NjY7CiAgY29sb3I6IHdoaXRlOwp9CgoucGFnaW5hdGlvbi1pbmZvIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBjb2xvcjogI2Q0YWYzNzsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5hY3Rpb24tYnV0dG9ucyB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBnYXA6IDQwcHg7CiAgcGFkZGluZzogNDBweCAwOwogIG1hcmdpbi10b3A6IDIwcHg7Cn0KCi5idG4tYmFjay1pbWFnZSB7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgouYnRuLWJhY2staW1hZ2UgaW1nIHsKICBtYXgtd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiBhdXRvOwogIGRpc3BsYXk6IGJsb2NrOwogIHRyYW5zaXRpb246IGFsbCAwLjFzIGVhc2U7Cn0KCi5idG4tYmFjay1pbWFnZTpob3ZlciBpbWcgewogIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7Cn0KCi5idG4tYmFjay1pbWFnZS5wcmVzc2VkIGltZyB7CiAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTsKfQoKLmJ0bi1lbnRlci1pbWFnZSB7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgouYnRuLWVudGVyLWltYWdlIGltZyB7CiAgbWF4LXdpZHRoOiAxMDAlOwogIGhlaWdodDogYXV0bzsKICBkaXNwbGF5OiBibG9jazsKICB0cmFuc2l0aW9uOiBhbGwgMC4xcyBlYXNlOwp9CgouYnRuLWVudGVyLWltYWdlOmhvdmVyOm5vdCguZGlzYWJsZWQpIGltZyB7CiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsKfQoKLmJ0bi1lbnRlci1pbWFnZS5wcmVzc2VkIGltZyB7CiAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTsKfQoKLmJ0bi1lbnRlci1pbWFnZS5kaXNhYmxlZCB7CiAgY3Vyc29yOiBub3QtYWxsb3dlZDsKICBvcGFjaXR5OiAwLjU7Cn0KCi5idG4tZW50ZXItaW1hZ2UuZGlzYWJsZWQgaW1nIHsKICBmaWx0ZXI6IGdyYXlzY2FsZSgxMDAlKTsKfQoKLnBhZ2UtZm9vdGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogNDBweDsKICBwYWRkaW5nOiAyMHB4IDA7Cn0KCi5mb290ZXItdGV4dCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjODg4OwogIGZvbnQtc3R5bGU6IGl0YWxpYzsKfQo="}, {"version": 3, "sources": ["RegionSelect.vue"], "names": [], "mappings": ";AA2OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "RegionSelect.vue", "sourceRoot": "src/views/setup", "sourcesContent": ["<template>\n  <GameLayout\n    page-type=\"region-select\"\n    custom-title=\" \"\n  >\n    <div class=\"region-select-container\">\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-spinner\"></div>\n        <p class=\"loading-text\">正在加载大区列表...</p>\n      </div>\n\n      <!-- 错误提示 -->\n      <div v-if=\"error && !isLoading\" class=\"error-container\">\n        <div class=\"error-message\">\n          <i class=\"error-icon\">⚠️</i>\n          <p>{{ error }}</p>\n          <div class=\"error-actions\">\n            <button @click=\"loadRegionList\" class=\"retry-btn\">重试</button>\n            <button v-if=\"showCleanupButton\" @click=\"goToCleanup\" class=\"cleanup-btn\">清理存储</button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 大区列表 -->\n      <div v-if=\"!isLoading && !error\" class=\"regions-container\">\n\n        <div class=\"regions-list\">\n          <div\n            v-for=\"region in regions\"\n            :key=\"region.id\"\n            class=\"region-item\"\n            :class=\"{\n              'selected': selectedRegion?.id === region.id,\n              'maintenance': !region.isActive,\n              'busy': region.isPvp\n            }\"\n            @click=\"selectRegionLocal(region)\"\n          >\n            <div class=\"region-name-section\">\n              <h3 class=\"region-name\">{{ region.name }}</h3>\n              <div class=\"region-status\" :class=\"region.isActive ? 'online' : 'maintenance'\">\n                {{ region.isActive ? '正常' : '维护中' }}\n              </div>\n            </div>\n\n          </div>\n        </div>\n\n        <!-- 分页信息 -->\n        <div class=\"pagination-info\">\n          第1/1页\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <div\n            @click=\"goBack\"\n            @mousedown=\"handleBackMouseDown\"\n            @mouseup=\"handleBackMouseUp\"\n            @mouseleave=\"handleBackMouseUp\"\n            class=\"btn-back-image\"\n            :class=\"{ 'pressed': isBackPressed }\"\n          >\n            <img\n              :src=\"getBackButtonImage()\"\n              alt=\"返回\"\n              draggable=\"false\"\n            />\n          </div>\n          <div\n            @click=\"confirmSelection\"\n            @mousedown=\"handleEnterMouseDown\"\n            @mouseup=\"handleEnterMouseUp\"\n            @mouseleave=\"handleEnterMouseUp\"\n            class=\"btn-enter-image\"\n            :class=\"{ 'disabled': !selectedRegion, 'pressed': isEnterPressed }\"\n          >\n            <img\n              :src=\"getEnterButtonImage()\"\n              alt=\"进入游戏\"\n              draggable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapState, mapActions } from 'vuex'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'RegionSelect',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      selectedRegion: null,\n      isLoading: false,\n      error: null,\n      isEnterPressed: false,\n      isBackPressed: false,\n      showCleanupButton: false\n    }\n  },\n  computed: {\n    ...mapState('game', ['regions'])\n  },\n  async created() {\n    logger.debug('[RegionSelect] 页面初始化')\n    await this.loadRegionList()\n  },\n  methods: {\n    ...mapActions('game', ['loadRegions', 'selectRegion', 'loadRecommendedRegions']),\n\n    async loadRegionList() {\n      this.isLoading = true\n      this.error = null\n\n      try {\n        await this.loadRegions()\n        if (this.regions.length === 0) {\n          this.error = '暂无可用大区'\n        }\n      } catch (error) {\n        logger.error('[RegionSelect] 加载大区失败:', error)\n        this.error = error.message || '加载大区失败，请重试'\n        this.showCleanupButton = false\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    selectRegionLocal(region) {\n      this.selectedRegion = region\n      logger.debug('[RegionSelect] 选择大区:', region.name)\n    },\n\n    async confirmSelection() {\n      if (!this.selectedRegion) {\n        this.showToast('请先选择一个大区')\n        return\n      }\n\n      try {\n        const success = await this.selectRegion(this.selectedRegion)\n        if (success) {\n          this.showToast('大区选择成功')\n          // 跳转到角色选择页面\n          this.$router.push('/setup/character-select')\n        } else {\n          this.showToast('选择大区失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[RegionSelect] 确认选择失败:', error)\n        this.showToast('选择大区失败，请重试')\n      }\n    },\n\n    goBack() {\n      this.$router.go(-1)\n    },\n\n    goToCleanup() {\n      this.$router.push('/debug/storage')\n    },\n\n    getStatusText(isActive) {\n      return isActive ? '正常' : '维护中';\n    },\n\n    handleEnterMouseDown() {\n      if (this.selectedRegion) {\n        this.isEnterPressed = true\n      }\n    },\n\n    handleEnterMouseUp() {\n      this.isEnterPressed = false\n    },\n\n    getEnterButtonImage() {\n      if (!this.selectedRegion) {\n        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片\n      }\n      return this.isEnterPressed\n        ? '/static/game/UI/anniu/jr_4.png'\n        : '/static/game/UI/anniu/jr_3.png'\n    },\n\n    handleBackMouseDown() {\n      this.isBackPressed = true\n    },\n\n    handleBackMouseUp() {\n      this.isBackPressed = false\n    },\n\n    getBackButtonImage() {\n      return this.isBackPressed\n        ? '/static/game/UI/anniu/fhui_.png'\n        : '/static/game/UI/anniu/fhui_2.png'\n    },\n\n    showToast(message) {\n      // 简单的提示实现\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.region-select-container {\n  padding: 30px 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n\n.region-select-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);\n  pointer-events: none;\n  z-index: -1;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 50px;\n  position: relative;\n}\n\n.title-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n}\n\n.title-background {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n}\n\n.title-image {\n  max-width: 100%;\n  height: auto;\n  display: block;\n}\n\n.page-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  margin: 0;\n  font-style: italic;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #333;\n  border-top: 4px solid #d4af37;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  color: #ccc;\n  font-size: 16px;\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.error-message {\n  text-align: center;\n  color: #ff6b6b;\n  background: rgba(255, 107, 107, 0.1);\n  padding: 30px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.error-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  margin-top: 15px;\n}\n\n.retry-btn {\n  padding: 8px 16px;\n  background: #d4af37;\n  color: #000;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.retry-btn:hover {\n  background: #b8941f;\n}\n\n.cleanup-btn {\n  padding: 8px 16px;\n  background: #ff6b6b;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.cleanup-btn:hover {\n  background: #ff5252;\n}\n\n.regions-container {\n  flex: 1;\n}\n\n.regions-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.regions-title {\n  font-size: 28px;\n  color: #d4af37;\n  margin: 0 0 10px 0;\n  text-shadow: 0 0 8px rgba(212, 175, 55, 0.4);\n  font-weight: bold;\n}\n\n.regions-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  font-style: italic;\n}\n\n.regions-list {\n  max-width: 600px;\n  margin: 0 auto 30px auto;\n}\n\n.region-item {\n  position: relative;\n  height: 60px;\n  margin-bottom: 12px;\n  border: 3px solid #4a5568;\n  border-radius: 8px;\n  cursor: pointer;\n  overflow: hidden;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n}\n\n.region-item:hover {\n  border-color: #d4af37;\n}\n\n.region-item.selected {\n  border-color: #d4af37;\n  box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);\n}\n\n.region-item.maintenance {\n  opacity: 0.6;\n  border-color: #666;\n}\n\n.region-item.busy {\n  border-color: #ff9800;\n}\n\n.region-name-section {\n  position: relative;\n  z-index: 3;\n  padding: 0 16px;\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background: rgba(0, 0, 0, 0.8);\n  height: 100%;\n  border-radius: 5px;\n}\n\n.region-name {\n  font-size: 20px;\n  color: #d4af37;\n  margin: 0;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n}\n\n.region-status {\n  font-size: 12px;\n  padding: 3px 6px;\n  border-radius: 3px;\n  font-weight: bold;\n}\n\n.region-status.online {\n  background: #4caf50;\n  color: white;\n}\n\n.region-status.busy {\n  background: #ff9800;\n  color: white;\n}\n\n.region-status.maintenance {\n  background: #f44336;\n  color: white;\n}\n\n.region-status.offline {\n  background: #666;\n  color: white;\n}\n\n.pagination-info {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #d4af37;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 40px;\n  padding: 40px 0;\n  margin-top: 20px;\n}\n\n.btn-back-image {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-back-image img {\n  max-width: 100%;\n  height: auto;\n  display: block;\n  transition: all 0.1s ease;\n}\n\n.btn-back-image:hover img {\n  transform: scale(1.05);\n}\n\n.btn-back-image.pressed img {\n  transform: scale(0.95);\n}\n\n.btn-enter-image {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-enter-image img {\n  max-width: 100%;\n  height: auto;\n  display: block;\n  transition: all 0.1s ease;\n}\n\n.btn-enter-image:hover:not(.disabled) img {\n  transform: scale(1.05);\n}\n\n.btn-enter-image.pressed img {\n  transform: scale(0.95);\n}\n\n.btn-enter-image.disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n\n.btn-enter-image.disabled img {\n  filter: grayscale(100%);\n}\n\n.page-footer {\n  text-align: center;\n  margin-top: 40px;\n  padding: 20px 0;\n}\n\n.footer-text {\n  font-size: 14px;\n  color: #888;\n  font-style: italic;\n}\n</style>\n"]}]}