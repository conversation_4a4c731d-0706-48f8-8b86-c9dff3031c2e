{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\fix431Error.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\fix431Error.js", "mtime": 1749872938375}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["logger", "fix431Error", "info", "clearAllCookies", "clearLargeLocalStorageItems", "sessionStorage", "clear", "e", "warn", "clearBrowserCache", "error", "debug", "cookies", "document", "cookie", "split", "for<PERSON>ach", "eqPos", "indexOf", "name", "substr", "trim", "expireDate", "paths", "domains", "path", "domain", "domainStr", "keysToRemove", "i", "localStorage", "length", "key", "value", "getItem", "size", "startsWith", "includes", "push", "removeItem", "minimalState", "auth", "isAuthenticated", "game", "settings", "language", "setItem", "JSON", "stringify", "window", "caches", "keys", "then", "names", "delete", "catch", "check431Risk", "totalSize", "cookieSize", "estimatedHeaderSize", "hasRisk", "localStorageSize", "message", "minimalFetch", "url", "options", "response", "fetch", "method", "headers", "credentials", "cache", "ok", "Error", "status", "statusText", "json"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/utils/fix431Error.js"], "sourcesContent": ["/**\n * 专门用于修复HTTP 431错误的工具函数\n */\nimport logger from './logger.js';\n\n/**\n * 立即清理所有可能导致431错误的数据\n */\nexport function fix431Error() {\n  logger.info('[Fix431] 开始修复HTTP 431错误...');\n\n  try {\n    // 1. 清理所有cookies\n    clearAllCookies();\n\n    // 2. 清理localStorage\n    clearLargeLocalStorageItems();\n\n    // 3. 清理sessionStorage\n    try {\n      sessionStorage.clear();\n    } catch (e) {\n      logger.warn('[Fix431] 清理sessionStorage失败:', e);\n    }\n\n    // 4. 清理可能的缓存\n    clearBrowserCache();\n\n    logger.info('[Fix431] HTTP 431错误修复完成');\n    return true;\n  } catch (error) {\n    logger.error('[Fix431] 修复失败:', error);\n    return false;\n  }\n}\n\n/**\n * 清理所有cookies\n */\nfunction clearAllCookies() {\n  try {\n    logger.debug('[Fix431] 清理cookies...');\n\n    // 获取所有cookies\n    const cookies = document.cookie.split(';');\n\n    // 清理每个cookie，尝试多种路径和域名\n    cookies.forEach(cookie => {\n      const eqPos = cookie.indexOf('=');\n      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();\n      if (name) {\n        // 多种清理方式确保彻底删除\n        const expireDate = 'Thu, 01 Jan 1970 00:00:00 GMT';\n        const paths = ['/', '/api', '/api/'];\n        const domains = ['', 'localhost', '.localhost', '127.0.0.1', '.127.0.0.1'];\n\n        paths.forEach(path => {\n          domains.forEach(domain => {\n            const domainStr = domain ? `;domain=${domain}` : '';\n            document.cookie = `${name}=;expires=${expireDate};path=${path}${domainStr}`;\n          });\n        });\n      }\n    });\n\n    logger.debug('[Fix431] Cookies清理完成');\n  } catch (e) {\n    logger.error('[Fix431] 清理cookies失败:', e);\n  }\n}\n\n/**\n * 清理大型localStorage项目\n */\nfunction clearLargeLocalStorageItems() {\n  try {\n    logger.debug('[Fix431] 清理localStorage...');\n\n    const keysToRemove = [];\n\n    // 检查所有localStorage项目\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (key) {\n        const value = localStorage.getItem(key) || '';\n        const size = key.length + value.length;\n\n        // 清理大于5KB的项目，或者特定的缓存项目\n        if (size > 5120 ||\n            key.startsWith('SZXY_CACHE_') ||\n            key.startsWith('cache_') ||\n            key.startsWith('vuex') ||\n            key.includes('token') ||\n            key.includes('session')) {\n          keysToRemove.push(key);\n        }\n      }\n    }\n\n    // 删除标记的项目\n    keysToRemove.forEach(key => {\n      localStorage.removeItem(key);\n      logger.debug(`[Fix431] 删除localStorage项目: ${key}`);\n    });\n\n    // 重新创建最小化的游戏状态\n    const minimalState = {\n      auth: {\n        isAuthenticated: false\n      },\n      game: {\n        settings: {\n          language: 'zh-CN'\n        }\n      }\n    };\n\n    localStorage.setItem('szxy-game-state', JSON.stringify(minimalState));\n\n    logger.debug('[Fix431] localStorage清理完成');\n  } catch (e) {\n    logger.error('[Fix431] 清理localStorage失败:', e);\n  }\n}\n\n/**\n * 尝试清理浏览器缓存\n */\nfunction clearBrowserCache() {\n  try {\n    logger.debug('[Fix431] 尝试清理浏览器缓存...');\n\n    // 如果支持，清理缓存存储\n    if ('caches' in window) {\n      caches.keys().then(names => {\n        names.forEach(name => {\n          caches.delete(name);\n        });\n      }).catch(e => {\n        logger.warn('[Fix431] 清理缓存存储失败:', e);\n      });\n    }\n\n    // 清理可能的IndexedDB\n    if ('indexedDB' in window) {\n      try {\n        // 这里可以添加清理IndexedDB的逻辑\n        logger.debug('[Fix431] IndexedDB清理跳过（需要具体实现）');\n      } catch (e) {\n        logger.warn('[Fix431] 清理IndexedDB失败:', e);\n      }\n    }\n\n    logger.debug('[Fix431] 浏览器缓存清理完成');\n  } catch (e) {\n    logger.error('[Fix431] 清理浏览器缓存失败:', e);\n  }\n}\n\n/**\n * 检查是否可能出现431错误\n */\nexport function check431Risk() {\n  try {\n    let totalSize = 0;\n    \n    // 检查localStorage大小\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (key) {\n        const value = localStorage.getItem(key) || '';\n        totalSize += key.length + value.length;\n      }\n    }\n    \n    // 检查cookies大小\n    const cookieSize = document.cookie.length;\n    \n    // 估算请求头大小\n    const estimatedHeaderSize = cookieSize + 2048; // 基础头部大小\n    \n    logger.debug(`[Fix431] 风险检查 - localStorage: ${totalSize} bytes, cookies: ${cookieSize} bytes, 估算请求头: ${estimatedHeaderSize} bytes`);\n    \n    // 如果估算的请求头大小超过8KB，认为有风险\n    return {\n      hasRisk: estimatedHeaderSize > 8192,\n      localStorageSize: totalSize,\n      cookieSize: cookieSize,\n      estimatedHeaderSize: estimatedHeaderSize\n    };\n  } catch (e) {\n    logger.error('[Fix431] 风险检查失败:', e);\n    return {\n      hasRisk: true,\n      error: e.message\n    };\n  }\n}\n\n/**\n * 创建最小化的fetch请求\n */\nexport async function minimalFetch(url, options = {}) {\n  try {\n    const response = await fetch(url, {\n      method: options.method || 'GET',\n      headers: {\n        'Accept': 'application/json'\n      },\n      credentials: 'omit',\n      cache: 'no-cache',\n      ...options\n    });\n    \n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n    \n    return await response.json();\n  } catch (error) {\n    logger.error('[Fix431] 最小化请求失败:', error);\n    throw error;\n  }\n}\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA,OAAOA,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5BD,MAAM,CAACE,IAAI,CAAC,4BAA4B,CAAC;EAEzC,IAAI;IACF;IACAC,eAAe,CAAC,CAAC;;IAEjB;IACAC,2BAA2B,CAAC,CAAC;;IAE7B;IACA,IAAI;MACFC,cAAc,CAACC,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVP,MAAM,CAACQ,IAAI,CAAC,8BAA8B,EAAED,CAAC,CAAC;IAChD;;IAEA;IACAE,iBAAiB,CAAC,CAAC;IAEnBT,MAAM,CAACE,IAAI,CAAC,yBAAyB,CAAC;IACtC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdV,MAAM,CAACU,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACrC,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA,SAASP,eAAeA,CAAA,EAAG;EACzB,IAAI;IACFH,MAAM,CAACW,KAAK,CAAC,uBAAuB,CAAC;;IAErC;IACA,MAAMC,OAAO,GAAGC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;;IAE1C;IACAH,OAAO,CAACI,OAAO,CAACF,MAAM,IAAI;MACxB,MAAMG,KAAK,GAAGH,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;MACjC,MAAMC,IAAI,GAAGF,KAAK,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACM,MAAM,CAAC,CAAC,EAAEH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC,GAAGP,MAAM,CAACO,IAAI,CAAC,CAAC;MACxE,IAAIF,IAAI,EAAE;QACR;QACA,MAAMG,UAAU,GAAG,+BAA+B;QAClD,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC;QACpC,MAAMC,OAAO,GAAG,CAAC,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;QAE1ED,KAAK,CAACP,OAAO,CAACS,IAAI,IAAI;UACpBD,OAAO,CAACR,OAAO,CAACU,MAAM,IAAI;YACxB,MAAMC,SAAS,GAAGD,MAAM,GAAG,WAAWA,MAAM,EAAE,GAAG,EAAE;YACnDb,QAAQ,CAACC,MAAM,GAAG,GAAGK,IAAI,aAAaG,UAAU,SAASG,IAAI,GAAGE,SAAS,EAAE;UAC7E,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF3B,MAAM,CAACW,KAAK,CAAC,sBAAsB,CAAC;EACtC,CAAC,CAAC,OAAOJ,CAAC,EAAE;IACVP,MAAM,CAACU,KAAK,CAAC,uBAAuB,EAAEH,CAAC,CAAC;EAC1C;AACF;;AAEA;AACA;AACA;AACA,SAASH,2BAA2BA,CAAA,EAAG;EACrC,IAAI;IACFJ,MAAM,CAACW,KAAK,CAAC,4BAA4B,CAAC;IAE1C,MAAMiB,YAAY,GAAG,EAAE;;IAEvB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,YAAY,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5C,MAAMG,GAAG,GAAGF,YAAY,CAACE,GAAG,CAACH,CAAC,CAAC;MAC/B,IAAIG,GAAG,EAAE;QACP,MAAMC,KAAK,GAAGH,YAAY,CAACI,OAAO,CAACF,GAAG,CAAC,IAAI,EAAE;QAC7C,MAAMG,IAAI,GAAGH,GAAG,CAACD,MAAM,GAAGE,KAAK,CAACF,MAAM;;QAEtC;QACA,IAAII,IAAI,GAAG,IAAI,IACXH,GAAG,CAACI,UAAU,CAAC,aAAa,CAAC,IAC7BJ,GAAG,CAACI,UAAU,CAAC,QAAQ,CAAC,IACxBJ,GAAG,CAACI,UAAU,CAAC,MAAM,CAAC,IACtBJ,GAAG,CAACK,QAAQ,CAAC,OAAO,CAAC,IACrBL,GAAG,CAACK,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC3BT,YAAY,CAACU,IAAI,CAACN,GAAG,CAAC;QACxB;MACF;IACF;;IAEA;IACAJ,YAAY,CAACZ,OAAO,CAACgB,GAAG,IAAI;MAC1BF,YAAY,CAACS,UAAU,CAACP,GAAG,CAAC;MAC5BhC,MAAM,CAACW,KAAK,CAAC,8BAA8BqB,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC;;IAEF;IACA,MAAMQ,YAAY,GAAG;MACnBC,IAAI,EAAE;QACJC,eAAe,EAAE;MACnB,CAAC;MACDC,IAAI,EAAE;QACJC,QAAQ,EAAE;UACRC,QAAQ,EAAE;QACZ;MACF;IACF,CAAC;IAEDf,YAAY,CAACgB,OAAO,CAAC,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAACR,YAAY,CAAC,CAAC;IAErExC,MAAM,CAACW,KAAK,CAAC,2BAA2B,CAAC;EAC3C,CAAC,CAAC,OAAOJ,CAAC,EAAE;IACVP,MAAM,CAACU,KAAK,CAAC,4BAA4B,EAAEH,CAAC,CAAC;EAC/C;AACF;;AAEA;AACA;AACA;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,IAAI;IACFT,MAAM,CAACW,KAAK,CAAC,uBAAuB,CAAC;;IAErC;IACA,IAAI,QAAQ,IAAIsC,MAAM,EAAE;MACtBC,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,IAAI,CAACC,KAAK,IAAI;QAC1BA,KAAK,CAACrC,OAAO,CAACG,IAAI,IAAI;UACpB+B,MAAM,CAACI,MAAM,CAACnC,IAAI,CAAC;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC,CAACoC,KAAK,CAAChD,CAAC,IAAI;QACZP,MAAM,CAACQ,IAAI,CAAC,oBAAoB,EAAED,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,WAAW,IAAI0C,MAAM,EAAE;MACzB,IAAI;QACF;QACAjD,MAAM,CAACW,KAAK,CAAC,gCAAgC,CAAC;MAChD,CAAC,CAAC,OAAOJ,CAAC,EAAE;QACVP,MAAM,CAACQ,IAAI,CAAC,yBAAyB,EAAED,CAAC,CAAC;MAC3C;IACF;IAEAP,MAAM,CAACW,KAAK,CAAC,oBAAoB,CAAC;EACpC,CAAC,CAAC,OAAOJ,CAAC,EAAE;IACVP,MAAM,CAACU,KAAK,CAAC,qBAAqB,EAAEH,CAAC,CAAC;EACxC;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASiD,YAAYA,CAAA,EAAG;EAC7B,IAAI;IACF,IAAIC,SAAS,GAAG,CAAC;;IAEjB;IACA,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,YAAY,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5C,MAAMG,GAAG,GAAGF,YAAY,CAACE,GAAG,CAACH,CAAC,CAAC;MAC/B,IAAIG,GAAG,EAAE;QACP,MAAMC,KAAK,GAAGH,YAAY,CAACI,OAAO,CAACF,GAAG,CAAC,IAAI,EAAE;QAC7CyB,SAAS,IAAIzB,GAAG,CAACD,MAAM,GAAGE,KAAK,CAACF,MAAM;MACxC;IACF;;IAEA;IACA,MAAM2B,UAAU,GAAG7C,QAAQ,CAACC,MAAM,CAACiB,MAAM;;IAEzC;IACA,MAAM4B,mBAAmB,GAAGD,UAAU,GAAG,IAAI,CAAC,CAAC;;IAE/C1D,MAAM,CAACW,KAAK,CAAC,iCAAiC8C,SAAS,oBAAoBC,UAAU,kBAAkBC,mBAAmB,QAAQ,CAAC;;IAEnI;IACA,OAAO;MACLC,OAAO,EAAED,mBAAmB,GAAG,IAAI;MACnCE,gBAAgB,EAAEJ,SAAS;MAC3BC,UAAU,EAAEA,UAAU;MACtBC,mBAAmB,EAAEA;IACvB,CAAC;EACH,CAAC,CAAC,OAAOpD,CAAC,EAAE;IACVP,MAAM,CAACU,KAAK,CAAC,kBAAkB,EAAEH,CAAC,CAAC;IACnC,OAAO;MACLqD,OAAO,EAAE,IAAI;MACblD,KAAK,EAAEH,CAAC,CAACuD;IACX,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAeC,YAAYA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAE;MAChCI,MAAM,EAAEH,OAAO,CAACG,MAAM,IAAI,KAAK;MAC/BC,OAAO,EAAE;QACP,QAAQ,EAAE;MACZ,CAAC;MACDC,WAAW,EAAE,MAAM;MACnBC,KAAK,EAAE,UAAU;MACjB,GAAGN;IACL,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,QAAQP,QAAQ,CAACQ,MAAM,KAAKR,QAAQ,CAACS,UAAU,EAAE,CAAC;IACpE;IAEA,OAAO,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOlE,KAAK,EAAE;IACdV,MAAM,CAACU,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IACxC,MAAMA,KAAK;EACb;AACF", "ignoreList": []}]}