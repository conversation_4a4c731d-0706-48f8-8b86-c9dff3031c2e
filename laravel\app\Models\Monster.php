<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class Monster extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'title',
        'description',
        'avatar',
        'level',
        'type',
        'element',
        'size',
        'location_id',
        'x',
        'y',
        'spawn_radius',
        'max_health',
        'current_health',
        'max_mana',
        'current_mana',
        'attack',
        'defense',
        'magic_attack',
        'magic_defense',
        'speed',
        'accuracy',
        'dodge',
        'critical',
        'ai_type',
        'aggro_range',
        'chase_range',
        'skills',
        'resistances',
        'is_active',
        'is_alive',
        'is_boss',
        'respawn_time',
        'last_death_time',
        'next_respawn_time',
        'drop_items',
        'exp_reward',
        'silver_reward',
        'drop_rate',
        'special_abilities',
        'immunities',
        'threat_level',
    ];

    protected $casts = [
        'skills' => 'array',
        'resistances' => 'array',
        'drop_items' => 'array',
        'special_abilities' => 'array',
        'immunities' => 'array',
        'is_active' => 'boolean',
        'is_alive' => 'boolean',
        'is_boss' => 'boolean',
        'last_death_time' => 'datetime',
        'next_respawn_time' => 'datetime',
    ];

    /**
     * 获取怪物所在位置
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * 检查怪物是否存活
     */
    public function isAlive()
    {
        return $this->is_alive && $this->current_health > 0;
    }

    /**
     * 检查怪物是否可以重生
     */
    public function canRespawn()
    {
        if ($this->is_alive) {
            return false;
        }

        if (!$this->next_respawn_time) {
            return true;
        }

        return Carbon::now()->gte($this->next_respawn_time);
    }

    /**
     * 杀死怪物
     */
    public function kill()
    {
        $this->is_alive = false;
        $this->current_health = 0;
        $this->last_death_time = Carbon::now();
        $this->next_respawn_time = Carbon::now()->addSeconds($this->respawn_time);
        $this->save();
    }

    /**
     * 重生怪物
     */
    public function respawn()
    {
        $this->is_alive = true;
        $this->current_health = $this->max_health;
        $this->current_mana = $this->max_mana;
        $this->next_respawn_time = null;
        $this->save();
    }

    /**
     * 获取掉落物品
     */
    public function getDropItems()
    {
        if (!$this->drop_items) {
            return collect();
        }

        $drops = collect();
        foreach ($this->drop_items as $dropConfig) {
            $itemId = $dropConfig['item_id'] ?? null;
            $dropRate = $dropConfig['rate'] ?? 100;
            $quantity = $dropConfig['quantity'] ?? 1;

            if ($itemId && rand(1, 100) <= $dropRate) {
                $item = Item::find($itemId);
                if ($item) {
                    $drops->push([
                        'item' => $item,
                        'quantity' => $quantity
                    ]);
                }
            }
        }

        return $drops;
    }

    /**
     * 获取战斗属性
     */
    public function getBattleStats()
    {
        return [
            'health' => $this->current_health,
            'max_health' => $this->max_health,
            'mana' => $this->current_mana,
            'max_mana' => $this->max_mana,
            'attack' => $this->attack,
            'defense' => $this->defense,
            'magic_attack' => $this->magic_attack,
            'magic_defense' => $this->magic_defense,
            'speed' => $this->speed,
            'accuracy' => $this->accuracy,
            'dodge' => $this->dodge,
            'critical' => $this->critical,
        ];
    }

    /**
     * 获取怪物的完整信息
     */
    public function getFullInfo()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'title' => $this->title,
            'description' => $this->description,
            'avatar' => $this->avatar,
            'level' => $this->level,
            'type' => $this->type,
            'element' => $this->element,
            'size' => $this->size,
            'is_boss' => $this->is_boss,
            'is_alive' => $this->is_alive,
            'threat_level' => $this->threat_level,
            'stats' => $this->getBattleStats(),
            'location' => $this->location->name ?? '未知',
        ];
    }

    /**
     * 根据位置获取活跃的怪物
     */
    public static function getByLocation($locationId)
    {
        return static::where('location_id', $locationId)
            ->where('is_active', true)
            ->where('is_alive', true)
            ->orderBy('level')
            ->get();
    }

    /**
     * 获取BOSS怪物
     */
    public static function getBosses()
    {
        return static::where('is_boss', true)
            ->where('is_active', true)
            ->get();
    }

    /**
     * 根据等级范围获取怪物
     */
    public static function getByLevelRange($minLevel, $maxLevel)
    {
        return static::whereBetween('level', [$minLevel, $maxLevel])
            ->where('is_active', true)
            ->where('is_alive', true)
            ->get();
    }

    /**
     * 获取需要重生的怪物
     */
    public static function getNeedRespawn()
    {
        return static::where('is_alive', false)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('next_respawn_time')
                    ->orWhere('next_respawn_time', '<=', Carbon::now());
            })
            ->get();
    }

    /**
     * 批量重生怪物
     */
    public static function respawnAll()
    {
        $monsters = static::getNeedRespawn();
        foreach ($monsters as $monster) {
            $monster->respawn();
        }
        return $monsters->count();
    }
}
