/**
 * 西游记主题地图数据配置
 * 用于开发和测试的模拟地图数据
 */

export const locations = {
  // 东胜神洲区域
  dongsheng_shenzhou: {
    id: 'dongsheng_shenzhou',
    name: '东胜神洲',
    type: 'town',
    description: '四大部洲之一，繁华的仙界城镇，各路神仙聚集之地',
    coordinates: { x: 0, y: 0 },
    npcs: [
      {
        id: 'city_god',
        name: '城隍爷',
        type: 'npc',
        level: 50,
        services: ['信息', '任务']
      },
      {
        id: 'immortal_merchant',
        name: '仙界商人',
        type: 'npc',
        level: 30,
        services: ['交易', '法宝']
      }
    ],
    monsters: [],
    facilities: ['bank', 'market', 'inn', 'clinic']
  },

  huaguo_shan: {
    id: 'huaguo_shan',
    name: '花果山',
    type: 'mountain',
    description: '美猴王的故乡，山清水秀，仙桃满树，是修炼的好地方',
    coordinates: { x: 1, y: 1 },
    npcs: [
      {
        id: 'monkey_elder',
        name: '猴族长老',
        type: 'npc',
        level: 40,
        services: ['指引', '武艺']
      }
    ],
    monsters: [
      {
        id: 'spirit_monkey',
        name: '灵猴',
        type: 'monster',
        level: 15,
        description: '花果山的灵猴，机灵活泼，略通人性'
      },
      {
        id: 'mountain_demon',
        name: '山魈',
        type: 'monster',
        level: 20,
        description: '山中精怪，力大无穷'
      }
    ],
    facilities: ['temple']
  },

  shuilian_dong: {
    id: 'shuilian_dong',
    name: '水帘洞',
    type: 'cave',
    description: '花果山水帘洞，美猴王的洞府，洞中别有洞天',
    coordinates: { x: 2, y: 1 },
    npcs: [
      {
        id: 'cave_guardian',
        name: '洞府守护',
        type: 'npc',
        level: 35,
        services: ['修炼', '秘籍']
      }
    ],
    monsters: [
      {
        id: 'water_spirit',
        name: '水灵',
        type: 'monster',
        level: 12,
        description: '水帘洞中的水系精灵'
      }
    ],
    facilities: []
  },

  // 西牛贺洲区域
  xiniu_hezhou: {
    id: 'xiniu_hezhou',
    name: '西牛贺洲',
    type: 'town',
    description: '四大部洲之一，佛法昌盛之地，通往西天的必经之路',
    coordinates: { x: -2, y: 0 },
    npcs: [
      {
        id: 'buddhist_monk',
        name: '得道高僧',
        type: 'npc',
        level: 60,
        services: ['佛法', '指引']
      }
    ],
    monsters: [],
    facilities: ['temple', 'market']
  },

  lingshan: {
    id: 'lingshan',
    name: '灵山',
    type: 'mountain',
    description: '佛祖所在的圣地，佛光普照，是西天取经的终点',
    coordinates: { x: -3, y: 1 },
    npcs: [
      {
        id: 'arhat',
        name: '罗汉',
        type: 'npc',
        level: 80,
        services: ['佛法', '真经']
      }
    ],
    monsters: [
      {
        id: 'guardian_vajra',
        name: '护法金刚',
        type: 'monster',
        level: 50,
        description: '守护灵山的金刚力士'
      }
    ],
    facilities: ['temple']
  },

  // 南瞻部洲区域
  nanzhan_buzhou: {
    id: 'nanzhan_buzhou',
    name: '南瞻部洲',
    type: 'town',
    description: '四大部洲之一，人间界所在，凡人聚居之地',
    coordinates: { x: 0, y: -2 },
    npcs: [
      {
        id: 'mortal_official',
        name: '凡间官员',
        type: 'npc',
        level: 20,
        services: ['信息', '任务']
      }
    ],
    monsters: [
      {
        id: 'bandit',
        name: '山贼',
        type: 'monster',
        level: 8,
        description: '人间的盗匪，武艺平平'
      }
    ],
    facilities: ['market', 'inn']
  },

  // 北俱芦洲区域
  beiju_luzhou: {
    id: 'beiju_luzhou',
    name: '北俱芦洲',
    type: 'field',
    description: '四大部洲之一，妖魔聚集之地，充满危险',
    coordinates: { x: 0, y: 2 },
    npcs: [
      {
        id: 'demon_hunter',
        name: '降妖师',
        type: 'npc',
        level: 45,
        services: ['降妖', '法器']
      }
    ],
    monsters: [
      {
        id: 'demon_soldier',
        name: '妖兵',
        type: 'monster',
        level: 25,
        description: '妖魔大军的士兵'
      },
      {
        id: 'demon_general',
        name: '妖将',
        type: 'monster',
        level: 35,
        description: '妖魔大军的将领，实力强劲'
      }
    ],
    facilities: []
  },

  // 龙宫
  dragon_palace: {
    id: 'dragon_palace',
    name: '东海龙宫',
    type: 'underwater',
    description: '东海龙王的水下宫殿，珍宝无数，戒备森严',
    coordinates: { x: 3, y: 0 },
    npcs: [
      {
        id: 'dragon_king',
        name: '东海龙王',
        type: 'npc',
        level: 70,
        services: ['法宝', '神兵']
      },
      {
        id: 'shrimp_soldier',
        name: '虾兵',
        type: 'npc',
        level: 25,
        services: ['守卫', '信息']
      }
    ],
    monsters: [
      {
        id: 'crab_general',
        name: '蟹将',
        type: 'monster',
        level: 30,
        description: '龙宫的蟹将，身披重甲'
      }
    ],
    facilities: ['treasure_vault']
  }
};

// 位置之间的连接关系
export const connections = {
  // 东胜神洲的连接
  dongsheng_shenzhou: ['huaguo_shan', 'nanzhan_buzhou', 'dragon_palace'],

  // 花果山的连接
  huaguo_shan: ['dongsheng_shenzhou', 'shuilian_dong'],

  // 水帘洞的连接
  shuilian_dong: ['huaguo_shan'],

  // 西牛贺洲的连接
  xiniu_hezhou: ['lingshan', 'nanzhan_buzhou'],

  // 灵山的连接
  lingshan: ['xiniu_hezhou'],

  // 南瞻部洲的连接
  nanzhan_buzhou: ['dongsheng_shenzhou', 'xiniu_hezhou', 'beiju_luzhou'],

  // 北俱芦洲的连接
  beiju_luzhou: ['nanzhan_buzhou'],

  // 东海龙宫的连接
  dragon_palace: ['dongsheng_shenzhou']
};

// 移动消耗配置（已完全移除体力消耗功能）
export const movementCosts = {
  // 基础消耗
  base: {
    time: 5 // 分钟
  },
  
  // 不同地形的消耗倍数
  terrainMultipliers: {
    town: 1.0,
    mountain: 1.5,
    cave: 1.2,
    field: 1.3,
    underwater: 2.0,
    heaven: 3.0,
    underworld: 2.5
  },
  
  // 距离消耗倍数
  distanceMultipliers: {
    1: 1.0,
    2: 1.5,
    3: 2.0,
    4: 2.5
  }
};

// 设施配置
export const facilities = {
  bank: {
    id: 'bank',
    name: '钱庄',
    type: 'facility',
    description: '可以存取银两和金币的地方',
    services: ['存款', '取款', '兑换']
  },
  market: {
    id: 'market',
    name: '集市',
    type: 'facility',
    description: '买卖物品的地方',
    services: ['购买', '出售', '拍卖']
  },
  inn: {
    id: 'inn',
    name: '客栈',
    type: 'facility',
    description: '休息和恢复的地方',
    services: ['休息', '恢复', '住宿']
  },
  clinic: {
    id: 'clinic',
    name: '医馆',
    type: 'facility',
    description: '治疗伤病的地方',
    services: ['治疗', '解毒', '恢复']
  },
  camp: {
    id: 'camp',
    name: '营地',
    type: 'facility',
    description: '临时休息的营地',
    services: ['休息', '补给']
  },
  temple: {
    id: 'temple',
    name: '寺庙',
    type: 'facility',
    description: '修炼佛法的神圣场所',
    services: ['祈祷', '祝福', '净化']
  },
  treasure_vault: {
    id: 'treasure_vault',
    name: '宝库',
    type: 'facility',
    description: '存放珍宝法器的地方',
    services: ['存宝', '取宝', '鉴定']
  },
  heavenly_court: {
    id: 'heavenly_court',
    name: '凌霄宝殿',
    type: 'facility',
    description: '天庭朝会之所，玉皇大帝的宫殿',
    services: ['朝拜', '册封', '天命']
  },
  peach_garden: {
    id: 'peach_garden',
    name: '蟠桃园',
    type: 'facility',
    description: '种植仙桃的园林，王母娘娘的后花园',
    services: ['采摘', '品尝', '修炼']
  },
  judgment_hall: {
    id: 'judgment_hall',
    name: '森罗殿',
    type: 'facility',
    description: '审判亡魂的大殿，阎王爷的法庭',
    services: ['审判', '轮回', '查询']
  }
};

/**
 * 获取位置数据
 */
export function getLocationData(locationId) {
  return locations[locationId] || null;
}

/**
 * 获取可移动的位置列表
 */
export function getAvailableLocations(currentLocationId) {
  const connectedIds = connections[currentLocationId] || [];
  return connectedIds.map(id => locations[id]).filter(Boolean);
}

/**
 * 计算移动消耗
 */
export function calculateMovementCost(fromLocationId, toLocationId) {
  const fromLocation = locations[fromLocationId];
  const toLocation = locations[toLocationId];
  
  if (!fromLocation || !toLocation) {
    return null;
  }
  
  // 计算距离
  const dx = Math.abs(toLocation.coordinates.x - fromLocation.coordinates.x);
  const dy = Math.abs(toLocation.coordinates.y - fromLocation.coordinates.y);
  const distance = Math.max(dx, dy);
  
  // 获取地形倍数
  const terrainMultiplier = movementCosts.terrainMultipliers[toLocation.type] || 1.0;
  const distanceMultiplier = movementCosts.distanceMultipliers[distance] || 1.0;
  
  // 计算最终消耗（已完全移除体力消耗功能）
  const baseCost = movementCosts.base;
  return {
    time: Math.ceil(baseCost.time * terrainMultiplier * distanceMultiplier),
    distance: distance
  };
}

export default {
  locations,
  connections,
  movementCosts,
  facilities,
  getLocationData,
  getAvailableLocations,
  calculateMovementCost
};
