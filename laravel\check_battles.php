<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// 获取battles表的结构
$columns = Schema::getColumnListing('battles');
echo "Battles表的列：\n";
print_r($columns);

// 检查是否有数据
$battles = DB::table('battles')->get();
echo "\nBattles表的数据：\n";
print_r($battles);

// 尝试创建一条新记录
try {
    $id = DB::table('battles')->insertGetId([
        'character_id' => 1,
        'opponent_type' => 'App\\Models\\Monster',
        'opponent_id' => 1,
        'status' => 'ongoing',
        'type' => 'pve',
        'rounds' => 0,
        'created_at' => now(),
        'updated_at' => now()
    ]);
    echo "\n成功创建记录，ID: " . $id . "\n";
} catch (Exception $e) {
    echo "\n创建记录失败：" . $e->getMessage() . "\n";
}

// 查询新创建的记录
$battle = DB::table('battles')->find($id ?? 0);
echo "\n新创建的记录：\n";
print_r($battle);
