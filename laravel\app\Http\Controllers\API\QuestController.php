<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use App\Models\Quest;
use App\Models\CharacterQuest;

class QuestController extends Controller
{
    /**
     * 获取角色任务列表
     */
    public function getCharacterQuests(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟任务数据（实际项目中应该从数据库获取）
        $quests = [
            [
                'id' => 1,
                'title' => '新手任务：熟悉操作',
                'description' => '学会基本的游戏操作，包括移动、攻击和使用物品。',
                'status' => 'available',
                'type' => 'main',
                'level_requirement' => 1,
                'progress' => null,
                'rewards' => [
                    ['type' => 'exp', 'amount' => 100],
                    ['type' => 'silver', 'amount' => 50],
                    ['type' => 'item', 'item_id' => 1, 'quantity' => 1]
                ]
            ],
            [
                'id' => 2,
                'title' => '击败野狼',
                'description' => '在森林中击败5只野狼，证明你的战斗能力。',
                'status' => 'in_progress',
                'type' => 'daily',
                'level_requirement' => 2,
                'progress' => ['current' => 2, 'total' => 5],
                'rewards' => [
                    ['type' => 'exp', 'amount' => 200],
                    ['type' => 'silver', 'amount' => 100]
                ]
            ],
            [
                'id' => 3,
                'title' => '收集草药',
                'description' => '为村里的药师收集10株治疗草药。',
                'status' => 'completed',
                'type' => 'side',
                'level_requirement' => 1,
                'progress' => ['current' => 10, 'total' => 10],
                'rewards' => [
                    ['type' => 'exp', 'amount' => 150],
                    ['type' => 'item', 'item_id' => 2, 'quantity' => 3]
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'quests' => $quests
            ]
        ]);
    }

    /**
     * 接受任务
     */
    public function acceptQuest(Request $request, Character $character, $questId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟接受任务逻辑
        return response()->json([
            'success' => true,
            'message' => '任务接受成功'
        ]);
    }

    /**
     * 完成任务
     */
    public function completeQuest(Request $request, Character $character, $questId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟完成任务逻辑
        return response()->json([
            'success' => true,
            'message' => '任务完成成功，获得奖励！'
        ]);
    }

    /**
     * 放弃任务
     */
    public function abandonQuest(Request $request, Character $character, $questId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟放弃任务逻辑
        return response()->json([
            'success' => true,
            'message' => '任务已放弃'
        ]);
    }

    /**
     * 更新任务进度
     */
    public function updateQuestProgress(Request $request, Character $character, $questId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟更新任务进度逻辑
        return response()->json([
            'success' => true,
            'message' => '任务进度已更新'
        ]);
    }

    /**
     * 获取可接受的任务列表
     */
    public function getAvailableQuests(Request $request)
    {
        // 模拟可接受任务数据
        $quests = [
            [
                'id' => 4,
                'title' => '探索古老遗迹',
                'description' => '前往神秘的古老遗迹，寻找传说中的宝藏。',
                'type' => 'main',
                'level_requirement' => 5,
                'rewards' => [
                    ['type' => 'exp', 'amount' => 500],
                    ['type' => 'silver', 'amount' => 300],
                    ['type' => 'item', 'item_id' => 3, 'quantity' => 1]
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'quests' => $quests
            ]
        ]);
    }

    /**
     * 获取任务详情
     */
    public function getQuestDetails(Request $request, $questId)
    {
        // 模拟任务详情数据
        $quest = [
            'id' => $questId,
            'title' => '示例任务',
            'description' => '这是一个示例任务的详细描述。',
            'type' => 'main',
            'level_requirement' => 1,
            'objectives' => [
                ['description' => '击败5只野狼', 'current' => 0, 'total' => 5],
                ['description' => '收集3个狼牙', 'current' => 0, 'total' => 3]
            ],
            'rewards' => [
                ['type' => 'exp', 'amount' => 200],
                ['type' => 'silver', 'amount' => 100]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $quest
        ]);
    }
}
