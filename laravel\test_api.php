<?php

// 测试属性点分配API
$url = 'http://localhost:8080/api/characters/2/attributes';

// 模拟前端发送的数据
$data = [
    'constitution' => 22,  // 体质：基础20 + 2点额外
    'intelligence' => 12,  // 智力：基础10 + 2点额外
    'strength' => 12,      // 力量：基础10 + 2点额外
    'agility' => 12        // 敏捷：基础10 + 2点额外
];

// 总共需要8点额外属性点，角色有21点可用

$headers = [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: Bearer 7|eEOU1P0h6uIOx4k3JVXudry9snN1qtLqxMeDZEwB'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
echo "响应内容:\n";
echo $response . "\n";

// 解析响应
$responseData = json_decode($response, true);
if ($responseData) {
    echo "\n解析后的响应:\n";
    print_r($responseData);
} else {
    echo "\n无法解析响应JSON\n";
}
