<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class HandleLargeHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查请求头大小
        $headerSize = 0;
        foreach ($request->headers->all() as $name => $values) {
            $headerSize += strlen($name);
            foreach ($values as $value) {
                $headerSize += strlen($value);
            }
        }

        // 如果请求头过大（超过8KB），记录警告
        if ($headerSize > 8192) {
            \Log::warning('Large request headers detected', [
                'size' => $headerSize,
                'url' => $request->url(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
            ]);

            // 对于大区API，如果请求头过大，返回简化的错误响应
            if (str_contains($request->path(), 'regions')) {
                return response()->json([
                    'success' => false,
                    'message' => '请求头过大，请清理浏览器缓存后重试',
                    'code' => 431
                ], 431);
            }
        }

        return $next($request);
    }
}
