<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Region;
use Carbon\Carbon;

class RegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 强制添加龙宫地图数据
        $this->command->info('添加龙宫地图数据...');

        // 创建龙宫地图
        $this->createDragonPalaceMap();

        $this->command->info('成功添加龙宫地图数据');
    }

    /**
     * 创建龙宫地图
     */
    private function createDragonPalaceMap()
    {
        // 检查是否已存在
        $existingRegion = DB::table('regions')->where('code', 'dragon_palace')->first();
        if ($existingRegion) {
            $this->command->info('龙宫地图已存在，ID: ' . $existingRegion->id);
            return;
        }

        $map = [
            'name' => '龙宫',
            'code' => 'dragon_palace',
            'description' => '东海龙宫，神奇而充满神秘力量的水下宫殿。',
                'type' => 'city',
                'level_range_min' => 1,
                'level_range_max' => 100,
                'danger_level' => 1,
                'is_pvp' => false,
                'weather_enabled' => true,
                'sort_order' => 100,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
                'locations' => [
                ['name' => '龙宫大门', 'type' => 'gate', 'x' => 100, 'y' => 500, 'is_safe' => true, 'level_requirement' => 1],
                ['name' => '东海市场', 'type' => 'market', 'x' => 250, 'y' => 400, 'is_safe' => true, 'level_requirement' => 1],
                ['name' => '龙王殿', 'type' => 'palace', 'x' => 200, 'y' => 200, 'is_safe' => true, 'level_requirement' => 10],
                ['name' => '珊瑚园', 'type' => 'garden', 'x' => 300, 'y' => 300, 'is_safe' => true, 'level_requirement' => 5],
                ]
        ];

        $locations = $map['locations'];
        unset($map['locations']);

        // 插入地图数据
        $regionId = DB::table('regions')->insertGetId($map);

            // 插入位置数据
            foreach ($locations as $locationData) {
                $locationData['region_id'] = $regionId;
                $locationData['created_at'] = now();
                $locationData['updated_at'] = now();
                $locationData['is_active'] = true;
                DB::table('locations')->insert($locationData);
        }
    }
}
