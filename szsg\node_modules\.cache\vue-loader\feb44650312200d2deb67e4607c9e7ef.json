{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue?vue&type=template&id=8100a840&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue", "mtime": 1749890251457}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_l", "testMonsters", "monster", "key", "id", "on", "click", "$event", "startTestBattle", "attrs", "src", "avatar", "alt", "name", "_s", "level", "type", "max_health", "attack", "ref", "testAttackAnimation", "testDamageAnimation", "testCriticalAnimation", "testHealAnimation", "testSkillAnimation", "testParticleAnimation", "disabled", "isTestingAPI", "testBattleAPI", "apiTestResult", "JSON", "stringify", "_e", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/views/debug/BattleTest.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"battle-test\" }, [\n    _c(\"h1\", [_vm._v(\"战斗系统测试\")]),\n    _c(\"div\", { staticClass: \"test-section\" }, [\n      _c(\"h2\", [_vm._v(\"测试怪物列表\")]),\n      _c(\n        \"div\",\n        { staticClass: \"monster-list\" },\n        _vm._l(_vm.testMonsters, function (monster) {\n          return _c(\n            \"div\",\n            {\n              key: monster.id,\n              staticClass: \"monster-card\",\n              on: {\n                click: function ($event) {\n                  return _vm.startTestBattle(monster)\n                },\n              },\n            },\n            [\n              _c(\"img\", {\n                staticClass: \"monster-avatar\",\n                attrs: { src: monster.avatar, alt: monster.name },\n              }),\n              _c(\"div\", { staticClass: \"monster-info\" }, [\n                _c(\"h3\", [_vm._v(_vm._s(monster.name))]),\n                _c(\"p\", [_vm._v(\"等级: \" + _vm._s(monster.level))]),\n                _c(\"p\", [_vm._v(\"类型: \" + _vm._s(monster.type))]),\n                _c(\"p\", [_vm._v(\"生命: \" + _vm._s(monster.max_health))]),\n                _c(\"p\", [_vm._v(\"攻击: \" + _vm._s(monster.attack))]),\n              ]),\n            ]\n          )\n        }),\n        0\n      ),\n    ]),\n    _c(\"div\", { staticClass: \"test-section\" }, [\n      _c(\"h2\", [_vm._v(\"动画测试\")]),\n      _c(\n        \"div\",\n        { staticClass: \"animation-test\" },\n        [\n          _c(\"BattleAnimation\", { ref: \"animationTest\" }),\n          _c(\"div\", { staticClass: \"animation-controls\" }, [\n            _c(\"button\", { on: { click: _vm.testAttackAnimation } }, [\n              _vm._v(\"测试攻击动画\"),\n            ]),\n            _c(\"button\", { on: { click: _vm.testDamageAnimation } }, [\n              _vm._v(\"测试伤害动画\"),\n            ]),\n            _c(\"button\", { on: { click: _vm.testCriticalAnimation } }, [\n              _vm._v(\"测试暴击动画\"),\n            ]),\n            _c(\"button\", { on: { click: _vm.testHealAnimation } }, [\n              _vm._v(\"测试治疗动画\"),\n            ]),\n            _c(\"button\", { on: { click: _vm.testSkillAnimation } }, [\n              _vm._v(\"测试技能特效\"),\n            ]),\n            _c(\"button\", { on: { click: _vm.testParticleAnimation } }, [\n              _vm._v(\"测试粒子效果\"),\n            ]),\n          ]),\n        ],\n        1\n      ),\n    ]),\n    _c(\"div\", { staticClass: \"test-section\" }, [\n      _c(\"h2\", [_vm._v(\"API测试\")]),\n      _c(\"div\", { staticClass: \"api-test\" }, [\n        _c(\n          \"button\",\n          {\n            attrs: { disabled: _vm.isTestingAPI },\n            on: { click: _vm.testBattleAPI },\n          },\n          [\n            _vm._v(\n              \" \" + _vm._s(_vm.isTestingAPI ? \"测试中...\" : \"测试战斗API\") + \" \"\n            ),\n          ]\n        ),\n        _vm.apiTestResult\n          ? _c(\"div\", { staticClass: \"api-result\" }, [\n              _c(\"h4\", [_vm._v(\"API测试结果:\")]),\n              _c(\"pre\", [\n                _vm._v(_vm._s(JSON.stringify(_vm.apiTestResult, null, 2))),\n              ]),\n            ])\n          : _vm._e(),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,YAAY,EAAE,UAAUC,OAAO,EAAE;IAC1C,OAAON,EAAE,CACP,KAAK,EACL;MACEO,GAAG,EAAED,OAAO,CAACE,EAAE;MACfN,WAAW,EAAE,cAAc;MAC3BO,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOZ,GAAG,CAACa,eAAe,CAACN,OAAO,CAAC;QACrC;MACF;IACF,CAAC,EACD,CACEN,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,gBAAgB;MAC7BW,KAAK,EAAE;QAAEC,GAAG,EAAER,OAAO,CAACS,MAAM;QAAEC,GAAG,EAAEV,OAAO,CAACW;MAAK;IAClD,CAAC,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACmB,EAAE,CAACZ,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,EACxCjB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,GAAGJ,GAAG,CAACmB,EAAE,CAACZ,OAAO,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC,EACjDnB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,GAAGJ,GAAG,CAACmB,EAAE,CAACZ,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,EAChDpB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,GAAGJ,GAAG,CAACmB,EAAE,CAACZ,OAAO,CAACe,UAAU,CAAC,CAAC,CAAC,CAAC,EACtDrB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,GAAGJ,GAAG,CAACmB,EAAE,CAACZ,OAAO,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC,CACnD,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,EACFtB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CAAC,iBAAiB,EAAE;IAAEuB,GAAG,EAAE;EAAgB,CAAC,CAAC,EAC/CvB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CAAC,QAAQ,EAAE;IAAES,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAACyB;IAAoB;EAAE,CAAC,EAAE,CACvDzB,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFH,EAAE,CAAC,QAAQ,EAAE;IAAES,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAAC0B;IAAoB;EAAE,CAAC,EAAE,CACvD1B,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFH,EAAE,CAAC,QAAQ,EAAE;IAAES,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAAC2B;IAAsB;EAAE,CAAC,EAAE,CACzD3B,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFH,EAAE,CAAC,QAAQ,EAAE;IAAES,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAAC4B;IAAkB;EAAE,CAAC,EAAE,CACrD5B,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFH,EAAE,CAAC,QAAQ,EAAE;IAAES,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAAC6B;IAAmB;EAAE,CAAC,EAAE,CACtD7B,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFH,EAAE,CAAC,QAAQ,EAAE;IAAES,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAAC8B;IAAsB;EAAE,CAAC,EAAE,CACzD9B,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CACA,QAAQ,EACR;IACEa,KAAK,EAAE;MAAEiB,QAAQ,EAAE/B,GAAG,CAACgC;IAAa,CAAC;IACrCtB,EAAE,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAACiC;IAAc;EACjC,CAAC,EACD,CACEjC,GAAG,CAACI,EAAE,CACJ,GAAG,GAAGJ,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACgC,YAAY,GAAG,QAAQ,GAAG,SAAS,CAAC,GAAG,GAC1D,CAAC,CAEL,CAAC,EACDhC,GAAG,CAACkC,aAAa,GACbjC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC9BH,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACmB,EAAE,CAACgB,IAAI,CAACC,SAAS,CAACpC,GAAG,CAACkC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAC3D,CAAC,CACH,CAAC,GACFlC,GAAG,CAACqC,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBvC,MAAM,CAACwC,aAAa,GAAG,IAAI;AAE3B,SAASxC,MAAM,EAAEuC,eAAe", "ignoreList": []}]}