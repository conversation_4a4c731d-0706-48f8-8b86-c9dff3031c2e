<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddStatusToBattlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('battles', function (Blueprint $table) {
            // 检查表中是否有status字段，如果没有则添加
            if (!Schema::hasColumn('battles', 'status')) {
                $table->enum('status', ['ongoing', 'active', 'victory', 'defeat', 'draw', 'fled', 'win', 'lose', 'escape'])
                      ->default('ongoing')
                      ->after('type')
                      ->comment('战斗状态');
            } else {
                // 如果已存在，修改为支持更多选项
                DB::statement("ALTER TABLE battles MODIFY COLUMN status ENUM('ongoing', 'active', 'victory', 'defeat', 'draw', 'fled', 'win', 'lose', 'escape') DEFAULT 'ongoing'");
            }

            // 添加一些可能缺少的字段
            if (!Schema::hasColumn('battles', 'rounds')) {
                $table->integer('rounds')->default(0)->after('status')->comment('战斗回合数');
            }

            if (!Schema::hasColumn('battles', 'exp_gained')) {
                $table->integer('exp_gained')->default(0)->after('rewards')->comment('获得的经验值');
            }

            if (!Schema::hasColumn('battles', 'gold_gained')) {
                $table->integer('gold_gained')->default(0)->after('exp_gained')->comment('获得的金币');
            }

            if (!Schema::hasColumn('battles', 'silver_gained')) {
                $table->integer('silver_gained')->default(0)->after('gold_gained')->comment('获得的银两');
            }

            if (!Schema::hasColumn('battles', 'battle_log')) {
                $table->json('battle_log')->nullable()->after('silver_gained')->comment('战斗日志');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('battles', function (Blueprint $table) {
            // 不删除status字段，因为它可能是原始表的一部分

            // 删除我们添加的其他字段
            $columnsToCheck = ['rounds', 'exp_gained', 'gold_gained', 'silver_gained', 'battle_log'];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('battles', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
}
