{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue?vue&type=template&id=e37a2986&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue", "mtime": 1749718635730}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}