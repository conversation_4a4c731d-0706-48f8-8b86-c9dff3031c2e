/**
 * 医馆系统API服务
 * 提供医馆相关的接口调用
 */
import { get, post } from '../request.js';
import logger from '../../utils/logger.js';
import { getCache, setCache, CACHE_TYPE } from './cacheService.js';

// 缓存键
const CACHE_KEYS = {
    SERVICE_TYPES: 'clinic_service_types',
    HEALTH_POTIONS: 'clinic_health_potions',
    MANA_POTIONS: 'clinic_mana_potions',
    TEAM_SERVICES: 'clinic_team_services'
};

/**
 * 医馆服务
 */
const clinicService = {
    /**
     * 获取医馆服务类型
     * @returns {Promise<Array>} - 医馆服务类型列表
     */
    getServiceTypes() {
        logger.debug('[ClinicService] 获取医馆服务类型');
        
        // 尝试从缓存获取
        const cachedData = getCache(CACHE_TYPE.API, CACHE_KEYS.SERVICE_TYPES);
        if (cachedData) {
            logger.debug('[ClinicService] 从缓存获取医馆服务类型');
            return Promise.resolve(cachedData);
        }

        return get('/clinic/service-types', {}, {
            loading: true,
            loadingText: '获取医馆服务类型...'
        }).then(res => {
            logger.debug('[ClinicService] 医馆服务类型获取成功:', res);
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.SERVICE_TYPES, res.data, 3600 * 1000); // 缓存1小时
            return res.data;
        }).catch(error => {
            logger.error('[ClinicService] 获取医馆服务类型失败:', error);
            throw error;
        });
    },

    /**
     * 获取气血药品列表
     * @returns {Promise<Array>} - 气血药品列表
     */
    getHealthPotions() {
        logger.debug('[ClinicService] 获取气血药品列表');
        
        // 尝试从缓存获取
        const cachedData = getCache(CACHE_TYPE.API, CACHE_KEYS.HEALTH_POTIONS);
        if (cachedData) {
            logger.debug('[ClinicService] 从缓存获取气血药品列表');
            return Promise.resolve(cachedData);
        }

        return get('/clinic/health-potions', {}, {
            loading: true,
            loadingText: '获取气血药品列表...'
        }).then(res => {
            logger.debug('[ClinicService] 气血药品列表获取成功:', res);
            
            // 格式化数据，确保字段名一致
            const formattedData = res.data.map(potion => ({
                ...potion,
                id: potion.id,
                name: potion.name,
                description: potion.description || `恢复${potion.hp_recovery_percent}%的气血`,
                effect_value: potion.hp_recovery_percent ? `${potion.hp_recovery_percent}%` : potion.hp_recovery || 0,
                price: potion.cost || 0,
                type: 'health',
                required_level: potion.required_level || 1
            }));
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.HEALTH_POTIONS, formattedData, 3600 * 1000); // 缓存1小时
            return formattedData;
        }).catch(error => {
            logger.error('[ClinicService] 获取气血药品列表失败:', error);
            throw error;
        });
    },

    /**
     * 获取精力药品列表
     * @returns {Promise<Array>} - 精力药品列表
     */
    getManaPotions() {
        logger.debug('[ClinicService] 获取精力药品列表');
        
        // 尝试从缓存获取
        const cachedData = getCache(CACHE_TYPE.API, CACHE_KEYS.MANA_POTIONS);
        if (cachedData) {
            logger.debug('[ClinicService] 从缓存获取精力药品列表');
            return Promise.resolve(cachedData);
        }

        return get('/clinic/mana-potions', {}, {
            loading: true,
            loadingText: '获取精力药品列表...'
        }).then(res => {
            logger.debug('[ClinicService] 精力药品列表获取成功:', res);
            
            // 格式化数据，确保字段名一致
            const formattedData = res.data.map(potion => ({
                ...potion,
                id: potion.id,
                name: potion.name,
                description: potion.description || `恢复${potion.mp_recovery_percent}%的精力`,
                effect_value: potion.mp_recovery_percent ? `${potion.mp_recovery_percent}%` : potion.mp_recovery || 0,
                price: potion.cost || 0,
                type: 'mana',
                required_level: potion.required_level || 1
            }));
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.MANA_POTIONS, formattedData, 3600 * 1000); // 缓存1小时
            return formattedData;
        }).catch(error => {
            logger.error('[ClinicService] 获取精力药品列表失败:', error);
            throw error;
        });
    },

    /**
     * 获取全员治疗服务列表
     * @returns {Promise<Array>} - 全员治疗服务列表
     */
    getTeamServices() {
        logger.debug('[ClinicService] 获取全员治疗服务列表');
        
        // 清除缓存，强制从服务器获取新数据
        setCache(CACHE_TYPE.API, CACHE_KEYS.TEAM_SERVICES, null, 0);

        return get('/clinic/team-services', {}, {
            loading: true,
            loadingText: '获取全员治疗服务列表...'
        }).then(res => {
            logger.debug('[ClinicService] 全员治疗服务列表获取成功:', res);
            
            // 格式化数据，确保字段名一致
            const formattedData = res.data.map(service => ({
                ...service,
                id: service.id,
                name: service.name,
                description: service.description || '恢复全队成员的气血和精力',
                price: service.cost || 0,
                type: 'team',
                required_level: service.required_level || 1
            }));
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.TEAM_SERVICES, formattedData, 3600 * 1000); // 缓存1小时
            return formattedData;
        }).catch(error => {
            logger.error('[ClinicService] 获取全员治疗服务列表失败:', error);
            throw error;
        });
    },

    /**
     * 购买并使用药品
     * @param {string} characterId - 角色ID
     * @param {number} potionId - 药品ID
     * @param {string} type - 药品类型 (health/mana)
     * @returns {Promise<Object>} - 购买结果
     */
    purchasePotion(characterId, potionId, type = 'health') {
        logger.debug('[ClinicService] 购买药品, characterId:', characterId, 'potionId:', potionId, 'type:', type);
        
        return post(`/clinic/purchase-potion/${characterId}`, { 
            potion_id: potionId,
            quantity: 1,
            type: type
        }, {
            loading: true,
            loadingText: '正在购买药品...'
        }).then(res => {
            logger.debug('[ClinicService] 购买药品成功:', res);
            
            // 清除相关缓存，确保下次获取最新数据
            if (type === 'health') {
                setCache(CACHE_TYPE.API, CACHE_KEYS.HEALTH_POTIONS, null, 0);
            } else if (type === 'mana') {
                setCache(CACHE_TYPE.API, CACHE_KEYS.MANA_POTIONS, null, 0);
            }
            
            return {
                success: true,
                message: res.data.message || '购买成功',
                hp_recovered: res.data.hp_recovered || 0,
                mp_recovered: res.data.mp_recovered || 0,
                character: {
                    hp: res.data.current_hp || 0,
                    maxHp: res.data.max_hp || 0,
                    mp: res.data.current_mp || 0,
                    maxMp: res.data.max_mp || 0,
                    silver: res.data.silver || 0,
                    gold: res.data.gold || 0
                }
            };
        }).catch(error => {
            logger.error('[ClinicService] 购买药品失败:', error);
            throw {
                success: false,
                message: error.response?.data?.error?.message || '购买药品失败，请重试'
            };
        });
    },

    /**
     * 使用全员治疗服务
     * @param {string} characterId - 角色ID
     * @param {number} serviceId - 服务ID
     * @returns {Promise<Object>} - 治疗结果
     */
    useTeamService(characterId, serviceId) {
        logger.debug('[ClinicService] 使用全员治疗服务, characterId:', characterId, 'serviceId:', serviceId);
        
        return post(`/clinic/team-healing/${characterId}`, { 
            service_id: serviceId 
        }, {
            loading: true,
            loadingText: '正在进行全员治疗...'
        }).then(res => {
            logger.debug('[ClinicService] 全员治疗成功:', res);
            
            // 清除相关缓存，确保下次获取最新数据
            setCache(CACHE_TYPE.API, CACHE_KEYS.TEAM_SERVICES, null, 0);
            
            // 获取当前角色的治疗结果
            const characterResult = res.data.team_results?.find(result => result.character_id === characterId) || {};
            
            return {
                success: true,
                message: res.data.message || '治疗成功',
                hp_recovered: characterResult.hp_recovered || 0,
                mp_recovered: characterResult.mp_recovered || 0,
                character: {
                    hp: characterResult.current_hp || 0,
                    maxHp: characterResult.max_hp || 0,
                    mp: characterResult.current_mp || 0,
                    maxMp: characterResult.max_mp || 0,
                    silver: res.data.silver || 0,
                    gold: res.data.gold || 0
                },
                team_results: res.data.team_results || []
            };
        }).catch(error => {
            logger.error('[ClinicService] 全员治疗失败:', error);
            throw {
                success: false,
                message: error.response?.data?.error?.message || '治疗失败，请重试'
            };
        });
    }
};

export default clinicService; 