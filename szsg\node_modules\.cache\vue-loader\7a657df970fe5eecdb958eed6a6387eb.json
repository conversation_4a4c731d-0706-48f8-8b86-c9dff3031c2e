{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue?vue&type=style&index=0&id=1bd62dc5&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue", "mtime": 1749724641760}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Vip.vue"], "names": [], "mappings": ";AAscA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Vip.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"vip-page\">\r\n      <!-- 返回按钮 -->\r\n      <div class=\"back-button-container\">\r\n        <div class=\"back-button\" @click=\"goBack\">\r\n          <img src=\"/static/game/UI/anniu/fhui_.png\" alt=\"返回\" />\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 加载状态显示 -->\r\n      <div class=\"loading-container\" v-if=\"loading\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <div class=\"loading-text\">加载中...</div>\r\n      </div>\r\n      \r\n      <!-- 错误状态显示 -->\r\n      <div class=\"error-container\" v-else-if=\"hasError\">\r\n        <div class=\"error-icon\">!</div>\r\n        <div class=\"error-text\">{{ errorMessage }}</div>\r\n        <button class=\"pixel-button gold\" @click=\"retryLoading\">重新加载</button>\r\n      </div>\r\n      \r\n      <div v-else>\r\n        <!-- 主标签导航 -->\r\n        <div class=\"main-tabs\">\r\n          <div \r\n            class=\"main-tab\" \r\n            :class=\"{ active: currentMainTab === 'vip' }\"\r\n            @click=\"currentMainTab = 'vip'\"\r\n          >\r\n            <span>VIP</span>\r\n          </div>\r\n          <div \r\n            class=\"main-tab\" \r\n            :class=\"{ active: currentMainTab === 'benefits' }\"\r\n            @click=\"currentMainTab = 'benefits'\"\r\n          >\r\n            <span>VIP福利</span>\r\n          </div>\r\n          <div \r\n            class=\"main-tab\" \r\n            :class=\"{ active: currentMainTab === 'privileges' }\"\r\n            @click=\"currentMainTab = 'privileges'\"\r\n          >\r\n            <span>VIP特权</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- VIP信息标签内容 -->\r\n        <div v-if=\"currentMainTab === 'vip'\" class=\"tab-content\">\r\n          <div class=\"vip-info pixel-border-box\">\r\n            <div class=\"vip-info-header\">\r\n              <img :src=\"vipIcon\" class=\"vip-icon\" :alt=\"'VIP' + vipLevel\" />\r\n            </div>\r\n            \r\n            <div class=\"vip-exp-section\">\r\n              <div class=\"vip-exp\">\r\n                当前经验：{{ vipExp }} / {{ vipNextExp }}\r\n                <span v-if=\"vipLevel < 20\" class=\"vip-next-exp\">\r\n                  ，还需 <span class=\"highlight-text\">{{ vipNextExp - vipExp }}</span> 经验升级到 VIP{{ vipLevel + 1 }}\r\n                </span>\r\n                <span v-else class=\"vip-next-exp\">（已满级）</span>\r\n              </div>\r\n              <div class=\"vip-progress-container\">\r\n                <div class=\"vip-progress-bar\">\r\n                  <div class=\"vip-progress\" :style=\"{width: vipPercent + '%'}\"></div>\r\n                  <div class=\"progress-stars\">\r\n                    <span class=\"progress-star\" v-for=\"n in 5\" :key=\"n\" :style=\"{left: (n * 20 - 10) + '%'}\">✦</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"vip-progress-text\">{{ vipPercent }}%</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <button v-if=\"canClaimReward\" class=\"pixel-button gold shine-effect\" @click=\"claimVipReward\" :disabled=\"isClaiming\">\r\n              <span v-if=\"isClaiming\">领取中...</span>\r\n              <span v-else>领取VIP{{ vipLevel }}奖励</span>\r\n            </button>\r\n          </div>\r\n          \r\n          <div class=\"vip-reward-history pixel-border-box\" v-if=\"vipRewardHistory.length\">\r\n            <div class=\"section-header\">\r\n              <div class=\"vip-title-with-icon\">\r\n                <img :src=\"vipIcon\" class=\"vip-small-icon\" alt=\"VIP图标\" />\r\n                <h3>VIP成长奖励历史</h3>\r\n              </div>\r\n              <div class=\"section-decor\"></div>\r\n            </div>\r\n            <div class=\"history-list\">\r\n              <div v-for=\"(reward, idx) in vipRewardHistory\" :key=\"idx\" class=\"history-item\">\r\n                <div class=\"history-info\">\r\n                  <div class=\"history-vip-badge\">\r\n                    <img :src=\"getVipIcon(reward.level)\" class=\"history-vip-icon\" :alt=\"'VIP' + reward.level\" />\r\n                  </div>\r\n                  <span class=\"history-date\">{{ reward.date || '未领取' }}</span>\r\n                  <span class=\"history-desc\">{{ reward.desc }}</span>\r\n                </div>\r\n                <button \r\n                  v-if=\"!reward.date\" \r\n                  class=\"pixel-button small gold shine-effect\" \r\n                  @click=\"claimHistoryReward(reward.level)\" \r\n                  :disabled=\"isClaiming\"\r\n                >\r\n                  补领\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- VIP福利标签内容 -->\r\n        <div v-else-if=\"currentMainTab === 'benefits'\" class=\"tab-content\">\r\n          <div class=\"vip-daily-reward pixel-border-box\">\r\n            <div class=\"section-header\">\r\n              <div class=\"vip-title-with-icon\">\r\n                <img :src=\"vipIcon\" class=\"vip-small-icon\" alt=\"VIP图标\" />\r\n                <h3>VIP福利</h3>\r\n              </div>\r\n              <div class=\"section-decor\"></div>\r\n            </div>\r\n            \r\n            <!-- 添加横向选择标签 -->\r\n            <div class=\"reward-tabs\">\r\n              <div \r\n                class=\"reward-tab\" \r\n                :class=\"{ active: currentRewardTab === 'daily' }\"\r\n                @click=\"currentRewardTab = 'daily'\"\r\n              >\r\n                每日福利\r\n              </div>\r\n              <div \r\n                class=\"reward-tab\" \r\n                :class=\"{ active: currentRewardTab === 'weekly' }\"\r\n                @click=\"currentRewardTab = 'weekly'\"\r\n              >\r\n                每周福利\r\n              </div>\r\n              <div \r\n                class=\"reward-tab\" \r\n                :class=\"{ active: currentRewardTab === 'monthly' }\"\r\n                @click=\"currentRewardTab = 'monthly'\"\r\n              >\r\n                每月福利\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 每日福利内容 -->\r\n            <div class=\"daily-reward-content\" v-if=\"currentRewardTab === 'daily'\">\r\n              <div class=\"reward-info-card\">\r\n                <ul class=\"reward-list\">\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">金砖</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 10 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">银两</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 1000 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">体力</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 5 }}</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <button \r\n                class=\"pixel-button\" \r\n                :class=\"canClaimDaily ? 'gold shine-effect' : 'disabled'\"\r\n                @click=\"claimDailyReward\" \r\n                :disabled=\"!canClaimDaily || isClaiming\"\r\n              >\r\n                <span v-if=\"isClaiming\">领取中...</span>\r\n                <span v-else-if=\"canClaimDaily\">领取每日福利</span>\r\n                <span v-else>今日已领取</span>\r\n              </button>\r\n            </div>\r\n            \r\n            <!-- 每周福利内容 -->\r\n            <div class=\"daily-reward-content\" v-else-if=\"currentRewardTab === 'weekly'\">\r\n              <div class=\"reward-info-card\">\r\n                <ul class=\"reward-list\">\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">金砖</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 30 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">银两</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 3000 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">高级装备箱</span>\r\n                    <span class=\"reward-amount\">x{{ Math.max(1, Math.floor(vipLevel/2)) }}</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <button \r\n                class=\"pixel-button\" \r\n                :class=\"canClaimWeekly ? 'gold shine-effect' : 'disabled'\"\r\n                @click=\"claimWeeklyReward\" \r\n                :disabled=\"!canClaimWeekly || isClaiming\"\r\n              >\r\n                <span v-if=\"isClaiming\">领取中...</span>\r\n                <span v-else-if=\"canClaimWeekly\">领取每周福利</span>\r\n                <span v-else>本周已领取</span>\r\n              </button>\r\n            </div>\r\n            \r\n            <!-- 每月福利内容 -->\r\n            <div class=\"daily-reward-content\" v-else>\r\n              <div class=\"reward-info-card\">\r\n                <ul class=\"reward-list\">\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">金砖</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 100 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">银两</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 10000 }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">稀有装备箱</span>\r\n                    <span class=\"reward-amount\">x{{ Math.max(1, Math.floor(vipLevel/3)) }}</span>\r\n                  </li>\r\n                  <li class=\"reward-list-item\">\r\n                    <span class=\"reward-name\">仙将碎片</span>\r\n                    <span class=\"reward-amount\">x{{ vipLevel * 5 }}</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n              <button \r\n                class=\"pixel-button\" \r\n                :class=\"canClaimMonthly ? 'gold shine-effect' : 'disabled'\"\r\n                @click=\"claimMonthlyReward\" \r\n                :disabled=\"!canClaimMonthly || isClaiming\"\r\n              >\r\n                <span v-if=\"isClaiming\">领取中...</span>\r\n                <span v-else-if=\"canClaimMonthly\">领取每月福利</span>\r\n                <span v-else>本月已领取</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- VIP特权标签内容 -->\r\n        <div v-else class=\"tab-content\">\r\n          <div class=\"vip-privileges pixel-border-box\">\r\n            <div class=\"section-header\">\r\n              <div class=\"vip-title-with-icon\">\r\n                <img :src=\"vipIcon\" class=\"vip-small-icon\" alt=\"VIP图标\" />\r\n                <h3>VIP{{ vipLevel }}特权说明</h3>\r\n              </div>\r\n              <div class=\"section-decor\"></div>\r\n            </div>\r\n            <ul class=\"privilege-list\">\r\n              <li v-for=\"(desc, idx) in vipPrivileges\" :key=\"idx\" class=\"privilege-item\">\r\n                <i class=\"privilege-icon\">✓</i>\r\n                <span>{{ desc }}</span>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue'\r\nimport { getVipInfo, claimVipReward, claimHistoryReward, claimDailyReward, claimWeeklyReward, claimMonthlyReward } from '@/api/services/vipService'\r\nimport logger from '@/utils/logger'\r\n\r\nexport default {\r\n  name: 'Vip',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      vipLevel: 0,\r\n      vipExp: 0,\r\n      vipNextExp: 100,\r\n      isClaiming: false,\r\n      canClaimReward: false,\r\n      vipPrivileges: [],\r\n      vipRewardHistory: [],\r\n      canClaimDaily: false,\r\n      canClaimWeekly: false,\r\n      canClaimMonthly: false,\r\n      selectedVipLevel: null,\r\n      loading: true,\r\n      hasError: false,\r\n      errorMessage: '加载数据失败，请稍后重试',\r\n      currentRewardTab: 'daily',\r\n      currentMainTab: 'vip'\r\n    }\r\n  },\r\n  computed: {\r\n    vipIcon() {\r\n      return `/static/game/UI/vip/${this.vipLevel}.png`\r\n    },\r\n    vipPercent() {\r\n      return Math.min(100, Math.round((this.vipExp / this.vipNextExp) * 100))\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      this.$router.push('/game/main');\r\n    },\r\n    getVipIcon(level) {\r\n      return `/static/game/UI/vip/${level}.png`\r\n    },\r\n    async fetchVipData() {\r\n      this.loading = true\r\n      this.hasError = false\r\n      \r\n      try {\r\n        const data = await getVipInfo()\r\n        this.vipLevel = data.level\r\n        this.vipExp = data.exp\r\n        this.vipNextExp = data.next_exp\r\n        this.canClaimReward = data.can_claim_reward\r\n        this.vipPrivileges = data.privileges || []\r\n        this.vipRewardHistory = data.reward_history || []\r\n        this.canClaimDaily = data.can_claim_daily\r\n        this.canClaimWeekly = data.can_claim_weekly\r\n        this.canClaimMonthly = data.can_claim_monthly\r\n        logger.info('VIP信息加载成功', { level: this.vipLevel, exp: this.vipExp })\r\n      } catch (e) {\r\n        this.$toast && this.$toast('获取VIP信息失败')\r\n        logger.error('获取VIP信息失败', e)\r\n        this.hasError = true\r\n        this.errorMessage = '获取VIP信息失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    async retryLoading() {\r\n      this.hasError = false\r\n      this.loading = true\r\n      \r\n      try {\r\n        // 不再需要并行加载VIP等级数据\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        logger.error('VIP页面重新加载失败', e)\r\n        this.hasError = true\r\n        this.errorMessage = '加载数据失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    async claimVipReward() {\r\n      if (this.isClaiming) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimVipReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取VIP奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimHistoryReward(level) {\r\n      if (this.isClaiming) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimHistoryReward(level)\r\n        this.$toast && this.$toast('补领成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('补领失败')\r\n        logger.error('补领历史奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimDailyReward() {\r\n      if (this.isClaiming || !this.canClaimDaily) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimDailyReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取每日奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimWeeklyReward() {\r\n      if (this.isClaiming || !this.canClaimWeekly) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimWeeklyReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取每周奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    async claimMonthlyReward() {\r\n      if (this.isClaiming || !this.canClaimMonthly) return\r\n      \r\n      this.isClaiming = true\r\n      try {\r\n        await claimMonthlyReward()\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        await this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n        logger.error('领取每月奖励失败', e)\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    },\r\n    showVipDetails(level) {\r\n      this.selectedVipLevel = level\r\n      // 找到对应等级的VIP信息\r\n      const levelInfo = this.vipLevels.find(item => item.level === level)\r\n      if (levelInfo) {\r\n        this.$toast && this.$toast(`VIP${level}特权: ${levelInfo.privileges.join('、')}`, 'info')\r\n      } else {\r\n        this.$toast && this.$toast(`VIP${level}特权详情`, 'info')\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    try {\r\n      // 并行加载数据以提高性能\r\n      await Promise.all([\r\n        this.fetchVipData()\r\n      ])\r\n    } catch (e) {\r\n      logger.error('VIP页面初始化失败', e)\r\n      // 设置错误状态\r\n      this.hasError = true\r\n      this.errorMessage = '加载数据失败，请稍后重试'\r\n      // 确保无论如何都结束加载状态\r\n      this.loading = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vip-page {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n  color: #ffd700;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.back-button-container {\r\n  position: fixed;\r\n  bottom: 40px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 10;\r\n  width: 100%;\r\n  max-width: 800px;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 0 20px;\r\n}\r\n\r\n.back-button {\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  width: 140px;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));\r\n  \r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: contain;\r\n  }\r\n  \r\n  &:hover {\r\n    transform: scale(1.05);\r\n    filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n// 添加设备适配变量\r\n:root {\r\n  --main-tab-padding: 12px 20px;\r\n  --main-tab-margin: 0 10px;\r\n  --main-tab-min-width: 100px;\r\n  --main-tab-font-size: 16px;\r\n  --section-padding: 20px;\r\n  --vip-icon-size: 120px;\r\n  --reward-tab-padding: 10px 20px;\r\n  --reward-tab-min-width: 90px;\r\n}\r\n\r\n.main-tabs {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 25px;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n  border: 2px solid #444;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.main-tab {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: var(--main-tab-padding);\r\n  margin: var(--main-tab-margin);\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border: 2px solid #555;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: var(--main-tab-min-width);\r\n  position: relative;\r\n  \r\n  span {\r\n    font-weight: bold;\r\n    font-size: var(--main-tab-font-size);\r\n    color: #ccc;\r\n    transition: color 0.3s ease;\r\n  }\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.5);\r\n    transform: translateY(-3px);\r\n    \r\n    span {\r\n      color: #fff;\r\n    }\r\n  }\r\n  \r\n  &.active {\r\n    background: linear-gradient(to bottom, rgba(255, 215, 0, 0.2), rgba(184, 134, 11, 0.2));\r\n    border-color: #ffd700;\r\n    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -2px;\r\n      left: 25%;\r\n      right: 25%;\r\n      height: 3px;\r\n      background: #ffd700;\r\n      border-radius: 3px;\r\n    }\r\n    \r\n    span {\r\n      color: #ffd700;\r\n    }\r\n  }\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.pixel-border-box {\r\n  border: 2px solid #ffd700;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  border-radius: 4px;\r\n  padding: var(--section-padding);\r\n  margin-bottom: 25px;\r\n  position: relative;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -2px;\r\n    left: -2px;\r\n    right: -2px;\r\n    bottom: -2px;\r\n    border: 1px solid #ff8800;\r\n    border-radius: 5px;\r\n    pointer-events: none;\r\n    z-index: 1;\r\n  }\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  text-align: center;\r\n  \r\n  .vip-title-with-icon {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 10px;\r\n    \r\n    .vip-small-icon {\r\n      width: 30px;\r\n      height: 30px;\r\n    }\r\n  }\r\n  \r\n  h3 {\r\n    color: #ffd700;\r\n    font-size: 20px;\r\n    margin: 0;\r\n    padding-bottom: 10px;\r\n    display: inline-block;\r\n  }\r\n  \r\n  .section-decor {\r\n    height: 2px;\r\n    background: linear-gradient(to right, transparent, #ffd700, transparent);\r\n    margin-top: 5px;\r\n  }\r\n}\r\n\r\n.vip-info {\r\n  text-align: center;\r\n  \r\n  .vip-info-header {\r\n    margin-bottom: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n  }\r\n  \r\n  .vip-icon {\r\n    width: var(--vip-icon-size);\r\n    height: var(--vip-icon-size);\r\n    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));\r\n    transition: transform 0.3s, filter 0.3s;\r\n    \r\n    &:hover {\r\n      transform: scale(1.05);\r\n      filter: drop-shadow(0 0 15px rgba(255, 215, 0, 1));\r\n    }\r\n  }\r\n}\r\n\r\n.vip-exp-section {\r\n  margin: 20px 0;\r\n}\r\n\r\n.vip-exp {\r\n  color: #ccc;\r\n  margin-bottom: 15px;\r\n  font-size: 15px;\r\n  \r\n  .vip-next-exp {\r\n    color: #ff8800;\r\n  }\r\n  \r\n  .highlight-text {\r\n    color: #ffd700;\r\n    font-weight: bold;\r\n    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);\r\n  }\r\n}\r\n\r\n.vip-progress-container {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  \r\n  .vip-progress-bar {\r\n    flex: 1;\r\n    height: 18px;\r\n    background: #222;\r\n    border: 1px solid #444;\r\n    border-radius: 9px;\r\n    overflow: hidden;\r\n    margin-right: 10px;\r\n    position: relative;\r\n    box-shadow: inset 0 2px 5px rgba(0,0,0,0.5);\r\n  }\r\n  \r\n  .vip-progress {\r\n    height: 100%;\r\n    background: linear-gradient(90deg, #ffd700, #ff8800);\r\n    border-radius: 9px;\r\n    transition: width 0.8s cubic-bezier(0.22, 1, 0.36, 1);\r\n    box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\r\n  }\r\n  \r\n  .progress-stars {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    pointer-events: none;\r\n  }\r\n  \r\n  .progress-star {\r\n    position: absolute;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    color: rgba(255, 255, 255, 0.6);\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .vip-progress-text {\r\n    width: 45px;\r\n    text-align: right;\r\n    font-size: 16px;\r\n    color: #ffd700;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.privilege-list {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style: none;\r\n}\r\n\r\n.privilege-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 15px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #4caf50;\r\n  transition: transform 0.2s, background 0.2s;\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.4);\r\n    transform: translateX(5px);\r\n  }\r\n  \r\n  .privilege-icon {\r\n    color: #4caf50;\r\n    margin-right: 12px;\r\n    font-style: normal;\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    width: 20px;\r\n    height: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: rgba(76, 175, 80, 0.2);\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.reward-tabs {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 20px;\r\n  padding: 5px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 6px;\r\n  border: 1px solid #444;\r\n}\r\n\r\n.reward-tab {\r\n  padding: var(--reward-tab-padding);\r\n  cursor: pointer;\r\n  background: #222;\r\n  border: 2px solid #444;\r\n  margin: 0 5px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  min-width: var(--reward-tab-min-width);\r\n  border-radius: 4px;\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -2px;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: transparent;\r\n  }\r\n  \r\n  &:hover {\r\n    border-color: #666;\r\n    transform: translateY(-2px);\r\n  }\r\n  \r\n  &.active {\r\n    background: linear-gradient(to bottom, #ffd700, #b8860b);\r\n    color: #000;\r\n    border-color: #ffd700;\r\n    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\r\n    \r\n    &::after {\r\n      background: #ffd700;\r\n    }\r\n  }\r\n}\r\n\r\n.daily-reward-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.reward-info-card {\r\n  width: 100%;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border: 1px solid #444;\r\n}\r\n\r\n.reward-list {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style: none;\r\n}\r\n\r\n.reward-list-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  margin-bottom: 8px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 4px;\r\n  transition: background 0.2s;\r\n  border-left: 3px solid #ffd700;\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.4);\r\n  }\r\n  \r\n  .reward-name {\r\n    font-weight: bold;\r\n    color: #ffd700;\r\n  }\r\n  \r\n  .reward-amount {\r\n    color: #ccc;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.history-list {\r\n  max-height: 250px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n  \r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n  \r\n  &::-webkit-scrollbar-track {\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 3px;\r\n  }\r\n  \r\n  &::-webkit-scrollbar-thumb {\r\n    background: #555;\r\n    border-radius: 3px;\r\n    \r\n    &:hover {\r\n      background: #666;\r\n    }\r\n  }\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 4px;\r\n  transition: background 0.2s;\r\n  \r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.4);\r\n  }\r\n  \r\n  .history-info {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n    \r\n    .history-vip-badge {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .history-vip-icon {\r\n        width: 30px;\r\n        height: 30px;\r\n        filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));\r\n      }\r\n    }\r\n    \r\n    .history-date {\r\n      color: #888;\r\n    }\r\n    \r\n    .history-desc {\r\n      flex-basis: 100%;\r\n      color: #ccc;\r\n      margin-top: 5px;\r\n    }\r\n  }\r\n}\r\n\r\n.pixel-button {\r\n  background: #333;\r\n  color: #fff;\r\n  border: 2px solid #555;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n  overflow: hidden;\r\n  \r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n  \r\n  &:active {\r\n    transform: translateY(1px);\r\n  }\r\n  \r\n  &.gold {\r\n    background: linear-gradient(to bottom, #ffd700, #b8860b);\r\n    border-color: #ffd700;\r\n    color: #000;\r\n    \r\n    &:hover {\r\n      box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);\r\n    }\r\n  }\r\n  \r\n  &.disabled {\r\n    background: #444;\r\n    border-color: #555;\r\n    color: #888;\r\n    cursor: not-allowed;\r\n    \r\n    &:hover {\r\n      transform: none;\r\n      box-shadow: none;\r\n    }\r\n  }\r\n  \r\n  &.small {\r\n    padding: 5px 12px;\r\n    font-size: 13px;\r\n  }\r\n  \r\n  &.shine-effect::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: -50%;\r\n    left: -50%;\r\n    width: 200%;\r\n    height: 200%;\r\n    background: linear-gradient(\r\n      to right,\r\n      rgba(255, 255, 255, 0) 0%,\r\n      rgba(255, 255, 255, 0.3) 50%,\r\n      rgba(255, 255, 255, 0) 100%\r\n    );\r\n    transform: rotate(45deg);\r\n    animation: shine 3s infinite;\r\n  }\r\n}\r\n\r\n@keyframes shine {\r\n  0% {\r\n    left: -150%;\r\n  }\r\n  100% {\r\n    left: 150%;\r\n  }\r\n}\r\n\r\n// 加载和错误状态样式\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 0;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 5px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 50%;\r\n  border-top-color: #ffd700;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.loading-text {\r\n  color: #ffd700;\r\n  font-size: 18px;\r\n  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 0;\r\n  \r\n  .error-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n    border-radius: 50%;\r\n    background: rgba(255, 0, 0, 0.2);\r\n    border: 3px solid #ff6b6b;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 40px;\r\n    font-weight: bold;\r\n    color: #ff6b6b;\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .error-text {\r\n    color: #ff6b6b;\r\n    font-size: 18px;\r\n    margin-bottom: 25px;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 大屏幕设备 (1200px以上)\r\n@media (min-width: 1201px) {\r\n  .vip-page {\r\n    max-width: 900px;\r\n    padding: 30px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 15px 30px;\r\n    min-width: 120px;\r\n    \r\n    span {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 150px;\r\n    height: 150px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 25px;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 平板设备 (768px - 1200px)\r\n@media (min-width: 768px) and (max-width: 1200px) {\r\n  .vip-page {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 12px 25px;\r\n    min-width: 110px;\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 100px;\r\n    height: 100px;\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 手机设备 (600px - 767px)\r\n@media (min-width: 601px) and (max-width: 767px) {\r\n  .vip-page {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 10px 20px;\r\n    min-width: 90px;\r\n    margin: 0 5px;\r\n    \r\n    span {\r\n      font-size: 15px;\r\n    }\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 90px;\r\n    height: 90px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 19px;\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 18px;\r\n  }\r\n  \r\n  .reward-tab {\r\n    padding: 8px 15px;\r\n    min-width: 85px;\r\n  }\r\n}\r\n\r\n// 响应式调整 - 小屏幕手机设备 (600px以下)\r\n@media (max-width: 600px) {\r\n  .vip-page {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    flex-wrap: wrap;\r\n    padding: 8px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 10px;\r\n    margin: 5px;\r\n    min-width: 80px;\r\n    \r\n    span {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 15px;\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n  \r\n  .reward-tabs {\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .reward-tab {\r\n    padding: 8px 15px;\r\n    margin: 3px;\r\n    min-width: 80px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .reward-list-item {\r\n    padding: 8px 12px;\r\n    \r\n    .reward-name, .reward-amount {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .privilege-item {\r\n    font-size: 14px;\r\n    padding: 8px 12px;\r\n  }\r\n  \r\n  .vip-exp {\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .vip-progress-container {\r\n    .vip-progress-bar {\r\n      height: 15px;\r\n    }\r\n    \r\n    .vip-progress-text {\r\n      width: 40px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .history-item {\r\n    padding: 10px;\r\n    \r\n    .history-info {\r\n      gap: 8px;\r\n      \r\n      .history-vip-icon {\r\n        width: 25px;\r\n        height: 25px;\r\n      }\r\n      \r\n      .history-date, .history-desc {\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .back-button-container {\r\n    bottom: 30px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 120px;\r\n    height: 45px;\r\n  }\r\n}\r\n\r\n// 极小屏幕设备 (375px以下)\r\n@media (max-width: 375px) {\r\n  .vip-page {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .main-tabs {\r\n    padding: 5px;\r\n  }\r\n  \r\n  .main-tab {\r\n    padding: 8px;\r\n    margin: 3px;\r\n    min-width: 70px;\r\n    \r\n    span {\r\n      font-size: 13px;\r\n    }\r\n  }\r\n  \r\n  .pixel-border-box {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .section-header h3 {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .vip-info .vip-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .reward-tab {\r\n    padding: 6px 10px;\r\n    min-width: 70px;\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .pixel-button {\r\n    padding: 8px 15px;\r\n    font-size: 13px;\r\n    \r\n    &.small {\r\n      padding: 4px 10px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n  \r\n  .back-button-container {\r\n    bottom: 25px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 110px;\r\n    height: 40px;\r\n  }\r\n}\r\n</style>"]}]}