{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Clinic.vue?vue&type=template&id=47b4dff0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Clinic.vue", "mtime": 1749792050849}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}