<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * 显示管理员登录页面
     */
    public function showLoginForm()
    {
        return view('admin.auth.login');
    }

    /**
     * 处理管理员登录请求
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // 尝试登录
        $credentials = $request->only('username', 'password');
        if (Auth::guard('admin')->attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            // 登录成功，重定向到管理后台首页
            return redirect()->intended(route('admin.admin'));
        }

        // 登录失败，返回错误信息
        throw ValidationException::withMessages([
            'username' => [trans('auth.failed')],
        ]);
    }

    /**
     * 管理员登出
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login');
    }

    /**
     * 显示修改密码页面
     */
    public function showChangePasswordForm()
    {
        return view('admin.auth.change-password');
    }

    /**
     * 处理修改密码请求
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $admin = Auth::guard('admin')->user();

        // 验证当前密码
        if (!Hash::check($request->current_password, $admin->password)) {
            return back()->withErrors(['current_password' => '当前密码不正确']);
        }

        // 更新密码
        $admin->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('admin.admin')->with('success', '密码修改成功');
    }
}
