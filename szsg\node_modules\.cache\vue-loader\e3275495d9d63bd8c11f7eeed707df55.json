{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue", "mtime": 1750348125826}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Main.vue"], "names": [], "mappings": ";AA0bA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Main.vue", "sourceRoot": "src/views/game", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"game-container\">\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <!-- 上半部分：左右分栏 -->\n        <div class=\"top-section\">\n          <!-- 左侧：个人信息 -->\n          <div class=\"left-panel\">\n            <div class=\"pixel-avatar-bar2\" @click=\"openCharacterStatus\">\n              <!-- <img class=\"pixel-bg-img2\" src=\"/static/game/UI/bj/tbu.png\" alt=\"头像背景\" /> -->\n              <img class=\"pixel-avatar-img2\" :src=\"characterInfo.avatar\" :alt=\"characterInfo.name\" />\n              <div class=\"pixel-name2\">{{ characterInfo.name }}</div>\n              <div class=\"pixel-bars2\">\n                <div class=\"pixel-bar2 pixel-hp2\">\n                  <div class=\"pixel-bar-inner2 pixel-hp-inner2\" :style=\"{width: hpPercent + '%'}\"></div>\n                </div>\n                <div class=\"pixel-bar2 pixel-mp2\">\n                  <div class=\"pixel-bar-inner2 pixel-mp-inner2\" :style=\"{width: mpPercent + '%'}\"></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"pixel-info-box\">\n              <div class=\"pixel-row\">职业: {{ characterInfo.profession }}</div>\n              <div class=\"pixel-row\">等级: {{ characterInfo.level }}</div>\n              <div class=\"pixel-row\">银两: {{ characterInfo.silver }}</div>\n              <div class=\"pixel-row\"><span class=\"pixel-label-gold\">金砖:</span><span class=\"pixel-value-gold\">{{ characterInfo.gold }}</span></div>\n              <div class=\"pixel-row pixel-exp-label\">经验: {{ characterInfo.exp }}/{{ characterInfo.expRequired }}</div>\n              <div class=\"pixel-exp-bar\">\n                <div class=\"pixel-exp-inner\" :style=\"{width: expPercent + '%'}\"></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 右侧：动态内容区域 -->\n          <div class=\"right-panel\">\n            <div class=\"panel-header\">{{ getCurrentPanelTitle() }}</div>\n            <div class=\"panel-content\">\n              <!-- 人物功能内容：显示当前地图的NPC和怪物 -->\n              <div v-if=\"!currentFunction || currentFunction === 'character'\" class=\"npc-content\">\n                <div class=\"pixel-border-box\">\n                  <!-- 四行布局容器 -->\n                  <div class=\"four-row-container\">\n                    <!-- 第一行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(0)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(0), getEntityForRow(0).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(0).type === 'monster' }\">\n                            {{ getEntityForRow(0).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(0).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(0))\"\n                        >\n                          {{ getEntityForRow(0).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第二行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(1)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(1), getEntityForRow(1).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(1).type === 'monster' }\">\n                            {{ getEntityForRow(1).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(1).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(1))\"\n                        >\n                          {{ getEntityForRow(1).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第三行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(2)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(2), getEntityForRow(2).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(2).type === 'monster' }\">\n                            {{ getEntityForRow(2).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(2).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(2))\"\n                        >\n                          {{ getEntityForRow(2).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第四行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(3)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(3), getEntityForRow(3).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(3).type === 'monster' }\">\n                            {{ getEntityForRow(3).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(3).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(3))\"\n                        >\n                          {{ getEntityForRow(3).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 实体信息弹窗 -->\n              <div v-if=\"showEntityModal\" class=\"entity-modal-overlay\" @click=\"closeEntityModal\">\n                <div class=\"entity-modal\" @click.stop>\n                  <div class=\"modal-header\">\n                    <h3>{{ selectedEntity.name }}</h3>\n                    <button class=\"close-btn\" @click=\"closeEntityModal\">×</button>\n                  </div>\n                  <div class=\"modal-content\">\n                    <div v-if=\"selectedEntityType === 'npc'\" class=\"npc-info\">\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">称号：</span>\n                        <span class=\"info-value\">{{ selectedEntity.title || '无' }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">等级：</span>\n                        <span class=\"info-value\">{{ selectedEntity.level }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">类型：</span>\n                        <span class=\"info-value\">{{ getNpcTypeText(selectedEntity.type) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">阵营：</span>\n                        <span class=\"info-value\">{{ getFactionText(selectedEntity.faction) }}</span>\n                      </div>\n                      <div v-if=\"selectedEntity.services && selectedEntity.services.length > 0\" class=\"info-row\">\n                        <span class=\"info-label\">服务：</span>\n                        <span class=\"info-value\">{{ selectedEntity.services.join(', ') }}</span>\n                      </div>\n                      <div class=\"info-row description\">\n                        <span class=\"info-label\">描述：</span>\n                        <span class=\"info-value\">{{ selectedEntity.description || '暂无描述' }}</span>\n                      </div>\n                    </div>\n                    <div v-else-if=\"selectedEntityType === 'monster'\" class=\"monster-info\">\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">称号：</span>\n                        <span class=\"info-value\">{{ selectedEntity.title || '无' }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">等级：</span>\n                        <span class=\"info-value\">{{ selectedEntity.level }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">类型：</span>\n                        <span class=\"info-value\">{{ getMonsterTypeText(selectedEntity.type) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">元素：</span>\n                        <span class=\"info-value\">{{ getElementText(selectedEntity.element) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">体型：</span>\n                        <span class=\"info-value\">{{ getSizeText(selectedEntity.size) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">威胁等级：</span>\n                        <span class=\"info-value threat-level\">{{ selectedEntity.threat_level || 1 }}</span>\n                      </div>\n                      <div v-if=\"selectedEntity.stats\" class=\"stats-section\">\n                        <div class=\"stats-title\">属性</div>\n                        <div class=\"stats-grid\">\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">生命：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.health }}/{{ selectedEntity.stats.max_health }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">攻击：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.attack }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">防御：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.defense }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">速度：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.speed }}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div class=\"info-row description\">\n                        <span class=\"info-label\">描述：</span>\n                        <span class=\"info-value\">{{ selectedEntity.description || '暂无描述' }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 设施功能内容 -->\n              <div v-else-if=\"currentFunction === 'equipment'\" class=\"facilities-content\">\n                <div class=\"facility-list six-grid\">\n                  <div class=\"facility-item\" @click=\"selectFacility('clinic')\">\n                    <span class=\"facility-name\">医馆</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('bank')\">\n                    <span class=\"facility-name\">钱庄</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('posthouse')\">\n                    <span class=\"facility-name\">馆驿</span>\n                    </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('market')\">\n                    <span class=\"facility-name\">市场</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('square')\">\n                    <span class=\"facility-name\">广场</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('government')\">\n                    <span class=\"facility-name\">官府</span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 移动功能内容 -->\n              <div v-else-if=\"currentFunction === 'move'\" class=\"move-content\">\n                <div class=\"pixel-border-box\">\n                  <!-- 四行位置布局容器 -->\n                  <div class=\"four-row-container\">\n                    <!-- 第一行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(0)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(0))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(0).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第二行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(1)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(1))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(1).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第三行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(2)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(2))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(2).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第四行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(3)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(3))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(3).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 功能菜单内容 -->\n              <div v-else-if=\"currentFunction === 'functions'\" class=\"functions-content\">\n                <div class=\"function-list functions-grid\">\n                  <div class=\"function-item\" @click=\"openFunction('status')\">\n                    <span class=\"function-name\">状态</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('items')\">\n                    <span class=\"function-name\">物品</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('immortal')\">\n                    <span class=\"function-name\">仙将</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('team')\">\n                    <span class=\"function-name\">组队</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('ranking')\">\n                    <span class=\"function-name\">排行</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('friends')\">\n                    <span class=\"function-name\">好友</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('mail')\">\n                    <span class=\"function-name\">邮件</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('quest')\">\n                    <span class=\"function-name\">任务</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('arena')\">\n                    <span class=\"function-name\">擂台</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('guild')\">\n                    <span class=\"function-name\">帮派</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('training')\">\n                    <span class=\"function-name\">训练</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('treasury')\">\n                    <span class=\"function-name\">宝库</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('notice')\">\n                    <span class=\"function-name\">公告</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('vip')\">\n                    <span class=\"function-name\">VIP</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('strategy')\">\n                    <span class=\"function-name\">攻略</span>\n                </div>\n                  <div class=\"function-item\" @click=\"openFunction('logout')\">\n                    <span class=\"function-name\">登出</span>\n              </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 下半部分：功能区域 -->\n        <div class=\"bottom-section\">\n          <!-- 中间：功能按钮栏 -->\n          <div class=\"function-bar\">\n            <div\n              v-for=\"(func, index) in mainFunctions\"\n              :key=\"index\"\n              class=\"function-btn\"\n              :class=\"{ 'active': currentFunction === func.action }\"\n              @click=\"handleFunction(func.action)\"\n            >\n              <img :src=\"func.image\" :alt=\"func.name\" class=\"function-btn-image\" />\n            </div>\n          </div>\n\n          <!-- 在线玩家头像区域 -->\n          <div class=\"online-players\">\n            <div class=\"section-title\">在线玩家头像</div>\n            <div class=\"players-avatars\">\n              <div\n                v-for=\"(player, index) in onlinePlayers\"\n                :key=\"index\"\n                class=\"player-avatar\"\n              >\n                <img :src=\"player.avatar\" :alt=\"player.name\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- 底部：聊天组件区域 -->\n          <div class=\"chat-section\">\n            <GameChat\n              :character-info=\"characterInfo\"\n              :auto-connect=\"true\"\n              :initial-minimized=\"false\"\n              @message-sent=\"onChatMessageSent\"\n              @channel-switched=\"onChatChannelSwitched\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport GameChat from '@/components/GameChat.vue'\nimport logger from '@/utils/logger'\nimport { getCharacterDetail, getCurrentCharacter, getCharacterStatus } from '@/api/services/characterService'\nimport { showMessage } from '@/utils/message'\n\nexport default {\n  name: 'Main',\n  components: {\n    GameLayout,\n    GameChat\n  },\n  data() {\n    return {\n      currentFunction: null, // 保持为null，默认显示NPC和怪物\n      characterInfo: {\n        name: '',\n        avatar: '',\n        profession: '',\n        silver: 0,\n        gold: 0,\n        expNeeded: 0,\n        hp: 0,\n        maxHp: 0,\n        mp: 0,\n        maxMp: 0,\n        level: 1,\n        exp: 0,\n        expRequired: 1000,\n        attributePoints: 0,\n        constitution: 0,\n        intelligence: 0,\n        strength: 0,\n        agility: 0,\n        attack: 0,\n        defense: 0,\n        speed: 0\n      },\n      npcList: [\n        {\n          name: '村长',\n          type: 'npc',\n          description: '村庄的领导者，可以接受任务',\n          level: 50,\n          avatar: '/static/game/UI/tx/npc/village_chief.png',\n          services: ['任务', '信息']\n        },\n        {\n          name: '武器商人',\n          type: 'npc',\n          description: '出售各种武器装备',\n          level: 30,\n          avatar: '/static/game/UI/tx/npc/weapon_merchant.png',\n          services: ['武器', '装备']\n        },\n        {\n          name: '药剂师',\n          type: 'npc',\n          description: '出售恢复药剂和魔法药水',\n          level: 25,\n          avatar: '/static/game/UI/tx/npc/alchemist.png',\n          services: ['药剂', '治疗']\n        },\n        {\n          name: '铁匠',\n          type: 'npc',\n          description: '可以强化和修理装备',\n          level: 40,\n          avatar: '/static/game/UI/tx/npc/blacksmith.png',\n          services: ['强化', '修理']\n        }\n      ],\n      mainFunctions: [\n        { name: '人物', action: 'character', image: '/static/game/UI/anniu/sc_gn_1.png' },\n        { name: '设施', action: 'equipment', image: '/static/game/UI/anniu/sc_gn_2.png' },\n        { name: '移动', action: 'move', image: '/static/game/UI/anniu/sc_gn_3.png' },\n        { name: '功能', action: 'functions', image: '/static/game/UI/anniu/sc_gn_4.png' }\n      ],\n      onlinePlayers: [\n        { name: '玩家1', avatar: '/static/game/UI/tx/male/tx2.png' },\n        { name: '玩家2', avatar: '/static/game/UI/tx/male/tx3.png' },\n        { name: '玩家3', avatar: '/static/game/UI/tx/male/tx4.png' },\n        { name: '玩家4', avatar: '/static/game/UI/tx/male/tx5.png' },\n        { name: '玩家5', avatar: '/static/game/UI/tx/male/tx6.png' }\n      ],\n\n      // 地图相关数据\n      loadingLocations: false,\n\n      // 实体信息弹窗\n      showEntityModal: false,\n      selectedEntity: null,\n      selectedEntityType: null\n    }\n  },\n  computed: {\n    hpPercent() {\n      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100\n    },\n    mpPercent() {\n      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100\n    },\n    expPercent() {\n      return (this.characterInfo.exp / this.characterInfo.expRequired) * 100\n    },\n\n    // 地图相关计算属性\n    availableLocations() {\n      return this.$store.state.map.availableLocations || []\n    },\n\n    isMoving() {\n      return this.$store.state.map.loading.moving\n    },\n\n    canMove() {\n      return this.$store.getters['map/canMove']\n    },\n\n    // 当前地图的NPC列表\n    currentLocationNpcs() {\n      try {\n        const currentLocation = this.$store.state.map.currentLocation;\n        return currentLocation.npcs || [];\n      } catch (error) {\n        logger.error('[Main] 获取当前位置NPC失败:', error);\n        return [];\n      }\n    },\n\n    // 当前地图的怪物列表\n    currentLocationMonsters() {\n      try {\n        const currentLocation = this.$store.state.map.currentLocation;\n        return currentLocation.monsters || [];\n      } catch (error) {\n        logger.error('[Main] 获取当前位置怪物失败:', error);\n        return [];\n      }\n    },\n\n    // 合并所有实体（NPC和怪物）\n    allEntities() {\n      const entities = [];\n\n      try {\n        // 添加NPC\n        if (this.currentLocationNpcs && Array.isArray(this.currentLocationNpcs)) {\n          this.currentLocationNpcs.forEach(npc => {\n            entities.push({\n              ...npc,\n              type: 'npc'\n            });\n          });\n        }\n\n        // 添加怪物\n        if (this.currentLocationMonsters && Array.isArray(this.currentLocationMonsters)) {\n          this.currentLocationMonsters.forEach(monster => {\n            entities.push({\n              ...monster,\n              type: 'monster'\n            });\n          });\n        }\n      } catch (error) {\n        logger.error('[Main] 获取实体列表失败:', error);\n      }\n\n      return entities;\n    }\n  },\n  methods: {\n    openCharacterStatus() {\n      logger.info('打开角色状态页面')\n      \n      // 在跳转前初始化角色状态数据\n      this.$store.dispatch('character/initCurrentCharacter').then(character => {\n        if (character) {\n          this.$store.dispatch('character/loadCharacterStatus').then(() => {\n            // 确保状态已加载完成，然后跳转\n            logger.debug('[Main] 角色状态加载成功，准备跳转到角色状态页面');\n            \n            // 强制刷新一次角色信息，确保数据最新\n            this.loadCharacterInfo().then(() => {\n              this.$router.push('/game/character-status');\n            });\n          }).catch(error => {\n            logger.error('[Main] 加载角色状态失败:', error);\n            showMessage('加载角色状态失败', 'error');\n            // 即使加载失败也跳转，CharacterStatus组件会处理错误情况\n            this.$router.push('/game/character-status');\n          });\n        } else {\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n        }\n      });\n    },\n    selectNpc(npc) {\n      logger.info('选择NPC/野怪', npc.name)\n\n      if (npc.type === 'npc') {\n        // NPC交互逻辑\n        this.handleNpcInteraction(npc)\n      } else if (npc.type === 'monster') {\n        // 怪物战斗逻辑\n        this.handleMonsterEncounter(npc)\n      }\n    },\n\n    handleNpcInteraction(npc) {\n      // 西游记主题的NPC对话\n      switch(npc.name) {\n        case '城隍爷':\n          this.showToast(`与${npc.name}对话：施主，此地乃东胜神洲，有何贵干？`)\n          break\n        case '仙界商人':\n          this.showToast(`与${npc.name}对话：仙友，我这里有各种法宝，要不要看看？`)\n          setTimeout(() => {\n            this.$router.push('/game/market')\n          }, 2000)\n          break\n        case '猴族长老':\n          this.showToast(`与${npc.name}对话：小猴子，想学武艺吗？花果山可是修炼的好地方！`)\n          break\n        case '洞府守护':\n          this.showToast(`与${npc.name}对话：水帘洞乃美猴王洞府，此处有上古秘籍！`)\n          break\n        case '得道高僧':\n          this.showToast(`与${npc.name}对话：阿弥陀佛，施主与佛有缘，可愿听贫僧讲经？`)\n          break\n        case '罗汉':\n          this.showToast(`与${npc.name}对话：善哉善哉，施主来到灵山，可是为求真经？`)\n          break\n        case '凡间官员':\n          this.showToast(`与${npc.name}对话：这位侠客，南瞻部洲最近不太平，小心山贼！`)\n          break\n        case '东海龙王':\n          this.showToast(`与${npc.name}对话：何方神圣闯入龙宫？若是有缘人，可赐你神兵利器！`)\n          setTimeout(() => {\n            this.$router.push('/game/market')\n          }, 2000)\n          break\n        case '虾兵':\n          this.showToast(`与${npc.name}对话：龙王有令，闲杂人等不得入内！`)\n          break\n        default:\n          this.showToast(`与${npc.name}对话：施主，贫道这厢有礼了！`)\n      }\n    },\n\n    handleMonsterEncounter(monster) {\n      // 西游记主题的怪物遭遇\n      let encounterMessage = '';\n\n      switch(monster.name) {\n        case '灵猴':\n          encounterMessage = `一只${monster.name}从树上跳下，似乎想要与你切磋武艺！`;\n          break\n        case '山魈':\n          encounterMessage = `${monster.name}从山林中现身，凶神恶煞地盯着你！`;\n          break\n        case '水灵':\n          encounterMessage = `洞中的${monster.name}感受到了你的气息，化作人形挡住去路！`;\n          break\n        case '护法金刚':\n          encounterMessage = `${monster.name}金光闪闪，威严地说：\"欲入灵山，先过我这一关！\"`;\n          break\n        case '山贼':\n          encounterMessage = `一群${monster.name}拦路抢劫：\"此路是我开，此树是我栽！\"`;\n          break\n        case '蟹将':\n          encounterMessage = `龙宫${monster.name}挥舞着巨钳：\"胆敢擅闯龙宫，受死！\"`;\n          break\n        default:\n          encounterMessage = `遭遇了${monster.name}，看起来来者不善！`;\n      }\n\n      this.showToast(`${encounterMessage}（等级${monster.level}）准备战斗！`)\n\n      // 获取当前角色和位置信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      const currentLocation = this.$store.state.map.currentLocation;\n\n      if (!currentCharacter || !currentCharacter.id) {\n        this.showToast('角色信息错误，无法开始战斗', 'error');\n        return;\n      }\n\n      setTimeout(() => {\n        // 传递战斗所需的参数\n        this.$router.push({\n          path: '/game/battle',\n          query: {\n            characterId: currentCharacter.id,\n            monsterId: monster.id,\n            locationId: currentLocation?.id || null\n          }\n        });\n      }, 2000)\n    },\n\n    // 获取指定行的实体（为后台管理预留接口）\n    getEntityForRow(rowIndex) {\n      try {\n        // 目前简单按顺序分配，后续可通过后台配置\n        const entities = this.allEntities || [];\n        return entities[rowIndex] || null;\n      } catch (error) {\n        logger.error('[Main] 获取行实体失败:', error);\n        return null;\n      }\n    },\n\n    // 统一的实体操作方法\n    handleEntityAction(entity) {\n      if (entity.type === 'npc') {\n        this.handleNpcInteraction(entity);\n      } else if (entity.type === 'monster') {\n        this.handleMonsterEncounter(entity);\n      }\n    },\n\n    // 获取指定行的位置（为后台管理预留接口）\n    getLocationForRow(rowIndex) {\n      try {\n        // 目前简单按顺序分配，后续可通过后台配置\n        const locations = this.availableLocations || [];\n        return locations[rowIndex] || null;\n      } catch (error) {\n        logger.error('[Main] 获取行位置失败:', error);\n        return null;\n      }\n    },\n    handleFunction(action) {\n      logger.info('点击功能', action)\n      if (this.currentFunction === action) {\n        // 如果当前功能已经是选中状态，不做任何操作\n        return;\n      } else {\n        // 设置为新的功能\n        this.currentFunction = action\n      }\n    },\n    getCurrentPanelTitle() {\n      switch(this.currentFunction) {\n        case 'character':\n          return '人物信息'\n        case 'equipment':\n          return '设施列表'\n        case 'move':\n          return '移动地点'\n        case 'functions':\n          return '功能菜单'\n        default:\n          return ''\n      }\n    },\n    // 设施相关方法\n    selectFacility(facility) {\n      logger.info('选择设施', facility)\n      switch(facility) {\n        case 'clinic':\n          this.$router.push('/game/clinic');\n          break;\n        case 'bank':\n          this.$router.push('/game/bank');\n          break;\n        case 'posthouse':\n          this.$router.push('/game/posthouse');\n          break;\n        case 'market':\n          this.$router.push('/game/market');\n          break;\n        case 'square':\n          this.$router.push('/game/square');\n          break;\n        case 'government':\n          this.$router.push('/game/government');\n          break;\n      }\n    },\n    // 移动相关方法\n    async moveToLocationDirectly(location) {\n      logger.info('[Main] 直接移动到位置:', location);\n\n      // 检查角色信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      if (!currentCharacter || !currentCharacter.id) {\n        logger.error('[Main] 移动失败: 未找到角色信息');\n        this.showToast('请先选择角色', 'error');\n        return;\n      }\n\n      // 检查是否正在移动\n      if (this.isMoving) {\n        this.showToast('正在移动中，请稍候', 'warning');\n        return;\n      }\n\n      try {\n        await this.$store.dispatch('map/moveToLocation', location.id);\n        this.showToast(`成功移动到${location.name}`, 'success');\n\n        // 重新加载当前位置的NPC和怪物\n        await this.loadLocationEntities();\n      } catch (error) {\n        logger.error('[Main] 移动失败:', error);\n        this.showToast('移动失败: ' + error.message, 'error');\n      }\n    },\n\n\n\n\n\n    async loadLocationEntities() {\n      try {\n        const currentCharacter = this.$store.getters['character/currentCharacter'];\n        const currentLocation = this.$store.state.map.currentLocation;\n\n        if (currentCharacter && currentLocation.id) {\n          logger.debug('[Main] 当前位置实体信息:', {\n            location: currentLocation.name,\n            npcs: currentLocation.npcs?.length || 0,\n            monsters: currentLocation.monsters?.length || 0\n          });\n\n          // 触发Vue的响应式更新\n          this.$forceUpdate();\n        }\n      } catch (error) {\n        logger.error('[Main] 加载位置实体失败:', error);\n      }\n    },\n\n\n\n\n\n    // 兼容旧的移动方法\n    moveToLocation(location) {\n      logger.info('[Main] 兼容性移动方法调用:', location);\n\n      // 如果是字符串，转换为位置对象\n      if (typeof location === 'string') {\n        const locationObj = {\n          id: location,\n          name: location,\n          type: location\n        };\n        this.moveToLocationDirectly(locationObj);\n      } else {\n        this.moveToLocationDirectly(location);\n      }\n    },\n    // 功能菜单相关方法\n    openFunction(func) {\n      logger.info('打开功能', func)\n      // 根据功能类型跳转到对应页面\n      switch(func) {\n        case 'status':\n          this.$router.push('/game/status')\n          break\n        case 'items':\n          this.$router.push('/game/items')\n          break\n        case 'immortal':\n          this.$router.push('/game/immortal')\n          break\n        case 'team':\n          this.$router.push('/game/team')\n          break\n        case 'ranking':\n          this.$router.push('/game/ranking')\n          break\n        case 'friends':\n          this.$router.push('/game/friends')\n          break\n        case 'mail':\n          this.$router.push('/game/mail')\n          break\n        case 'quest':\n          this.$router.push('/game/quest')\n          break\n        case 'arena':\n          this.$router.push('/game/arena')\n          break\n        case 'guild':\n          this.$router.push('/game/guild')\n          break\n        case 'training':\n          this.$router.push('/game/training')\n          break\n        case 'treasury':\n          this.$router.push('/game/treasury')\n          break\n        case 'notice':\n          this.$router.push('/game/notice')\n          break\n        case 'vip':\n          this.$router.push('/game/vip')\n          break\n        case 'strategy':\n          this.$router.push('/game/strategy')\n          break\n        case 'logout':\n          this.$router.push('/game/logout')\n          break\n      }\n    },\n    // 聊天组件事件处理\n    onChatMessageSent(messageData) {\n      logger.info('[Main] 聊天消息已发送:', messageData);\n    },\n\n    onChatChannelSwitched(channelData) {\n      logger.info('[Main] 聊天频道已切换:', channelData);\n    },\n\n    showToast(message, type = 'info') {\n      // 简单的提示实现，使用logger代替alert避免弹出框\n      logger.info(`[Toast ${type}]:`, message)\n    },\n\n    // 实体信息弹窗相关方法\n    showEntityInfo(entity, type) {\n      this.selectedEntity = entity;\n      this.selectedEntityType = type;\n      this.showEntityModal = true;\n      logger.info(`[Main] 显示${type}信息:`, entity.name);\n    },\n\n    closeEntityModal() {\n      this.showEntityModal = false;\n      this.selectedEntity = null;\n      this.selectedEntityType = null;\n    },\n\n    // 获取类型文本的方法\n    getNpcTypeText(type) {\n      const typeMap = {\n        'merchant': '商人',\n        'quest_giver': '任务发布者',\n        'trainer': '训练师',\n        'guard': '守卫',\n        'official': '官员',\n        'immortal': '仙人',\n        'monk': '僧人',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n\n    getMonsterTypeText(type) {\n      const typeMap = {\n        'beast': '野兽',\n        'demon': '妖魔',\n        'spirit': '精灵',\n        'undead': '不死族',\n        'dragon': '龙族',\n        'immortal': '仙族',\n        'elemental': '元素',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n\n    getFactionText(faction) {\n      const factionMap = {\n        'heaven': '天庭',\n        'buddhist': '佛门',\n        'mortal': '凡间',\n        'demon': '妖魔',\n        'dragon': '龙族',\n        'neutral': '中立'\n      };\n      return factionMap[faction] || faction;\n    },\n\n    getElementText(element) {\n      const elementMap = {\n        'none': '无',\n        'fire': '火',\n        'water': '水',\n        'earth': '土',\n        'wind': '风',\n        'thunder': '雷',\n        'ice': '冰',\n        'light': '光',\n        'dark': '暗'\n      };\n      return elementMap[element] || element;\n    },\n\n    getSizeText(size) {\n      const sizeMap = {\n        'tiny': '微小',\n        'small': '小型',\n        'medium': '中型',\n        'large': '大型',\n        'huge': '巨型',\n        'giant': '超巨型'\n      };\n      return sizeMap[size] || size;\n    },\n\n    getDefaultAvatar(type) {\n      if (type === 'npc') {\n        return '/static/game/UI/tx/npc/default.png'\n      } else {\n        return '/static/game/UI/tx/monster/default.png'\n    }\n  },\n    async loadCharacterInfo() {\n      try {\n        // 获取当前角色基本信息\n    const current = getCurrentCharacter();\n        if (!current || !current.id) {\n          logger.error('[Main] 未找到当前角色信息');\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n          return;\n        }\n\n        // 将角色信息设置到store中\n        await this.$store.dispatch('character/selectCharacter', current);\n        logger.debug('[Main] 角色信息已设置到store:', current);\n\n        // 获取角色详情\n        try {\n          const detailResponse = await getCharacterDetail(current.id);\n          if (detailResponse && detailResponse.data) {\n            const detail = detailResponse.data;\n            \n            // 职业英文到中文的映射\n            const professionMap = {\n              'warrior': '武士',\n              'scholar': '文人',\n              'mystic': '异人'\n            };\n            \n            // 更新角色基本信息\n        this.characterInfo = {\n          ...this.characterInfo,\n              name: detail.name || current.name,\n              avatar: detail.avatar || `/static/game/UI/tx/${detail.gender || 'male'}/tx1.png`,\n              profession: professionMap[detail.profession] || detail.profession || '未知',\n              gold: detail.gold || 0,\n              silver: detail.silver || 0,\n              level: detail.level || 1\n        };\n          }\n        } catch (detailError) {\n          logger.error('[Main] 获取角色详情失败:', detailError);\n    }\n\n        // 获取角色状态信息\n        try {\n          const statusResponse = await getCharacterStatus(current.id);\n          if (statusResponse && statusResponse.data) {\n            const status = statusResponse.data;\n            \n            // 更新角色状态信息\n            this.characterInfo = {\n              ...this.characterInfo,\n              hp: parseInt(status.hp || 0),\n              maxHp: parseInt(status.max_hp || 100),\n              mp: parseInt(status.mp || 0),\n              maxMp: parseInt(status.max_mp || 100),\n              exp: parseInt(status.exp || 0),\n              expRequired: parseInt(status.exp_required || 1000),\n              attributePoints: parseInt(status.attribute_points || 0),\n              constitution: parseInt(status.constitution || 0),\n              intelligence: parseInt(status.intelligence || 0),\n              strength: parseInt(status.strength || 0),\n              agility: parseInt(status.agility || 0),\n              attack: parseInt(status.attack || 0),\n              defense: parseInt(status.defense || 0),\n              speed: parseInt(status.speed || 0)\n            };\n            \n            // 调试输出\n            logger.debug('[Main] 角色状态已更新:', this.characterInfo);\n          }\n        } catch (statusError) {\n          logger.error('[Main] 获取角色状态失败:', statusError);\n        }\n      } catch (error) {\n        logger.error('[Main] 加载角色信息失败:', error);\n        showMessage('加载角色信息失败', 'error');\n      }\n      \n      // 返回Promise以支持链式调用\n      return Promise.resolve();\n    },\n  },\n  async mounted() {\n    try {\n      // 加载角色信息\n      await this.loadCharacterInfo();\n\n      // 检查用户是否已登录且有角色信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      const isAuthenticated = this.$store.state.auth.isAuthenticated;\n\n      logger.debug('[Main] 挂载后状态检查:', {\n        isAuthenticated,\n        hasCharacter: !!currentCharacter,\n        characterId: currentCharacter?.id\n      });\n\n      if (isAuthenticated && currentCharacter && currentCharacter.id) {\n        // 初始化地图数据\n        try {\n          await this.$store.dispatch('map/initializeMap');\n          logger.info('[Main] 地图数据初始化完成');\n        } catch (error) {\n          logger.error('[Main] 地图数据初始化失败:', error);\n          // 地图初始化失败不影响主要功能，只记录错误\n        }\n      } else {\n        logger.warn('[Main] 用户未登录或无角色信息，跳过地图初始化');\n      }\n    } catch (error) {\n      logger.error('[Main] 组件挂载过程中发生错误:', error);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.game-container {\n  position: relative;\n  height: 100%;\n  width: 100%;\n  color: #ffd700;\n  overflow: hidden;\n  background: url('/static/game/UI/bg/main_bg.png') center center no-repeat;\n  background-size: cover;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(0, 0, 50, 0.4), rgba(50, 0, 0, 0.4));\n    z-index: 0;\n  }\n}\n\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n  padding: 8px;\n}\n\n.top-section {\n  display: flex;\n  gap: 8px;\n  height: 320px;\n  flex-shrink: 0;\n\n  @media (max-width: 768px) {\n    height: 280px;\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    height: 240px;\n    gap: 4px;\n  }\n}\n\n.bottom-section {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-height: 0;\n\n  @media (max-width: 768px) {\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    gap: 4px;\n  }\n}\n\n.left-panel, .right-panel {\n  background: none;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  border: none;\n}\n\n.left-panel {\n  width: 120px;\n  min-width: 100px;\n  background: none;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  border: none;\n}\n\n.pixel-avatar-bar2 {\n  cursor: pointer;\n  transition: transform 0.2s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.right-panel {\n  flex: 1;\n  min-width: 0;\n}\n\n.panel-header {\n  background: none;\n  color: inherit;\n  padding: 0;\n  font-weight: normal;\n  font-size: inherit;\n  text-align: center;\n  text-shadow: none;\n  border-bottom: none;\n}\n\n.character-info {\n  flex: 1;\n  padding: 6px;\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  background: none;\n}\n\n.info-details {\n  flex: 1;\n}\n\n.character-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 1px;\n  text-shadow: none;\n}\n\n.character-level {\n  font-size: 10px;\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 6px;\n  text-shadow: none;\n}\n\n.stats {\n  display: flex;\n  flex-direction: column;\n  gap: 1px;\n}\n\n.stat-item {\n  font-size: 10px;\n  color: #ffd700;\n  padding: 1px 0;\n  text-shadow: none;\n}\n\n.avatar-section, .status-bars {\n  display: none !important;\n}\n\n.panel-content {\n  flex: 1;\n  padding: 15px;\n  overflow-y: auto;\n  background: none;\n}\n\n.npc-content,\n.npc-content * {\n  background: none !important;\n  box-shadow: none !important;\n  border: none !important;\n}\n\n.npc-text-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  background: none;\n  border: none;\n}\n\n.npc-text-list li {\n  border-bottom: 1px solid #888 !important;\n  margin: 0 !important;\n  padding: 2px 0 !important;\n}\n\n.npc-action-btn {\n  background: none !important;\n  border: none !important;\n  color: inherit !important;\n  font: inherit !important;\n  box-shadow: none !important;\n  border-radius: 0 !important;\n  padding: 0 !important;\n  margin-left: 4px !important;\n  cursor: pointer;\n}\n\n.npc-action-btn:hover,\n.npc-action-btn:active {\n  background: none !important;\n  color: inherit !important;\n  box-shadow: none !important;\n  border: none !important;\n  outline: none !important;\n}\n\n.character-content {\n  height: auto;\n  padding: 0;\n  overflow: visible;\n  background: none;\n  box-shadow: none;\n}\n\n.character-card {\n  background: none;\n  border-radius: 0;\n  padding: 0;\n  box-shadow: none;\n  border: none;\n}\n\n.character-avatar-section,\n.character-basic-info,\n.character-profession,\n.character-bars,\n.character-stats,\n.character-wealth,\n.character-exp {\n  background: none !important;\n  box-shadow: none !important;\n  border: none !important;\n  border-radius: 0 !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n\n.character-avatar-img {\n  width: 60px;\n  height: 60px;\n  border-radius: 10%;\n  border: 2px solid #ffd700;\n  object-fit: cover;\n  box-shadow: none;\n}\n\n.character-name {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-profession {\n  margin: 0;\n  font-size: 14px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-bars {\n  margin-bottom: 16px;\n}\n\n.bar-item {\n  margin-bottom: 8px;\n}\n\n.bar-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n  font-size: 12px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.bar-value {\n  font-weight: bold;\n}\n\n.progress-bar {\n  height: 8px;\n  background: none;\n  border-radius: 0;\n  overflow: hidden;\n  border: 1px solid #088be2;\n}\n\n.progress-fill {\n  height: 100%;\n  transition: width 0.3s ease;\n}\n\n.hp-bar .progress-fill {\n  background: linear-gradient(90deg, #ff0000, #ff3333);\n}\n\n.mp-bar .progress-fill {\n  background: linear-gradient(90deg, #088be2, #4eadf5);\n}\n\n.character-stats {\n  margin-bottom: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.stat-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-wealth {\n  margin-bottom: 16px;\n}\n\n.wealth-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n  margin-bottom: 6px;\n}\n\n.wealth-label {\n  flex: 1;\n  font-size: 13px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.wealth-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-exp {\n  padding: 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n}\n\n.exp-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 13px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.exp-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n\n  .label {\n    font-weight: bold;\n    color: #ffd700;\n    text-shadow: none;\n  }\n\n  .value {\n    color: #ffd700;\n    text-shadow: none;\n  }\n}\n\n.facilities-content {\n  height: 100%;\n  background: none !important;\n}\n\n.facility-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.facility-item {\n  padding: 8px 0;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.move-content {\n  height: 100%;\n  background: none !important;\n}\n\n.location-list {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n}\n\n.location-item {\n  padding: 6px;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.functions-content {\n  height: 100%;\n  background: none !important;\n}\n\n.function-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.function-item {\n  padding: 12px;\n  background: none;\n  border: 1px solid #088be2;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: none;\n    border-color: #ffd700;\n    transform: translateY(-1px);\n    box-shadow: none;\n  }\n\n  .function-name {\n    display: block;\n    font-weight: bold;\n    color: #ffd700;\n    margin-bottom: 4px;\n    text-shadow: none;\n  }\n\n  .function-desc {\n    display: block;\n    font-size: 12px;\n    color: #ccc;\n  }\n}\n\n.function-bar {\n  display: flex;\n  gap: 8px;\n  height: 50px;\n  padding: 0;\n  background: none !important;\n  border-radius: 0;\n  border: none;\n  flex-shrink: 0;\n  overflow: visible;\n}\n\n.function-btn {\n  flex: 1;\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2px;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-2px);\n    background: none;\n  }\n\n  &:active {\n    background: none;\n    transform: scale(0.95);\n  }\n\n  &.active {\n    background: none;\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: -3px;\n      left: 25%;\n      right: 25%;\n      height: 3px;\n      background: #ffd700;\n      border-radius: 3px;\n    }\n  }\n}\n\n.function-btn-image {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  max-width: 100%;\n  max-height: 100%;\n}\n\n.online-players {\n  border: none;\n  background: none !important;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  box-shadow: none;\n  flex-shrink: 0;\n}\n\n.section-title {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  color: #ffd700;\n  padding: 6px 12px;\n  font-weight: bold;\n  font-size: 13px;\n  text-align: center;\n  text-shadow: none;\n  border-bottom: none;\n\n  @media (max-width: 768px) {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n}\n\n.players-avatars {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  gap: 8px;\n  overflow-x: auto;\n  background: none;\n\n  @media (max-width: 768px) {\n    padding: 6px 10px;\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 4px 8px;\n    gap: 4px;\n  }\n}\n\n.player-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 10%;\n  border: 2px solid #ffd700;\n  overflow: hidden;\n  flex-shrink: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: none;\n\n  @media (max-width: 768px) {\n    width: 35px;\n    height: 35px;\n  }\n\n  @media (max-width: 480px) {\n    width: 30px;\n    height: 30px;\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n\n  &:hover {\n    border-color: #088be2;\n    transform: scale(1.1);\n    box-shadow: none;\n  }\n}\n\n.chat-section {\n  border: none;\n  background: none !important;\n  height: 420px;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  box-shadow: none;\n  flex-shrink: 0;\n  position: relative;\n  overflow: hidden;\n  min-height: 200px;\n  max-height: 50vh;\n}\n\n/* 聊天区域现在使用GameChat组件，移除旧的样式 */\n\n@media (orientation: landscape) and (max-height: 600px) {\n  .top-section {\n    height: 200px;\n  }\n\n  .online-players {\n    height: 60px;\n  }\n\n  .chat-section {\n    height: 280px;\n    max-height: 40vh;\n    min-height: 200px;\n  }\n\n  .function-bar {\n    height: 40px;\n  }\n}\n\n@media (max-width: 320px) {\n  .main-content {\n    padding: 4px;\n    gap: 4px;\n  }\n\n  .left-panel {\n    width: 140px;\n    min-width: 120px;\n  }\n\n  .player-avatar {\n    width: 25px;\n    height: 25px;\n  }\n\n  .function-btn {\n    font-size: 10px;\n  }\n\n  .panel-header {\n    font-size: 10px;\n    padding: 3px 6px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .main-content {\n    max-width: 1000px;\n    margin: 0 auto;\n    padding: 20px;\n  }\n\n  .top-section {\n    height: 350px;\n  }\n\n  .left-panel {\n    width: 320px;\n  }\n\n  .player-avatar {\n    width: 100px;\n    height: 100px;\n  }\n\n  .function-bar {\n    height: 60px;\n  }\n\n  .function-btn {\n    font-size: 16px;\n  }\n\n  .online-players {\n    height: 100px;\n  }\n\n  .chat-section {\n    height: 360px;\n    max-height: 45vh;\n  }\n}\n\n/* 超小屏幕特殊优化 */\n@media (max-width: 480px) {\n  .chat-section {\n    height: 240px !important;\n    max-height: 35vh !important;\n    min-height: 180px !important;\n  }\n\n  .main-content {\n    gap: 4px;\n  }\n\n  .content-section {\n    padding: 4px;\n  }\n}\n\n/* 横屏模式优化 */\n@media (orientation: landscape) and (max-height: 600px) {\n  .chat-section {\n    height: 200px !important;\n    max-height: 30vh !important;\n    min-height: 150px !important;\n  }\n\n  .main-content {\n    gap: 2px;\n  }\n}\n\n/* 高分辨率屏幕优化 */\n@media (min-width: 1440px) {\n  .chat-section {\n    height: 480px;\n    max-height: 55vh;\n  }\n}\n\n@media (hover: none) and (pointer: coarse) {\n  .function-btn, .chat-tab, .npc-item, .player-avatar {\n    min-height: 44px;\n  }\n\n  .function-btn:active {\n    background: #088be2;\n    transform: scale(0.95);\n  }\n\n  .chat-tab:active {\n    background: #088be2;\n  }\n}\n\n.npc-actions {\n  display: flex;\n  justify-content: space-around;\n  margin-top: 10px;\n}\n.npc-action-btn {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  color: #ffd700;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n  text-shadow: none;\n  transition: all 0.3s ease;\n}\n.npc-action-btn:hover {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  transform: none;\n}\n.npc-action-btn:active {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  transform: none;\n}\n\n.pixel-avatar-bar2 {\n  position: relative;\n  width: 180px;\n  height: 60px;\n  margin: 0 auto 8px auto;\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n}\n.pixel-bg-img2 {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 180px;\n  height: 60px;\n  z-index: 0;\n  pointer-events: none;\n}\n.pixel-avatar-img2 {\n  position: absolute;\n  left: 6px;\n  bottom: 6px;\n  width: 38px;\n  height: 38px;\n  border-radius: 6px;\n  border: 2px solid #ffd700;\n  background: #222;\n  z-index: 2;\n  object-fit: cover;\n  box-shadow: none;\n}\n.pixel-name2 {\n  position: absolute;\n  left: 0;\n  top: 4px;\n  width: 180px;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: none;\n  z-index: 3;\n  pointer-events: none;\n}\n.pixel-bars2 {\n  position: absolute;\n  left: 54px;\n  top: 26px;\n  width: 116px;\n  z-index: 2;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.pixel-bar2 {\n  width: 100%;\n  height: 12px;\n  background: #222;\n  border: 1.5px solid #ffd700;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 2px;\n}\n.pixel-bar-inner2 {\n  height: 100%;\n  transition: width 0.3s;\n  border-radius: 4px;\n}\n.pixel-hp-inner2 {\n  background: linear-gradient(90deg, #ff3c3c, #ffb03c);\n}\n.pixel-mp-inner2 {\n  background: linear-gradient(90deg, #3c7cff, #b0e0ff);\n}\n\n* {\n  -webkit-tap-highlight-color: transparent !important;\n}\nbutton, [type=\"button\"], [type=\"submit\"], [type=\"reset\"], .function-btn, .npc-action-btn, .chat-tab, .player-avatar, .location-item, .facility-item, .function-item, .panel-header, .section-title, .stat-item, .character-card, .character-content, .panel-content, .right-panel, .left-panel {\n  outline: none !important;\n  box-shadow: none !important;\n  background: none !important;\n}\nbutton:active, button:focus, .function-btn:active, .function-btn:focus, .npc-action-btn:active, .npc-action-btn:focus, .chat-tab:active, .chat-tab:focus, .player-avatar:active, .player-avatar:focus, .location-item:active, .location-item:focus, .facility-item:active, .facility-item:focus, .function-item:active, .function-item:focus, .location-item-simple:active, .location-item-simple:focus {\n  background: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n  color: inherit !important;\n}\n\n/* 防止虚拟键盘弹出 */\n* {\n  -webkit-user-select: none !important;\n  -moz-user-select: none !important;\n  -ms-user-select: none !important;\n  user-select: none !important;\n  -webkit-touch-callout: none !important;\n  -webkit-tap-highlight-color: transparent !important;\n}\n\n/* 禁用所有元素的焦点 */\n*:focus {\n  outline: none !important;\n  -webkit-tap-highlight-color: transparent !important;\n}\n\n/* 添加六宫格布局样式 */\n.six-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  grid-template-rows: repeat(2, 1fr);\n  gap: 4px;\n  justify-items: center;\n  align-items: center;\n}\n\n.six-grid .facility-item {\n  width: 100%;\n  text-align: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: #ffd700;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  padding: 8px 0;\n  transition: background 0.2s;\n}\n\n.six-grid .facility-item:active {\n  background: none;\n}\n\n/* 添加功能宫格布局样式 */\n.functions-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  grid-template-rows: repeat(4, 1fr);\n  gap: 4px;\n  justify-items: center;\n  align-items: center;\n}\n\n.functions-grid .function-item {\n  width: 100%;\n  text-align: center;\n  font-size: 15px;\n  font-weight: bold;\n  color: #ffd700;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  padding: 8px 0;\n  transition: background 0.2s;\n}\n\n.functions-grid .function-item:active {\n  background: none;\n}\n\n.pixel-info-box {\n  background: rgba(0, 0, 50, 0.7);\n  border: 1px solid #5555ff;\n  border-radius: 4px;\n  padding: 8px;\n  margin-top: 8px;\n  font-size: 12px;\n  color: #ffffff;\n}\n\n.pixel-row {\n  margin-bottom: 4px;\n  line-height: 1.2;\n}\n\n.pixel-label-gold {\n  color: #ffcc00;\n}\n\n.pixel-value-gold {\n  color: #ffcc00;\n  font-weight: bold;\n}\n\n.pixel-exp-label {\n  margin-top: 6px;\n  color: #00ffff;\n}\n\n.pixel-exp-bar {\n  height: 6px;\n  background: rgba(0, 0, 50, 0.5);\n  border: 1px solid #5555ff;\n  border-radius: 3px;\n  margin-top: 2px;\n  margin-bottom: 6px;\n  position: relative;\n  overflow: hidden;\n}\n\n.pixel-exp-inner {\n  position: absolute;\n  height: 100%;\n  background: linear-gradient(to right, #00ffff, #00aaff);\n  left: 0;\n  top: 0;\n}\n\n/* NPC和怪物显示样式 */\n.entity-section {\n  margin-bottom: 20px;\n}\n\n.section-header {\n  font-size: 16px;\n  font-weight: bold;\n  color: #d4af37;\n  margin-bottom: 10px;\n  padding: 5px 10px;\n  background: rgba(212, 175, 55, 0.1);\n  border-left: 3px solid #d4af37;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.entity-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.entity-row {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 8px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.entity-row:hover {\n  background: rgba(0, 0, 0, 0.5);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n\n.npc-row:hover {\n  border-color: rgba(135, 206, 235, 0.5);\n}\n\n.monster-row:hover {\n  border-color: rgba(255, 107, 107, 0.5);\n}\n\n.entity-left {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  cursor: pointer;\n}\n\n.entity-right {\n  flex-shrink: 0;\n}\n\n.entity-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #fff;\n  transition: color 0.3s ease;\n}\n\n.entity-name.clickable:hover {\n  color: #87ceeb;\n}\n\n.monster-name {\n  color: #ff6b6b;\n}\n\n.monster-name.clickable:hover {\n  color: #ff8a8a;\n}\n\n.entity-level {\n  font-size: 12px;\n  color: #ffd700;\n  background: rgba(255, 215, 0, 0.2);\n  padding: 2px 6px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  display: inline-block;\n  width: fit-content;\n}\n\n.entity-services {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 2px;\n}\n\n.service-tag {\n  font-size: 10px;\n  color: #87ceeb;\n  background: rgba(135, 206, 235, 0.2);\n  padding: 1px 4px;\n  border-radius: 8px;\n  border: 1px solid rgba(135, 206, 235, 0.3);\n}\n\n.entity-type {\n  font-size: 11px;\n  color: #ccc;\n  font-style: italic;\n}\n\n.action-btn {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 60px;\n}\n\n.npc-action-btn {\n  background: linear-gradient(135deg, #87ceeb, #5f9ea0);\n  color: white;\n  border: 1px solid #87ceeb;\n}\n\n.npc-action-btn:hover {\n  background: linear-gradient(135deg, #5f9ea0, #4682b4);\n  box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);\n}\n\n.battle-btn {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: white;\n  border: 1px solid #ff6b6b;\n}\n\n.battle-btn:hover {\n  background: linear-gradient(135deg, #ff3742, #ff2f3a);\n  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n  color: #666;\n}\n\n.empty-message {\n  font-size: 14px;\n  font-style: italic;\n}\n\n/* 实体信息弹窗样式 */\n.entity-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.entity-modal {\n  background: linear-gradient(135deg, #2c3e50, #34495e);\n  border-radius: 12px;\n  border: 2px solid #d4af37;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n  max-width: 500px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 15px 20px;\n  background: rgba(212, 175, 55, 0.1);\n  border-bottom: 1px solid rgba(212, 175, 55, 0.3);\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #d4af37;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: #ff6b6b;\n}\n\n.modal-content {\n  padding: 20px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 12px;\n  align-items: flex-start;\n}\n\n.info-row.description {\n  flex-direction: column;\n  gap: 5px;\n}\n\n.info-label {\n  font-weight: bold;\n  color: #d4af37;\n  min-width: 80px;\n  margin-right: 10px;\n}\n\n.info-value {\n  color: #fff;\n  flex: 1;\n}\n\n.threat-level {\n  color: #ff6b6b;\n  font-weight: bold;\n}\n\n.stats-section {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.stats-title {\n  font-weight: bold;\n  color: #d4af37;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 4px 8px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n}\n\n.stat-label {\n  color: #ccc;\n  font-size: 12px;\n}\n\n.stat-value {\n  color: #fff;\n  font-weight: bold;\n  font-size: 12px;\n}\n\n/* 移动功能样式 */\n.move-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n\n\n.location-description {\n  font-size: 11px;\n  color: #cccccc;\n  line-height: 1.4;\n}\n\n.movement-options {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.section-title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #88ccff;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n}\n\n.location-list {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.location-item {\n  padding: 12px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.location-item:hover:not(.disabled) {\n  background: none;\n  transform: translateY(-1px);\n}\n\n.location-item.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 简化的地图列表样式 */\n.location-item-simple {\n  padding: 8px 12px;\n  background: none;\n  border: none;\n  border-bottom: 1px solid #444;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  color: #ffd700;\n  font-size: 14px;\n  text-align: left;\n  display: block;\n  width: 100%;\n}\n\n.location-item-simple:hover:not(.disabled) {\n  background: none;\n  color: #ffffff;\n}\n\n.location-item-simple.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.location-item-simple:last-child {\n  border-bottom: none;\n}\n\n\n\n\n\n\n\n.no-locations, .loading-locations {\n  text-align: center;\n  padding: 20px;\n  color: #888888;\n  font-size: 12px;\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n/* 四行布局样式 */\n.four-row-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 0;\n}\n\n.entity-row-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: 2px 8px;\n  min-height: 20px;\n}\n\n.entity-list-horizontal {\n  display: flex;\n  gap: 15px;\n  width: 100%;\n  align-items: center;\n}\n\n.entity-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex: 1;\n}\n\n.entity-item-compact .entity-name {\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  flex: 1;\n  line-height: 1.2;\n}\n\n.entity-item-compact .entity-name:hover {\n  color: #87ceeb;\n}\n\n.entity-item-compact .monster-name {\n  color: #ff6b6b;\n}\n\n.entity-item-compact .monster-name:hover {\n  color: #ff8a8a;\n}\n\n.action-btn-compact {\n  padding: 4px 8px;\n  border: none;\n  border-radius: 3px;\n  font-size: 11px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 40px;\n}\n\n.action-btn-compact.npc-action-btn {\n  background: linear-gradient(135deg, #87ceeb, #5f9ea0);\n  color: white;\n  border: 1px solid #87ceeb;\n}\n\n.action-btn-compact.npc-action-btn:hover {\n  background: linear-gradient(135deg, #5f9ea0, #4682b4);\n  box-shadow: 0 0 8px rgba(135, 206, 235, 0.4);\n}\n\n.action-btn-compact.battle-btn {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: white;\n  border: 1px solid #ff6b6b;\n}\n\n.action-btn-compact.battle-btn:hover {\n  background: linear-gradient(135deg, #ff3742, #ff2f3a);\n  box-shadow: 0 0 8px rgba(255, 71, 87, 0.4);\n}\n\n.empty-row {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.empty-text {\n  color: #666;\n  font-size: 11px;\n  font-style: italic;\n  line-height: 1.1;\n}\n\n.divider-line {\n  height: 1px;\n  background: #666;\n  margin: 0 8px;\n  border: none;\n}\n\n/* 单行实体显示样式 */\n.entity-row-single {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  gap: 12px;\n}\n\n.entity-info {\n  flex: 1;\n  min-width: 0;\n  cursor: pointer;\n}\n\n.entity-info .entity-name {\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 150px;\n  display: block;\n  line-height: 1.2;\n}\n\n.entity-info .entity-name:hover {\n  color: #87ceeb;\n}\n\n.entity-info .monster-name {\n  color: #ff6b6b;\n}\n\n.entity-info .monster-name:hover {\n  color: #ff8a8a;\n}\n\n/* 位置行显示样式 */\n.location-row-container {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  min-height: 40px;\n}\n\n.location-row-single {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  gap: 12px;\n}\n\n.location-info {\n  flex: 1;\n  min-width: 0;\n  cursor: pointer;\n  text-align: center;\n}\n\n.location-info.disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n\n.location-info .location-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #ffd700;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.location-info .location-name:hover {\n  color: #87ceeb;\n}\n\n.location-info.disabled .location-name {\n  color: #666;\n}\n\n.location-info.disabled .location-name:hover {\n  color: #666;\n}\n\n</style>\n\n"]}]}