<?php

namespace App\Http\Controllers;

use App\Models\Battle;
use App\Models\Monster;
use App\Services\BattleManager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class BattleController extends Controller
{
    private BattleManager $battleManager;

    public function __construct(BattleManager $battleManager)
    {
        $this->battleManager = $battleManager;
    }

    /**
     * 开始战斗
     */
    public function start(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'monster_id' => 'required|integer|exists:monsters,id',
                'character_id' => 'required|integer|exists:characters,id',
                'location_id' => 'nullable|string|max:50'
            ]);

            $battle = $this->battleManager->initBattle(
                $request->character_id,
                $request->monster_id,
                $request->location_id
            );

            return response()->json([
                'success' => true,
                'message' => '战斗开始！',
                'data' => [
                    'battle_id' => $battle->id,
                    'battle' => $this->formatBattleData($battle)
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('开始战斗失败', [
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 执行战斗动作
     */
    public function action(Request $request, int $battleId): JsonResponse
    {
        try {
            $request->validate([
                'action' => 'required|string|in:attack,flee',
                'character_id' => 'required|integer|exists:characters,id'
            ]);

            $battle = Battle::with(['character.characterStatus', 'monster'])->findOrFail($battleId);

            // 验证角色权限
            if ($battle->character_id !== $request->character_id) {
                return response()->json([
                    'success' => false,
                    'message' => '无权操作此战斗'
                ], 403);
            }

            if (!$battle->isOngoing()) {
                return response()->json([
                    'success' => false,
                    'message' => '战斗已结束'
                ], 400);
            }

            $result = [];

            switch ($request->action) {
                case 'attack':
                    // 角色攻击
                    $characterAttackResult = $this->battleManager->executeAttack($battleId, 'character');
                    $result['character_action'] = $characterAttackResult;

                    // 如果战斗未结束，怪物反击
                    if (!isset($characterAttackResult['battle_end'])) {
                        $monsterAttackResult = $this->battleManager->executeAttack($battleId, 'monster');
                        $result['monster_action'] = $monsterAttackResult;
                    }
                    break;

                case 'flee':
                    $fleeResult = $this->battleManager->flee($battleId);
                    $result['flee_result'] = $fleeResult;
                    break;
            }

            // 重新加载战斗数据
            $battle->refresh();

            return response()->json([
                'success' => true,
                'message' => '动作执行成功',
                'data' => [
                    'battle' => $this->formatBattleData($battle),
                    'action_results' => $result
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('执行战斗动作失败', [
                'battle_id' => $battleId,
                'request' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取战斗状态
     */
    public function status(int $battleId): JsonResponse
    {
        try {
            $battle = Battle::with(['character.characterStatus', 'monster'])->findOrFail($battleId);

            return response()->json([
                'success' => true,
                'data' => [
                    'battle' => $this->formatBattleData($battle)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取战斗状态失败', [
                'battle_id' => $battleId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取战斗状态失败'
            ], 500);
        }
    }

    /**
     * 获取战斗日志
     */
    public function log(int $battleId): JsonResponse
    {
        try {
            $battle = Battle::findOrFail($battleId);

            return response()->json([
                'success' => true,
                'data' => [
                    'battle_log' => $battle->battle_log ?? [],
                    'rounds' => $battle->rounds,
                    'status' => $battle->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取战斗日志失败', [
                'battle_id' => $battleId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取战斗日志失败'
            ], 500);
        }
    }

    /**
     * 格式化战斗数据
     */
    private function formatBattleData(Battle $battle): array
    {
        $character = $battle->character;
        $monster = $battle->monster;
        $characterStatus = $character->characterStatus;

        // 计算怪物当前HP（从战斗日志）
        $monsterCurrentHp = $monster->max_health;
        $logs = $battle->battle_log ?? [];
        foreach ($logs as $log) {
            if ($log['action'] === 'character_attack') {
                $monsterCurrentHp -= $log['data']['damage'] ?? 0;
            }
        }
        $monsterCurrentHp = max(0, $monsterCurrentHp);

        return [
            'id' => $battle->id,
            'status' => $battle->status,
            'rounds' => $battle->rounds,
            'character' => [
                'id' => $character->id,
                'name' => $character->name,
                'level' => $character->level,
                'avatar' => $character->avatar,
                'hp' => $characterStatus->hp,
                'max_hp' => $characterStatus->max_hp,
                'mp' => $characterStatus->mp,
                'max_mp' => $characterStatus->max_mp,
                'attack' => $characterStatus->attack,
                'defense' => $characterStatus->defense,
                'speed' => $characterStatus->speed
            ],
            'monster' => [
                'id' => $monster->id,
                'name' => $monster->name,
                'level' => $monster->level,
                'avatar' => $monster->avatar,
                'hp' => $monsterCurrentHp,
                'max_hp' => $monster->max_health,
                'mp' => $monster->max_mp,
                'max_mp' => $monster->max_mp,
                'attack' => $monster->attack,
                'defense' => $monster->defense,
                'speed' => $monster->speed,
                'type' => $monster->type,
                'element' => $monster->element
            ],
            'rewards' => [
                'exp_gained' => $battle->exp_gained,
                'gold_gained' => $battle->gold_gained,
                'items_gained' => $battle->items_gained
            ],
            'duration' => $battle->duration,
            'can_act' => $battle->isOngoing()
        ];
    }
}
