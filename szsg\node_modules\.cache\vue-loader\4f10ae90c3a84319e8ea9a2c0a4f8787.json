{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=template&id=f28434cc&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750348445915}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "minimized", "compact", "compactMode", "on", "click", "toggleMinimize", "_v", "connected", "isConnected", "attrs", "title", "$event", "stopPropagation", "clearMessages", "apply", "arguments", "_e", "toggleCompactMode", "_s", "_l", "channels", "channel", "index", "key", "active", "currentChannelIndex", "switchChannel", "name", "unread", "ref", "filteredMessages", "length", "msg", "type", "isSelf", "handleSenderClick", "sender", "timestamp", "formatTime", "content", "directives", "rawName", "value", "newMessage", "expression", "placeholder", "getInputPlaceholder", "disabled", "maxlength", "domProps", "keyup", "indexOf", "_k", "keyCode", "sendMessage", "focus", "onInputFocus", "blur", "onInputBlur", "input", "target", "composing", "trim", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/components/GameChat.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"chat-container\",\n      class: { minimized: _vm.minimized, compact: _vm.compactMode },\n    },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"chat-header\", on: { click: _vm.toggleMinimize } },\n        [\n          _c(\"div\", { staticClass: \"chat-title-section\" }, [\n            _c(\"span\", { staticClass: \"chat-title\" }, [_vm._v(\"聊天\")]),\n            _c(\"div\", {\n              staticClass: \"chat-status\",\n              class: { connected: _vm.isConnected },\n              attrs: { title: _vm.isConnected ? \"已连接\" : \"未连接\" },\n            }),\n          ]),\n          _c(\"div\", { staticClass: \"chat-controls\" }, [\n            !_vm.minimized\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"control-btn\",\n                    attrs: { title: \"清空消息\" },\n                    on: {\n                      click: function ($event) {\n                        $event.stopPropagation()\n                        return _vm.clearMessages.apply(null, arguments)\n                      },\n                    },\n                  },\n                  [_c(\"span\", { staticClass: \"btn-text\" }, [_vm._v(\"清空\")])]\n                )\n              : _vm._e(),\n            !_vm.minimized\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"control-btn\",\n                    attrs: { title: \"紧凑模式\" },\n                    on: {\n                      click: function ($event) {\n                        $event.stopPropagation()\n                        return _vm.toggleCompactMode.apply(null, arguments)\n                      },\n                    },\n                  },\n                  [\n                    _c(\"span\", { staticClass: \"btn-text\" }, [\n                      _vm._v(_vm._s(_vm.compactMode ? \"展开\" : \"紧凑\")),\n                    ]),\n                  ]\n                )\n              : _vm._e(),\n            _c(\n              \"button\",\n              {\n                staticClass: \"control-btn minimize-btn\",\n                attrs: { title: _vm.minimized ? \"展开聊天\" : \"收起聊天\" },\n              },\n              [\n                _c(\"span\", { staticClass: \"btn-text\" }, [\n                  _vm._v(_vm._s(_vm.minimized ? \"展开\" : \"收起\")),\n                ]),\n              ]\n            ),\n          ]),\n        ]\n      ),\n      !_vm.minimized\n        ? _c(\"div\", { staticClass: \"chat-body\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"chat-channels\" },\n              _vm._l(_vm.channels, function (channel, index) {\n                return _c(\n                  \"button\",\n                  {\n                    key: index,\n                    staticClass: \"channel-tab\",\n                    class: { active: _vm.currentChannelIndex === index },\n                    on: {\n                      click: function ($event) {\n                        return _vm.switchChannel(index)\n                      },\n                    },\n                  },\n                  [\n                    _c(\"span\", { staticClass: \"channel-name\" }, [\n                      _vm._v(_vm._s(channel.name)),\n                    ]),\n                    channel.unread > 0\n                      ? _c(\"span\", { staticClass: \"channel-badge\" }, [\n                          _vm._v(\n                            _vm._s(channel.unread > 99 ? \"99+\" : channel.unread)\n                          ),\n                        ])\n                      : _vm._e(),\n                  ]\n                )\n              }),\n              0\n            ),\n            _c(\n              \"div\",\n              { ref: \"chatMessagesContainer\", staticClass: \"chat-messages\" },\n              [\n                _vm.filteredMessages.length > 0\n                  ? _vm._l(_vm.filteredMessages, function (msg, index) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: index,\n                          staticClass: \"chat-message\",\n                          class: {\n                            \"system-message\": msg.type === \"system\",\n                            \"npc-message\": msg.type === \"npc\",\n                            \"player-message\": msg.type === \"player\",\n                            \"self-message\": msg.isSelf,\n                            compact: _vm.compactMode,\n                          },\n                        },\n                        [\n                          msg.type !== \"system\"\n                            ? _c(\"div\", { staticClass: \"message-header\" }, [\n                                _c(\n                                  \"span\",\n                                  {\n                                    staticClass: \"message-sender\",\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.handleSenderClick(msg)\n                                      },\n                                    },\n                                  },\n                                  [_vm._v(_vm._s(msg.sender))]\n                                ),\n                                msg.timestamp && !_vm.compactMode\n                                  ? _c(\n                                      \"span\",\n                                      { staticClass: \"message-time\" },\n                                      [\n                                        _vm._v(\n                                          _vm._s(_vm.formatTime(msg.timestamp))\n                                        ),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                              ])\n                            : _vm._e(),\n                          _c(\"div\", { staticClass: \"message-content\" }, [\n                            _vm._v(_vm._s(msg.content)),\n                          ]),\n                          msg.timestamp && _vm.compactMode\n                            ? _c(\n                                \"div\",\n                                { staticClass: \"message-time-compact\" },\n                                [_vm._v(_vm._s(_vm.formatTime(msg.timestamp)))]\n                              )\n                            : _vm._e(),\n                        ]\n                      )\n                    })\n                  : _c(\"div\", { staticClass: \"empty-messages\" }, [\n                      _c(\"div\", { staticClass: \"empty-text\" }, [\n                        _vm._v(\"暂无消息\"),\n                      ]),\n                      _c(\"div\", { staticClass: \"empty-hint\" }, [\n                        _vm._v(\"在下方输入框发送消息开始聊天\"),\n                      ]),\n                    ]),\n              ],\n              2\n            ),\n            _c(\"div\", { staticClass: \"chat-input-container\" }, [\n              _c(\"div\", { staticClass: \"input-wrapper\" }, [\n                _c(\"input\", {\n                  directives: [\n                    {\n                      name: \"model\",\n                      rawName: \"v-model\",\n                      value: _vm.newMessage,\n                      expression: \"newMessage\",\n                    },\n                  ],\n                  staticClass: \"chat-input\",\n                  attrs: {\n                    placeholder: _vm.getInputPlaceholder(),\n                    disabled: !_vm.isConnected,\n                    maxlength: \"200\",\n                  },\n                  domProps: { value: _vm.newMessage },\n                  on: {\n                    keyup: function ($event) {\n                      if (\n                        !$event.type.indexOf(\"key\") &&\n                        _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                      )\n                        return null\n                      return _vm.sendMessage.apply(null, arguments)\n                    },\n                    focus: _vm.onInputFocus,\n                    blur: _vm.onInputBlur,\n                    input: function ($event) {\n                      if ($event.target.composing) return\n                      _vm.newMessage = $event.target.value\n                    },\n                  },\n                }),\n                _vm.newMessage.length > 150\n                  ? _c(\"div\", { staticClass: \"input-counter\" }, [\n                      _vm._v(_vm._s(_vm.newMessage.length) + \"/200\"),\n                    ])\n                  : _vm._e(),\n              ]),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"send-btn\",\n                  attrs: {\n                    disabled: !_vm.isConnected || !_vm.newMessage.trim(),\n                    title: !_vm.isConnected ? \"未连接到聊天服务器\" : \"发送消息\",\n                  },\n                  on: { click: _vm.sendMessage },\n                },\n                [_c(\"span\", { staticClass: \"send-text\" }, [_vm._v(\"发送\")])]\n              ),\n            ]),\n          ])\n        : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MAAEC,SAAS,EAAEL,GAAG,CAACK,SAAS;MAAEC,OAAO,EAAEN,GAAG,CAACO;IAAY;EAC9D,CAAC,EACD,CACEN,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,aAAa;IAAEK,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACU;IAAe;EAAE,CAAC,EACjE,CACET,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACzDV,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEQ,SAAS,EAAEZ,GAAG,CAACa;IAAY,CAAC;IACrCC,KAAK,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACa,WAAW,GAAG,KAAK,GAAG;IAAM;EAClD,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1C,CAACH,GAAG,CAACK,SAAS,GACVJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,aAAa;IAC1BW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBP,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;QACvBA,MAAM,CAACC,eAAe,CAAC,CAAC;QACxB,OAAOjB,GAAG,CAACkB,aAAa,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACjD;IACF;EACF,CAAC,EACD,CAACnB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC1D,CAAC,GACDX,GAAG,CAACqB,EAAE,CAAC,CAAC,EACZ,CAACrB,GAAG,CAACK,SAAS,GACVJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,aAAa;IAC1BW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBP,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;QACvBA,MAAM,CAACC,eAAe,CAAC,CAAC;QACxB,OAAOjB,GAAG,CAACsB,iBAAiB,CAACH,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACrD;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACtCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACO,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAC9C,CAAC,CAEN,CAAC,GACDP,GAAG,CAACqB,EAAE,CAAC,CAAC,EACZpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,0BAA0B;IACvCW,KAAK,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACK,SAAS,GAAG,MAAM,GAAG;IAAO;EAClD,CAAC,EACD,CACEJ,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACtCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACK,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAC5C,CAAC,CAEN,CAAC,CACF,CAAC,CAEN,CAAC,EACD,CAACL,GAAG,CAACK,SAAS,GACVJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChCH,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACyB,QAAQ,EAAE,UAAUC,OAAO,EAAEC,KAAK,EAAE;IAC7C,OAAO1B,EAAE,CACP,QAAQ,EACR;MACE2B,GAAG,EAAED,KAAK;MACVxB,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE;QAAEyB,MAAM,EAAE7B,GAAG,CAAC8B,mBAAmB,KAAKH;MAAM,CAAC;MACpDnB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;UACvB,OAAOhB,GAAG,CAAC+B,aAAa,CAACJ,KAAK,CAAC;QACjC;MACF;IACF,CAAC,EACD,CACE1B,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACG,OAAO,CAACM,IAAI,CAAC,CAAC,CAC7B,CAAC,EACFN,OAAO,CAACO,MAAM,GAAG,CAAC,GACdhC,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CACJX,GAAG,CAACuB,EAAE,CAACG,OAAO,CAACO,MAAM,GAAG,EAAE,GAAG,KAAK,GAAGP,OAAO,CAACO,MAAM,CACrD,CAAC,CACF,CAAC,GACFjC,GAAG,CAACqB,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEiC,GAAG,EAAE,uBAAuB;IAAE/B,WAAW,EAAE;EAAgB,CAAC,EAC9D,CACEH,GAAG,CAACmC,gBAAgB,CAACC,MAAM,GAAG,CAAC,GAC3BpC,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACmC,gBAAgB,EAAE,UAAUE,GAAG,EAAEV,KAAK,EAAE;IACjD,OAAO1B,EAAE,CACP,KAAK,EACL;MACE2B,GAAG,EAAED,KAAK;MACVxB,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QACL,gBAAgB,EAAEiC,GAAG,CAACC,IAAI,KAAK,QAAQ;QACvC,aAAa,EAAED,GAAG,CAACC,IAAI,KAAK,KAAK;QACjC,gBAAgB,EAAED,GAAG,CAACC,IAAI,KAAK,QAAQ;QACvC,cAAc,EAAED,GAAG,CAACE,MAAM;QAC1BjC,OAAO,EAAEN,GAAG,CAACO;MACf;IACF,CAAC,EACD,CACE8B,GAAG,CAACC,IAAI,KAAK,QAAQ,GACjBrC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,MAAM,EACN;MACEE,WAAW,EAAE,gBAAgB;MAC7BK,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;UACvB,OAAOhB,GAAG,CAACwC,iBAAiB,CAACH,GAAG,CAAC;QACnC;MACF;IACF,CAAC,EACD,CAACrC,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACc,GAAG,CAACI,MAAM,CAAC,CAAC,CAC7B,CAAC,EACDJ,GAAG,CAACK,SAAS,IAAI,CAAC1C,GAAG,CAACO,WAAW,GAC7BN,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEH,GAAG,CAACW,EAAE,CACJX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAAC2C,UAAU,CAACN,GAAG,CAACK,SAAS,CAAC,CACtC,CAAC,CAEL,CAAC,GACD1C,GAAG,CAACqB,EAAE,CAAC,CAAC,CACb,CAAC,GACFrB,GAAG,CAACqB,EAAE,CAAC,CAAC,EACZpB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACc,GAAG,CAACO,OAAO,CAAC,CAAC,CAC5B,CAAC,EACFP,GAAG,CAACK,SAAS,IAAI1C,GAAG,CAACO,WAAW,GAC5BN,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CAACH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAAC2C,UAAU,CAACN,GAAG,CAACK,SAAS,CAAC,CAAC,CAAC,CAChD,CAAC,GACD1C,GAAG,CAACqB,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CAAC,CAAC,GACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACW,EAAE,CAAC,gBAAgB,CAAC,CACzB,CAAC,CACH,CAAC,CACP,EACD,CACF,CAAC,EACDV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,OAAO,EAAE;IACV4C,UAAU,EAAE,CACV;MACEb,IAAI,EAAE,OAAO;MACbc,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE/C,GAAG,CAACgD,UAAU;MACrBC,UAAU,EAAE;IACd,CAAC,CACF;IACD9C,WAAW,EAAE,YAAY;IACzBW,KAAK,EAAE;MACLoC,WAAW,EAAElD,GAAG,CAACmD,mBAAmB,CAAC,CAAC;MACtCC,QAAQ,EAAE,CAACpD,GAAG,CAACa,WAAW;MAC1BwC,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE;MAAEP,KAAK,EAAE/C,GAAG,CAACgD;IAAW,CAAC;IACnCxC,EAAE,EAAE;MACF+C,KAAK,EAAE,SAAAA,CAAUvC,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACsB,IAAI,CAACkB,OAAO,CAAC,KAAK,CAAC,IAC3BxD,GAAG,CAACyD,EAAE,CAACzC,MAAM,CAAC0C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE1C,MAAM,CAACY,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,OAAO5B,GAAG,CAAC2D,WAAW,CAACxC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C,CAAC;MACDwC,KAAK,EAAE5D,GAAG,CAAC6D,YAAY;MACvBC,IAAI,EAAE9D,GAAG,CAAC+D,WAAW;MACrBC,KAAK,EAAE,SAAAA,CAAUhD,MAAM,EAAE;QACvB,IAAIA,MAAM,CAACiD,MAAM,CAACC,SAAS,EAAE;QAC7BlE,GAAG,CAACgD,UAAU,GAAGhC,MAAM,CAACiD,MAAM,CAAClB,KAAK;MACtC;IACF;EACF,CAAC,CAAC,EACF/C,GAAG,CAACgD,UAAU,CAACZ,MAAM,GAAG,GAAG,GACvBnC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACgD,UAAU,CAACZ,MAAM,CAAC,GAAG,MAAM,CAAC,CAC/C,CAAC,GACFpC,GAAG,CAACqB,EAAE,CAAC,CAAC,CACb,CAAC,EACFpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,UAAU;IACvBW,KAAK,EAAE;MACLsC,QAAQ,EAAE,CAACpD,GAAG,CAACa,WAAW,IAAI,CAACb,GAAG,CAACgD,UAAU,CAACmB,IAAI,CAAC,CAAC;MACpDpD,KAAK,EAAE,CAACf,GAAG,CAACa,WAAW,GAAG,WAAW,GAAG;IAC1C,CAAC;IACDL,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAAC2D;IAAY;EAC/B,CAAC,EACD,CAAC1D,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3D,CAAC,CACF,CAAC,CACH,CAAC,GACFX,GAAG,CAACqB,EAAE,CAAC,CAAC,CAEhB,CAAC;AACH,CAAC;AACD,IAAI+C,eAAe,GAAG,EAAE;AACxBrE,MAAM,CAACsE,aAAa,GAAG,IAAI;AAE3B,SAAStE,MAAM,EAAEqE,eAAe", "ignoreList": []}]}