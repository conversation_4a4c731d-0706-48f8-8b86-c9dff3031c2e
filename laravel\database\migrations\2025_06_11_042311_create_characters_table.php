<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('characters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('region_id')->default(1);
            $table->string('name');
            $table->enum('gender', ['male', 'female']);
            $table->enum('profession', ['warrior', 'scholar', 'mystic']);
            $table->integer('level')->default(1);
            $table->integer('experience')->default(0);
            $table->integer('attribute_points')->default(4);
            $table->json('attributes')->nullable();
            $table->json('equipment')->nullable();
            $table->json('skills')->nullable();
            $table->json('stats')->nullable();
            $table->integer('gold')->default(0);
            $table->integer('silver')->default(0);
            $table->integer('energy')->default(100);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('characters');
    }
};
