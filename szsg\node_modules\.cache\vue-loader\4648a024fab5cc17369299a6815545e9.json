{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Status.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Status.vue", "mtime": 1749719688754}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgR2FtZUxheW91dCBmcm9tICdAL2xheW91dHMvR2FtZUxheW91dC52dWUnOw0KZXhwb3J0IGRlZmF1bHQgeyBuYW1lOiAnU3RhdHVzJywgY29tcG9uZW50czogeyBHYW1lTGF5b3V0IH0gfTsNCg=="}, {"version": 3, "sources": ["Status.vue"], "names": [], "mappings": ";AAMA;AACA", "file": "Status.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"function-page\">状态页面</div>\r\n  </GameLayout>\r\n</template>\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nexport default { name: 'Status', components: { GameLayout } };\r\n</script>\r\n<style scoped>.function-page { font-size: 22px; text-align: center; margin-top: 40px; }</style> "]}]}