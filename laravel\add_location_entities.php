<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Location;

echo "=== 添加地图NPC和怪物数据 ===\n";

// 西游记主题的地图实体数据
$locationEntities = [
    '东胜神洲' => [
        'npcs' => [
            [
                'id' => 'city_god',
                'name' => '城隍爷',
                'type' => 'npc',
                'level' => 50,
                'services' => ['信息', '任务'],
                'description' => '东胜神洲的城隍爷，掌管此地阴阳两界'
            ],
            [
                'id' => 'immortal_merchant',
                'name' => '仙界商人',
                'type' => 'npc',
                'level' => 30,
                'services' => ['交易', '法宝'],
                'description' => '来自天庭的商人，出售各种仙家法宝'
            ]
        ],
        'monsters' => [],
        'facilities' => ['钱庄', '市场', '客栈', '医馆']
    ],
    
    '花果山' => [
        'npcs' => [
            [
                'id' => 'monkey_elder',
                'name' => '猴族长老',
                'type' => 'npc',
                'level' => 40,
                'services' => ['指引', '武艺'],
                'description' => '花果山的猴族长老，精通各种武艺'
            ]
        ],
        'monsters' => [
            [
                'id' => 'spirit_monkey',
                'name' => '灵猴',
                'type' => 'monster',
                'level' => 15,
                'description' => '花果山的灵猴，机灵活泼，略通人性'
            ],
            [
                'id' => 'mountain_demon',
                'name' => '山魈',
                'type' => 'monster',
                'level' => 20,
                'description' => '山中精怪，力大无穷'
            ]
        ],
        'facilities' => ['神庙']
    ],
    
    '水帘洞' => [
        'npcs' => [
            [
                'id' => 'cave_guardian',
                'name' => '洞府守护',
                'type' => 'npc',
                'level' => 35,
                'services' => ['修炼', '秘籍'],
                'description' => '水帘洞的守护者，掌管洞中秘籍'
            ]
        ],
        'monsters' => [
            [
                'id' => 'water_spirit',
                'name' => '水灵',
                'type' => 'monster',
                'level' => 12,
                'description' => '水帘洞中的水系精灵'
            ]
        ],
        'facilities' => []
    ],
    
    '西牛贺洲' => [
        'npcs' => [
            [
                'id' => 'buddhist_monk',
                'name' => '得道高僧',
                'type' => 'npc',
                'level' => 60,
                'services' => ['佛法', '指引'],
                'description' => '西牛贺洲的得道高僧，佛法精深'
            ]
        ],
        'monsters' => [],
        'facilities' => ['神庙', '市场']
    ],
    
    '灵山' => [
        'npcs' => [
            [
                'id' => 'arhat',
                'name' => '罗汉',
                'type' => 'npc',
                'level' => 80,
                'services' => ['佛法', '真经'],
                'description' => '灵山的罗汉，可传授佛法真经'
            ]
        ],
        'monsters' => [
            [
                'id' => 'guardian_vajra',
                'name' => '护法金刚',
                'type' => 'monster',
                'level' => 50,
                'description' => '守护灵山的金刚力士'
            ]
        ],
        'facilities' => ['神庙']
    ],
    
    '南瞻部洲' => [
        'npcs' => [
            [
                'id' => 'mortal_official',
                'name' => '凡间官员',
                'type' => 'npc',
                'level' => 20,
                'services' => ['信息', '任务'],
                'description' => '南瞻部洲的凡间官员'
            ]
        ],
        'monsters' => [
            [
                'id' => 'bandit',
                'name' => '山贼',
                'type' => 'monster',
                'level' => 8,
                'description' => '人间的盗匪，武艺平平'
            ]
        ],
        'facilities' => ['市场', '客栈']
    ],
    
    '北俱芦洲' => [
        'npcs' => [
            [
                'id' => 'barbarian_chief',
                'name' => '蛮族首领',
                'type' => 'npc',
                'level' => 45,
                'services' => ['战斗', '狩猎'],
                'description' => '北俱芦洲的蛮族首领，勇猛善战'
            ]
        ],
        'monsters' => [
            [
                'id' => 'ice_wolf',
                'name' => '冰狼',
                'type' => 'monster',
                'level' => 25,
                'description' => '北方的冰狼，行动迅速'
            ]
        ],
        'facilities' => []
    ],
    
    '东海龙宫' => [
        'npcs' => [
            [
                'id' => 'dragon_king',
                'name' => '东海龙王',
                'type' => 'npc',
                'level' => 70,
                'services' => ['法宝', '神兵'],
                'description' => '东海龙王，掌管东海，拥有无数神兵利器'
            ],
            [
                'id' => 'shrimp_soldier',
                'name' => '虾兵',
                'type' => 'npc',
                'level' => 25,
                'services' => ['守卫', '信息'],
                'description' => '龙宫的虾兵，负责守卫龙宫'
            ]
        ],
        'monsters' => [
            [
                'id' => 'crab_general',
                'name' => '蟹将',
                'type' => 'monster',
                'level' => 30,
                'description' => '龙宫的蟹将，身披重甲'
            ]
        ],
        'facilities' => ['宝库']
    ]
];

// 更新每个位置的数据
foreach ($locationEntities as $locationName => $entities) {
    $location = Location::where('name', $locationName)->first();
    
    if ($location) {
        $location->npcs = $entities['npcs'];
        $location->monsters = $entities['monsters'];
        $location->facilities = $entities['facilities'];
        $location->save();
        
        echo "✅ 更新 {$locationName}:\n";
        echo "   - NPC: " . count($entities['npcs']) . " 个\n";
        echo "   - 怪物: " . count($entities['monsters']) . " 个\n";
        echo "   - 设施: " . count($entities['facilities']) . " 个\n";
    } else {
        echo "❌ 未找到位置: {$locationName}\n";
    }
}

echo "\n=== 数据更新完成 ===\n";
echo "现在每个地图都有了对应的NPC和怪物！\n";
