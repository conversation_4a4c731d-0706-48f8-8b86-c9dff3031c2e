<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('regions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['city', 'wilderness', 'dungeon', 'instance', 'special'])->default('city');
            $table->integer('level_range_min')->default(1);
            $table->integer('level_range_max')->default(100);
            $table->integer('danger_level')->default(1);
            $table->boolean('is_pvp')->default(false);
            $table->boolean('weather_enabled')->default(true);
            $table->string('background_image')->nullable();
            $table->string('map_image')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('regions');
    }
};
