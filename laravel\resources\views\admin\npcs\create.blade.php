@extends('admin.layouts.app')

@section('title', '添加NPC')

@section('content')
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加NPC</div>
        <div class="layui-card-body">
            <form class="layui-form" action="/admin/npcs" method="POST" lay-filter="npc-form">
                @csrf
                <input type="hidden" name="region_id" value="{{ request('region_id') }}">

                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" lay-verify="required" placeholder="请输入NPC名称" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="请输入描述" class="layui-textarea"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">NPC类型</label>
                    <div class="layui-input-block">
                        <select name="npc_type" lay-verify="required">
                            <option value="quest">任务NPC</option>
                            <option value="merchant">商人</option>
                            <option value="trainer">训练师</option>
                            <option value="banker">银行家</option>
                            <option value="innkeeper">旅店老板</option>
                            <option value="guard">守卫</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">位置</label>
                    <div class="layui-input-block">
                        <select name="location_id" lay-verify="required">
                            <option value="">请选择位置</option>
                            <!-- 这里将通过AJAX加载位置数据 -->
                        </select>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">等级</label>
                    <div class="layui-input-inline">
                        <input type="number" name="level" lay-verify="required|number" value="1" min="1" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item" pane>
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="status" value="active" lay-skin="switch" lay-text="活跃|非活跃" checked>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="saveBtn">保存</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;
    var $ = layui.jquery;

    // 加载位置数据
    var regionId = $('input[name="region_id"]').val();
    if(regionId) {
        $.ajax({
            url: '/admin/api/locations',
            type: 'GET',
            data: {region_id: regionId},
            dataType: 'json',
            success: function(res){
                if(res.success){
                    var html = '<option value="">请选择位置</option>';
                    $.each(res.data, function(i, item){
                        html += '<option value="' + item.id + '">' + item.name + '</option>';
                    });
                    $('select[name="location_id"]').html(html);
                    form.render('select');
                }else{
                    layer.msg('获取位置数据失败：' + res.message);
                }
            },
            error: function(){
                layer.msg('获取位置数据失败，请稍后再试');
            }
        });
    }

    // 表单提交
    form.on('submit(saveBtn)', function(data){
        var index = parent.layer.getFrameIndex(window.name);

        $.ajax({
            url: data.form.action,
            type: 'POST',
            data: data.field,
            dataType: 'json',
            success: function(res){
                if(res.success){
                    layer.msg('保存成功', {icon: 1});
                    setTimeout(function(){
                        parent.layer.close(index);
                        parent.location.reload();
                    }, 1000);
                }else{
                    layer.msg('保存失败：' + res.message, {icon: 2});
                }
            },
            error: function(xhr){
                var errors = xhr.responseJSON;
                if(errors && errors.errors){
                    var errorMsg = '';
                    for(var key in errors.errors){
                        errorMsg += errors.errors[key][0] + '<br>';
                    }
                    layer.msg(errorMsg, {icon: 2});
                }else{
                    layer.msg('保存失败，请稍后再试', {icon: 2});
                }
            }
        });

        return false;
    });
});
</script>
@endsection
