@extends('admin.layouts.app')

@section('title', '技能管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        技能列表
        <a href="{{ route('admin.skills.create') }}" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加技能</a>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>类型</th>
                    <th>等级要求</th>
                    <th>MP消耗</th>
                    <th>冷却时间</th>
                    <th>伤害倍率</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($skills as $skill)
                <tr>
                    <td>{{ $skill->id }}</td>
                    <td>{{ $skill->name }}</td>
                    <td>
                        @if($skill->type == 'active')
                        <span class="layui-badge layui-bg-blue">主动</span>
                        @else
                        <span class="layui-badge layui-bg-green">被动</span>
                        @endif
                    </td>
                    <td>{{ $skill->level_requirement ?? 1 }}</td>
                    <td>{{ $skill->mp_cost ?? 0 }}</td>
                    <td>{{ $skill->cooldown ?? 0 }}秒</td>
                    <td>{{ $skill->damage_multiplier ?? 1.0 }}x</td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.skills.show', $skill->id) }}" class="layui-btn layui-btn-xs layui-btn-primary">查看</a>
                            <a href="{{ route('admin.skills.edit', $skill->id) }}" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteSkill({{ $skill->id }}, '{{ $skill->name }}')">删除</button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="layui-center">暂无技能数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $skills->links('admin.layouts.pagination') }}
    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@section('scripts')
<script>
function deleteSkill(id, name) {
    layer.confirm('确定要删除技能 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "{{ route('admin.skills.destroy', '') }}/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
