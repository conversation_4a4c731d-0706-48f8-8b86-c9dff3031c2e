@extends('admin.layouts.app')

@section('title', '创建角色')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">创建新角色</div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.characters.store') }}">
            @csrf

            <div class="layui-form-item">
                <label class="layui-form-label">所属用户</label>
                <div class="layui-input-block">
                    <select name="user_id" lay-verify="required">
                        <option value="">请选择用户</option>
                        @foreach($users as $user)
                        <option value="{{ $user->id }}">{{ $user->username ?? $user->name ?? 'ID:'.$user->id }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">角色名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" required lay-verify="required" placeholder="请输入角色名称" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级</label>
                <div class="layui-input-inline">
                    <input type="number" name="level" value="{{ old('level', 1) }}" required lay-verify="required|number" placeholder="请输入等级" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">经验值</label>
                <div class="layui-input-inline">
                    <input type="number" name="exp" value="{{ old('exp', 0) }}" required lay-verify="required|number" placeholder="请输入经验值" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">生命值</label>
                <div class="layui-input-inline">
                    <input type="number" name="hp" value="{{ old('hp', 100) }}" required lay-verify="required|number" placeholder="请输入生命值" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">法力值</label>
                <div class="layui-input-inline">
                    <input type="number" name="mp" value="{{ old('mp', 50) }}" required lay-verify="required|number" placeholder="请输入法力值" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">攻击力</label>
                <div class="layui-input-inline">
                    <input type="number" name="attack" value="{{ old('attack', 10) }}" required lay-verify="required|number" placeholder="请输入攻击力" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">防御力</label>
                <div class="layui-input-inline">
                    <input type="number" name="defense" value="{{ old('defense', 5) }}" required lay-verify="required|number" placeholder="请输入防御力" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">速度</label>
                <div class="layui-input-inline">
                    <input type="number" name="speed" value="{{ old('speed', 5) }}" required lay-verify="required|number" placeholder="请输入速度" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">银两</label>
                <div class="layui-input-inline">
                    <input type="number" name="silver" value="{{ old('silver', 100) }}" required lay-verify="required|number" placeholder="请输入银两" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="status" value="1" title="启用" checked>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
                    <a href="{{ route('admin.characters.index') }}" class="layui-btn layui-btn-primary">返回</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 重新渲染表单
    form.render();
});
</script>
@endsection
