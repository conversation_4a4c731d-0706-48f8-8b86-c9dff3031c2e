@extends('admin.layouts.app')

@section('title', '创建物品')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">创建新物品</div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.items.store') }}">
            @csrf

            <div class="layui-form-item">
                <label class="layui-form-label">物品名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" required lay-verify="required" placeholder="请输入物品名称" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">物品类型</label>
                <div class="layui-input-block">
                    <select name="type" lay-verify="required">
                        <option value="">请选择物品类型</option>
                        <option value="weapon" {{ old('type') == 'weapon' ? 'selected' : '' }}>武器</option>
                        <option value="armor" {{ old('type') == 'armor' ? 'selected' : '' }}>防具</option>
                        <option value="accessory" {{ old('type') == 'accessory' ? 'selected' : '' }}>饰品</option>
                        <option value="consumable" {{ old('type') == 'consumable' ? 'selected' : '' }}>消耗品</option>
                        <option value="material" {{ old('type') == 'material' ? 'selected' : '' }}>材料</option>
                        <option value="quest" {{ old('type') == 'quest' ? 'selected' : '' }}>任务物品</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">物品描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入物品描述" class="layui-textarea">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级要求</label>
                <div class="layui-input-inline">
                    <input type="number" name="level_requirement" value="{{ old('level_requirement', 0) }}" required lay-verify="required|number" placeholder="请输入等级要求" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">购买价格</label>
                <div class="layui-input-inline">
                    <input type="number" name="buy_price" value="{{ old('buy_price', 0) }}" required lay-verify="required|number" placeholder="请输入购买价格" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">出售价格</label>
                <div class="layui-input-inline">
                    <input type="number" name="sell_price" value="{{ old('sell_price', 0) }}" required lay-verify="required|number" placeholder="请输入出售价格" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">攻击加成</label>
                <div class="layui-input-inline">
                    <input type="number" name="attack_bonus" value="{{ old('attack_bonus', 0) }}" placeholder="请输入攻击加成" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">防御加成</label>
                <div class="layui-input-inline">
                    <input type="number" name="defense_bonus" value="{{ old('defense_bonus', 0) }}" placeholder="请输入防御加成" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">生命加成</label>
                <div class="layui-input-inline">
                    <input type="number" name="hp_bonus" value="{{ old('hp_bonus', 0) }}" placeholder="请输入生命加成" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">法力加成</label>
                <div class="layui-input-inline">
                    <input type="number" name="mp_bonus" value="{{ old('mp_bonus', 0) }}" placeholder="请输入法力加成" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">速度加成</label>
                <div class="layui-input-inline">
                    <input type="number" name="speed_bonus" value="{{ old('speed_bonus', 0) }}" placeholder="请输入速度加成" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">可交易</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="is_tradable" value="1" title="可交易" {{ old('is_tradable') ? 'checked' : '' }}>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">可堆叠</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="is_stackable" value="1" title="可堆叠" {{ old('is_stackable') ? 'checked' : '' }}>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">最大堆叠</label>
                <div class="layui-input-inline">
                    <input type="number" name="max_stack" value="{{ old('max_stack', 1) }}" placeholder="请输入最大堆叠数量" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">稀有度</label>
                <div class="layui-input-block">
                    <select name="rarity" lay-verify="required">
                        <option value="1" {{ old('rarity') == 1 ? 'selected' : '' }}>普通</option>
                        <option value="2" {{ old('rarity') == 2 ? 'selected' : '' }}>精良</option>
                        <option value="3" {{ old('rarity') == 3 ? 'selected' : '' }}>稀有</option>
                        <option value="4" {{ old('rarity') == 4 ? 'selected' : '' }}>史诗</option>
                        <option value="5" {{ old('rarity') == 5 ? 'selected' : '' }}>传说</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
                    <a href="{{ route('admin.items.index') }}" class="layui-btn layui-btn-primary">返回</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 重新渲染表单
    form.render();
});
</script>
@endsection
