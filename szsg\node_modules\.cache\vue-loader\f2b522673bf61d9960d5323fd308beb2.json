{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Bank.vue?vue&type=style&index=0&id=cc113978&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Bank.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Bank.vue"], "names": [], "mappings": ";AA4vBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Bank.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"bank-page\">\r\n      <div class=\"bank-container\">\r\n        <!-- 钱庄标题 -->\r\n        <div class=\"bank-header\">\r\n          <h1>钱庄</h1>\r\n          <div class=\"character-status\">\r\n            <div class=\"status-box\">\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">银两:</div>\r\n                <div class=\"status-value silver-value\">{{ characterInfo.silver }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">金砖:</div>\r\n                <div class=\"status-value gold-value\">{{ characterInfo.gold }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">存款银两:</div>\r\n                <div class=\"status-value silver-value\">{{ accountInfo.silver }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">存款金砖:</div>\r\n                <div class=\"status-value gold-value\">{{ accountInfo.gold_ingot }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 选项卡导航 -->\r\n        <div class=\"tabs-container\">\r\n          <div \r\n            v-for=\"tab in tabs\" \r\n            :key=\"tab.id\" \r\n            class=\"tab\" \r\n            :class=\"{ 'active': activeTab === tab.id }\"\r\n            @click=\"activeTab = tab.id\"\r\n          >\r\n            <span class=\"tab-name\">{{ tab.name }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容区域 -->\r\n        <div class=\"content-area\">\r\n          <!-- 存款 -->\r\n          <div v-if=\"activeTab === 'deposit'\" class=\"tab-content\">\r\n            <div class=\"operation-form\">\r\n              <div class=\"form-group\">\r\n                <label for=\"deposit-currency\">货币类型:</label>\r\n                <select id=\"deposit-currency\" v-model=\"depositForm.currency\" class=\"form-control\">\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"form-group\">\r\n                <label for=\"deposit-amount\">存款金额:</label>\r\n                <input \r\n                  id=\"deposit-amount\" \r\n                  type=\"number\" \r\n                  v-model=\"depositForm.amount\" \r\n                  class=\"form-control\" \r\n                  min=\"1\"\r\n                  :max=\"getMaxDepositAmount()\"\r\n                />\r\n              </div>\r\n              <div class=\"form-actions\">\r\n                <button \r\n                  @click=\"deposit\" \r\n                  class=\"action-button\" \r\n                  :disabled=\"!canDeposit()\"\r\n                >\r\n                  存款\r\n                </button>\r\n                <button @click=\"setMaxDepositAmount\" class=\"max-button\">最大</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 取款 -->\r\n          <div v-if=\"activeTab === 'withdraw'\" class=\"tab-content\">\r\n            <div class=\"operation-form\">\r\n              <div class=\"form-group\">\r\n                <label for=\"withdraw-currency\">货币类型:</label>\r\n                <select id=\"withdraw-currency\" v-model=\"withdrawForm.currency\" class=\"form-control\">\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"form-group\">\r\n                <label for=\"withdraw-amount\">取款金额:</label>\r\n                <input \r\n                  id=\"withdraw-amount\" \r\n                  type=\"number\" \r\n                  v-model=\"withdrawForm.amount\" \r\n                  class=\"form-control\" \r\n                  min=\"1\"\r\n                  :max=\"getMaxWithdrawAmount()\"\r\n                />\r\n              </div>\r\n              <div class=\"form-actions\">\r\n                <button \r\n                  @click=\"withdraw\" \r\n                  class=\"action-button\" \r\n                  :disabled=\"!canWithdraw()\"\r\n                >\r\n                  取款\r\n                </button>\r\n                <button @click=\"setMaxWithdrawAmount\" class=\"max-button\">最大</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 交易记录 -->\r\n          <div v-if=\"activeTab === 'transactions'\" class=\"tab-content\">\r\n            <div class=\"transaction-filters\">\r\n              <div class=\"filter-group\">\r\n                <label for=\"filter-currency\">货币类型:</label>\r\n                <select id=\"filter-currency\" v-model=\"transactionFilters.currency\" class=\"form-control\" @change=\"loadTransactions\">\r\n                  <option value=\"\">全部</option>\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"filter-group\">\r\n                <label for=\"filter-type\">交易类型:</label>\r\n                <select id=\"filter-type\" v-model=\"transactionFilters.type\" class=\"form-control\" @change=\"loadTransactions\">\r\n                  <option value=\"\">全部</option>\r\n                  <option value=\"deposit\">存款</option>\r\n                  <option value=\"withdraw\">取款</option>\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"transaction-list\">\r\n              <table v-if=\"formattedTransactions.length > 0\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>时间</th>\r\n                    <th>类型</th>\r\n                    <th>货币</th>\r\n                    <th>金额</th>\r\n                    <th>余额</th>\r\n                    <th>说明</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr v-for=\"transaction in formattedTransactions\" :key=\"transaction.id\">\r\n                    <td>{{ formatDate(transaction.created_at) }}</td>\r\n                    <td>{{ transaction.type === 'deposit' ? '存款' : '取款' }}</td>\r\n                    <td>{{ transaction.currency === 'silver' ? '银两' : '金砖' }}</td>\r\n                    <td>{{ transaction.amount }}</td>\r\n                    <td>{{ transaction.balance }}</td>\r\n                    <td>{{ transaction.description }}</td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n              <div v-else class=\"no-transactions\">\r\n                暂无交易记录\r\n              </div>\r\n              \r\n              <!-- 分页 -->\r\n              <div v-if=\"pagination && pagination.total > 0\" class=\"pagination\">\r\n                <button \r\n                  @click=\"changePage(pagination.current_page - 1)\" \r\n                  :disabled=\"pagination.current_page <= 1\"\r\n                  class=\"pagination-button\"\r\n                >\r\n                  上一页\r\n                </button>\r\n                <span class=\"page-info\">{{ pagination.current_page }} / {{ pagination.last_page }}</span>\r\n                <button \r\n                  @click=\"changePage(pagination.current_page + 1)\" \r\n                  :disabled=\"pagination.current_page >= pagination.last_page\"\r\n                  class=\"pagination-button\"\r\n                >\r\n                  下一页\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 返回按钮 -->\r\n        <div class=\"bottom-actions\">\r\n          <button class=\"back-button\" @click=\"$router.push('/game/main')\">返回城镇</button>\r\n        </div>\r\n\r\n        <!-- 操作结果弹窗 -->\r\n        <div v-if=\"showResult\" class=\"result-modal\">\r\n          <div class=\"result-content\" :class=\"{ 'error': resultError }\">\r\n            <h3>{{ resultError ? '操作失败' : '操作成功' }}</h3>\r\n            <p>{{ resultMessage }}</p>\r\n            <button @click=\"showResult = false\">确定</button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 加载中和错误提示 -->\r\n        <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n          <div class=\"loading-spinner\"></div>\r\n          <div>加载中...</div>\r\n        </div>\r\n        <div v-if=\"error\" class=\"error-message\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nimport bankService from '@/api/services/bankService';\r\nimport { showMessage } from '@/utils/message';\r\nimport logger from '@/utils/logger';\r\nimport { getCurrentCharacter } from '@/api/services/characterService';\r\n\r\nexport default {\r\n  name: 'Bank',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      isLoading: true,\r\n      error: null,\r\n      activeTab: 'deposit', // 默认选中存款选项卡\r\n      tabs: [\r\n        {\r\n          id: 'deposit',\r\n          name: '存款'\r\n        },\r\n        {\r\n          id: 'withdraw',\r\n          name: '取款'\r\n        },\r\n        {\r\n          id: 'transactions',\r\n          name: '交易记录'\r\n        }\r\n      ],\r\n      characterInfo: {\r\n        id: null,\r\n        name: '',\r\n        silver: 0,\r\n        gold: 0\r\n      },\r\n      accountInfo: {\r\n        silver: 0,\r\n        gold_ingot: 0\r\n      },\r\n      depositForm: {\r\n        currency: 'silver',\r\n        amount: 100\r\n      },\r\n      withdrawForm: {\r\n        currency: 'silver',\r\n        amount: 100\r\n      },\r\n      transactions: [],\r\n      pagination: null,\r\n      transactionFilters: {\r\n        currency: '',\r\n        type: '',\r\n        page: 1,\r\n        per_page: 10\r\n      },\r\n      showResult: false,\r\n      resultError: false,\r\n      resultMessage: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.loadData();\r\n  },\r\n  watch: {\r\n    activeTab(newTab) {\r\n      if (newTab === 'transactions') {\r\n        this.loadTransactions();\r\n      }\r\n    },\r\n    'depositForm.currency'() {\r\n      // 切换货币类型时，重置金额\r\n      this.depositForm.amount = 100;\r\n    },\r\n    'withdrawForm.currency'() {\r\n      // 切换货币类型时，重置金额\r\n      this.withdrawForm.amount = 100;\r\n    }\r\n  },\r\n  computed: {\r\n    /**\r\n     * 格式化交易记录，确保数据格式一致\r\n     * 处理可能的数据格式不一致问题\r\n     */\r\n    formattedTransactions() {\r\n      return this.transactions.map(transaction => {\r\n        // 确保所有必要字段都存在，使用默认值代替可能缺失的字段\r\n        return {\r\n          id: transaction.id || Math.random().toString(36).substr(2, 9), // 生成随机ID如果缺失\r\n          created_at: transaction.created_at || new Date().toISOString(),\r\n          type: transaction.type || 'deposit',\r\n          currency: transaction.currency || 'silver',\r\n          amount: parseInt(transaction.amount) || 0,\r\n          balance: parseInt(transaction.balance) || 0,\r\n          description: transaction.description || this.generateDescription(transaction)\r\n        };\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 加载初始数据\r\n     */\r\n    async loadData() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        // 获取当前角色信息\r\n        const character = getCurrentCharacter();\r\n        if (!character) {\r\n          logger.error('[Bank] 未找到角色信息，尝试从LocalStorage获取');\r\n          \r\n          // 尝试直接从LocalStorage获取\r\n          try {\r\n            const characterId = localStorage.getItem('selectedCharacterId');\r\n            const characterJson = localStorage.getItem('selectedCharacter');\r\n            \r\n            if (characterId && characterJson) {\r\n              const savedCharacter = JSON.parse(characterJson);\r\n              logger.debug('[Bank] 从LocalStorage获取到角色:', savedCharacter);\r\n              \r\n              this.characterInfo = {\r\n                id: savedCharacter.id,\r\n                name: savedCharacter.name,\r\n                silver: savedCharacter.silver || 0,\r\n                gold: savedCharacter.gold || 0\r\n              };\r\n            } else {\r\n              throw new Error('未找到角色信息，请先选择角色');\r\n            }\r\n          } catch (e) {\r\n            logger.error('[Bank] 从LocalStorage获取角色失败:', e);\r\n            throw new Error('未找到角色信息，请先选择角色');\r\n          }\r\n        } else {\r\n          // 正常获取到角色信息\r\n          this.characterInfo = {\r\n            id: character.id,\r\n            name: character.name,\r\n            silver: character.silver || 0,\r\n            gold: character.gold || 0\r\n          };\r\n          \r\n          logger.debug('[Bank] 获取到角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        // 从Vuex获取更详细的角色信息\r\n        try {\r\n          const characterStatus = this.$store?.state?.character?.characterStatus;\r\n          if (characterStatus) {\r\n            this.characterInfo.silver = characterStatus.silver || this.characterInfo.silver;\r\n            this.characterInfo.gold = characterStatus.gold || this.characterInfo.gold;\r\n            logger.debug('[Bank] 从Vuex更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n          } else {\r\n            logger.warn('[Bank] 未从Vuex获取到角色状态，使用默认值');\r\n          }\r\n        } catch (stateError) {\r\n          logger.error('[Bank] 从Vuex获取角色状态失败:', stateError);\r\n        }\r\n        \r\n        // 银行账户信息初始化为默认值，防止API调用失败时没有数据\r\n        this.accountInfo = {\r\n          silver: 0,\r\n          gold_ingot: 0\r\n        };\r\n        \r\n        try {\r\n          // 获取银行账户信息\r\n          logger.debug('[Bank] 开始获取银行账户信息, ID:', this.characterInfo.id);\r\n          const accountResponse = await bankService.getAccountInfo(this.characterInfo.id);\r\n          logger.debug('[Bank] 银行账户响应:', JSON.stringify(accountResponse));\r\n          \r\n          // 处理可能的不同响应格式\r\n          if (accountResponse) {\r\n            // 检查不同的响应数据结构\r\n            if (accountResponse.data && accountResponse.data.account) {\r\n              // 标准格式: { data: { account: {...} } }\r\n              this.accountInfo = accountResponse.data.account;\r\n              logger.debug('[Bank] 获取到账户信息(标准格式):', JSON.stringify(this.accountInfo));\r\n              \r\n              // 更新角色信息\r\n              if (accountResponse.data.character) {\r\n                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.data.character));\r\n                this.characterInfo.name = accountResponse.data.character.name || this.characterInfo.name;\r\n                this.characterInfo.silver = accountResponse.data.character.silver || this.characterInfo.silver;\r\n                this.characterInfo.gold = accountResponse.data.character.gold || this.characterInfo.gold;\r\n                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n              }\r\n            } else if (accountResponse.account) {\r\n              // 替代格式1: { account: {...} }\r\n              this.accountInfo = accountResponse.account;\r\n              logger.debug('[Bank] 获取到账户信息(替代格式1):', JSON.stringify(this.accountInfo));\r\n              \r\n              // 更新角色信息\r\n              if (accountResponse.character) {\r\n                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.character));\r\n                this.characterInfo.name = accountResponse.character.name || this.characterInfo.name;\r\n                this.characterInfo.silver = accountResponse.character.silver || this.characterInfo.silver;\r\n                this.characterInfo.gold = accountResponse.character.gold || this.characterInfo.gold;\r\n                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n              }\r\n            } else if (typeof accountResponse === 'object' && accountResponse !== null) {\r\n              // 替代格式2: 响应本身就是账户对象\r\n              // 检查是否至少有银两或金砖字段\r\n              if ('silver' in accountResponse || 'gold_ingot' in accountResponse) {\r\n                this.accountInfo = {\r\n                  silver: accountResponse.silver || 0,\r\n                  gold_ingot: accountResponse.gold_ingot || 0\r\n                };\r\n                logger.debug('[Bank] 获取到账户信息(替代格式2):', JSON.stringify(this.accountInfo));\r\n                \r\n                // 检查是否包含角色信息\r\n                if ('name' in accountResponse || 'silver' in accountResponse || 'gold' in accountResponse) {\r\n                  logger.debug('[Bank] 从API响应对象中获取角色信息');\r\n                  if ('name' in accountResponse) this.characterInfo.name = accountResponse.name;\r\n                  if ('silver' in accountResponse) this.characterInfo.silver = accountResponse.silver;\r\n                  if ('gold' in accountResponse) this.characterInfo.gold = accountResponse.gold;\r\n                  logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n                }\r\n              } else {\r\n                throw new Error('获取银行账户信息失败: 响应数据格式无效');\r\n              }\r\n            } else {\r\n              throw new Error('获取银行账户信息失败: 响应数据格式无效');\r\n            }\r\n          } else {\r\n            throw new Error('获取银行账户信息失败: 无响应数据');\r\n          }\r\n        } catch (apiError) {\r\n          // 记录API错误但不阻止页面加载，使用默认账户信息\r\n          logger.error('[Bank] API调用失败:', apiError);\r\n          logger.error('[Bank] API调用失败详情:', JSON.stringify(apiError));\r\n          \r\n          // 显示警告消息\r\n          showMessage('获取银行账户信息失败，将使用临时数据显示', 'warning');\r\n          \r\n          // 默认值已初始化，无需重复设置\r\n        } finally {\r\n          // 无论API成功与否，都初始化表单\r\n          this.depositForm.amount = Math.min(100, this.characterInfo.silver || 0);\r\n          this.withdrawForm.amount = Math.min(100, this.accountInfo.silver || 0);\r\n        }\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        this.error = error.message || '加载数据失败，请刷新重试';\r\n        this.isLoading = false;\r\n        logger.error('[Bank] 加载数据失败:', error);\r\n        \r\n        // 如果是未找到角色信息，则跳转到角色选择页面\r\n        if (error.message && error.message.includes('未找到角色信息')) {\r\n          showMessage('未找到角色信息，请先选择角色', 'error');\r\n          this.$router.push('/setup/character-select');\r\n        }\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 加载交易记录\r\n     */\r\n    async loadTransactions() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        // 如果角色信息不存在，不尝试加载交易记录\r\n        if (!this.characterInfo || !this.characterInfo.id) {\r\n          logger.warn('[Bank] 无法加载交易记录: 角色信息不存在');\r\n          this.transactions = [];\r\n          this.isLoading = false;\r\n          return;\r\n        }\r\n        \r\n        // 构建请求参数，只有当筛选值不为空字符串时才添加到请求参数中\r\n        const params = {};\r\n        if (this.transactionFilters.page) {\r\n          params.page = this.transactionFilters.page;\r\n        }\r\n        if (this.transactionFilters.per_page) {\r\n          params.per_page = this.transactionFilters.per_page;\r\n        }\r\n        // 只有当值不是空字符串时才添加筛选条件\r\n        if (this.transactionFilters.currency && this.transactionFilters.currency !== '') {\r\n          params.currency = this.transactionFilters.currency;\r\n        }\r\n        if (this.transactionFilters.type && this.transactionFilters.type !== '') {\r\n          params.type = this.transactionFilters.type;\r\n        }\r\n        \r\n        logger.debug('[Bank] 加载交易记录, 参数:', params);\r\n        \r\n        try {\r\n          const response = await bankService.getTransactionHistory(this.characterInfo.id, params);\r\n          logger.debug('[Bank] 交易记录响应:', JSON.stringify(response));\r\n          \r\n          // 处理响应结构\r\n          if (response && response.data) {\r\n            // 标准响应结构\r\n            this.transactions = response.data.transactions || [];\r\n            this.pagination = response.data.pagination || null;\r\n            logger.debug('[Bank] 解析到交易记录:', this.transactions.length);\r\n          } else if (response && Array.isArray(response)) {\r\n            // 响应直接是交易数组\r\n            this.transactions = response;\r\n            logger.debug('[Bank] 解析到交易记录(数组格式):', this.transactions.length);\r\n          } else if (response && Array.isArray(response.transactions)) {\r\n            // 响应中有transactions数组\r\n            this.transactions = response.transactions;\r\n            this.pagination = response.pagination || null;\r\n            logger.debug('[Bank] 解析到交易记录(嵌套数组):', this.transactions.length);\r\n          } else if (response && response.success && Array.isArray(response.data)) {\r\n            // success包装的数组\r\n            this.transactions = response.data;\r\n            logger.debug('[Bank] 解析到交易记录(success包装数组):', this.transactions.length);\r\n          } else {\r\n            // 无效响应，使用空数组\r\n            logger.warn('[Bank] 无效的交易记录响应格式，使用空数组:', JSON.stringify(response));\r\n            this.transactions = [];\r\n            this.pagination = null;\r\n          }\r\n          \r\n          // 检查交易记录是否有效\r\n          if (this.transactions.length > 0) {\r\n            logger.debug('[Bank] 交易记录首条样例:', JSON.stringify(this.transactions[0]));\r\n          } else {\r\n            logger.warn('[Bank] 未获取到交易记录，筛选条件:', this.transactionFilters);\r\n          }\r\n        } catch (apiError) {\r\n          // API调用失败，使用空数组\r\n          logger.error('[Bank] 交易记录API调用失败:', apiError);\r\n          this.transactions = [];\r\n          this.pagination = null;\r\n          \r\n          // 显示警告消息，但不中断加载过程\r\n          showMessage('获取交易记录失败，将显示空记录', 'warning');\r\n        }\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        this.error = error.message || '加载交易记录失败，请刷新重试';\r\n        this.isLoading = false;\r\n        logger.error('[Bank] 加载交易记录失败:', error);\r\n        \r\n        // 确保有默认值\r\n        this.transactions = this.transactions || [];\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 存款操作\r\n     */\r\n    async deposit() {\r\n      try {\r\n        if (!this.canDeposit()) {\r\n          return this.showResultMessage('无法进行存款操作', true);\r\n        }\r\n        \r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        const response = await bankService.deposit(\r\n          this.characterInfo.id,\r\n          this.depositForm.currency,\r\n          parseInt(this.depositForm.amount) || 0\r\n        );\r\n        \r\n        // 更新本地数据\r\n        if (response.account) {\r\n          this.accountInfo = response.account;\r\n          logger.debug('[Bank] 存款后更新账户信息:', JSON.stringify(this.accountInfo));\r\n        }\r\n        \r\n        if (response.character) {\r\n          // 确保数据类型正确转换\r\n          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;\r\n          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;\r\n          logger.debug('[Bank] 存款后更新角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        this.isLoading = false;\r\n        this.showResultMessage(response.message || '存款成功', false);\r\n      } catch (error) {\r\n        this.isLoading = false;\r\n        this.showResultMessage(error.message || '存款失败，请重试', true);\r\n        logger.error('[Bank] 存款操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 取款操作\r\n     */\r\n    async withdraw() {\r\n      try {\r\n        if (!this.canWithdraw()) {\r\n          return this.showResultMessage('无法进行取款操作', true);\r\n        }\r\n        \r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        const response = await bankService.withdraw(\r\n          this.characterInfo.id,\r\n          this.withdrawForm.currency,\r\n          parseInt(this.withdrawForm.amount) || 0\r\n        );\r\n        \r\n        // 更新本地数据\r\n        if (response.account) {\r\n          this.accountInfo = response.account;\r\n          logger.debug('[Bank] 取款后更新账户信息:', JSON.stringify(this.accountInfo));\r\n        }\r\n        \r\n        if (response.character) {\r\n          // 确保数据类型正确转换\r\n          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;\r\n          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;\r\n          logger.debug('[Bank] 取款后更新角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        this.isLoading = false;\r\n        this.showResultMessage(response.message || '取款成功', false);\r\n      } catch (error) {\r\n        this.isLoading = false;\r\n        this.showResultMessage(error.message || '取款失败，请重试', true);\r\n        logger.error('[Bank] 取款操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 显示操作结果消息\r\n     */\r\n    showResultMessage(message, isError = false) {\r\n      this.resultMessage = message;\r\n      this.resultError = isError;\r\n      this.showResult = true;\r\n    },\r\n    \r\n    /**\r\n     * 为缺失描述的交易记录生成默认描述\r\n     */\r\n    generateDescription(transaction) {\r\n      const type = transaction.type === 'deposit' ? '存入' : '取出';\r\n      const currency = transaction.currency === 'silver' ? '银两' : '金砖';\r\n      const amount = transaction.amount || 0;\r\n      return `${type} ${amount} ${currency}`;\r\n    },\r\n    \r\n    /**\r\n     * 格式化日期\r\n     */\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未知时间';\r\n      \r\n      try {\r\n        const date = new Date(dateStr);\r\n        if (isNaN(date.getTime())) return '日期格式错误';\r\n        \r\n        return date.toLocaleString('zh-CN', {\r\n          year: 'numeric',\r\n          month: '2-digit',\r\n          day: '2-digit',\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          second: '2-digit'\r\n        });\r\n      } catch (e) {\r\n        logger.error('[Bank] 日期格式化失败:', e);\r\n        return '日期格式错误';\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 切换分页\r\n     */\r\n    changePage(page) {\r\n      if (page < 1 || (this.pagination && page > this.pagination.last_page)) {\r\n        return;\r\n      }\r\n      \r\n      this.transactionFilters.page = page;\r\n      this.loadTransactions();\r\n    },\r\n    \r\n    /**\r\n     * 获取最大可存款金额\r\n     */\r\n    getMaxDepositAmount() {\r\n      if (this.depositForm.currency === 'silver') {\r\n        return this.characterInfo.silver;\r\n      } else {\r\n        return this.characterInfo.gold;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 获取最大可取款金额\r\n     */\r\n    getMaxWithdrawAmount() {\r\n      if (this.withdrawForm.currency === 'silver') {\r\n        return this.accountInfo.silver;\r\n      } else {\r\n        return this.accountInfo.gold_ingot;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 设置最大存款金额\r\n     */\r\n    setMaxDepositAmount() {\r\n      this.depositForm.amount = this.getMaxDepositAmount();\r\n    },\r\n    \r\n    /**\r\n     * 设置最大取款金额\r\n     */\r\n    setMaxWithdrawAmount() {\r\n      this.withdrawForm.amount = this.getMaxWithdrawAmount();\r\n    },\r\n    \r\n    /**\r\n     * 判断是否可以存款\r\n     */\r\n    canDeposit() {\r\n      const amount = parseInt(this.depositForm.amount);\r\n      if (!amount || amount <= 0) {\r\n        return false;\r\n      }\r\n      \r\n      if (this.depositForm.currency === 'silver') {\r\n        return amount <= this.characterInfo.silver;\r\n      } else {\r\n        return amount <= this.characterInfo.gold;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 判断是否可以取款\r\n     */\r\n    canWithdraw() {\r\n      const amount = parseInt(this.withdrawForm.amount);\r\n      if (!amount || amount <= 0) {\r\n        return false;\r\n      }\r\n      \r\n      if (this.withdrawForm.currency === 'silver') {\r\n        return amount <= this.accountInfo.silver;\r\n      } else {\r\n        return amount <= this.accountInfo.gold_ingot;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bank-page {\r\n  padding: 10px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.bank-container {\r\n  background-color: rgba(0, 0, 20, 0.7);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  box-shadow: 0 0 20px rgba(0, 0, 50, 0.5);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.bank-header {\r\n  margin-bottom: 15px;\r\n  padding: 12px;\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.bank-header h1 {\r\n  margin: 0 0 10px 0;\r\n  text-align: center;\r\n  color: #ffcc00;\r\n  font-size: 22px;\r\n  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);\r\n}\r\n\r\n.character-status {\r\n  margin-top: 10px;\r\n}\r\n\r\n.status-box {\r\n  background-color: rgba(153, 0, 0, 0.2);\r\n  border: 1px solid rgba(153, 0, 0, 0.5);\r\n  border-radius: 5px;\r\n  padding: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 8px;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 3px 5px;\r\n}\r\n\r\n.status-label {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-value {\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.silver-value {\r\n  color: #ffcc00;\r\n}\r\n\r\n.gold-value {\r\n  color: #ff9900;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid rgba(51, 51, 204, 0.5);\r\n}\r\n\r\n.tab {\r\n  padding: 10px 15px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  flex: 1;\r\n  border-radius: 5px 5px 0 0;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n}\r\n\r\n.tab:hover {\r\n  background-color: rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.tab.active {\r\n  background-color: rgba(51, 51, 204, 0.5);\r\n  border: 1px solid rgba(51, 51, 204, 0.8);\r\n  border-bottom: none;\r\n}\r\n\r\n.tab-name {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tab-content {\r\n  padding: 10px;\r\n}\r\n\r\n.operation-form {\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-control {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border-radius: 5px;\r\n  border: 1px solid rgba(51, 51, 204, 0.5);\r\n  background-color: rgba(0, 0, 20, 0.7);\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.action-button {\r\n  flex: 3;\r\n  padding: 12px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-button:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.action-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.max-button {\r\n  flex: 1;\r\n  padding: 12px;\r\n  background-color: #990000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.max-button:hover {\r\n  background-color: #cc0000;\r\n}\r\n\r\n.transaction-filters {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-group {\r\n  flex: 1;\r\n}\r\n\r\n.transaction-list {\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\ntable {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\nthead {\r\n  position: sticky;\r\n  top: 0;\r\n  background-color: rgba(0, 0, 51, 0.8);\r\n  z-index: 1;\r\n}\r\n\r\nth, td {\r\n  padding: 10px;\r\n  text-align: left;\r\n  border-bottom: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\nth {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\ntr:hover {\r\n  background-color: rgba(51, 51, 204, 0.1);\r\n}\r\n\r\n.no-transactions {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #aaaaaa;\r\n}\r\n\r\n.pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n  gap: 10px;\r\n}\r\n\r\n.pagination-button {\r\n  padding: 8px 15px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.pagination-button:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.pagination-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.page-info {\r\n  font-weight: bold;\r\n}\r\n\r\n.bottom-actions {\r\n  margin-top: auto;\r\n  text-align: center;\r\n}\r\n\r\n.back-button {\r\n  padding: 10px 20px;\r\n  background-color: #cc0000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #dd4444;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.back-button:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.result-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.result-content {\r\n  background-color: #000033;\r\n  border: 2px solid #3333cc;\r\n  border-radius: 6px;\r\n  padding: 20px;\r\n  width: 80%;\r\n  max-width: 400px;\r\n  text-align: center;\r\n}\r\n\r\n.result-content.error {\r\n  border-color: #cc0000;\r\n}\r\n\r\n.result-content h3 {\r\n  color: #ffcc00;\r\n  margin-top: 0;\r\n}\r\n\r\n.result-content p {\r\n  margin: 15px 0;\r\n}\r\n\r\n.result-content button {\r\n  padding: 8px 20px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.result-content button:hover {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n  color: white;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 4px solid rgba(51, 51, 204, 0.3);\r\n  border-top: 4px solid #3333cc;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.error-message {\r\n  background-color: rgba(153, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 10px;\r\n  border-radius: 5px;\r\n  margin: 10px 0;\r\n  text-align: center;\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 480px) {\r\n  .status-box {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .transaction-filters {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  th, td {\r\n    padding: 8px 5px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .action-button, .max-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style> "]}]}