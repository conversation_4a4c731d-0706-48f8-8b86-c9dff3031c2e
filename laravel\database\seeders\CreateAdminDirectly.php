<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class CreateAdminDirectly extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('admins')->insert([
            'name' => '管理员',
            'username' => 'admin',
            'password' => Hash::make('123456'),
            'role' => 'super_admin',
            'permissions' => json_encode([
                'all' => '*',
            ]),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $this->command->info('管理员账号创建成功!');
        $this->command->info('用户名: admin');
        $this->command->info('密码: 123456');
    }
}
