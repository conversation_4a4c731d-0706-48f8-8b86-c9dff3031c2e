{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\bagService.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\bagService.js", "mtime": 1749707325412}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["get", "post", "del", "logger", "bagService", "getBagData", "characterId", "debug", "loading", "loadingText", "then", "res", "data", "catch", "error", "useItem", "itemId", "quantity", "equipItem", "clearCache", "unequipItem", "discardItem", "sortBag", "expandBag", "slots", "getItemDetails"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/api/services/bagService.js"], "sourcesContent": ["/**\n * 背包系统API服务\n * 提供背包相关的接口调用\n */\nimport { get, post, del } from '../request.js';\nimport logger from '../../utils/logger.js';\n\n/**\n * 背包服务\n */\nconst bagService = {\n    /**\n     * 获取角色背包数据\n     * @param {string} characterId - 角色ID\n     * @returns {Promise<Object>} - 背包数据\n     */\n    getBagData(characterId) {\n        logger.debug('[BagService] 获取背包数据, characterId:', characterId);\n        \n        return get(`/characters/${characterId}/bag`, {}, {\n            loading: true,\n            loadingText: '加载背包数据...'\n        }).then(res => {\n            logger.debug('[BagService] 背包数据响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 获取背包数据失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 使用物品\n     * @param {string} characterId - 角色ID\n     * @param {string} itemId - 物品ID\n     * @param {number} quantity - 使用数量\n     * @returns {Promise<Object>} - 使用结果\n     */\n    useItem(characterId, itemId, quantity = 1) {\n        logger.debug('[BagService] 使用物品, characterId:', characterId, 'itemId:', itemId, 'quantity:', quantity);\n        \n        return post(`/characters/${characterId}/bag/items/${itemId}/use`, {\n            quantity\n        }, {\n            loading: true,\n            loadingText: '使用物品中...'\n        }).then(res => {\n            logger.debug('[BagService] 使用物品响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 使用物品失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 装备物品\n     * @param {string} characterId - 角色ID\n     * @param {string} itemId - 物品ID\n     * @returns {Promise<Object>} - 装备结果\n     */\n    equipItem(characterId, itemId) {\n        logger.debug('[BagService] 装备物品, characterId:', characterId, 'itemId:', itemId);\n        \n        return post(`/characters/${characterId}/bag/items/${itemId}/equip`, {}, {\n            loading: true,\n            loadingText: '装备中...'\n        }).then(res => {\n            logger.debug('[BagService] 装备物品响应:', res);\n            // 清除背包缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 装备物品失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 卸下装备\n     * @param {string} characterId - 角色ID\n     * @param {string} itemId - 物品ID\n     * @returns {Promise<Object>} - 卸下结果\n     */\n    unequipItem(characterId, itemId) {\n        logger.debug('[BagService] 卸下装备, characterId:', characterId, 'itemId:', itemId);\n        \n        return post(`/characters/${characterId}/bag/items/${itemId}/unequip`, {}, {\n            loading: true,\n            loadingText: '卸下装备中...'\n        }).then(res => {\n            logger.debug('[BagService] 卸下装备响应:', res);\n            // 清除背包缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 卸下装备失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 丢弃物品\n     * @param {string} characterId - 角色ID\n     * @param {string} itemId - 物品ID\n     * @param {number} quantity - 丢弃数量\n     * @returns {Promise<Object>} - 丢弃结果\n     */\n    discardItem(characterId, itemId, quantity = 1) {\n        logger.debug('[BagService] 丢弃物品, characterId:', characterId, 'itemId:', itemId, 'quantity:', quantity);\n\n        return del(`/characters/${characterId}/bag/items/${itemId}`, {\n            quantity\n        }, {\n            loading: true,\n            loadingText: '丢弃物品中...'\n        }).then(res => {\n            logger.debug('[BagService] 丢弃物品响应:', res);\n            // 清除背包缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 丢弃物品失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 整理背包\n     * @param {string} characterId - 角色ID\n     * @returns {Promise<Object>} - 整理结果\n     */\n    sortBag(characterId) {\n        logger.debug('[BagService] 整理背包, characterId:', characterId);\n        \n        return post(`/characters/${characterId}/bag/sort`, {}, {\n            loading: true,\n            loadingText: '整理背包中...'\n        }).then(res => {\n            logger.debug('[BagService] 整理背包响应:', res);\n            // 清除背包缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 整理背包失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 扩展背包容量\n     * @param {string} characterId - 角色ID\n     * @param {number} slots - 扩展的格子数\n     * @returns {Promise<Object>} - 扩展结果\n     */\n    expandBag(characterId, slots) {\n        logger.debug('[BagService] 扩展背包, characterId:', characterId, 'slots:', slots);\n        \n        return post(`/characters/${characterId}/bag/expand`, {\n            slots\n        }, {\n            loading: true,\n            loadingText: '扩展背包中...'\n        }).then(res => {\n            logger.debug('[BagService] 扩展背包响应:', res);\n            // 清除背包缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 扩展背包失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取物品详情\n     * @param {string} itemId - 物品ID\n     * @returns {Promise<Object>} - 物品详情\n     */\n    getItemDetails(itemId) {\n        logger.debug('[BagService] 获取物品详情, itemId:', itemId);\n        \n        return get(`/items/${itemId}`, {}, {\n            loading: true,\n            loadingText: '加载物品详情...'\n        }).then(res => {\n            logger.debug('[BagService] 物品详情响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[BagService] 获取物品详情失败:', error);\n            throw error;\n        });\n    },\n\n\n};\n\nexport default bagService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AAC9C,OAAOC,MAAM,MAAM,uBAAuB;;AAE1C;AACA;AACA;AACA,MAAMC,UAAU,GAAG;EACf;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAACC,WAAW,EAAE;IACpBH,MAAM,CAACI,KAAK,CAAC,mCAAmC,EAAED,WAAW,CAAC;IAE9D,OAAON,GAAG,CAAC,eAAeM,WAAW,MAAM,EAAE,CAAC,CAAC,EAAE;MAC7CE,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACT,WAAW,EAAEU,MAAM,EAAEC,QAAQ,GAAG,CAAC,EAAE;IACvCd,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAED,WAAW,EAAE,SAAS,EAAEU,MAAM,EAAE,WAAW,EAAEC,QAAQ,CAAC;IAEtG,OAAOhB,IAAI,CAAC,eAAeK,WAAW,cAAcU,MAAM,MAAM,EAAE;MAC9DC;IACJ,CAAC,EAAE;MACCT,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACII,SAASA,CAACZ,WAAW,EAAEU,MAAM,EAAE;IAC3Bb,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAED,WAAW,EAAE,SAAS,EAAEU,MAAM,CAAC;IAE/E,OAAOf,IAAI,CAAC,eAAeK,WAAW,cAAcU,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE;MACpER,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC;MACA,IAAI,CAACQ,UAAU,CAACb,WAAW,CAAC;MAC5B,OAAOK,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIM,WAAWA,CAACd,WAAW,EAAEU,MAAM,EAAE;IAC7Bb,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAED,WAAW,EAAE,SAAS,EAAEU,MAAM,CAAC;IAE/E,OAAOf,IAAI,CAAC,eAAeK,WAAW,cAAcU,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE;MACtER,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC;MACA,IAAI,CAACQ,UAAU,CAACb,WAAW,CAAC;MAC5B,OAAOK,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIO,WAAWA,CAACf,WAAW,EAAEU,MAAM,EAAEC,QAAQ,GAAG,CAAC,EAAE;IAC3Cd,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAED,WAAW,EAAE,SAAS,EAAEU,MAAM,EAAE,WAAW,EAAEC,QAAQ,CAAC;IAEtG,OAAOf,GAAG,CAAC,eAAeI,WAAW,cAAcU,MAAM,EAAE,EAAE;MACzDC;IACJ,CAAC,EAAE;MACCT,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC;MACA,IAAI,CAACQ,UAAU,CAACb,WAAW,CAAC;MAC5B,OAAOK,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIQ,OAAOA,CAAChB,WAAW,EAAE;IACjBH,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAED,WAAW,CAAC;IAE5D,OAAOL,IAAI,CAAC,eAAeK,WAAW,WAAW,EAAE,CAAC,CAAC,EAAE;MACnDE,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC;MACA,IAAI,CAACQ,UAAU,CAACb,WAAW,CAAC;MAC5B,OAAOK,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIS,SAASA,CAACjB,WAAW,EAAEkB,KAAK,EAAE;IAC1BrB,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAED,WAAW,EAAE,QAAQ,EAAEkB,KAAK,CAAC;IAE7E,OAAOvB,IAAI,CAAC,eAAeK,WAAW,aAAa,EAAE;MACjDkB;IACJ,CAAC,EAAE;MACChB,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC;MACA,IAAI,CAACQ,UAAU,CAACb,WAAW,CAAC;MAC5B,OAAOK,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIW,cAAcA,CAACT,MAAM,EAAE;IACnBb,MAAM,CAACI,KAAK,CAAC,8BAA8B,EAAES,MAAM,CAAC;IAEpD,OAAOhB,GAAG,CAAC,UAAUgB,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE;MAC/BR,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXR,MAAM,CAACI,KAAK,CAAC,sBAAsB,EAAEI,GAAG,CAAC;MACzC,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdX,MAAM,CAACW,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN;AAGJ,CAAC;AAED,eAAeV,UAAU", "ignoreList": []}]}