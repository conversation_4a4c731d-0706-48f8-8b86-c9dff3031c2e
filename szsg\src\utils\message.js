/**
 * 消息提示工具
 * 提供统一的消息提示功能
 */
import logger from './logger';

// 消息类型
const MESSAGE_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
};

// 默认配置
const DEFAULT_OPTIONS = {
  duration: 3000, // 显示时长，单位毫秒
  closable: true, // 是否可关闭
  position: 'top', // 显示位置
};

// 消息容器
let messageContainer = null;

/**
 * 创建消息容器
 * @returns {HTMLElement} 消息容器元素
 */
function createMessageContainer() {
  if (messageContainer) return messageContainer;
  
  messageContainer = document.createElement('div');
  messageContainer.className = 'game-message-container';
  messageContainer.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    width: auto;
    max-width: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    pointer-events: none;
  `;
  
  document.body.appendChild(messageContainer);
  return messageContainer;
}

/**
 * 创建单个消息元素
 * @param {string} content - 消息内容
 * @param {string} type - 消息类型
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 消息元素
 */
function createMessageElement(content, type, options) {
  const messageElement = document.createElement('div');
  messageElement.className = `game-message game-message-${type}`;
  
  // 样式
  let backgroundColor = '#000033';
  let textColor = '#ffffff';
  let borderColor = '#5555ff';
  
  switch (type) {
    case MESSAGE_TYPES.SUCCESS:
      backgroundColor = '#003300';
      borderColor = '#00ff00';
      textColor = '#00ff00';
      break;
    case MESSAGE_TYPES.WARNING:
      backgroundColor = '#333300';
      borderColor = '#ffff00';
      textColor = '#ffff00';
      break;
    case MESSAGE_TYPES.ERROR:
      backgroundColor = '#330000';
      borderColor = '#ff0000';
      textColor = '#ff0000';
      break;
    default:
      // 默认info样式
      break;
  }
  
  messageElement.style.cssText = `
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    background-color: ${backgroundColor};
    color: ${textColor};
    border: 1px solid ${borderColor};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    pointer-events: auto;
    max-width: 100%;
    word-break: break-word;
    text-align: center;
  `;
  
  messageElement.textContent = content;
  
  // 添加关闭按钮
  if (options.closable) {
    const closeBtn = document.createElement('span');
    closeBtn.className = 'game-message-close';
    closeBtn.innerHTML = '&times;';
    closeBtn.style.cssText = `
      margin-left: 10px;
      cursor: pointer;
      font-weight: bold;
    `;
    closeBtn.addEventListener('click', () => {
      removeMessage(messageElement);
    });
    messageElement.appendChild(closeBtn);
  }
  
  return messageElement;
}

/**
 * 移除消息
 * @param {HTMLElement} messageElement - 消息元素
 */
function removeMessage(messageElement) {
  messageElement.style.opacity = '0';
  messageElement.style.transform = 'translateY(-10px)';
  
  setTimeout(() => {
    if (messageElement.parentNode) {
      messageElement.parentNode.removeChild(messageElement);
    }
    
    // 如果容器为空，移除容器
    if (messageContainer && messageContainer.children.length === 0) {
      document.body.removeChild(messageContainer);
      messageContainer = null;
    }
  }, 300);
}

/**
 * 显示消息
 * @param {string} content - 消息内容
 * @param {string} type - 消息类型
 * @param {Object} customOptions - 自定义选项
 */
function showMessage(content, type = MESSAGE_TYPES.INFO, customOptions = {}) {
  try {
    logger.debug(`[Message] ${type}: ${content}`);
    
    const options = { ...DEFAULT_OPTIONS, ...customOptions };
    const container = createMessageContainer();
    const messageElement = createMessageElement(content, type, options);
    
    container.appendChild(messageElement);
    
    // 触发重排以应用过渡效果
    messageElement.offsetHeight;
    messageElement.style.opacity = '1';
    
    // 设置自动关闭
    if (options.duration > 0) {
      setTimeout(() => {
        removeMessage(messageElement);
      }, options.duration);
    }
  } catch (error) {
    logger.error('[Message] 显示消息失败:', error);
  }
}

/**
 * 显示信息消息
 * @param {string} content - 消息内容
 * @param {Object} options - 配置选项
 */
function info(content, options = {}) {
  showMessage(content, MESSAGE_TYPES.INFO, options);
}

/**
 * 显示成功消息
 * @param {string} content - 消息内容
 * @param {Object} options - 配置选项
 */
function success(content, options = {}) {
  showMessage(content, MESSAGE_TYPES.SUCCESS, options);
}

/**
 * 显示警告消息
 * @param {string} content - 消息内容
 * @param {Object} options - 配置选项
 */
function warning(content, options = {}) {
  showMessage(content, MESSAGE_TYPES.WARNING, options);
}

/**
 * 显示错误消息
 * @param {string} content - 消息内容
 * @param {Object} options - 配置选项
 */
function error(content, options = {}) {
  showMessage(content, MESSAGE_TYPES.ERROR, options);
}

// 导出
export {
  showMessage,
  info,
  success,
  warning,
  error,
  MESSAGE_TYPES
}; 