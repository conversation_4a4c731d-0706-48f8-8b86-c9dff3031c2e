@extends('admin.layouts.app')

@section('title', '钱庄管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">钱庄账户管理</div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">银行统计</div>
                    <div class="layui-card-body">
                        <p><i class="layui-icon layui-icon-dollar"></i> 总银两: {{ number_format($totalSilver) }}</p>
                        <p><i class="layui-icon layui-icon-diamond"></i> 总金砖: {{ number_format($totalGoldIngot) }}</p>
                    </div>
                </div>
            </div>
            <div class="layui-col-md8">
                <div class="layui-btn-group">
                    <a href="{{ route('admin.bank.transactions') }}" class="layui-btn">查看交易记录</a>
                </div>
            </div>
        </div>

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>角色名</th>
                    <th>银两余额</th>
                    <th>金砖余额</th>
                    <th>更新时间</th>
                </tr>
            </thead>
            <tbody>
                @forelse($accounts as $account)
                <tr>
                    <td>{{ $account->id }}</td>
                    <td>{{ $account->character_name }}</td>
                    <td>{{ number_format($account->silver) }}</td>
                    <td>{{ number_format($account->gold_ingot) }}</td>
                    <td>{{ $account->updated_at }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="5" class="layui-center">暂无账户数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $accounts->links('admin.layouts.pagination') }}
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
