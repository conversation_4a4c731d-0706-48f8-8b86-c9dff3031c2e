<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Character;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class WebSocketController extends Controller
{
    /**
     * 获取WebSocket认证令牌
     */
    public function getToken(Request $request)
    {
        // 验证请求
        $request->validate([
            'character_id' => 'required|exists:characters,id',
        ]);

        // 确保用户只能获取自己的角色的令牌
        $character = Character::find($request->character_id);
        if ($character->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权为此角色获取令牌',
                ]
            ], 403);
        }

        // 生成JWT令牌
        $token = $this->generateJWT([
            'character_id' => $character->id,
            'user_id' => Auth::id(),
            'exp' => time() + 3600 // 1小时过期
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'token' => $token,
                'expires_in' => 3600
            ]
        ]);
    }

    /**
     * 广播游戏事件
     */
    public function broadcastEvent(Request $request)
    {
        $request->validate([
            'event' => 'required|string',
            'target_type' => 'required|in:character,team,world',
            'target_id' => 'required_if:target_type,character,team',
            'data' => 'required|array'
        ]);

        $event = $request->event;
        $data = $request->data;

        switch ($request->target_type) {
            case 'character':
                // 向特定角色广播
                broadcast(new \App\Events\GameEvent($event, $request->target_id, $data))->toOthers();
                break;

            case 'team':
                // 向团队广播
                broadcast(new \App\Events\TeamEvent($event, $request->target_id, $data))->toOthers();
                break;

            case 'world':
                // 向所有在线玩家广播
                broadcast(new \App\Events\WorldEvent($event, $data))->toOthers();
                break;
        }

        return response()->json([
            'success' => true,
            'message' => '事件已广播'
        ]);
    }

    /**
     * 简单的JWT生成器
     */
    private function generateJWT($payload)
    {
        $header = json_encode([
            'typ' => 'JWT',
            'alg' => 'HS256'
        ]);

        $payload = json_encode($payload);

        $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64UrlPayload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, env('JWT_SECRET'), true);
        $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;
    }
}
