<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('npcs', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('NPC名称');
            $table->string('title')->nullable()->comment('称号/职位');
            $table->text('description')->nullable()->comment('描述');
            $table->string('avatar')->nullable()->comment('头像路径');
            $table->integer('level')->default(1)->comment('等级');
            $table->enum('type', ['merchant', 'quest_giver', 'trainer', 'guard', 'official', 'immortal', 'monk', 'other'])->default('other')->comment('NPC类型');
            $table->enum('faction', ['heaven', 'buddhist', 'mortal', 'demon', 'dragon', 'neutral'])->default('neutral')->comment('阵营');
            
            // 位置信息
            $table->foreignId('location_id')->constrained('locations')->onDelete('cascade')->comment('所在位置');
            $table->integer('x')->default(0)->comment('位置X坐标');
            $table->integer('y')->default(0)->comment('位置Y坐标');
            
            // 服务和功能
            $table->json('services')->nullable()->comment('提供的服务列表');
            $table->json('dialogue')->nullable()->comment('对话内容');
            $table->json('quests')->nullable()->comment('可接受的任务ID列表');
            $table->json('shop_items')->nullable()->comment('商店物品ID列表');
            
            // 属性
            $table->integer('health')->default(100)->comment('生命值');
            $table->integer('mana')->default(50)->comment('法力值');
            $table->integer('attack')->default(10)->comment('攻击力');
            $table->integer('defense')->default(10)->comment('防御力');
            $table->integer('speed')->default(10)->comment('速度');
            
            // 状态
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->boolean('is_friendly')->default(true)->comment('是否友好');
            $table->integer('respawn_time')->default(0)->comment('重生时间(秒)');
            
            // 掉落和奖励
            $table->json('drop_items')->nullable()->comment('掉落物品');
            $table->integer('exp_reward')->default(0)->comment('经验奖励');
            $table->integer('silver_reward')->default(0)->comment('银两奖励');
            
            $table->timestamps();
            
            // 索引
            $table->index(['location_id', 'is_active']);
            $table->index(['type', 'is_active']);
            $table->index(['level']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('npcs');
    }
};
