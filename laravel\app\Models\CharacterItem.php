<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CharacterItem extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'character_id',
        'item_id',
        'quantity',
        'equipped',
        'custom_stats',
    ];

    /**
     * 应该被转换为原生类型的属性
     */
    protected $casts = [
        'custom_stats' => 'array',
        'equipped' => 'boolean',
    ];

    /**
     * 获取此物品所属的角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 获取物品详情
     */
    public function item()
    {
        return $this->belongsTo(Item::class);
    }
}
