<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Npc extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'title',
        'description',
        'avatar',
        'level',
        'type',
        'faction',
        'location_id',
        'x',
        'y',
        'services',
        'dialogue',
        'quests',
        'shop_items',
        'health',
        'mana',
        'attack',
        'defense',
        'speed',
        'is_active',
        'is_friendly',
        'respawn_time',
        'drop_items',
        'exp_reward',
        'silver_reward',
    ];

    protected $casts = [
        'services' => 'array',
        'dialogue' => 'array',
        'quests' => 'array',
        'shop_items' => 'array',
        'drop_items' => 'array',
        'is_active' => 'boolean',
        'is_friendly' => 'boolean',
    ];

    /**
     * 获取NPC所在位置
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * 获取NPC的对话内容
     */
    public function getDialogue($context = 'default')
    {
        $dialogues = $this->dialogue ?? [];
        return $dialogues[$context] ?? $dialogues['default'] ?? '你好，骚年！';
    }

    /**
     * 检查NPC是否提供指定服务
     */
    public function hasService($service)
    {
        $services = $this->services ?? [];
        return in_array($service, $services);
    }

    /**
     * 获取NPC的商店物品
     */
    public function getShopItems()
    {
        if (!$this->shop_items) {
            return collect();
        }

        return Item::whereIn('id', $this->shop_items)
            ->where('is_active', true)
            ->get();
    }

    /**
     * 获取NPC可接受的任务
     */
    public function getAvailableQuests()
    {
        if (!$this->quests) {
            return collect();
        }

        // 这里可以添加任务系统的逻辑
        return collect($this->quests);
    }

    /**
     * 获取NPC的完整信息
     */
    public function getFullInfo()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'title' => $this->title,
            'description' => $this->description,
            'avatar' => $this->avatar,
            'level' => $this->level,
            'type' => $this->type,
            'faction' => $this->faction,
            'services' => $this->services ?? [],
            'is_friendly' => $this->is_friendly,
            'location' => $this->location->name ?? '未知',
        ];
    }

    /**
     * 根据位置获取活跃的NPC
     */
    public static function getByLocation($locationId)
    {
        return static::where('location_id', $locationId)
            ->where('is_active', true)
            ->orderBy('level')
            ->get();
    }

    /**
     * 根据类型获取NPC
     */
    public static function getByType($type)
    {
        return static::where('type', $type)
            ->where('is_active', true)
            ->get();
    }

    /**
     * 获取商人NPC
     */
    public static function getMerchants()
    {
        return static::where('type', 'merchant')
            ->where('is_active', true)
            ->get();
    }

    /**
     * 获取任务发布者NPC
     */
    public static function getQuestGivers()
    {
        return static::where('type', 'quest_giver')
            ->where('is_active', true)
            ->get();
    }
}
