{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue?vue&type=style&index=0&id=e37a2986&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue", "mtime": 1749718635730}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Friends.vue"], "names": [], "mappings": ";AA8dA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Friends.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"friends-page\">\n      <!-- 返回按钮 -->\n      <div class=\"header-section\">\n        <button class=\"return-btn\" @click=\"goBack\">\n          <img src=\"/static/game/UI/anniu/fhui_2.png\" alt=\"返回\" class=\"btn-image\" />\n        </button>\n        <h2 class=\"page-title\">好友系统</h2>\n      </div>\n      \n      <!-- 功能标签 -->\n      <div class=\"friends-tabs\">\n        <div \n          v-for=\"(tab, index) in friendsTabs\" \n          :key=\"index\"\n          class=\"friends-tab\"\n          :class=\"{ active: currentTab === index }\"\n          @click=\"switchTab(index)\"\n        >\n          {{ tab.name }}\n        </div>\n      </div>\n      \n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-text\">加载中...</div>\n      </div>\n      \n      <!-- 错误状态 -->\n      <div v-else-if=\"error\" class=\"error-container\">\n        <div class=\"error-text\">{{ error }}</div>\n        <div class=\"error-actions\">\n          <button v-if=\"error.includes('登录')\" class=\"retry-btn\" @click=\"goToLogin\">前往登录</button>\n          <button v-else-if=\"error.includes('角色')\" class=\"retry-btn\" @click=\"goToCharacterSelect\">选择角色</button>\n          <button v-else class=\"retry-btn\" @click=\"fetchFriendsData\">重试</button>\n        </div>\n      </div>\n      \n      <!-- 好友内容 -->\n      <div v-else class=\"friends-content\">\n        <!-- 好友列表 -->\n        <div v-if=\"currentTab === 0\" class=\"friends-list\">\n          <div v-if=\"friends.length === 0\" class=\"empty-tip\">\n            <span>暂无好友</span>\n          </div>\n          \n          <div v-else class=\"friend-items\">\n            <div \n              v-for=\"friend in friends\" \n              :key=\"friend.id\"\n              class=\"friend-item\"\n              :class=\"{ online: friend.isOnline }\"\n            >\n              <div class=\"friend-avatar\">\n                <img :src=\"friend.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"friend.name\" />\n                <div v-if=\"friend.isOnline\" class=\"online-indicator\"></div>\n              </div>\n              \n              <div class=\"friend-info\">\n                <div class=\"friend-name\">{{ friend.name }}</div>\n                <div class=\"friend-level\">等级 {{ friend.level }}</div>\n                <div class=\"friend-status\">{{ friend.isOnline ? '在线' : '离线' }}</div>\n              </div>\n              \n              <div class=\"friend-actions\">\n                <button \n                  class=\"action-btn chat\"\n                  @click=\"chatWithFriend(friend)\"\n                  :disabled=\"!friend.isOnline\"\n                >\n                  聊天\n                </button>\n                <button \n                  class=\"action-btn remove\"\n                  @click=\"removeFriend(friend)\"\n                >\n                  删除\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 好友申请 -->\n        <div v-if=\"currentTab === 1\" class=\"friend-requests\">\n          <div v-if=\"friendRequests.length === 0\" class=\"empty-tip\">\n            <span>暂无好友申请</span>\n          </div>\n          \n          <div v-else class=\"request-items\">\n            <div \n              v-for=\"request in friendRequests\" \n              :key=\"request.id\"\n              class=\"request-item\"\n            >\n              <div class=\"request-avatar\">\n                <img :src=\"request.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"request.name\" />\n              </div>\n              \n              <div class=\"request-info\">\n                <div class=\"request-name\">{{ request.name }}</div>\n                <div class=\"request-level\">等级 {{ request.level }}</div>\n                <div class=\"request-time\">{{ formatTime(request.requestTime) }}</div>\n              </div>\n              \n              <div class=\"request-actions\">\n                <button \n                  class=\"action-btn accept\"\n                  @click=\"acceptFriendRequest(request)\"\n                  :disabled=\"isActionLoading\"\n                >\n                  接受\n                </button>\n                <button \n                  class=\"action-btn reject\"\n                  @click=\"rejectFriendRequest(request)\"\n                  :disabled=\"isActionLoading\"\n                >\n                  拒绝\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 添加好友 -->\n        <div v-if=\"currentTab === 2\" class=\"add-friend\">\n          <div class=\"search-section\">\n            <div class=\"search-input-group\">\n              <input \n                v-model=\"searchQuery\"\n                type=\"text\"\n                placeholder=\"输入玩家名称搜索\"\n                class=\"search-input\"\n                @keyup.enter=\"searchPlayers\"\n              />\n              <button \n                class=\"search-btn\"\n                @click=\"searchPlayers\"\n                :disabled=\"isSearching || !searchQuery.trim()\"\n              >\n                搜索\n              </button>\n            </div>\n          </div>\n          \n          <div v-if=\"isSearching\" class=\"searching-tip\">\n            <span>搜索中...</span>\n          </div>\n          \n          <div v-else-if=\"searchResults.length === 0 && hasSearched\" class=\"empty-tip\">\n            <span>未找到相关玩家</span>\n          </div>\n          \n          <div v-else-if=\"searchResults.length > 0\" class=\"search-results\">\n            <div \n              v-for=\"player in searchResults\" \n              :key=\"player.id\"\n              class=\"search-result-item\"\n            >\n              <div class=\"result-avatar\">\n                <img :src=\"player.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"player.name\" />\n              </div>\n              \n              <div class=\"result-info\">\n                <div class=\"result-name\">{{ player.name }}</div>\n                <div class=\"result-level\">等级 {{ player.level }}</div>\n                <div class=\"result-status\">{{ player.isOnline ? '在线' : '离线' }}</div>\n              </div>\n              \n              <div class=\"result-actions\">\n                <button \n                  class=\"action-btn add\"\n                  @click=\"sendFriendRequest(player)\"\n                  :disabled=\"isActionLoading || isAlreadyFriend(player) || hasPendingRequest(player)\"\n                >\n                  {{ getAddButtonText(player) }}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport axios from 'axios'\nimport { API_BASE_URL } from '@/api/config.js'\nimport { ERROR_MESSAGES } from '@/api/constants.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      currentTab: 0,\n      friendsTabs: [\n        { name: '好友列表', type: 'friends' },\n        { name: '好友申请', type: 'requests' },\n        { name: '添加好友', type: 'add' }\n      ],\n      friends: [],\n      friendRequests: [],\n      searchQuery: '',\n      searchResults: [],\n      hasSearched: false,\n      isLoading: true,\n      isSearching: false,\n      isActionLoading: false,\n      error: null\n    }\n  },\n  computed: {\n    authToken() {\n      return this.$store.state.token || localStorage.getItem('authToken')\n    },\n    selectedCharacterId() {\n      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')\n    }\n  },\n  created() {\n    // 检查认证状态\n    if (!this.authToken) {\n      logger.warn('Friends页面: 未找到认证token')\n      this.error = '请先登录'\n      return\n    }\n\n    // 检查角色选择状态\n    if (!this.selectedCharacterId) {\n      logger.warn('Friends页面: 未选择角色')\n      this.error = '请先选择角色'\n      return\n    }\n\n    this.fetchFriendsData()\n  },\n  methods: {\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    switchTab(index) {\n      this.currentTab = index\n      logger.info('切换好友标签', this.friendsTabs[index].name)\n      \n      if (index === 1) {\n        this.fetchFriendRequests()\n      }\n    },\n    \n    async fetchFriendsData() {\n      this.isLoading = true\n      this.error = null\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friends`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.friends = response.data.friends || []\n        logger.info('获取好友列表成功', this.friends.length)\n      } catch (err) {\n        this.error = err.message || ERROR_MESSAGES.UNKNOWN_ERROR\n        this.showToast(this.error)\n        logger.error('获取好友列表失败', err)\n      } finally {\n        this.isLoading = false\n      }\n    },\n    \n    async fetchFriendRequests() {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.friendRequests = response.data.requests || []\n        logger.info('获取好友申请成功', this.friendRequests.length)\n      } catch (err) {\n        logger.error('获取好友申请失败', err)\n      }\n    },\n    \n    async searchPlayers() {\n      if (!this.searchQuery.trim()) return\n      \n      this.isSearching = true\n      this.hasSearched = false\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/players/search`, {\n          params: { query: this.searchQuery.trim() },\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.searchResults = response.data.players || []\n        this.hasSearched = true\n        logger.info('搜索玩家成功', this.searchResults.length)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('搜索玩家失败', err)\n      } finally {\n        this.isSearching = false\n      }\n    },\n    \n    async sendFriendRequest(player) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`,\n          { target_player_id: player.id },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '好友申请已发送', 'success')\n        logger.info('发送好友申请成功', player.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('发送好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async acceptFriendRequest(request) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/accept`,\n          {},\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已接受好友申请', 'success')\n        await this.fetchFriendRequests()\n        await this.fetchFriendsData()\n        logger.info('接受好友申请成功', request.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('接受好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async rejectFriendRequest(request) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/reject`,\n          {},\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已拒绝好友申请', 'success')\n        await this.fetchFriendRequests()\n        logger.info('拒绝好友申请成功', request.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('拒绝好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async removeFriend(friend) {\n      if (!confirm(`确定要删除好友\"${friend.name}\"吗？`)) {\n        return\n      }\n      \n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.delete(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friends/${friend.id}`,\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已删除好友', 'success')\n        await this.fetchFriendsData()\n        logger.info('删除好友成功', friend.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('删除好友失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    chatWithFriend(friend) {\n      this.showToast(`与${friend.name}的聊天功能开发中...`, 'info')\n      // 这里可以实现私聊功能\n    },\n    \n    isAlreadyFriend(player) {\n      return this.friends.some(friend => friend.id === player.id)\n    },\n    \n    hasPendingRequest(player) {\n      return this.friendRequests.some(request => request.id === player.id)\n    },\n    \n    getAddButtonText(player) {\n      if (this.isAlreadyFriend(player)) return '已是好友'\n      if (this.hasPendingRequest(player)) return '已申请'\n      return '添加好友'\n    },\n    \n    formatTime(timestamp) {\n      const date = new Date(timestamp)\n      const now = new Date()\n      const diff = now - date\n      \n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`\n      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`\n      return `${Math.floor(diff / 86400000)}天前`\n    },\n    \n    showToast(message, type = 'error') {\n      alert(message)\n    },\n\n    goToLogin() {\n      this.$router.push('/login')\n    },\n\n    goToCharacterSelect() {\n      this.$router.push('/setup/character-select')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.friends-page {\n  padding: 15px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2d1b69, #1a0f3d);\n  color: #fff;\n}\n\n.header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  gap: 15px;\n}\n\n.return-btn {\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  padding: 0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.btn-image {\n  width: 60px;\n  height: 40px;\n  object-fit: contain;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #fff;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.friends-tabs {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 20px;\n  border-bottom: 2px solid rgba(255, 255, 255, 0.2);\n  padding-bottom: 10px;\n}\n\n.friends-tab {\n  padding: 10px 20px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 500;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-2px);\n  }\n\n  &.active {\n    background: rgba(255, 255, 255, 0.3);\n    border-color: rgba(255, 255, 255, 0.5);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.loading-container, .error-container {\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.loading-text {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.error-text {\n  font-size: 16px;\n  color: #ff6b6b;\n  margin-bottom: 15px;\n}\n\n.error-actions {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.retry-btn {\n  padding: 10px 20px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n}\n\n.friends-content {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.empty-tip {\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  padding: 60px 20px;\n  font-size: 16px;\n}\n\n// 好友列表样式\n.friend-items, .request-items, .search-results {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.friend-item, .request-item, .search-result-item {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 12px;\n  padding: 15px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.12);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\n  }\n\n  &.online {\n    border-left: 4px solid #2ecc71;\n  }\n}\n\n.friend-avatar, .request-avatar, .result-avatar {\n  position: relative;\n  width: 60px;\n  height: 60px;\n  margin-right: 15px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 50%;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n}\n\n.online-indicator {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 16px;\n  height: 16px;\n  background: #2ecc71;\n  border: 2px solid #fff;\n  border-radius: 50%;\n}\n\n.friend-info, .request-info, .result-info {\n  flex: 1;\n  margin-right: 15px;\n}\n\n.friend-name, .request-name, .result-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 4px;\n}\n\n.friend-level, .request-level, .result-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 4px;\n}\n\n.friend-status, .result-status {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.request-time {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.friend-actions, .request-actions, .result-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.chat {\n    background: #4ecdc4;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #45b7aa;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.remove {\n    background: #e74c3c;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #c0392b;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.accept {\n    background: #2ecc71;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #27ae60;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.reject {\n    background: #95a5a6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #7f8c8d;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.add {\n    background: #3498db;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #2980b9;\n      transform: translateY(-1px);\n    }\n  }\n}\n\n// 搜索区域样式\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-input-group {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.search-input {\n  flex: 1;\n  padding: 12px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 8px;\n  color: #fff;\n  font-size: 16px;\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #4ecdc4;\n    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);\n  }\n}\n\n.search-btn {\n  padding: 12px 24px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.searching-tip {\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  padding: 40px 20px;\n  font-size: 16px;\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .friends-page {\n    padding: 10px;\n  }\n\n  .header-section {\n    margin-bottom: 15px;\n    gap: 10px;\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .btn-image {\n    width: 50px;\n    height: 35px;\n  }\n\n  .friends-tabs {\n    flex-wrap: wrap;\n    gap: 6px;\n  }\n\n  .friends-tab {\n    padding: 8px 16px;\n    font-size: 14px;\n  }\n\n  .friend-item, .request-item, .search-result-item {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .friend-avatar, .request-avatar, .result-avatar {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .friend-info, .request-info, .result-info {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .friend-actions, .request-actions, .result-actions {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n  }\n\n  .search-input, .search-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n  .friends-page {\n    padding: 8px;\n  }\n\n  .friends-content {\n    padding: 15px;\n  }\n\n  .friend-avatar, .request-avatar, .result-avatar {\n    width: 50px;\n    height: 50px;\n  }\n\n  .action-btn {\n    padding: 6px 12px;\n    font-size: 11px;\n  }\n}\n</style>\n"]}]}