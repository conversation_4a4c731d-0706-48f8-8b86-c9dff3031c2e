{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750347661761}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZm9yLWVhY2guanMiOwppbXBvcnQgc29ja2V0TWFuYWdlciBmcm9tICcuLi91dGlscy9zb2NrZXRNYW5hZ2VyJzsKCi8vIOiBiuWkqemikemBk+m<PERSON><PERSON><PERSON>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"}, {"version": 3, "names": ["socketManager", "CHANNELS", "id", "name", "unread", "props", "characterInfo", "type", "Object", "default", "autoConnect", "Boolean", "initialMinimized", "data", "isConnected", "channels", "currentChannelIndex", "messages", "newMessage", "minimized", "reconnecting", "compactMode", "inputFocused", "lastMessageTime", "messageHistory", "historyIndex", "computed", "currentChannel", "_this$channels$this$c", "filteredMessages", "filter", "msg", "channel", "teamId", "watch", "handler", "newInfo", "connectChat", "immediate", "$nextTick", "scrollToBottom", "mounted", "_this$characterInfo", "beforeUnmount", "disconnectChat", "methods", "_this$characterInfo2", "console", "error", "init", "setupEventListeners", "joinChannels", "addSystemMessage", "unsubscribers", "for<PERSON>ach", "unsubscribe", "disconnect", "push", "subscribe", "reason", "_this$characterInfo3", "handleChatMessage", "sender", "content", "message", "timestamp", "sender_id", "isSelf", "_this$characterInfo4", "team_id", "_this$characterInfo5", "_this$characterInfo6", "receiverId", "receiver_id", "joinChannel", "messageData", "length", "slice", "channelIndex", "findIndex", "c", "Date", "now", "switchChannel", "index", "sendMessage", "trim", "messageOptions", "character_id", "sendChatMessage", "handleSenderClick", "$emit", "container", "$refs", "chatMessagesContainer", "$el", "scrollTop", "scrollHeight", "undefined", "toggleMinimize", "formatTime", "date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "toDateString", "month", "getMonth", "day", "getDate", "toggleCompactMode", "clearMessages", "getInputPlaceholder", "placeholders", "onInputFocus", "onInputBlur", "addToHistory", "indexOf", "splice", "unshift"], "sources": ["src/components/GameChat.vue"], "sourcesContent": ["<template>\r\n    <div class=\"chat-container\" :class=\"{ 'minimized': minimized, 'compact': compactMode }\">\r\n        <div class=\"chat-header\" @click=\"toggleMinimize\">\r\n            <div class=\"chat-title-section\">\r\n                <span class=\"chat-title\">聊天</span>\r\n                <div class=\"chat-status\" :class=\"{ 'connected': isConnected }\" :title=\"isConnected ? '已连接' : '未连接'\"></div>\r\n            </div>\r\n            <div class=\"chat-controls\">\r\n                <button v-if=\"!minimized\" class=\"control-btn\" @click.stop=\"clearMessages\" title=\"清空消息\">\r\n                    <span class=\"icon\">🗑</span>\r\n                </button>\r\n                <button v-if=\"!minimized\" class=\"control-btn\" @click.stop=\"toggleCompactMode\" title=\"紧凑模式\">\r\n                    <span class=\"icon\">{{ compactMode ? '📖' : '📄' }}</span>\r\n                </button>\r\n                <button class=\"control-btn minimize-btn\" :title=\"minimized ? '展开聊天' : '收起聊天'\">\r\n                    <span class=\"icon\">{{ minimized ? '⬆' : '⬇' }}</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n\r\n        <div v-if=\"!minimized\" class=\"chat-body\">\r\n            <div class=\"chat-channels\">\r\n                <button\r\n                    v-for=\"(channel, index) in channels\"\r\n                    :key=\"index\"\r\n                    class=\"channel-tab\"\r\n                    :class=\"{ active: currentChannelIndex === index }\"\r\n                    @click=\"switchChannel(index)\"\r\n                >\r\n                    <span class=\"channel-name\">{{ channel.name }}</span>\r\n                    <span v-if=\"channel.unread > 0\" class=\"channel-badge\">{{ channel.unread > 99 ? '99+' : channel.unread }}</span>\r\n                </button>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"chatMessagesContainer\">\r\n                <template v-if=\"filteredMessages.length > 0\">\r\n                    <div\r\n                        v-for=\"(msg, index) in filteredMessages\"\r\n                        :key=\"index\"\r\n                        class=\"chat-message\"\r\n                        :class=\"{\r\n                            'system-message': msg.type === 'system',\r\n                            'npc-message': msg.type === 'npc',\r\n                            'player-message': msg.type === 'player',\r\n                            'self-message': msg.isSelf,\r\n                            'compact': compactMode\r\n                        }\"\r\n                    >\r\n                        <div v-if=\"msg.type !== 'system'\" class=\"message-header\">\r\n                            <span class=\"message-sender\" @click=\"handleSenderClick(msg)\">{{ msg.sender }}</span>\r\n                            <span v-if=\"msg.timestamp && !compactMode\" class=\"message-time\">{{ formatTime(msg.timestamp) }}</span>\r\n                        </div>\r\n                        <div class=\"message-content\">{{ msg.content }}</div>\r\n                        <div v-if=\"msg.timestamp && compactMode\" class=\"message-time-compact\">{{ formatTime(msg.timestamp) }}</div>\r\n                    </div>\r\n                </template>\r\n                <div v-else class=\"empty-messages\">\r\n                    <div class=\"empty-icon\">💬</div>\r\n                    <div class=\"empty-text\">暂无消息</div>\r\n                    <div class=\"empty-hint\">在下方输入框发送消息开始聊天</div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input-container\">\r\n                <div class=\"input-wrapper\">\r\n                    <input\r\n                        class=\"chat-input\"\r\n                        v-model=\"newMessage\"\r\n                        :placeholder=\"getInputPlaceholder()\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        @focus=\"onInputFocus\"\r\n                        @blur=\"onInputBlur\"\r\n                        :disabled=\"!isConnected\"\r\n                        maxlength=\"200\"\r\n                    />\r\n                    <div class=\"input-counter\" v-if=\"newMessage.length > 150\">{{ newMessage.length }}/200</div>\r\n                </div>\r\n                <button\r\n                    class=\"send-btn\"\r\n                    @click=\"sendMessage\"\r\n                    :disabled=\"!isConnected || !newMessage.trim()\"\r\n                    :title=\"!isConnected ? '未连接到聊天服务器' : '发送消息'\"\r\n                >\r\n                    <span class=\"send-icon\">📤</span>\r\n                    <span class=\"send-text\">发送</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport socketManager from '../utils/socketManager';\r\n\r\n// 聊天频道配置\r\nconst CHANNELS = [\r\n    { id: 'world', name: '世界', unread: 0 },\r\n    { id: 'trade', name: '交易', unread: 0 },\r\n    { id: 'team', name: '队伍', unread: 0 },\r\n    { id: 'private', name: '私聊', unread: 0 }\r\n];\r\n\r\nexport default {\r\n    name: 'GameChat',\r\n    props: {\r\n        characterInfo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        autoConnect: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        initialMinimized: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            isConnected: false,\r\n            channels: [...CHANNELS],\r\n            currentChannelIndex: 0,\r\n            messages: [],\r\n            newMessage: '',\r\n            minimized: this.initialMinimized,\r\n            reconnecting: false,\r\n            compactMode: false,\r\n            inputFocused: false,\r\n            lastMessageTime: null,\r\n            messageHistory: [], // 消息历史记录，用于上下键切换\r\n            historyIndex: -1\r\n        };\r\n    },\r\n    computed: {\r\n        currentChannel() {\r\n            return this.channels[this.currentChannelIndex]?.id || 'world';\r\n        },\r\n        filteredMessages() {\r\n            // 如果是私聊频道，显示所有私聊消息\r\n            if (this.currentChannel === 'private') {\r\n                return this.messages.filter(msg => msg.channel === 'private');\r\n            }\r\n            \r\n            // 如果是队伍频道，根据characterInfo中的teamId过滤队伍消息\r\n            if (this.currentChannel === 'team' && this.characterInfo.teamId) {\r\n                return this.messages.filter(msg => \r\n                    msg.channel === 'team' && \r\n                    msg.teamId === this.characterInfo.teamId\r\n                );\r\n            }\r\n            \r\n            // 否则只显示当前频道的消息\r\n            return this.messages.filter(msg => msg.channel === this.currentChannel);\r\n        }\r\n    },\r\n    watch: {\r\n        characterInfo: {\r\n            handler(newInfo) {\r\n                if (newInfo && newInfo.id && this.autoConnect) {\r\n                    this.connectChat();\r\n                }\r\n            },\r\n            immediate: true\r\n        },\r\n        currentChannelIndex() {\r\n            // 切换频道时，重置未读消息数\r\n            if (this.channels[this.currentChannelIndex]) {\r\n                this.channels[this.currentChannelIndex].unread = 0;\r\n            }\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        }\r\n    },\r\n    mounted() {\r\n        // 当前组件已挂载\r\n        if (this.characterInfo?.id && this.autoConnect) {\r\n            this.connectChat();\r\n        }\r\n    },\r\n    beforeUnmount() {\r\n        // 组件销毁前，移除所有事件监听器\r\n        this.disconnectChat();\r\n    },\r\n    methods: {\r\n        async connectChat() {\r\n            if (!this.characterInfo?.id) {\r\n                console.error('[GameChat] 无法连接聊天，缺少角色信息');\r\n                return;\r\n            }\r\n            \r\n            try {\r\n                // 初始化Socket连接\r\n                await socketManager.init();\r\n                \r\n                // 添加事件监听\r\n                this.setupEventListeners();\r\n                \r\n                // 加入相关频道\r\n                this.joinChannels();\r\n                \r\n                // 设置连接状态\r\n                this.isConnected = true;\r\n                this.reconnecting = false;\r\n                \r\n                // 添加系统消息\r\n                this.addSystemMessage('已连接到聊天服务器');\r\n                \r\n            } catch (error) {\r\n                console.error('[GameChat] 连接失败:', error);\r\n                \r\n                this.isConnected = false;\r\n                if (!this.reconnecting) {\r\n                    this.reconnecting = true;\r\n                    this.addSystemMessage('连接失败，正在尝试重新连接...');\r\n                }\r\n            }\r\n        },\r\n        \r\n        disconnectChat() {\r\n            // 移除事件监听\r\n            if (this.unsubscribers) {\r\n                this.unsubscribers.forEach(unsubscribe => unsubscribe());\r\n                this.unsubscribers = [];\r\n            }\r\n            \r\n            // 断开socket连接\r\n            socketManager.disconnect();\r\n            \r\n            // 更新状态\r\n            this.isConnected = false;\r\n        },\r\n        \r\n        setupEventListeners() {\r\n            // 存储所有取消订阅的函数\r\n            this.unsubscribers = [];\r\n            \r\n            // 监听连接事件\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('connect', () => {\r\n                    this.isConnected = true;\r\n                    this.reconnecting = false;\r\n                })\r\n            );\r\n            \r\n            // 监听断开连接事件\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('disconnect', (reason) => {\r\n                    this.isConnected = false;\r\n                    this.addSystemMessage(`连接已断开 (${reason})`);\r\n                })\r\n            );\r\n            \r\n            // 世界消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('world_message', (data) => {\r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'world',\r\n                        sender: data.sender.name,\r\n                        content: data.message,\r\n                        timestamp: data.timestamp,\r\n                        sender_id: data.sender.id,\r\n                        isSelf: data.sender.id === this.characterInfo?.id\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 队伍消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('team_message', (data) => {\r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'team',\r\n                        teamId: data.team_id,\r\n                        sender: data.message.sender.name,\r\n                        content: data.message.message,\r\n                        timestamp: data.message.timestamp,\r\n                        sender_id: data.message.sender.id,\r\n                        isSelf: data.message.sender.id === this.characterInfo?.id\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 私聊消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('private_message', (data) => {\r\n                    const isSelf = data.sender_id === this.characterInfo?.id;\r\n                    const sender = isSelf ? this.characterInfo?.name : data.message.sender.name;\r\n                    const receiverId = isSelf ? data.receiver_id : data.sender_id;\r\n                    \r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'private',\r\n                        sender,\r\n                        content: data.message.message,\r\n                        timestamp: data.message.timestamp,\r\n                        sender_id: data.message.sender.id,\r\n                        receiver_id: receiverId,\r\n                        isSelf\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 系统消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('system_message', (data) => {\r\n                    this.addSystemMessage(data.message || '系统通知');\r\n                })\r\n            );\r\n        },\r\n        \r\n        joinChannels() {\r\n            // 加入世界频道\r\n            socketManager.joinChannel('chat.world');\r\n            \r\n            // 如果角色有队伍，加入队伍频道\r\n            if (this.characterInfo.teamId) {\r\n                socketManager.joinChannel(`team.${this.characterInfo.teamId}`);\r\n            }\r\n            \r\n            // 加入角色私聊频道\r\n            socketManager.joinChannel(`chat.user.${this.characterInfo.id}`);\r\n        },\r\n        \r\n        handleChatMessage(messageData) {\r\n            // 添加到消息列表\r\n            this.messages.push(messageData);\r\n            \r\n            // 控制消息列表大小，防止过长\r\n            if (this.messages.length > 200) {\r\n                this.messages = this.messages.slice(-150);\r\n            }\r\n            \r\n            // 如果消息不是当前频道，更新未读计数\r\n            if (messageData.channel !== this.currentChannel) {\r\n                const channelIndex = this.channels.findIndex(c => c.id === messageData.channel);\r\n                if (channelIndex !== -1) {\r\n                    this.channels[channelIndex].unread++;\r\n                }\r\n            }\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n        \r\n        addSystemMessage(content, timestamp = null) {\r\n            this.messages.push({\r\n                type: 'system',\r\n                channel: 'system',\r\n                sender: '系统',\r\n                content,\r\n                timestamp: timestamp || Date.now()\r\n            });\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n        \r\n        switchChannel(index) {\r\n            this.currentChannelIndex = index;\r\n        },\r\n        \r\n        sendMessage() {\r\n            if (!this.isConnected) {\r\n                this.addSystemMessage('未连接到聊天服务器，无法发送消息');\r\n                return;\r\n            }\r\n            \r\n            const message = this.newMessage.trim();\r\n            if (!message) return;\r\n            \r\n            // 获取当前频道\r\n            const channel = this.currentChannel;\r\n            \r\n            // 构建消息数据\r\n            let messageOptions = {\r\n                channel: channel,\r\n                message: message,\r\n                character_id: this.characterInfo.id\r\n            };\r\n            \r\n            switch (channel) {\r\n                case 'team':\r\n                    if (!this.characterInfo.teamId) {\r\n                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');\r\n                        return;\r\n                    }\r\n                    messageOptions.team_id = this.characterInfo.teamId;\r\n                    break;\r\n                    \r\n                case 'private':\r\n                    // 私聊需要指定接收者\r\n                    // TODO: 添加私聊目标选择功能\r\n                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');\r\n                    return;\r\n            }\r\n            \r\n            // 使用socketManager的sendChatMessage方法\r\n            socketManager.sendChatMessage(messageOptions);\r\n            \r\n            // 清空输入框\r\n            this.newMessage = '';\r\n        },\r\n        \r\n        handleSenderClick(msg) {\r\n            // 点击发送者名称\r\n            if (msg.type === 'player' && !msg.isSelf) {\r\n                this.$emit('player-click', {\r\n                    id: msg.sender_id,\r\n                    name: msg.sender\r\n                });\r\n            }\r\n        },\r\n        \r\n        scrollToBottom() {\r\n            const container = this.$refs.chatMessagesContainer;\r\n            if (container) {\r\n                // 注意: uni-app环境中可能需要特殊处理\r\n                if (container.$el) {\r\n                    container.$el.scrollTop = container.$el.scrollHeight;\r\n                } else if (container.scrollTop !== undefined) {\r\n                    container.scrollTop = container.scrollHeight;\r\n                }\r\n            }\r\n        },\r\n        \r\n        toggleMinimize() {\r\n            this.minimized = !this.minimized;\r\n            \r\n            if (!this.minimized) {\r\n                // 展开时，滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n            }\r\n        },\r\n        \r\n        formatTime(timestamp) {\r\n            if (!timestamp) return '';\r\n\r\n            const date = new Date(timestamp);\r\n            const now = new Date();\r\n            const hours = date.getHours().toString().padStart(2, '0');\r\n            const minutes = date.getMinutes().toString().padStart(2, '0');\r\n\r\n            // 如果是今天，只显示时间\r\n            if (date.toDateString() === now.toDateString()) {\r\n                return `${hours}:${minutes}`;\r\n            }\r\n\r\n            // 如果不是今天，显示月日和时间\r\n            const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n            const day = date.getDate().toString().padStart(2, '0');\r\n            return `${month}-${day} ${hours}:${minutes}`;\r\n        },\r\n\r\n        // 新增方法\r\n        toggleCompactMode() {\r\n            this.compactMode = !this.compactMode;\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n\r\n        clearMessages() {\r\n            this.messages = [];\r\n            this.addSystemMessage('消息已清空');\r\n        },\r\n\r\n        getInputPlaceholder() {\r\n            const channel = this.currentChannel;\r\n            const placeholders = {\r\n                'world': '对所有人说...',\r\n                'trade': '发布交易信息...',\r\n                'team': '对队友说...',\r\n                'private': '私聊消息...'\r\n            };\r\n            return placeholders[channel] || '输入消息...';\r\n        },\r\n\r\n        onInputFocus() {\r\n            this.inputFocused = true;\r\n        },\r\n\r\n        onInputBlur() {\r\n            this.inputFocused = false;\r\n        },\r\n\r\n        // 处理消息历史记录\r\n        addToHistory(message) {\r\n            if (message && message.trim()) {\r\n                // 移除重复的消息\r\n                const index = this.messageHistory.indexOf(message);\r\n                if (index > -1) {\r\n                    this.messageHistory.splice(index, 1);\r\n                }\r\n\r\n                // 添加到历史记录开头\r\n                this.messageHistory.unshift(message);\r\n\r\n                // 限制历史记录数量\r\n                if (this.messageHistory.length > 20) {\r\n                    this.messageHistory = this.messageHistory.slice(0, 20);\r\n                }\r\n            }\r\n            this.historyIndex = -1;\r\n        },\r\n\r\n        // 增强的消息发送\r\n        sendMessage() {\r\n            if (!this.isConnected) {\r\n                this.addSystemMessage('未连接到聊天服务器，无法发送消息');\r\n                return;\r\n            }\r\n\r\n            const message = this.newMessage.trim();\r\n            if (!message) return;\r\n\r\n            // 检查消息长度\r\n            if (message.length > 200) {\r\n                this.addSystemMessage('消息长度不能超过200个字符');\r\n                return;\r\n            }\r\n\r\n            // 防止刷屏（限制发送频率）\r\n            const now = Date.now();\r\n            if (this.lastMessageTime && (now - this.lastMessageTime) < 1000) {\r\n                this.addSystemMessage('发送消息过于频繁，请稍后再试');\r\n                return;\r\n            }\r\n            this.lastMessageTime = now;\r\n\r\n            // 添加到历史记录\r\n            this.addToHistory(message);\r\n\r\n            // 获取当前频道\r\n            const channel = this.currentChannel;\r\n\r\n            // 构建消息数据\r\n            let messageOptions = {\r\n                channel: channel,\r\n                message: message,\r\n                character_id: this.characterInfo.id\r\n            };\r\n\r\n            switch (channel) {\r\n                case 'team':\r\n                    if (!this.characterInfo.teamId) {\r\n                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');\r\n                        return;\r\n                    }\r\n                    messageOptions.team_id = this.characterInfo.teamId;\r\n                    break;\r\n\r\n                case 'private':\r\n                    // 私聊需要指定接收者\r\n                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');\r\n                    return;\r\n            }\r\n\r\n            // 使用socketManager的sendChatMessage方法\r\n            socketManager.sendChatMessage(messageOptions);\r\n\r\n            // 清空输入框\r\n            this.newMessage = '';\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.chat-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(20,20,30,0.9) 100%);\r\n    backdrop-filter: blur(15px);\r\n    border-radius: 12px 12px 0 0;\r\n    overflow: hidden;\r\n    height: 420px;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: 1px solid rgba(255,255,255,0.1);\r\n    box-shadow: 0 8px 32px rgba(0,0,0,0.3);\r\n}\r\n\r\n.chat-container.minimized {\r\n    height: 48px;\r\n}\r\n\r\n.chat-container.compact {\r\n    height: 320px;\r\n}\r\n\r\n.chat-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 8px 12px;\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(30,30,40,0.8) 100%);\r\n    border-bottom: 1px solid rgba(255,255,255,0.15);\r\n    height: 48px;\r\n    box-sizing: border-box;\r\n    cursor: pointer;\r\n    user-select: none;\r\n}\r\n\r\n.chat-header:hover {\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(30,30,40,0.9) 100%);\r\n}\r\n\r\n.chat-title-section {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.chat-title {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    font-weight: 600;\r\n    text-shadow: 0 1px 2px rgba(0,0,0,0.5);\r\n}\r\n\r\n.chat-controls {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.control-btn {\r\n    background: rgba(255,255,255,0.1);\r\n    border: none;\r\n    border-radius: 6px;\r\n    width: 28px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    color: #ccc;\r\n}\r\n\r\n.control-btn:hover {\r\n    background: rgba(255,255,255,0.2);\r\n    color: #fff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.control-btn .icon {\r\n    font-size: 12px;\r\n}\r\n\r\n.chat-status {\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #ff4444;\r\n    border-radius: 50%;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 0 8px rgba(255,68,68,0.5);\r\n}\r\n\r\n.chat-status.connected {\r\n    background-color: #4CAF50;\r\n    box-shadow: 0 0 8px rgba(76,175,80,0.5);\r\n}\r\n\r\n.chat-body {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n}\r\n\r\n.chat-channels {\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.3) 0%, rgba(20,20,30,0.4) 100%);\r\n    border-bottom: 1px solid rgba(255,255,255,0.1);\r\n    padding: 4px;\r\n    gap: 2px;\r\n}\r\n\r\n.channel-tab {\r\n    background: none;\r\n    border: none;\r\n    padding: 8px 16px;\r\n    font-size: 12px;\r\n    color: #aaa;\r\n    border-radius: 6px;\r\n    position: relative;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    min-width: 0;\r\n}\r\n\r\n.channel-tab:hover {\r\n    background: rgba(255,255,255,0.1);\r\n    color: #ddd;\r\n}\r\n\r\n.channel-tab.active {\r\n    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);\r\n    color: #fff;\r\n    box-shadow: 0 2px 8px rgba(232,81,28,0.3);\r\n}\r\n\r\n.channel-name {\r\n    white-space: nowrap;\r\n}\r\n\r\n.channel-badge {\r\n    background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);\r\n    color: white;\r\n    border-radius: 12px;\r\n    padding: 2px 6px;\r\n    font-size: 10px;\r\n    font-weight: 600;\r\n    min-width: 16px;\r\n    height: 16px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    line-height: 1;\r\n    box-shadow: 0 2px 4px rgba(255,68,68,0.3);\r\n}\r\n\r\n.chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 8px 12px;\r\n    background: rgba(0,0,0,0.1);\r\n    scrollbar-width: thin;\r\n    scrollbar-color: rgba(255,255,255,0.3) transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 4px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(255,255,255,0.3);\r\n    border-radius: 2px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(255,255,255,0.5);\r\n}\r\n\r\n.chat-message {\r\n    margin-bottom: 8px;\r\n    padding: 6px 8px;\r\n    border-radius: 8px;\r\n    background: rgba(255,255,255,0.02);\r\n    transition: all 0.2s ease;\r\n    border-left: 3px solid transparent;\r\n}\r\n\r\n.chat-message:hover {\r\n    background: rgba(255,255,255,0.05);\r\n}\r\n\r\n.chat-message.compact {\r\n    margin-bottom: 4px;\r\n    padding: 4px 6px;\r\n}\r\n\r\n.message-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 2px;\r\n}\r\n\r\n.message-sender {\r\n    font-size: 11px;\r\n    color: #4fc3f7;\r\n    cursor: pointer;\r\n    font-weight: 600;\r\n    transition: color 0.2s ease;\r\n}\r\n\r\n.message-sender:hover {\r\n    color: #29b6f6;\r\n    text-decoration: underline;\r\n}\r\n\r\n.self-message .message-sender {\r\n    color: #90CAF9;\r\n}\r\n\r\n.npc-message .message-sender {\r\n    color: #a5d6a7;\r\n}\r\n\r\n.player-message {\r\n    border-left-color: #4fc3f7;\r\n}\r\n\r\n.self-message {\r\n    border-left-color: #90CAF9;\r\n    background: rgba(144,202,249,0.05);\r\n}\r\n\r\n.npc-message {\r\n    border-left-color: #a5d6a7;\r\n}\r\n\r\n.message-content {\r\n    font-size: 12px;\r\n    color: #f0f0f0;\r\n    line-height: 1.4;\r\n    word-break: break-word;\r\n    margin: 2px 0;\r\n}\r\n\r\n.message-time {\r\n    font-size: 10px;\r\n    color: #888;\r\n    opacity: 0.7;\r\n}\r\n\r\n.message-time-compact {\r\n    font-size: 9px;\r\n    color: #666;\r\n    text-align: right;\r\n    margin-top: 2px;\r\n}\r\n\r\n.system-message {\r\n    border-left-color: #90caf9;\r\n    background: rgba(144,202,249,0.08);\r\n    font-style: italic;\r\n}\r\n\r\n.system-message .message-content {\r\n    color: #90caf9;\r\n    font-size: 11px;\r\n}\r\n\r\n.empty-messages {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    opacity: 0.6;\r\n    color: #aaa;\r\n    text-align: center;\r\n    padding: 20px;\r\n}\r\n\r\n.empty-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n    opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.empty-hint {\r\n    font-size: 11px;\r\n    opacity: 0.7;\r\n}\r\n\r\n.chat-input-container {\r\n    display: flex;\r\n    padding: 8px 12px;\r\n    border-top: 1px solid rgba(255,255,255,0.15);\r\n    gap: 8px;\r\n    flex-shrink: 0;\r\n    background: rgba(0,0,0,0.2);\r\n}\r\n\r\n.input-wrapper {\r\n    flex: 1;\r\n    position: relative;\r\n}\r\n\r\n.chat-input {\r\n    width: 100%;\r\n    background: rgba(255,255,255,0.1);\r\n    border: 1px solid rgba(255,255,255,0.2);\r\n    border-radius: 8px;\r\n    padding: 8px 12px;\r\n    color: #fff;\r\n    font-size: 12px;\r\n    height: 36px;\r\n    box-sizing: border-box;\r\n    transition: all 0.2s ease;\r\n    outline: none;\r\n}\r\n\r\n.chat-input:focus {\r\n    background: rgba(255,255,255,0.15);\r\n    border-color: #e8511c;\r\n    box-shadow: 0 0 0 2px rgba(232,81,28,0.2);\r\n}\r\n\r\n.chat-input:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n}\r\n\r\n.chat-input::placeholder {\r\n    color: #aaa;\r\n}\r\n\r\n.input-counter {\r\n    position: absolute;\r\n    right: 8px;\r\n    bottom: -16px;\r\n    font-size: 9px;\r\n    color: #888;\r\n}\r\n\r\n.send-btn {\r\n    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 0 16px;\r\n    border-radius: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 4px;\r\n    height: 36px;\r\n    font-size: 11px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    min-width: 60px;\r\n}\r\n\r\n.send-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d64516 0%, #e55a2b 100%);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(232,81,28,0.3);\r\n}\r\n\r\n.send-btn:active:not(:disabled) {\r\n    transform: translateY(0);\r\n}\r\n\r\n.send-btn:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.send-icon {\r\n    font-size: 12px;\r\n}\r\n\r\n.send-text {\r\n    font-size: 11px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .chat-container {\r\n        height: 300px;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 240px;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 6px 10px;\r\n        height: 40px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 24px;\r\n        height: 24px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 6px 12px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .chat-messages {\r\n        padding: 6px 8px;\r\n    }\r\n\r\n    .chat-input-container {\r\n        padding: 6px 8px;\r\n    }\r\n\r\n    .send-btn {\r\n        min-width: 50px;\r\n        padding: 0 12px;\r\n    }\r\n}\r\n</style> "], "mappings": ";;;;AA4FA,OAAAA,aAAA;;AAEA;AACA,MAAAC,QAAA,IACA;EAAAC,EAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA,GACA;EAAAF,EAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA,GACA;EAAAF,EAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA,GACA;EAAAF,EAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA,EACA;AAEA;EACAD,IAAA;EACAE,KAAA;IACAC,aAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAG,gBAAA;MACAL,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAI,KAAA;IACA;MACAC,WAAA;MACAC,QAAA,MAAAd,QAAA;MACAe,mBAAA;MACAC,QAAA;MACAC,UAAA;MACAC,SAAA,OAAAP,gBAAA;MACAQ,YAAA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MACAC,cAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,eAAA;MAAA,IAAAC,qBAAA;MACA,SAAAA,qBAAA,QAAAb,QAAA,MAAAC,mBAAA,eAAAY,qBAAA,uBAAAA,qBAAA,CAAA1B,EAAA;IACA;IACA2B,iBAAA;MACA;MACA,SAAAF,cAAA;QACA,YAAAV,QAAA,CAAAa,MAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAC,OAAA;MACA;;MAEA;MACA,SAAAL,cAAA,oBAAArB,aAAA,CAAA2B,MAAA;QACA,YAAAhB,QAAA,CAAAa,MAAA,CAAAC,GAAA,IACAA,GAAA,CAAAC,OAAA,eACAD,GAAA,CAAAE,MAAA,UAAA3B,aAAA,CAAA2B,MACA;MACA;;MAEA;MACA,YAAAhB,QAAA,CAAAa,MAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAC,OAAA,UAAAL,cAAA;IACA;EACA;EACAO,KAAA;IACA5B,aAAA;MACA6B,QAAAC,OAAA;QACA,IAAAA,OAAA,IAAAA,OAAA,CAAAlC,EAAA,SAAAQ,WAAA;UACA,KAAA2B,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACAtB,oBAAA;MACA;MACA,SAAAD,QAAA,MAAAC,mBAAA;QACA,KAAAD,QAAA,MAAAC,mBAAA,EAAAZ,MAAA;MACA;;MAEA;MACA,KAAAmC,SAAA;QACA,KAAAC,cAAA;MACA;IACA;EACA;EACAC,QAAA;IAAA,IAAAC,mBAAA;IACA;IACA,KAAAA,mBAAA,QAAApC,aAAA,cAAAoC,mBAAA,eAAAA,mBAAA,CAAAxC,EAAA,SAAAQ,WAAA;MACA,KAAA2B,WAAA;IACA;EACA;EACAM,cAAA;IACA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,MAAAR,YAAA;MAAA,IAAAS,oBAAA;MACA,OAAAA,oBAAA,QAAAxC,aAAA,cAAAwC,oBAAA,eAAAA,oBAAA,CAAA5C,EAAA;QACA6C,OAAA,CAAAC,KAAA;QACA;MACA;MAEA;QACA;QACA,MAAAhD,aAAA,CAAAiD,IAAA;;QAEA;QACA,KAAAC,mBAAA;;QAEA;QACA,KAAAC,YAAA;;QAEA;QACA,KAAArC,WAAA;QACA,KAAAM,YAAA;;QAEA;QACA,KAAAgC,gBAAA;MAEA,SAAAJ,KAAA;QACAD,OAAA,CAAAC,KAAA,qBAAAA,KAAA;QAEA,KAAAlC,WAAA;QACA,UAAAM,YAAA;UACA,KAAAA,YAAA;UACA,KAAAgC,gBAAA;QACA;MACA;IACA;IAEAR,eAAA;MACA;MACA,SAAAS,aAAA;QACA,KAAAA,aAAA,CAAAC,OAAA,CAAAC,WAAA,IAAAA,WAAA;QACA,KAAAF,aAAA;MACA;;MAEA;MACArD,aAAA,CAAAwD,UAAA;;MAEA;MACA,KAAA1C,WAAA;IACA;IAEAoC,oBAAA;MACA;MACA,KAAAG,aAAA;;MAEA;MACA,KAAAA,aAAA,CAAAI,IAAA,CACAzD,aAAA,CAAA0D,SAAA;QACA,KAAA5C,WAAA;QACA,KAAAM,YAAA;MACA,EACA;;MAEA;MACA,KAAAiC,aAAA,CAAAI,IAAA,CACAzD,aAAA,CAAA0D,SAAA,eAAAC,MAAA;QACA,KAAA7C,WAAA;QACA,KAAAsC,gBAAA,WAAAO,MAAA;MACA,EACA;;MAEA;MACA,KAAAN,aAAA,CAAAI,IAAA,CACAzD,aAAA,CAAA0D,SAAA,kBAAA7C,IAAA;QAAA,IAAA+C,oBAAA;QACA,KAAAC,iBAAA;UACAtD,IAAA;UACAyB,OAAA;UACA8B,MAAA,EAAAjD,IAAA,CAAAiD,MAAA,CAAA3D,IAAA;UACA4D,OAAA,EAAAlD,IAAA,CAAAmD,OAAA;UACAC,SAAA,EAAApD,IAAA,CAAAoD,SAAA;UACAC,SAAA,EAAArD,IAAA,CAAAiD,MAAA,CAAA5D,EAAA;UACAiE,MAAA,EAAAtD,IAAA,CAAAiD,MAAA,CAAA5D,EAAA,OAAA0D,oBAAA,QAAAtD,aAAA,cAAAsD,oBAAA,uBAAAA,oBAAA,CAAA1D,EAAA;QACA;MACA,EACA;;MAEA;MACA,KAAAmD,aAAA,CAAAI,IAAA,CACAzD,aAAA,CAAA0D,SAAA,iBAAA7C,IAAA;QAAA,IAAAuD,oBAAA;QACA,KAAAP,iBAAA;UACAtD,IAAA;UACAyB,OAAA;UACAC,MAAA,EAAApB,IAAA,CAAAwD,OAAA;UACAP,MAAA,EAAAjD,IAAA,CAAAmD,OAAA,CAAAF,MAAA,CAAA3D,IAAA;UACA4D,OAAA,EAAAlD,IAAA,CAAAmD,OAAA,CAAAA,OAAA;UACAC,SAAA,EAAApD,IAAA,CAAAmD,OAAA,CAAAC,SAAA;UACAC,SAAA,EAAArD,IAAA,CAAAmD,OAAA,CAAAF,MAAA,CAAA5D,EAAA;UACAiE,MAAA,EAAAtD,IAAA,CAAAmD,OAAA,CAAAF,MAAA,CAAA5D,EAAA,OAAAkE,oBAAA,QAAA9D,aAAA,cAAA8D,oBAAA,uBAAAA,oBAAA,CAAAlE,EAAA;QACA;MACA,EACA;;MAEA;MACA,KAAAmD,aAAA,CAAAI,IAAA,CACAzD,aAAA,CAAA0D,SAAA,oBAAA7C,IAAA;QAAA,IAAAyD,oBAAA,EAAAC,oBAAA;QACA,MAAAJ,MAAA,GAAAtD,IAAA,CAAAqD,SAAA,OAAAI,oBAAA,QAAAhE,aAAA,cAAAgE,oBAAA,uBAAAA,oBAAA,CAAApE,EAAA;QACA,MAAA4D,MAAA,GAAAK,MAAA,IAAAI,oBAAA,QAAAjE,aAAA,cAAAiE,oBAAA,uBAAAA,oBAAA,CAAApE,IAAA,GAAAU,IAAA,CAAAmD,OAAA,CAAAF,MAAA,CAAA3D,IAAA;QACA,MAAAqE,UAAA,GAAAL,MAAA,GAAAtD,IAAA,CAAA4D,WAAA,GAAA5D,IAAA,CAAAqD,SAAA;QAEA,KAAAL,iBAAA;UACAtD,IAAA;UACAyB,OAAA;UACA8B,MAAA;UACAC,OAAA,EAAAlD,IAAA,CAAAmD,OAAA,CAAAA,OAAA;UACAC,SAAA,EAAApD,IAAA,CAAAmD,OAAA,CAAAC,SAAA;UACAC,SAAA,EAAArD,IAAA,CAAAmD,OAAA,CAAAF,MAAA,CAAA5D,EAAA;UACAuE,WAAA,EAAAD,UAAA;UACAL;QACA;MACA,EACA;;MAEA;MACA,KAAAd,aAAA,CAAAI,IAAA,CACAzD,aAAA,CAAA0D,SAAA,mBAAA7C,IAAA;QACA,KAAAuC,gBAAA,CAAAvC,IAAA,CAAAmD,OAAA;MACA,EACA;IACA;IAEAb,aAAA;MACA;MACAnD,aAAA,CAAA0E,WAAA;;MAEA;MACA,SAAApE,aAAA,CAAA2B,MAAA;QACAjC,aAAA,CAAA0E,WAAA,cAAApE,aAAA,CAAA2B,MAAA;MACA;;MAEA;MACAjC,aAAA,CAAA0E,WAAA,mBAAApE,aAAA,CAAAJ,EAAA;IACA;IAEA2D,kBAAAc,WAAA;MACA;MACA,KAAA1D,QAAA,CAAAwC,IAAA,CAAAkB,WAAA;;MAEA;MACA,SAAA1D,QAAA,CAAA2D,MAAA;QACA,KAAA3D,QAAA,QAAAA,QAAA,CAAA4D,KAAA;MACA;;MAEA;MACA,IAAAF,WAAA,CAAA3C,OAAA,UAAAL,cAAA;QACA,MAAAmD,YAAA,QAAA/D,QAAA,CAAAgE,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA9E,EAAA,KAAAyE,WAAA,CAAA3C,OAAA;QACA,IAAA8C,YAAA;UACA,KAAA/D,QAAA,CAAA+D,YAAA,EAAA1E,MAAA;QACA;MACA;;MAEA;MACA,KAAAmC,SAAA;QACA,KAAAC,cAAA;MACA;IACA;IAEAY,iBAAAW,OAAA,EAAAE,SAAA;MACA,KAAAhD,QAAA,CAAAwC,IAAA;QACAlD,IAAA;QACAyB,OAAA;QACA8B,MAAA;QACAC,OAAA;QACAE,SAAA,EAAAA,SAAA,IAAAgB,IAAA,CAAAC,GAAA;MACA;;MAEA;MACA,KAAA3C,SAAA;QACA,KAAAC,cAAA;MACA;IACA;IAEA2C,cAAAC,KAAA;MACA,KAAApE,mBAAA,GAAAoE,KAAA;IACA;IAEAC,YAAA;MACA,UAAAvE,WAAA;QACA,KAAAsC,gBAAA;QACA;MACA;MAEA,MAAAY,OAAA,QAAA9C,UAAA,CAAAoE,IAAA;MACA,KAAAtB,OAAA;;MAEA;MACA,MAAAhC,OAAA,QAAAL,cAAA;;MAEA;MACA,IAAA4D,cAAA;QACAvD,OAAA,EAAAA,OAAA;QACAgC,OAAA,EAAAA,OAAA;QACAwB,YAAA,OAAAlF,aAAA,CAAAJ;MACA;MAEA,QAAA8B,OAAA;QACA;UACA,UAAA1B,aAAA,CAAA2B,MAAA;YACA,KAAAmB,gBAAA;YACA;UACA;UACAmC,cAAA,CAAAlB,OAAA,QAAA/D,aAAA,CAAA2B,MAAA;UACA;QAEA;UACA;UACA;UACA,KAAAmB,gBAAA;UACA;MACA;;MAEA;MACApD,aAAA,CAAAyF,eAAA,CAAAF,cAAA;;MAEA;MACA,KAAArE,UAAA;IACA;IAEAwE,kBAAA3D,GAAA;MACA;MACA,IAAAA,GAAA,CAAAxB,IAAA,kBAAAwB,GAAA,CAAAoC,MAAA;QACA,KAAAwB,KAAA;UACAzF,EAAA,EAAA6B,GAAA,CAAAmC,SAAA;UACA/D,IAAA,EAAA4B,GAAA,CAAA+B;QACA;MACA;IACA;IAEAtB,eAAA;MACA,MAAAoD,SAAA,QAAAC,KAAA,CAAAC,qBAAA;MACA,IAAAF,SAAA;QACA;QACA,IAAAA,SAAA,CAAAG,GAAA;UACAH,SAAA,CAAAG,GAAA,CAAAC,SAAA,GAAAJ,SAAA,CAAAG,GAAA,CAAAE,YAAA;QACA,WAAAL,SAAA,CAAAI,SAAA,KAAAE,SAAA;UACAN,SAAA,CAAAI,SAAA,GAAAJ,SAAA,CAAAK,YAAA;QACA;MACA;IACA;IAEAE,eAAA;MACA,KAAAhF,SAAA,SAAAA,SAAA;MAEA,UAAAA,SAAA;QACA;QACA,KAAAoB,SAAA;UACA,KAAAC,cAAA;QACA;MACA;IACA;IAEA4D,WAAAnC,SAAA;MACA,KAAAA,SAAA;MAEA,MAAAoC,IAAA,OAAApB,IAAA,CAAAhB,SAAA;MACA,MAAAiB,GAAA,OAAAD,IAAA;MACA,MAAAqB,KAAA,GAAAD,IAAA,CAAAE,QAAA,GAAAC,QAAA,GAAAC,QAAA;MACA,MAAAC,OAAA,GAAAL,IAAA,CAAAM,UAAA,GAAAH,QAAA,GAAAC,QAAA;;MAEA;MACA,IAAAJ,IAAA,CAAAO,YAAA,OAAA1B,GAAA,CAAA0B,YAAA;QACA,UAAAN,KAAA,IAAAI,OAAA;MACA;;MAEA;MACA,MAAAG,KAAA,IAAAR,IAAA,CAAAS,QAAA,QAAAN,QAAA,GAAAC,QAAA;MACA,MAAAM,GAAA,GAAAV,IAAA,CAAAW,OAAA,GAAAR,QAAA,GAAAC,QAAA;MACA,UAAAI,KAAA,IAAAE,GAAA,IAAAT,KAAA,IAAAI,OAAA;IACA;IAEA;IACAO,kBAAA;MACA,KAAA5F,WAAA,SAAAA,WAAA;MACA,KAAAkB,SAAA;QACA,KAAAC,cAAA;MACA;IACA;IAEA0E,cAAA;MACA,KAAAjG,QAAA;MACA,KAAAmC,gBAAA;IACA;IAEA+D,oBAAA;MACA,MAAAnF,OAAA,QAAAL,cAAA;MACA,MAAAyF,YAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAApF,OAAA;IACA;IAEAqF,aAAA;MACA,KAAA/F,YAAA;IACA;IAEAgG,YAAA;MACA,KAAAhG,YAAA;IACA;IAEA;IACAiG,aAAAvD,OAAA;MACA,IAAAA,OAAA,IAAAA,OAAA,CAAAsB,IAAA;QACA;QACA,MAAAF,KAAA,QAAA5D,cAAA,CAAAgG,OAAA,CAAAxD,OAAA;QACA,IAAAoB,KAAA;UACA,KAAA5D,cAAA,CAAAiG,MAAA,CAAArC,KAAA;QACA;;QAEA;QACA,KAAA5D,cAAA,CAAAkG,OAAA,CAAA1D,OAAA;;QAEA;QACA,SAAAxC,cAAA,CAAAoD,MAAA;UACA,KAAApD,cAAA,QAAAA,cAAA,CAAAqD,KAAA;QACA;MACA;MACA,KAAApD,YAAA;IACA;IAEA;IACA4D,YAAA;MACA,UAAAvE,WAAA;QACA,KAAAsC,gBAAA;QACA;MACA;MAEA,MAAAY,OAAA,QAAA9C,UAAA,CAAAoE,IAAA;MACA,KAAAtB,OAAA;;MAEA;MACA,IAAAA,OAAA,CAAAY,MAAA;QACA,KAAAxB,gBAAA;QACA;MACA;;MAEA;MACA,MAAA8B,GAAA,GAAAD,IAAA,CAAAC,GAAA;MACA,SAAA3D,eAAA,IAAA2D,GAAA,QAAA3D,eAAA;QACA,KAAA6B,gBAAA;QACA;MACA;MACA,KAAA7B,eAAA,GAAA2D,GAAA;;MAEA;MACA,KAAAqC,YAAA,CAAAvD,OAAA;;MAEA;MACA,MAAAhC,OAAA,QAAAL,cAAA;;MAEA;MACA,IAAA4D,cAAA;QACAvD,OAAA,EAAAA,OAAA;QACAgC,OAAA,EAAAA,OAAA;QACAwB,YAAA,OAAAlF,aAAA,CAAAJ;MACA;MAEA,QAAA8B,OAAA;QACA;UACA,UAAA1B,aAAA,CAAA2B,MAAA;YACA,KAAAmB,gBAAA;YACA;UACA;UACAmC,cAAA,CAAAlB,OAAA,QAAA/D,aAAA,CAAA2B,MAAA;UACA;QAEA;UACA;UACA,KAAAmB,gBAAA;UACA;MACA;;MAEA;MACApD,aAAA,CAAAyF,eAAA,CAAAF,cAAA;;MAEA;MACA,KAAArE,UAAA;IACA;EACA;AACA", "ignoreList": []}]}