/**
 * 战斗系统API服务
 * 提供战斗相关的接口调用
 */
import { get, post } from '../request.js';
import cacheService from './cacheService.js';
import logger from '../../utils/logger.js';

// 缓存键
const CACHE_KEYS = {
    ENEMY_LIST: 'enemy_list',
    BATTLE_STATE: 'battle_state',
    BATTLE_ITEMS: 'battle_items'
};

/**
 * 战斗服务
 */
const battleService = {
    /**
     * 获取敌人列表
     * @param {Object} params - 查询参数
     * @param {string} params.area - 区域ID
     * @param {number} params.level - 等级范围
     * @param {string} params.type - 敌人类型
     * @returns {Promise<Object>} - 敌人列表
     */
    getEnemies(params = {}) {
        logger.debug('[BattleService] 获取敌人列表, params:', params);
        
        return get('/battle/enemies', params, {
            loading: true,
            loadingText: '加载敌人列表...'
        }).then(res => {
            logger.debug('[BattleService] 敌人列表响应:', res);
            // 缓存结果
            const cacheKey = `${CACHE_KEYS.ENEMY_LIST}_${JSON.stringify(params)}`;
            cacheService.setCache('battle', cacheKey, res.data, 300000); // 缓存5分钟
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取敌人列表失败:', error);
            throw error;
        });
    },

    /**
     * 开始战斗
     * @param {number} characterId - 角色ID
     * @param {number} monsterId - 怪物ID
     * @param {string} locationId - 位置ID
     * @returns {Promise<Object>} - 战斗初始化结果
     */
    startBattle(characterId, monsterId, locationId = null) {
        logger.debug('[BattleService] 开始战斗, characterId:', characterId, 'monsterId:', monsterId, 'locationId:', locationId);

        return post('/battle/start', {
            character_id: characterId,
            monster_id: monsterId,
            location_id: locationId
        }, {
            loading: true,
            loadingText: '初始化战斗...'
        }).then(res => {
            logger.debug('[BattleService] 开始战斗响应:', res);
            // 缓存战斗状态
            if (res.data && res.data.battle_id) {
                cacheService.setCache('battle', `${CACHE_KEYS.BATTLE_STATE}_${characterId}`, res.data, 1800000); // 缓存30分钟
            }
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 开始战斗失败:', error);
            logger.error('[BattleService] 错误详情:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                config: error.config
            });
            throw error;
        });
    },

    /**
     * 执行战斗行动
     * @param {number} battleId - 战斗ID
     * @param {number} characterId - 角色ID
     * @param {string} action - 行动类型 (attack/flee)
     * @returns {Promise<Object>} - 行动结果
     */
    performAction(battleId, characterId, action) {
        logger.debug('[BattleService] 执行战斗行动, battleId:', battleId, 'characterId:', characterId, 'action:', action);

        return post(`/battle/${battleId}/action`, {
            character_id: characterId,
            action: action
        }, {
            loading: true,
            loadingText: '执行行动中...'
        }).then(res => {
            logger.debug('[BattleService] 战斗行动响应:', res);
            // 更新战斗状态缓存
            if (res.data && res.data.battle) {
                cacheService.setCache('battle', `${CACHE_KEYS.BATTLE_STATE}_${characterId}`, res.data.battle, 1800000);
            }
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 执行战斗行动失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗状态
     * @param {number} battleId - 战斗ID
     * @returns {Promise<Object>} - 战斗状态
     */
    getBattleStatus(battleId) {
        logger.debug('[BattleService] 获取战斗状态, battleId:', battleId);

        return get(`/battle/${battleId}/status`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[BattleService] 战斗状态响应:', res);
            // 缓存战斗状态
            if (res.data && res.data.battle) {
                const characterId = res.data.battle.character.id;
                cacheService.setCache('battle', `${CACHE_KEYS.BATTLE_STATE}_${characterId}`, res.data.battle, 1800000);
            }
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取战斗状态失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗日志
     * @param {number} battleId - 战斗ID
     * @returns {Promise<Object>} - 战斗日志
     */
    getBattleLog(battleId) {
        logger.debug('[BattleService] 获取战斗日志, battleId:', battleId);

        return get(`/battle/${battleId}/log`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[BattleService] 战斗日志响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取战斗日志失败:', error);
            throw error;
        });
    },

    /**
     * 结束战斗
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 战斗结束结果
     */
    endBattle(characterId) {
        logger.debug('[BattleService] 结束战斗, characterId:', characterId);
        
        return post('/battle/end', {
            character_id: characterId
        }, {
            loading: true,
            loadingText: '结束战斗...'
        }).then(res => {
            logger.debug('[BattleService] 结束战斗响应:', res);
            // 清除战斗状态缓存
            cacheService.removeCache('battle', `${CACHE_KEYS.BATTLE_STATE}_${characterId}`);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 结束战斗失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗可用物品
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 战斗物品列表
     */
    getBattleItems(characterId) {
        logger.debug('[BattleService] 获取战斗物品, characterId:', characterId);
        
        return get(`/characters/${characterId}/items/battle`, {}, {
            loading: true,
            loadingText: '加载战斗物品...'
        }).then(res => {
            logger.debug('[BattleService] 战斗物品响应:', res);
            // 缓存结果
            cacheService.setCache('battle', `${CACHE_KEYS.BATTLE_ITEMS}_${characterId}`, res.data, 300000); // 缓存5分钟
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取战斗物品失败:', error);
            throw error;
        });
    },

    /**
     * 逃跑
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 逃跑结果
     */
    flee(characterId) {
        logger.debug('[BattleService] 逃跑, characterId:', characterId);
        
        return post('/battle/flee', {
            character_id: characterId
        }, {
            loading: true,
            loadingText: '逃跑中...'
        }).then(res => {
            logger.debug('[BattleService] 逃跑响应:', res);
            // 清除战斗状态缓存
            cacheService.removeCache('battle', `${CACHE_KEYS.BATTLE_STATE}_${characterId}`);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 逃跑失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗历史
     * @param {string} characterId - 角色ID
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise<Object>} - 战斗历史
     */
    getBattleHistory(characterId, params = {}) {
        logger.debug('[BattleService] 获取战斗历史, characterId:', characterId, 'params:', params);
        
        return get(`/characters/${characterId}/battle/history`, params, {
            loading: true,
            loadingText: '加载战斗历史...'
        }).then(res => {
            logger.debug('[BattleService] 战斗历史响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取战斗历史失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗统计
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 战斗统计
     */
    getBattleStats(characterId) {
        logger.debug('[BattleService] 获取战斗统计, characterId:', characterId);
        
        return get(`/characters/${characterId}/battle/stats`, {}, {
            loading: true,
            loadingText: '加载战斗统计...'
        }).then(res => {
            logger.debug('[BattleService] 战斗统计响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取战斗统计失败:', error);
            throw error;
        });
    },

    /**
     * 获取敌人详情
     * @param {string} enemyId - 敌人ID
     * @returns {Promise<Object>} - 敌人详情
     */
    getEnemyDetails(enemyId) {
        logger.debug('[BattleService] 获取敌人详情, enemyId:', enemyId);
        
        return get(`/battle/enemies/${enemyId}`, {}, {
            loading: true,
            loadingText: '加载敌人详情...'
        }).then(res => {
            logger.debug('[BattleService] 敌人详情响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取敌人详情失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗奖励预览
     * @param {string} enemyId - 敌人ID
     * @returns {Promise<Object>} - 战斗奖励预览
     */
    getBattleRewards(enemyId) {
        logger.debug('[BattleService] 获取战斗奖励预览, enemyId:', enemyId);
        
        return get(`/battle/enemies/${enemyId}/rewards`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[BattleService] 战斗奖励预览响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BattleService] 获取战斗奖励预览失败:', error);
            throw error;
        });
    },

    /**
     * 清除战斗相关的缓存
     * @param {string} characterId - 角色ID
     */
    clearCache(characterId) {
        if (characterId) {
            cacheService.removeCache('battle', `${CACHE_KEYS.BATTLE_STATE}_${characterId}`);
            cacheService.removeCache('battle', `${CACHE_KEYS.BATTLE_ITEMS}_${characterId}`);
        } else {
            // 清除所有战斗缓存
            cacheService.clearCache('battle');
        }
    }
};

export default battleService;
