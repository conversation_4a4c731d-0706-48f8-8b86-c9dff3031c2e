{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\storage.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\storage.js", "mtime": 1749871368643}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["setStorageSync", "key", "data", "value", "JSON", "stringify", "localStorage", "setItem", "e", "console", "error", "getStorageSync", "getItem", "parse", "removeStorageSync", "removeItem", "getStorageInfoSync", "keys", "size", "i", "length", "push", "currentSize", "limitSize", "clearExpiredCache", "startsWith", "for<PERSON>ach", "cacheData", "timestamp", "expireTime", "now", "Date", "log", "cleanupLargeStorageItems", "storageInfo", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearAllCookies", "cookies", "document", "cookie", "split", "eqPos", "indexOf", "name", "substr", "trim", "emergencyCleanupStorage", "sessionStorage", "clear", "gameState", "_state$auth", "state", "minimalState", "auth", "isAuthenticated", "allKeys", "showLoading", "options", "title", "loadingEl", "createElement", "id", "style", "cssText", "content", "innerText", "append<PERSON><PERSON><PERSON>", "body", "hideLoading", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "showToast", "toast", "className", "setTimeout", "opacity", "parentNode", "duration", "vibrateShort", "navigator", "vibrate", "getNetworkType", "callback", "Promise", "resolve", "networkType", "onLine", "onNetworkStatusChange", "handleOnline", "isConnected", "handleOffline", "window", "addEventListener", "removeEventListener", "navigateTo", "url", "path", "replace", "location", "href", "getSystemInfoSync", "ua", "userAgent", "isAndroid", "test", "isIOS", "platform", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "pixelRatio", "devicePixelRatio", "model", "system"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/utils/storage.js"], "sourcesContent": ["/**\r\n * 本地存储工具函数\r\n * 用于替代uni-app的存储API\r\n */\r\n\r\n/**\r\n * 存储数据到localStorage\r\n * @param {string} key - 存储键名\r\n * @param {*} data - 要存储的数据，将自动序列化为JSON\r\n */\r\nexport function setStorageSync(key, data) {\r\n  try {\r\n    const value = typeof data === 'object' ? JSON.stringify(data) : data;\r\n    localStorage.setItem(key, value);\r\n  } catch (e) {\r\n    console.error('Storage error:', e);\r\n  }\r\n}\r\n\r\n/**\r\n * 从localStorage获取数据\r\n * @param {string} key - 存储键名\r\n * @returns {*} 存储的数据，如果是JSON格式会自动解析\r\n */\r\nexport function getStorageSync(key) {\r\n  try {\r\n    const value = localStorage.getItem(key);\r\n    if (!value) return null;\r\n    \r\n    // 尝试解析JSON\r\n    try {\r\n      return JSON.parse(value);\r\n    } catch (e) {\r\n      // 如果不是JSON格式，直接返回原始值\r\n      return value;\r\n    }\r\n  } catch (e) {\r\n    console.error('Storage error:', e);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * 从localStorage删除数据\r\n * @param {string} key - 要删除的存储键名\r\n */\r\nexport function removeStorageSync(key) {\r\n  try {\r\n    localStorage.removeItem(key);\r\n  } catch (e) {\r\n    console.error('Storage error:', e);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取storage信息\r\n * @returns {Object} storage信息，包括keys和大小\r\n */\r\nexport function getStorageInfoSync() {\r\n  try {\r\n    const keys = [];\r\n    let size = 0;\r\n\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      keys.push(key);\r\n      const value = localStorage.getItem(key);\r\n      size += (key.length + (value ? value.length : 0));\r\n    }\r\n\r\n    return {\r\n      keys,\r\n      currentSize: size,\r\n      limitSize: 10240, // 浏览器localStorage限制约为5MB，这里设置一个默认值\r\n    };\r\n  } catch (e) {\r\n    console.error('Storage error:', e);\r\n    return {\r\n      keys: [],\r\n      currentSize: 0,\r\n      limitSize: 10240\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * 清理过期的缓存数据\r\n */\r\nexport function clearExpiredCache() {\r\n  try {\r\n    const keys = [];\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      if (key && key.startsWith('SZXY_CACHE_')) {\r\n        keys.push(key);\r\n      }\r\n    }\r\n\r\n    keys.forEach(key => {\r\n      try {\r\n        const value = localStorage.getItem(key);\r\n        if (value) {\r\n          const cacheData = JSON.parse(value);\r\n          if (cacheData.timestamp && cacheData.expireTime) {\r\n            const now = Date.now();\r\n            if (now - cacheData.timestamp > cacheData.expireTime) {\r\n              localStorage.removeItem(key);\r\n              console.log(`[Storage] 清理过期缓存: ${key}`);\r\n            }\r\n          }\r\n        }\r\n      } catch (e) {\r\n        // 如果解析失败，直接删除\r\n        localStorage.removeItem(key);\r\n        console.log(`[Storage] 清理无效缓存: ${key}`);\r\n      }\r\n    });\r\n  } catch (e) {\r\n    console.error('清理缓存失败:', e);\r\n  }\r\n}\r\n\r\n/**\r\n * 清理大型存储项目以减少请求头大小\r\n */\r\nexport function cleanupLargeStorageItems() {\r\n  try {\r\n    const storageInfo = getStorageInfoSync();\r\n    console.log(`[Storage] 当前存储大小: ${storageInfo.currentSize} bytes`);\r\n\r\n    // 如果存储过大（超过1MB），清理一些非关键数据\r\n    if (storageInfo.currentSize > 1024 * 1024) {\r\n      console.warn('[Storage] 存储空间过大，开始清理...');\r\n\r\n      // 清理过期缓存\r\n      clearExpiredCache();\r\n\r\n      // 清理一些可能很大的非关键数据\r\n      const keysToCheck = [\r\n        'szxy-game-state', // Vuex持久化状态\r\n        'vuex-persist', // 可能的Vuex持久化键\r\n      ];\r\n\r\n      keysToCheck.forEach(key => {\r\n        const value = localStorage.getItem(key);\r\n        if (value && value.length > 100000) { // 超过100KB的项目\r\n          console.warn(`[Storage] 发现大型存储项目: ${key} (${value.length} bytes)`);\r\n          // 可以选择性地清理或压缩数据\r\n        }\r\n      });\r\n    }\r\n  } catch (e) {\r\n    console.error('清理存储失败:', e);\r\n  }\r\n}\r\n\r\n/**\r\n * 清理所有cookies\r\n */\r\nexport function clearAllCookies() {\r\n  try {\r\n    console.warn('[Storage] 清理所有cookies...');\r\n\r\n    // 获取所有cookies\r\n    const cookies = document.cookie.split(';');\r\n\r\n    // 清理每个cookie\r\n    cookies.forEach(cookie => {\r\n      const eqPos = cookie.indexOf('=');\r\n      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();\r\n      if (name) {\r\n        // 设置过期时间为过去的时间来删除cookie\r\n        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\r\n        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=localhost`;\r\n        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.localhost`;\r\n        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=127.0.0.1`;\r\n      }\r\n    });\r\n\r\n    console.log('[Storage] Cookies清理完成');\r\n    return true;\r\n  } catch (e) {\r\n    console.error('[Storage] 清理cookies失败:', e);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * 紧急清理存储空间（用于431错误时）\r\n */\r\nexport function emergencyCleanupStorage() {\r\n  try {\r\n    console.warn('[Storage] 执行紧急存储清理...');\r\n\r\n    // 1. 清理所有cookies\r\n    clearAllCookies();\r\n\r\n    // 2. 清理sessionStorage\r\n    try {\r\n      sessionStorage.clear();\r\n    } catch (e) {\r\n      console.warn('[Storage] 清理sessionStorage失败:', e);\r\n    }\r\n\r\n    // 3. 清理所有缓存\r\n    const keys = [];\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      if (key && (key.startsWith('SZXY_CACHE_') || key.startsWith('cache_'))) {\r\n        keys.push(key);\r\n      }\r\n    }\r\n    keys.forEach(key => localStorage.removeItem(key));\r\n\r\n    // 4. 清理大型Vuex状态，只保留最基本的\r\n    const gameState = localStorage.getItem('szxy-game-state');\r\n    if (gameState) {\r\n      try {\r\n        const state = JSON.parse(gameState);\r\n        // 只保留最基本的认证状态\r\n        const minimalState = {\r\n          auth: {\r\n            isAuthenticated: state.auth?.isAuthenticated || false\r\n          }\r\n        };\r\n        localStorage.setItem('szxy-game-state', JSON.stringify(minimalState));\r\n      } catch (e) {\r\n        // 如果解析失败，直接删除\r\n        localStorage.removeItem('szxy-game-state');\r\n      }\r\n    }\r\n\r\n    // 5. 清理其他可能的大型项目\r\n    const allKeys = [];\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      allKeys.push(localStorage.key(i));\r\n    }\r\n\r\n    allKeys.forEach(key => {\r\n      if (key && key !== 'szxy-game-state') {\r\n        const value = localStorage.getItem(key);\r\n        if (value && value.length > 10000) { // 超过10KB的项目\r\n          console.warn(`[Storage] 紧急清理大型项目: ${key} (${value.length} bytes)`);\r\n          localStorage.removeItem(key);\r\n        }\r\n      }\r\n    });\r\n\r\n    console.log('[Storage] 紧急清理完成');\r\n    return true;\r\n  } catch (e) {\r\n    console.error('[Storage] 紧急清理失败:', e);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * 显示加载提示\r\n * @param {Object} options 配置选项\r\n * @param {string} options.title 提示文字\r\n */\r\nexport function showLoading(options = {}) {\r\n  // 这里可以使用一个自定义的loading组件来替代\r\n  console.log('显示加载中:', options.title || 'Loading...');\r\n  // 可以创建一个简单的loading DOM元素\r\n  const loadingEl = document.createElement('div');\r\n  loadingEl.id = 'app-loading';\r\n  loadingEl.style.cssText = 'position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;z-index:9999;';\r\n  \r\n  const content = document.createElement('div');\r\n  content.style.cssText = 'padding:15px;background:#fff;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);';\r\n  content.innerText = options.title || 'Loading...';\r\n  \r\n  loadingEl.appendChild(content);\r\n  document.body.appendChild(loadingEl);\r\n}\r\n\r\n/**\r\n * 隐藏加载提示\r\n */\r\nexport function hideLoading() {\r\n  // 移除可能存在的loading元素\r\n  const loadingEl = document.getElementById('app-loading');\r\n  if (loadingEl) {\r\n    document.body.removeChild(loadingEl);\r\n  }\r\n}\r\n\r\n/**\r\n * 显示提示信息\r\n * @param {Object} options 配置选项\r\n * @param {string} options.title 提示内容\r\n * @param {string} options.icon 图标类型\r\n * @param {number} options.duration 显示时长，默认1500ms\r\n */\r\nexport function showToast(options = {}) {\r\n  const toast = document.createElement('div');\r\n  toast.className = 'app-toast';\r\n  toast.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);padding:10px 20px;background:rgba(0,0,0,0.7);color:#fff;border-radius:4px;z-index:10000;transition:opacity 0.3s;';\r\n  toast.innerText = options.title || '';\r\n  \r\n  document.body.appendChild(toast);\r\n  \r\n  // 淡入\r\n  setTimeout(() => {\r\n    toast.style.opacity = '1';\r\n  }, 0);\r\n  \r\n  // 淡出并移除\r\n  setTimeout(() => {\r\n    toast.style.opacity = '0';\r\n    setTimeout(() => {\r\n      if (toast.parentNode) {\r\n        document.body.removeChild(toast);\r\n      }\r\n    }, 300);\r\n  }, options.duration || 1500);\r\n}\r\n\r\n/**\r\n * 一个简单的振动API封装\r\n */\r\nexport function vibrateShort() {\r\n  if (navigator.vibrate) {\r\n    navigator.vibrate(20);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取网络类型\r\n * @param {Function} callback 回调函数\r\n */\r\nexport function getNetworkType(callback) {\r\n  // 使用Promise模拟异步API\r\n  return new Promise((resolve) => {\r\n    // 浏览器环境下，无法准确获取网络类型，只能判断是否在线\r\n    const networkType = navigator.onLine ? 'wifi' : 'none';\r\n    \r\n    if (callback) {\r\n      callback({ networkType });\r\n    }\r\n    \r\n    resolve([null, { networkType }]);\r\n  });\r\n}\r\n\r\n/**\r\n * 监听网络状态变化\r\n * @param {Function} callback 回调函数\r\n */\r\nexport function onNetworkStatusChange(callback) {\r\n  const handleOnline = () => {\r\n    callback && callback({\r\n      isConnected: true,\r\n      networkType: 'wifi' // 浏览器环境中只能模拟\r\n    });\r\n  };\r\n  \r\n  const handleOffline = () => {\r\n    callback && callback({\r\n      isConnected: false,\r\n      networkType: 'none'\r\n    });\r\n  };\r\n  \r\n  window.addEventListener('online', handleOnline);\r\n  window.addEventListener('offline', handleOffline);\r\n  \r\n  // 返回取消监听的函数\r\n  return () => {\r\n    window.removeEventListener('online', handleOnline);\r\n    window.removeEventListener('offline', handleOffline);\r\n  };\r\n}\r\n\r\n/**\r\n * 路由跳转\r\n * @param {Object} options 配置选项\r\n * @param {string} options.url 目标路由\r\n */\r\nexport function navigateTo(options) {\r\n  if (options && options.url) {\r\n    // 转换uni-app路径格式为vue-router格式\r\n    const path = options.url.replace(/^\\/pages/, '').replace(/\\/([^/]+)$/, '/$1');\r\n    window.location.href = path;\r\n  }\r\n}\r\n\r\n/**\r\n * 获取系统信息\r\n * @returns {Object} 系统信息\r\n */\r\nexport function getSystemInfoSync() {\r\n  const ua = navigator.userAgent;\r\n  const isAndroid = /android/i.test(ua);\r\n  const isIOS = /iphone|ipad|ipod/i.test(ua);\r\n  \r\n  return {\r\n    platform: isAndroid ? 'android' : (isIOS ? 'ios' : 'unknown'),\r\n    windowWidth: window.innerWidth,\r\n    windowHeight: window.innerHeight,\r\n    pixelRatio: window.devicePixelRatio || 1,\r\n    model: ua,\r\n    system: isAndroid ? 'Android' : (isIOS ? 'iOS' : navigator.platform)\r\n  };\r\n}\r\n\r\nexport default {\r\n  setStorageSync,\r\n  getStorageSync,\r\n  removeStorageSync,\r\n  getStorageInfoSync,\r\n  showLoading,\r\n  hideLoading,\r\n  showToast,\r\n  vibrateShort,\r\n  getNetworkType,\r\n  onNetworkStatusChange,\r\n  navigateTo,\r\n  getSystemInfoSync\r\n}; "], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACxC,IAAI;IACF,MAAMC,KAAK,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,GAAGA,IAAI;IACpEI,YAAY,CAACC,OAAO,CAACN,GAAG,EAAEE,KAAK,CAAC;EAClC,CAAC,CAAC,OAAOK,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEF,CAAC,CAAC;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAACV,GAAG,EAAE;EAClC,IAAI;IACF,MAAME,KAAK,GAAGG,YAAY,CAACM,OAAO,CAACX,GAAG,CAAC;IACvC,IAAI,CAACE,KAAK,EAAE,OAAO,IAAI;;IAEvB;IACA,IAAI;MACF,OAAOC,IAAI,CAACS,KAAK,CAACV,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOK,CAAC,EAAE;MACV;MACA,OAAOL,KAAK;IACd;EACF,CAAC,CAAC,OAAOK,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEF,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASM,iBAAiBA,CAACb,GAAG,EAAE;EACrC,IAAI;IACFK,YAAY,CAACS,UAAU,CAACd,GAAG,CAAC;EAC9B,CAAC,CAAC,OAAOO,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEF,CAAC,CAAC;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,kBAAkBA,CAAA,EAAG;EACnC,IAAI;IACF,MAAMC,IAAI,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,CAAC;IAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,MAAMlB,GAAG,GAAGK,YAAY,CAACL,GAAG,CAACkB,CAAC,CAAC;MAC/BF,IAAI,CAACI,IAAI,CAACpB,GAAG,CAAC;MACd,MAAME,KAAK,GAAGG,YAAY,CAACM,OAAO,CAACX,GAAG,CAAC;MACvCiB,IAAI,IAAKjB,GAAG,CAACmB,MAAM,IAAIjB,KAAK,GAAGA,KAAK,CAACiB,MAAM,GAAG,CAAC,CAAE;IACnD;IAEA,OAAO;MACLH,IAAI;MACJK,WAAW,EAAEJ,IAAI;MACjBK,SAAS,EAAE,KAAK,CAAE;IACpB,CAAC;EACH,CAAC,CAAC,OAAOf,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEF,CAAC,CAAC;IAClC,OAAO;MACLS,IAAI,EAAE,EAAE;MACRK,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;IACb,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,IAAI;IACF,MAAMP,IAAI,GAAG,EAAE;IACf,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,MAAMlB,GAAG,GAAGK,YAAY,CAACL,GAAG,CAACkB,CAAC,CAAC;MAC/B,IAAIlB,GAAG,IAAIA,GAAG,CAACwB,UAAU,CAAC,aAAa,CAAC,EAAE;QACxCR,IAAI,CAACI,IAAI,CAACpB,GAAG,CAAC;MAChB;IACF;IAEAgB,IAAI,CAACS,OAAO,CAACzB,GAAG,IAAI;MAClB,IAAI;QACF,MAAME,KAAK,GAAGG,YAAY,CAACM,OAAO,CAACX,GAAG,CAAC;QACvC,IAAIE,KAAK,EAAE;UACT,MAAMwB,SAAS,GAAGvB,IAAI,CAACS,KAAK,CAACV,KAAK,CAAC;UACnC,IAAIwB,SAAS,CAACC,SAAS,IAAID,SAAS,CAACE,UAAU,EAAE;YAC/C,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;YACtB,IAAIA,GAAG,GAAGH,SAAS,CAACC,SAAS,GAAGD,SAAS,CAACE,UAAU,EAAE;cACpDvB,YAAY,CAACS,UAAU,CAACd,GAAG,CAAC;cAC5BQ,OAAO,CAACuB,GAAG,CAAC,qBAAqB/B,GAAG,EAAE,CAAC;YACzC;UACF;QACF;MACF,CAAC,CAAC,OAAOO,CAAC,EAAE;QACV;QACAF,YAAY,CAACS,UAAU,CAACd,GAAG,CAAC;QAC5BQ,OAAO,CAACuB,GAAG,CAAC,qBAAqB/B,GAAG,EAAE,CAAC;MACzC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOO,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEF,CAAC,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASyB,wBAAwBA,CAAA,EAAG;EACzC,IAAI;IACF,MAAMC,WAAW,GAAGlB,kBAAkB,CAAC,CAAC;IACxCP,OAAO,CAACuB,GAAG,CAAC,qBAAqBE,WAAW,CAACZ,WAAW,QAAQ,CAAC;;IAEjE;IACA,IAAIY,WAAW,CAACZ,WAAW,GAAG,IAAI,GAAG,IAAI,EAAE;MACzCb,OAAO,CAAC0B,IAAI,CAAC,0BAA0B,CAAC;;MAExC;MACAX,iBAAiB,CAAC,CAAC;;MAEnB;MACA,MAAMY,WAAW,GAAG,CAClB,iBAAiB;MAAE;MACnB,cAAc,CAAE;MAAA,CACjB;MAEDA,WAAW,CAACV,OAAO,CAACzB,GAAG,IAAI;QACzB,MAAME,KAAK,GAAGG,YAAY,CAACM,OAAO,CAACX,GAAG,CAAC;QACvC,IAAIE,KAAK,IAAIA,KAAK,CAACiB,MAAM,GAAG,MAAM,EAAE;UAAE;UACpCX,OAAO,CAAC0B,IAAI,CAAC,uBAAuBlC,GAAG,KAAKE,KAAK,CAACiB,MAAM,SAAS,CAAC;UAClE;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,OAAOZ,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEF,CAAC,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAAS6B,eAAeA,CAAA,EAAG;EAChC,IAAI;IACF5B,OAAO,CAAC0B,IAAI,CAAC,0BAA0B,CAAC;;IAExC;IACA,MAAMG,OAAO,GAAGC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;;IAE1C;IACAH,OAAO,CAACZ,OAAO,CAACc,MAAM,IAAI;MACxB,MAAME,KAAK,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;MACjC,MAAMC,IAAI,GAAGF,KAAK,GAAG,CAAC,CAAC,GAAGF,MAAM,CAACK,MAAM,CAAC,CAAC,EAAEH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC,GAAGN,MAAM,CAACM,IAAI,CAAC,CAAC;MACxE,IAAIF,IAAI,EAAE;QACR;QACAL,QAAQ,CAACC,MAAM,GAAG,GAAGI,IAAI,gDAAgD;QACzEL,QAAQ,CAACC,MAAM,GAAG,GAAGI,IAAI,iEAAiE;QAC1FL,QAAQ,CAACC,MAAM,GAAG,GAAGI,IAAI,kEAAkE;QAC3FL,QAAQ,CAACC,MAAM,GAAG,GAAGI,IAAI,iEAAiE;MAC5F;IACF,CAAC,CAAC;IAEFnC,OAAO,CAACuB,GAAG,CAAC,uBAAuB,CAAC;IACpC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOxB,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEF,CAAC,CAAC;IAC1C,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASuC,uBAAuBA,CAAA,EAAG;EACxC,IAAI;IACFtC,OAAO,CAAC0B,IAAI,CAAC,uBAAuB,CAAC;;IAErC;IACAE,eAAe,CAAC,CAAC;;IAEjB;IACA,IAAI;MACFW,cAAc,CAACC,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOzC,CAAC,EAAE;MACVC,OAAO,CAAC0B,IAAI,CAAC,+BAA+B,EAAE3B,CAAC,CAAC;IAClD;;IAEA;IACA,MAAMS,IAAI,GAAG,EAAE;IACf,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,MAAMlB,GAAG,GAAGK,YAAY,CAACL,GAAG,CAACkB,CAAC,CAAC;MAC/B,IAAIlB,GAAG,KAAKA,GAAG,CAACwB,UAAU,CAAC,aAAa,CAAC,IAAIxB,GAAG,CAACwB,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE;QACtER,IAAI,CAACI,IAAI,CAACpB,GAAG,CAAC;MAChB;IACF;IACAgB,IAAI,CAACS,OAAO,CAACzB,GAAG,IAAIK,YAAY,CAACS,UAAU,CAACd,GAAG,CAAC,CAAC;;IAEjD;IACA,MAAMiD,SAAS,GAAG5C,YAAY,CAACM,OAAO,CAAC,iBAAiB,CAAC;IACzD,IAAIsC,SAAS,EAAE;MACb,IAAI;QAAA,IAAAC,WAAA;QACF,MAAMC,KAAK,GAAGhD,IAAI,CAACS,KAAK,CAACqC,SAAS,CAAC;QACnC;QACA,MAAMG,YAAY,GAAG;UACnBC,IAAI,EAAE;YACJC,eAAe,EAAE,EAAAJ,WAAA,GAAAC,KAAK,CAACE,IAAI,cAAAH,WAAA,uBAAVA,WAAA,CAAYI,eAAe,KAAI;UAClD;QACF,CAAC;QACDjD,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAEH,IAAI,CAACC,SAAS,CAACgD,YAAY,CAAC,CAAC;MACvE,CAAC,CAAC,OAAO7C,CAAC,EAAE;QACV;QACAF,YAAY,CAACS,UAAU,CAAC,iBAAiB,CAAC;MAC5C;IACF;;IAEA;IACA,MAAMyC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5CqC,OAAO,CAACnC,IAAI,CAACf,YAAY,CAACL,GAAG,CAACkB,CAAC,CAAC,CAAC;IACnC;IAEAqC,OAAO,CAAC9B,OAAO,CAACzB,GAAG,IAAI;MACrB,IAAIA,GAAG,IAAIA,GAAG,KAAK,iBAAiB,EAAE;QACpC,MAAME,KAAK,GAAGG,YAAY,CAACM,OAAO,CAACX,GAAG,CAAC;QACvC,IAAIE,KAAK,IAAIA,KAAK,CAACiB,MAAM,GAAG,KAAK,EAAE;UAAE;UACnCX,OAAO,CAAC0B,IAAI,CAAC,uBAAuBlC,GAAG,KAAKE,KAAK,CAACiB,MAAM,SAAS,CAAC;UAClEd,YAAY,CAACS,UAAU,CAACd,GAAG,CAAC;QAC9B;MACF;IACF,CAAC,CAAC;IAEFQ,OAAO,CAACuB,GAAG,CAAC,kBAAkB,CAAC;IAC/B,OAAO,IAAI;EACb,CAAC,CAAC,OAAOxB,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,mBAAmB,EAAEF,CAAC,CAAC;IACrC,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiD,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EACxC;EACAjD,OAAO,CAACuB,GAAG,CAAC,QAAQ,EAAE0B,OAAO,CAACC,KAAK,IAAI,YAAY,CAAC;EACpD;EACA,MAAMC,SAAS,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,KAAK,CAAC;EAC/CD,SAAS,CAACE,EAAE,GAAG,aAAa;EAC5BF,SAAS,CAACG,KAAK,CAACC,OAAO,GAAG,8IAA8I;EAExK,MAAMC,OAAO,GAAG1B,QAAQ,CAACsB,aAAa,CAAC,KAAK,CAAC;EAC7CI,OAAO,CAACF,KAAK,CAACC,OAAO,GAAG,uFAAuF;EAC/GC,OAAO,CAACC,SAAS,GAAGR,OAAO,CAACC,KAAK,IAAI,YAAY;EAEjDC,SAAS,CAACO,WAAW,CAACF,OAAO,CAAC;EAC9B1B,QAAQ,CAAC6B,IAAI,CAACD,WAAW,CAACP,SAAS,CAAC;AACtC;;AAEA;AACA;AACA;AACA,OAAO,SAASS,WAAWA,CAAA,EAAG;EAC5B;EACA,MAAMT,SAAS,GAAGrB,QAAQ,CAAC+B,cAAc,CAAC,aAAa,CAAC;EACxD,IAAIV,SAAS,EAAE;IACbrB,QAAQ,CAAC6B,IAAI,CAACG,WAAW,CAACX,SAAS,CAAC;EACtC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,SAASA,CAACd,OAAO,GAAG,CAAC,CAAC,EAAE;EACtC,MAAMe,KAAK,GAAGlC,QAAQ,CAACsB,aAAa,CAAC,KAAK,CAAC;EAC3CY,KAAK,CAACC,SAAS,GAAG,WAAW;EAC7BD,KAAK,CAACV,KAAK,CAACC,OAAO,GAAG,iLAAiL;EACvMS,KAAK,CAACP,SAAS,GAAGR,OAAO,CAACC,KAAK,IAAI,EAAE;EAErCpB,QAAQ,CAAC6B,IAAI,CAACD,WAAW,CAACM,KAAK,CAAC;;EAEhC;EACAE,UAAU,CAAC,MAAM;IACfF,KAAK,CAACV,KAAK,CAACa,OAAO,GAAG,GAAG;EAC3B,CAAC,EAAE,CAAC,CAAC;;EAEL;EACAD,UAAU,CAAC,MAAM;IACfF,KAAK,CAACV,KAAK,CAACa,OAAO,GAAG,GAAG;IACzBD,UAAU,CAAC,MAAM;MACf,IAAIF,KAAK,CAACI,UAAU,EAAE;QACpBtC,QAAQ,CAAC6B,IAAI,CAACG,WAAW,CAACE,KAAK,CAAC;MAClC;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAEf,OAAO,CAACoB,QAAQ,IAAI,IAAI,CAAC;AAC9B;;AAEA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC7B,IAAIC,SAAS,CAACC,OAAO,EAAE;IACrBD,SAAS,CAACC,OAAO,CAAC,EAAE,CAAC;EACvB;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAE;EACvC;EACA,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B;IACA,MAAMC,WAAW,GAAGN,SAAS,CAACO,MAAM,GAAG,MAAM,GAAG,MAAM;IAEtD,IAAIJ,QAAQ,EAAE;MACZA,QAAQ,CAAC;QAAEG;MAAY,CAAC,CAAC;IAC3B;IAEAD,OAAO,CAAC,CAAC,IAAI,EAAE;MAAEC;IAAY,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,qBAAqBA,CAACL,QAAQ,EAAE;EAC9C,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBN,QAAQ,IAAIA,QAAQ,CAAC;MACnBO,WAAW,EAAE,IAAI;MACjBJ,WAAW,EAAE,MAAM,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BR,QAAQ,IAAIA,QAAQ,CAAC;MACnBO,WAAW,EAAE,KAAK;MAClBJ,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAEDM,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EAC/CG,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEF,aAAa,CAAC;;EAEjD;EACA,OAAO,MAAM;IACXC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAClDG,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEH,aAAa,CAAC;EACtD,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,UAAUA,CAACrC,OAAO,EAAE;EAClC,IAAIA,OAAO,IAAIA,OAAO,CAACsC,GAAG,EAAE;IAC1B;IACA,MAAMC,IAAI,GAAGvC,OAAO,CAACsC,GAAG,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;IAC7EN,MAAM,CAACO,QAAQ,CAACC,IAAI,GAAGH,IAAI;EAC7B;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,iBAAiBA,CAAA,EAAG;EAClC,MAAMC,EAAE,GAAGtB,SAAS,CAACuB,SAAS;EAC9B,MAAMC,SAAS,GAAG,UAAU,CAACC,IAAI,CAACH,EAAE,CAAC;EACrC,MAAMI,KAAK,GAAG,mBAAmB,CAACD,IAAI,CAACH,EAAE,CAAC;EAE1C,OAAO;IACLK,QAAQ,EAAEH,SAAS,GAAG,SAAS,GAAIE,KAAK,GAAG,KAAK,GAAG,SAAU;IAC7DE,WAAW,EAAEhB,MAAM,CAACiB,UAAU;IAC9BC,YAAY,EAAElB,MAAM,CAACmB,WAAW;IAChCC,UAAU,EAAEpB,MAAM,CAACqB,gBAAgB,IAAI,CAAC;IACxCC,KAAK,EAAEZ,EAAE;IACTa,MAAM,EAAEX,SAAS,GAAG,SAAS,GAAIE,KAAK,GAAG,KAAK,GAAG1B,SAAS,CAAC2B;EAC7D,CAAC;AACH;AAEA,eAAe;EACb3G,cAAc;EACdW,cAAc;EACdG,iBAAiB;EACjBE,kBAAkB;EAClByC,WAAW;EACXY,WAAW;EACXG,SAAS;EACTO,YAAY;EACZG,cAAc;EACdM,qBAAqB;EACrBO,UAAU;EACVM;AACF,CAAC", "ignoreList": []}]}