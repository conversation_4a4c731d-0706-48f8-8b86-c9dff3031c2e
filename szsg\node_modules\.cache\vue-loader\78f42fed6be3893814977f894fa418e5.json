{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue?vue&type=style&index=0&id=9f58988a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue", "mtime": 1749890706309}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GameLayout.vue"], "names": [], "mappings": ";AAm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file": "GameLayout.vue", "sourceRoot": "src/layouts", "sourcesContent": ["<template>\r\n  <div class=\"game-layout-container\">\r\n    <!-- 动态背景效果 -->\r\n    <div class=\"background-effects\">\r\n      <div class=\"floating-particles\">\r\n        <div v-for=\"i in 20\" :key=\"i\" class=\"particle\" :style=\"getParticleStyle(i)\"></div>\r\n      </div>\r\n      <div class=\"background-gradient\"></div>\r\n    </div>\r\n\r\n    <!-- 顶部装饰边框 -->\r\n    <div class=\"top-border-frame\" :style=\"{ backgroundImage: `url(${topBorderImage})` }\">\r\n      <div class=\"top-border-overlay\">\r\n        <div class=\"border-content\">\r\n          <!-- 页面标题和地图名 -->\r\n          <div class=\"page-title-container\">\r\n            <div class=\"page-title\">{{ pageTitle }}</div>\r\n            <div v-if=\"showLocationName && currentLocationName\" class=\"location-name\">\r\n              <span class=\"location-text\">{{ currentLocationName }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 左右固定边框 -->\r\n    <div class=\"left-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n    <div class=\"right-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n\r\n    <!-- 页面实际内容插入点 -->\r\n    <div class=\"main-content-area\">\r\n      <div class=\"content-wrapper\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部装饰边框 -->\r\n    <div class=\"bottom-border-frame\">\r\n      <div class=\"footer-decoration\">\r\n        <div class=\"footer-pattern\"></div>\r\n        <div class=\"footer-text\">\r\n          <span class=\"version-info\">Version 1.0.0</span>\r\n          <span class=\"copyright\">© 2025 神之西游</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载遮罩 -->\r\n    <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n      <div class=\"loading-content\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <div class=\"loading-text\">{{ loadingText || '加载中...' }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logger from '@/utils/logger';\r\n\r\nexport default {\r\n  name: 'GameLayout',\r\n  props: {\r\n    // 页面类型：'setup', 'game', 'main', 'battle' 等\r\n    pageType: {\r\n      type: String,\r\n      default: 'main'\r\n    },\r\n    // 自定义页面标题\r\n    customTitle: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 是否显示装饰元素\r\n    showDecorations: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isLoading: false,\r\n      loadingText: ''\r\n    };\r\n  },\r\n  computed: {\r\n    // 从store获取全局加载状态\r\n    globalLoading() {\r\n      return this.$store.state.isLoading;\r\n    },\r\n\r\n    // 根据页面类型或路由获取顶部边框图片\r\n    topBorderImage() {\r\n      // 如果有自定义标题，优先使用pageType\r\n      if (this.pageType && this.pageType !== 'main') {\r\n        return this.getImageByPageType(this.pageType);\r\n      }\r\n\r\n      // 根据当前路由自动判断\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '/static/game/UI/bj/xuanzefenqu.png';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '/static/game/UI/bj/xuanzejuese.png';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '/static/game/UI/bj/chuangjian.png';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '/static/game/UI/bj/top.jpg';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '/static/game/UI/bj/zhandou.png';\r\n      }\r\n\r\n      // 默认主页面\r\n      return '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 页面标题\r\n    pageTitle() {\r\n      if (this.customTitle) {\r\n        return this.customTitle;\r\n      }\r\n\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '选择大区';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '选择角色';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '创建角色';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '战斗场景';\r\n      }\r\n\r\n      return '神之西游';\r\n    },\r\n\r\n    // 是否显示地图名\r\n    showLocationName() {\r\n      const currentPath = this.$route.path;\r\n      return currentPath.includes('/game');\r\n    },\r\n\r\n    // 当前地图名\r\n    currentLocationName() {\r\n      try {\r\n        // 从store获取当前位置信息\r\n        const currentLocation = this.$store.state.map?.currentLocation;\r\n        if (currentLocation && currentLocation.name) {\r\n          return currentLocation.name;\r\n        }\r\n\r\n        // 如果store中没有数据，返回默认值\r\n        return '未知位置';\r\n      } catch (error) {\r\n        logger.error('[GameLayout] 获取当前位置失败:', error);\r\n        return '未知位置';\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    globalLoading(newVal) {\r\n      this.isLoading = newVal;\r\n    }\r\n  },\r\n  created() {\r\n    logger.debug('[GameLayout] 初始化');\r\n\r\n    // 获取当前页面路径\r\n    const currentPath = this.$route.path;\r\n    logger.debug('[GameLayout] 当前路径:', currentPath);\r\n\r\n    // 设置页面列表（这些页面只需要验证登录，不需要验证角色选择）\r\n    const setupPages = [\r\n      '/setup/region-select',\r\n      '/setup/character-select',\r\n      '/setup/create-character'\r\n    ];\r\n\r\n    // 检查认证状态（路由守卫已经处理了基本的登录检查，这里只做额外检查）\r\n    try {\r\n      const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');\r\n      const storeAuth = this.$store.state.auth?.isAuthenticated;\r\n      const isAuthenticated = storeAuth || !!authToken;\r\n\r\n      // 如果没有认证信息，让路由守卫处理重定向\r\n      if (!isAuthenticated) {\r\n        logger.debug('[GameLayout] 未登录，由路由守卫处理');\r\n        return;\r\n      }\r\n\r\n      logger.debug('[GameLayout] 已登录，继续检查角色状态');\r\n\r\n      // 只有非设置页面才需要检查角色选择状态\r\n      const isSetupPage = setupPages.some(page => currentPath.includes(page));\r\n      if (!isSetupPage) {\r\n        const selectedCharacter = localStorage.getItem('selectedCharacter');\r\n        if (!selectedCharacter) {\r\n          logger.debug('[GameLayout] 未选择角色，重定向到区域选择页');\r\n          this.showToast('请先选择角色');\r\n          // 使用nextTick避免在created中立即重定向\r\n          this.$nextTick(() => {\r\n            this.$router.replace('/setup/region-select');\r\n          });\r\n          return;\r\n        }\r\n      }\r\n\r\n      logger.debug('[GameLayout] 认证检查通过，路径:', currentPath);\r\n    } catch (error) {\r\n      logger.error('[GameLayout] 认证检查失败:', error);\r\n    }\r\n  },\r\n  methods: {\r\n    // 根据页面类型获取图片\r\n    getImageByPageType(pageType) {\r\n      const imageMap = {\r\n        'setup': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'region-select': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'character-select': '/static/game/UI/bj/xuanzejuese.png',\r\n        'create-character': '/static/game/UI/bj/chuangjian.png',\r\n        'game': '/static/game/UI/bj/youxi.png',\r\n        'battle': '/static/game/UI/bj/zhandou.png',\r\n        'main': '/static/game/UI/bj/szxy_1.png'\r\n      };\r\n      return imageMap[pageType] || '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 获取粒子样式\r\n    getParticleStyle() {\r\n      const delay = Math.random() * 20;\r\n      const duration = 15 + Math.random() * 10;\r\n      const size = 2 + Math.random() * 4;\r\n      const left = Math.random() * 100;\r\n\r\n      return {\r\n        left: `${left}%`,\r\n        animationDelay: `${delay}s`,\r\n        animationDuration: `${duration}s`,\r\n        width: `${size}px`,\r\n        height: `${size}px`\r\n      };\r\n    },\r\n\r\n    // 显示提示框\r\n    showToast(message, duration = 2000) {\r\n      logger.debug('[GameLayout] 显示提示:', message);\r\n      const toast = document.createElement('div');\r\n      toast.innerHTML = message;\r\n      toast.style.cssText = `\r\n        position: fixed;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        background: linear-gradient(135deg, rgba(212, 175, 55, 0.9), rgba(184, 148, 31, 0.9));\r\n        color: #000;\r\n        padding: 15px 25px;\r\n        border-radius: 8px;\r\n        border: 2px solid #d4af37;\r\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.3);\r\n        z-index: 10000;\r\n        font-weight: bold;\r\n        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);\r\n        animation: toastSlideIn 0.3s ease-out;\r\n      `;\r\n\r\n      // 添加动画样式\r\n      const style = document.createElement('style');\r\n      style.textContent = `\r\n        @keyframes toastSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translate(-50%, -50%) scale(0.8);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translate(-50%, -50%) scale(1);\r\n          }\r\n        }\r\n      `;\r\n      document.head.appendChild(style);\r\n\r\n      document.body.appendChild(toast);\r\n      setTimeout(() => {\r\n        toast.style.animation = 'toastSlideIn 0.3s ease-out reverse';\r\n        setTimeout(() => {\r\n          if (document.body.contains(toast)) {\r\n            document.body.removeChild(toast);\r\n          }\r\n          if (document.head.contains(style)) {\r\n            document.head.removeChild(style);\r\n          }\r\n        }, 300);\r\n      }, duration);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 应用全局背景和基本布局 */\r\n.game-layout-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  box-sizing: border-box;\r\n  background:\r\n    linear-gradient(135deg, rgba(10, 10, 10, 0.3) 0%, rgba(26, 26, 46, 0.2) 50%, rgba(22, 33, 62, 0.3) 100%),\r\n    url('/static/game/UI/bj/zise.png');\r\n  background-size: cover, cover;\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-attachment: fixed;\r\n  overflow-x: hidden;\r\n\r\n  /* 为边框留出空间 */\r\n  padding: 70px 40px 50px 30px;\r\n}\r\n\r\n/* 动态背景效果 */\r\n.background-effects {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: -1;\r\n}\r\n\r\n.background-gradient {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background:\r\n    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),\r\n    linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.02) 50%, transparent 70%);\r\n  animation: gradientShift 20s ease-in-out infinite;\r\n}\r\n\r\n@keyframes gradientShift {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n/* 浮动粒子效果 */\r\n.floating-particles {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  background: #d4af37;\r\n  border-radius: 50%;\r\n  opacity: 0.6;\r\n  animation: float-up linear infinite;\r\n}\r\n\r\n@keyframes float-up {\r\n  0% {\r\n    transform: translateY(100vh) rotate(0deg);\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 0.6;\r\n  }\r\n  90% {\r\n    opacity: 0.6;\r\n  }\r\n  100% {\r\n    transform: translateY(-100px) rotate(360deg);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 顶部边框装饰 */\r\n.top-border-frame {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 60px;\r\n  z-index: 1500;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.top-border-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.border-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 页面标题容器 */\r\n.page-title-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 26px;\r\n  font-weight: bold;\r\n  color: #d4af37;\r\n  text-shadow:\r\n    2px 2px 4px rgba(0, 0, 0, 0.8),\r\n    0 0 15px rgba(212, 175, 55, 0.6);\r\n  letter-spacing: 2px;\r\n}\r\n\r\n/* 地图位置显示 */\r\n.location-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  padding: 6px 16px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(212, 175, 55, 0.4);\r\n  backdrop-filter: blur(8px);\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.location-label {\r\n  color: #ccc;\r\n  font-size: 16px;\r\n}\r\n\r\n.location-text {\r\n  color: #ffd700;\r\n  font-weight: bold;\r\n  font-size: 22px;\r\n  text-shadow:\r\n    1px 1px 2px rgba(0, 0, 0, 0.8),\r\n    0 0 8px rgba(255, 215, 0, 0.4);\r\n}\r\n\r\n\r\n\r\n@keyframes sparkle {\r\n  0%, 100% { opacity: 0.7; transform: scale(1); }\r\n  50% { opacity: 1; transform: scale(1.1); }\r\n}\r\n\r\n/* 左右边框样式 */\r\n.left-border-frame,\r\n.right-border-frame {\r\n  position: fixed;\r\n  top: 60px;\r\n  bottom: 40px;\r\n  z-index: 1400;\r\n  pointer-events: none;\r\n}\r\n\r\n.left-border-frame {\r\n  left: 0;\r\n  width: 30px;\r\n  background: url('/static/game/UI/bk/gnl_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.right-border-frame {\r\n  right: 0;\r\n  width: 40px;\r\n  background: url('/static/game/UI/bk/gnl1_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.border-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: repeating-linear-gradient(\r\n    0deg,\r\n    transparent,\r\n    transparent 20px,\r\n    rgba(212, 175, 55, 0.1) 21px,\r\n    rgba(212, 175, 55, 0.1) 22px\r\n  );\r\n}\r\n\r\n.border-glow {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 2px;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, transparent, #d4af37, transparent);\r\n  opacity: 0.6;\r\n  animation: borderPulse 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderPulse {\r\n  0%, 100% { opacity: 0.3; }\r\n  50% { opacity: 0.8; }\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 50px 15px 35px 15px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .game-title {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 60px 25px 45px 20px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    height: 50px;\r\n  }\r\n\r\n  .left-border-frame,\r\n  .right-border-frame {\r\n    top: 50px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n\r\n  /* 移动端标题样式调整 */\r\n  .page-title-container {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .location-name {\r\n    font-size: 16px;\r\n    padding: 4px 12px;\r\n  }\r\n\r\n  .location-label {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .location-text {\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .page-title-container {\r\n    gap: 6px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .location-name {\r\n    font-size: 14px;\r\n    padding: 3px 8px;\r\n  }\r\n\r\n  .location-label {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .location-text {\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n</style> "]}]}