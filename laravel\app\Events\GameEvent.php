<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GameEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 事件名称
     */
    public $event;

    /**
     * 角色ID
     */
    public $characterId;

    /**
     * 事件数据
     */
    public $data;

    /**
     * Create a new event instance.
     *
     * @param string $event
     * @param int $characterId
     * @param array $data
     * @return void
     */
    public function __construct($event, $characterId, $data)
    {
        $this->event = $event;
        $this->characterId = $characterId;
        $this->data = $data;
    }

    /**
     * 获取广播频道
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('character.'.$this->characterId);
    }

    /**
     * 获取广播事件名称
     *
     * @return string
     */
    public function broadcastAs()
    {
        return $this->event;
    }
}
