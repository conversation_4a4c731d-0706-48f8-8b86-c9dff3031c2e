<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\ChatSocketService;
use App\Models\Character;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    /**
     * ChatSocketService 实例
     */
    protected $chatSocketService;

    /**
     * 创建新的控制器实例
     *
     * @param ChatSocketService $chatSocketService
     */
    public function __construct(ChatSocketService $chatSocketService)
    {
        $this->chatSocketService = $chatSocketService;
    }

    /**
     * 获取可用的聊天频道列表
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function getChannels(Request $request)
    {
        // 基础频道列表
        $channels = [
            [
                'id' => 'world',
                'name' => '世界',
                'type' => 'public',
                'description' => '所有玩家可见的世界聊天频道',
                'icon' => 'world-icon.png'
            ],
            [
                'id' => 'system',
                'name' => '系统',
                'type' => 'system',
                'description' => '游戏系统公告和通知',
                'icon' => 'system-icon.png'
            ]
        ];

        // 获取当前登录用户的角色
        $userId = Auth::id();
        $characters = Character::where('user_id', $userId)->get();

        // 每个角色的队伍频道
        foreach ($characters as $character) {
            if ($character->teamMember) {
                $team = $character->teamMember->team;
                $channels[] = [
                    'id' => 'team_' . $team->id,
                    'name' => '队伍:' . $team->name,
                    'type' => 'team',
                    'team_id' => $team->id,
                    'character_id' => $character->id,
                    'description' => '队伍专属聊天频道',
                    'icon' => 'team-icon.png'
                ];
            }
        }

        return response()->json([
            'channels' => $channels
        ]);
    }

    /**
     * 获取频道的历史消息
     *
     * @param Request $request
     * @param string $channel
     * @return \Illuminate\Http\Response
     */
    public function getMessages(Request $request, $channel)
    {
        $validator = Validator::make($request->all(), [
            'character_id' => 'sometimes|exists:characters,id',
            'receiver_id' => 'sometimes|exists:characters,id',
            'limit' => 'sometimes|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证用户是否有权访问请求的角色
        $characterId = $request->character_id;
        if ($characterId) {
            $character = Character::where('id', $characterId)
                ->where('user_id', Auth::id())
                ->first();

            if (!$character) {
                return response()->json(['message' => '未找到角色或无权访问'], 403);
            }
        }

        // 处理特殊频道格式（如team_123）
        $actualChannel = $channel;
        $teamId = null;

        if (strpos($channel, 'team_') === 0) {
            $actualChannel = 'team';
            $teamId = substr($channel, 5);

            // 验证用户是否在此队伍中
            if ($characterId) {
                $character = Character::find($characterId);
                if (!$character || !$character->teamMember || $character->teamMember->team_id != $teamId) {
                    return response()->json(['message' => '您不在此队伍中，无法访问队伍聊天'], 403);
                }
            }
        }

        // 获取消息历史
        $limit = $request->limit ?? 50;
        $messages = $this->chatSocketService->getChannelHistory(
            $actualChannel,
            $characterId,
            $request->receiver_id,
            $limit
        );

        return response()->json([
            'channel' => $channel,
            'messages' => $messages
        ]);
    }

    /**
     * 发送频道消息
     *
     * @param Request $request
     * @param string $channel
     * @return \Illuminate\Http\Response
     */
    public function sendMessage(Request $request, $channel)
    {
        $validator = Validator::make($request->all(), [
            'character_id' => 'required|exists:characters,id',
            'message' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证角色所有权
        $characterId = $request->character_id;
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 处理特殊频道格式
        $actualChannel = $channel;

        if (strpos($channel, 'team_') === 0) {
            $actualChannel = 'team';
            $teamId = substr($channel, 5);

            // 验证用户是否在此队伍中
            if (!$character->teamMember || $character->teamMember->team_id != $teamId) {
                return response()->json(['message' => '您不在此队伍中，无法发送队伍聊天消息'], 403);
            }
        }

        // 发送消息
        $message = $this->chatSocketService->sendMessage(
            $characterId,
            $actualChannel,
            $request->message,
            null // 群聊没有特定接收者
        );

        return response()->json([
            'message' => '消息已发送',
            'data' => $message
        ]);
    }

    /**
     * 发送私聊消息
     *
     * @param Request $request
     * @param int $target 接收者角色ID
     * @return \Illuminate\Http\Response
     */
    public function sendPrivateMessage(Request $request, $target)
    {
        $validator = Validator::make($request->all(), [
            'character_id' => 'required|exists:characters,id',
            'message' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证角色所有权
        $characterId = $request->character_id;
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 验证目标角色是否存在
        $targetCharacter = Character::find($target);
        if (!$targetCharacter) {
            return response()->json(['message' => '目标角色不存在'], 404);
        }

        // 发送私聊消息
        $message = $this->chatSocketService->sendMessage(
            $characterId,
            'private',
            $request->message,
            $target
        );

        return response()->json([
            'message' => '私聊消息已发送',
            'data' => $message
        ]);
    }
}
