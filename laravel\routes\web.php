<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\MapController;
use App\Http\Controllers\Admin\ApiController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// 管理后台路由
Route::prefix('admin')->name('admin.')->group(function () {
    // 仪表盘
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // 地图管理
    Route::resource('maps', MapController::class);
    Route::post('maps/{map}/locations', [MapController::class, 'saveLocations'])->name('maps.locations.save');
    Route::post('maps/{map}/upload', [MapController::class, 'uploadMapImage'])->name('maps.upload');
    Route::get('maps-debug', [MapController::class, 'debug'])->name('maps.debug');

    // API路由
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('maps/{map}/characters', [ApiController::class, 'getMapCharacters'])->name('map.characters');
        Route::get('maps/{map}/stats', [ApiController::class, 'getMapStats'])->name('map.stats');
    });
});
