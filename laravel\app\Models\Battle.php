<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Battle extends Model
{
    use HasFactory;

    /**
     * 战斗状态常量
     */
    const STATUS_ONGOING = 'ongoing';
    const STATUS_ACTIVE = 'active';
    const STATUS_VICTORY = 'victory';
    const STATUS_DEFEAT = 'defeat';
    const STATUS_DRAW = 'draw';
    const STATUS_FLED = 'fled';
    // 兼容旧数据的状态
    const STATUS_WIN = 'win';
    const STATUS_LOSE = 'lose';
    const STATUS_ESCAPE = 'escape';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'character_id',
        'monster_id',
        'opponent_type',
        'opponent_id',
        'location_id',
        'type',
        'status',
        'rounds',
        'battle_data',
        'rewards',
        'exp_gained',
        'gold_gained',
        'silver_gained',
        'battle_log',
        'started_at',
        'ended_at'
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'rewards' => 'array',
        'battle_data' => 'array',
        'battle_log' => 'array',
        'rounds' => 'integer',
        'exp_gained' => 'integer',
        'gold_gained' => 'integer',
        'silver_gained' => 'integer',
    ];

    /**
     * 关联角色
     */
    public function character(): BelongsTo
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 关联怪物
     */
    public function monster(): BelongsTo
    {
        return $this->belongsTo(Monster::class, 'opponent_id')
                    ->where('opponent_type', Monster::class);
    }

    /**
     * 关联位置
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * 检查战斗是否进行中
     */
    public function isOngoing(): bool
    {
        return in_array($this->status, [self::STATUS_ONGOING, self::STATUS_ACTIVE]);
    }

    /**
     * 检查战斗是否已结束
     */
    public function isFinished(): bool
    {
        return in_array($this->status, [
            self::STATUS_VICTORY, self::STATUS_DEFEAT, self::STATUS_DRAW, self::STATUS_FLED,
            self::STATUS_WIN, self::STATUS_LOSE, self::STATUS_ESCAPE
        ]);
    }

    /**
     * 检查战斗是否胜利
     */
    public function isVictory(): bool
    {
        return in_array($this->status, [self::STATUS_VICTORY, self::STATUS_WIN]);
    }

    /**
     * 添加战斗日志
     */
    public function addLog(string $action, array $data = []): void
    {
        $logs = $this->battle_log ?? [];
        $logs[] = [
            'round' => $this->rounds,
            'action' => $action,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ];
        $this->battle_log = $logs;
        $this->save();
    }

    /**
     * 结束战斗
     */
    public function finish(string $status, int $expGained = 0, int $goldGained = 0, array $itemsGained = []): void
    {
        $this->status = $status;
        $this->ended_at = now();
        $this->exp_gained = $expGained;
        $this->gold_gained = $goldGained;

        // 处理物品奖励
        $rewards = $this->rewards ?? [];
        if (!isset($rewards['items'])) {
            $rewards['items'] = [];
        }
        $rewards['items'] = array_merge($rewards['items'], $itemsGained);
        $this->rewards = $rewards;

        $this->save();
    }

    /**
     * 获取战斗持续时间（秒）
     */
    public function getDurationAttribute(): int
    {
        if (!$this->ended_at) {
            return now()->diffInSeconds($this->started_at ?? $this->created_at);
        }
        return $this->ended_at->diffInSeconds($this->started_at ?? $this->created_at);
    }
}
