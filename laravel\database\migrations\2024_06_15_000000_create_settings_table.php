<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateSettingsTable extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('group', 100)->comment('设置组');
            $table->string('key', 100)->comment('设置键名');
            $table->text('value')->nullable()->comment('设置值');
            $table->timestamps();

            $table->unique(['group', 'key']);
        });

        // 添加基本设置
        DB::table('settings')->insert([
            [
                'group' => 'basic',
                'key' => 'site_name',
                'value' => '神之西游',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'group' => 'basic',
                'key' => 'site_description',
                'value' => '神之西游游戏管理系统',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'group' => 'basic',
                'key' => 'maintenance_mode',
                'value' => '0',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'group' => 'game',
                'key' => 'initial_silver',
                'value' => '100',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'group' => 'game',
                'key' => 'initial_health',
                'value' => '100',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'group' => 'game',
                'key' => 'experience_rate',
                'value' => '1.0',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'group' => 'game',
                'key' => 'drop_rate',
                'value' => '1.0',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settings');
    }
}
