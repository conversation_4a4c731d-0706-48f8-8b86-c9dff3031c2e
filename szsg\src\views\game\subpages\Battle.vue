<template>
  <GameLayout>
    <div class="battle-page">
      <!-- 返回按钮 -->
      <div class="header-section">
        <button class="return-btn" @click="goBack">
          <img src="/static/game/UI/anniu/fhui_2.png" alt="返回" class="btn-image" />
        </button>
        <h2 class="page-title">战斗系统</h2>
      </div>
      
      <!-- 战斗状态显示 -->
      <div v-if="battleState.inBattle" class="battle-arena">
        <!-- 敌人信息 -->
        <div class="enemy-section">
          <div class="enemy-info">
            <div class="enemy-name">{{ battleState.enemy.name }}</div>
            <div class="enemy-level">等级 {{ battleState.enemy.level }}</div>
            <div class="health-bar">
              <div class="bar-fill enemy-hp" :style="{ width: enemyHpPercent + '%' }"></div>
              <div class="bar-text">{{ battleState.enemy.hp }}/{{ battleState.enemy.maxHp }}</div>
            </div>
          </div>
          <div class="enemy-avatar">
            <img :src="battleState.enemy.avatar || '/static/game/UI/ts/ts2.png'" :alt="battleState.enemy.name" />
          </div>
        </div>
        
        <!-- 战斗日志 -->
        <div class="battle-log">
          <div class="log-title">战斗记录</div>
          <div class="log-content">
            <div 
              v-for="(log, index) in battleState.logs" 
              :key="index"
              class="log-item"
              :class="log.type"
            >
              {{ log.message }}
            </div>
          </div>
        </div>
        
        <!-- 玩家信息 -->
        <div class="player-section">
          <div class="player-avatar">
            <img :src="characterInfo.avatar || '/static/game/UI/tx/male/tx1.png'" :alt="characterInfo.name" />
          </div>
          <div class="player-info">
            <div class="player-name">{{ characterInfo.name }}</div>
            <div class="player-level">等级 {{ characterInfo.level }}</div>
            <div class="health-bar">
              <div class="bar-fill player-hp" :style="{ width: playerHpPercent + '%' }"></div>
              <div class="bar-text">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</div>
            </div>
            <div class="mana-bar">
              <div class="bar-fill player-mp" :style="{ width: playerMpPercent + '%' }"></div>
              <div class="bar-text">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</div>
            </div>
          </div>
        </div>
        
        <!-- 战斗操作 -->
        <div class="battle-actions">
          <button 
            class="action-btn attack"
            @click="performAction('attack')"
            :disabled="isActionLoading || battleState.isPlayerTurn === false"
          >
            普通攻击
          </button>
          
          <button 
            class="action-btn skill"
            @click="showSkillMenu = !showSkillMenu"
            :disabled="isActionLoading || battleState.isPlayerTurn === false"
          >
            使用技能
          </button>
          
          <button 
            class="action-btn item"
            @click="showItemMenu = !showItemMenu"
            :disabled="isActionLoading || battleState.isPlayerTurn === false"
          >
            使用道具
          </button>
          
          <button 
            class="action-btn flee"
            @click="performAction('flee')"
            :disabled="isActionLoading"
          >
            逃跑
          </button>
        </div>
        
        <!-- 技能菜单 -->
        <div v-if="showSkillMenu" class="action-menu skill-menu">
          <div class="menu-title">选择技能</div>
          <div class="menu-items">
            <div 
              v-for="skill in availableSkills" 
              :key="skill.id"
              class="menu-item"
              @click="useSkill(skill)"
              :class="{ disabled: !canUseSkill(skill) }"
            >
              <span class="item-name">{{ skill.name }}</span>
              <span class="item-cost">{{ skill.manaCost }} MP</span>
            </div>
          </div>
        </div>
        
        <!-- 道具菜单 -->
        <div v-if="showItemMenu" class="action-menu item-menu">
          <div class="menu-title">选择道具</div>
          <div class="menu-items">
            <div 
              v-for="item in availableItems" 
              :key="item.id"
              class="menu-item"
              @click="useItem(item)"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="item-count">x{{ item.quantity }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 非战斗状态 - 选择敌人 -->
      <div v-else class="enemy-selection">
        <div class="section-title">选择对手</div>
        
        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-text">{{ error }}</div>
          <button class="retry-btn" @click="fetchEnemies">重试</button>
        </div>
        
        <!-- 敌人列表 -->
        <div v-else class="enemies-grid">
          <div 
            v-for="enemy in enemies" 
            :key="enemy.id"
            class="enemy-card"
            @click="startBattle(enemy)"
          >
            <div class="enemy-card-avatar">
              <img :src="enemy.avatar || '/static/game/UI/ts/ts2.png'" :alt="enemy.name" />
            </div>
            <div class="enemy-card-info">
              <div class="enemy-card-name">{{ enemy.name }}</div>
              <div class="enemy-card-level">等级 {{ enemy.level }}</div>
              <div class="enemy-card-difficulty" :class="getDifficultyClass(enemy)">
                {{ getDifficultyText(enemy) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import axios from 'axios'
import { API_BASE_URL } from '@/api/config.js'
import { ERROR_MESSAGES } from '@/api/constants.js'
import logger from '@/utils/logger'

export default {
  components: {
    GameLayout
  },
  data() {
    return {
      battleState: {
        inBattle: false,
        isPlayerTurn: true,
        enemy: null,
        logs: []
      },
      characterInfo: {
        name: '',
        level: 1,
        hp: 100,
        maxHp: 100,
        mp: 50,
        maxMp: 50,
        avatar: ''
      },
      enemies: [],
      availableSkills: [],
      availableItems: [],
      showSkillMenu: false,
      showItemMenu: false,
      isLoading: true,
      error: null,
      isActionLoading: false
    }
  },
  computed: {
    authToken() {
      return this.$store.state.token || localStorage.getItem('authToken')
    },
    selectedCharacterId() {
      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')
    },
    enemyHpPercent() {
      if (!this.battleState.enemy) return 0
      return Math.round((this.battleState.enemy.hp / this.battleState.enemy.maxHp) * 100)
    },
    playerHpPercent() {
      return Math.round((this.characterInfo.hp / this.characterInfo.maxHp) * 100)
    },
    playerMpPercent() {
      return Math.round((this.characterInfo.mp / this.characterInfo.maxMp) * 100)
    }
  },
  created() {
    // 检查认证状态
    if (!this.authToken) {
      logger.warn('Battle页面: 未找到认证token')
      this.error = '请先登录'
      return
    }

    // 检查角色选择状态
    if (!this.selectedCharacterId) {
      logger.warn('Battle页面: 未选择角色')
      this.error = '请先选择角色'
      return
    }

    this.fetchEnemies()
    this.fetchCharacterInfo()
    this.fetchBattleResources()
  },
  methods: {
    goBack() {
      if (this.battleState.inBattle) {
        if (confirm('确定要退出战斗吗？')) {
          this.endBattle()
        }
      } else {
        this.$router.go(-1)
      }
    },
    
    async fetchEnemies() {
      this.isLoading = true
      this.error = null
      
      try {
        const response = await axios.get(`${API_BASE_URL}/battle/enemies`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        
        this.enemies = response.data.enemies || []
        logger.info('获取敌人列表成功', this.enemies.length)
      } catch (err) {
        if (err.response && err.response.status === 404) {
          this.error = '战斗API暂未实现，请等待后端开发完成'
        } else {
          this.error = err.response?.data?.message || err.message || ERROR_MESSAGES.UNKNOWN_ERROR
        }
        logger.warn('获取敌人列表失败', err.response?.status, err.message)
      } finally {
        this.isLoading = false
      }
    },
    
    async fetchCharacterInfo() {
      try {
        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        
        this.characterInfo = {
          name: response.data.name || '',
          level: response.data.level || 1,
          hp: response.data.hp || 100,
          maxHp: response.data.maxHp || 100,
          mp: response.data.mp || 50,
          maxMp: response.data.maxMp || 50,
          avatar: response.data.avatar || ''
        }
      } catch (err) {
        logger.error('获取角色信息失败', err)
      }
    },
    
    async fetchBattleResources() {
      try {
        // 获取可用技能
        const skillsResponse = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/skills/battle`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        this.availableSkills = skillsResponse.data.skills || []
        
        // 获取可用道具
        const itemsResponse = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/items/battle`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        this.availableItems = itemsResponse.data.items || []
      } catch (err) {
        logger.error('获取战斗资源失败', err)
      }
    },
    
    async startBattle(enemy) {
      this.isActionLoading = true
      
      try {
        const response = await axios.post(
          `${API_BASE_URL}/battle/start`,
          { 
            character_id: this.selectedCharacterId,
            enemy_id: enemy.id 
          },
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Accept': 'application/json'
            }
          }
        )
        
        this.battleState = {
          inBattle: true,
          isPlayerTurn: true,
          enemy: response.data.enemy,
          logs: [{ message: `战斗开始！遭遇了 ${enemy.name}`, type: 'system' }]
        }
        
        logger.info('开始战斗', enemy.name)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('开始战斗失败', err)
      } finally {
        this.isActionLoading = false
      }
    },
    
    async performAction(action) {
      if (this.isActionLoading) return
      this.isActionLoading = true
      this.showSkillMenu = false
      this.showItemMenu = false
      
      try {
        const response = await axios.post(
          `${API_BASE_URL}/battle/action`,
          { 
            action: action,
            character_id: this.selectedCharacterId
          },
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Accept': 'application/json'
            }
          }
        )
        
        this.processBattleResult(response.data)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('战斗行动失败', err)
      } finally {
        this.isActionLoading = false
      }
    },
    
    async useSkill(skill) {
      if (!this.canUseSkill(skill)) return
      
      this.showSkillMenu = false
      await this.performAction(`skill:${skill.id}`)
    },
    
    async useItem(item) {
      this.showItemMenu = false
      await this.performAction(`item:${item.id}`)
    },
    
    canUseSkill(skill) {
      return this.characterInfo.mp >= skill.manaCost
    },
    
    processBattleResult(result) {
      // 更新战斗状态
      if (result.enemy) {
        this.battleState.enemy = result.enemy
      }
      
      if (result.character) {
        this.characterInfo.hp = result.character.hp
        this.characterInfo.mp = result.character.mp
      }
      
      // 添加战斗日志
      if (result.logs) {
        this.battleState.logs.push(...result.logs)
      }
      
      // 检查战斗是否结束
      if (result.battleEnd) {
        this.endBattle(result.victory, result.rewards)
      } else {
        this.battleState.isPlayerTurn = result.isPlayerTurn
      }
    },
    
    endBattle(victory = false, rewards = null) {
      if (victory) {
        let message = '战斗胜利！'
        if (rewards) {
          message += ` 获得经验: ${rewards.exp || 0}, 金币: ${rewards.gold || 0}`
        }
        this.showToast(message, 'success')
      }
      
      this.battleState = {
        inBattle: false,
        isPlayerTurn: true,
        enemy: null,
        logs: []
      }
      
      // 刷新角色信息
      this.fetchCharacterInfo()
    },
    
    getDifficultyClass(enemy) {
      const levelDiff = enemy.level - this.characterInfo.level
      if (levelDiff <= -5) return 'very-easy'
      if (levelDiff <= -2) return 'easy'
      if (levelDiff <= 2) return 'normal'
      if (levelDiff <= 5) return 'hard'
      return 'very-hard'
    },
    
    getDifficultyText(enemy) {
      const levelDiff = enemy.level - this.characterInfo.level
      if (levelDiff <= -5) return '非常简单'
      if (levelDiff <= -2) return '简单'
      if (levelDiff <= 2) return '普通'
      if (levelDiff <= 5) return '困难'
      return '非常困难'
    },
    
    showToast(message, type = 'error') {
      alert(message)
    }
  }
}
</script>

<style lang="scss" scoped>
.battle-page {
  padding: 15px;
  min-height: 100vh;
  background: linear-gradient(135deg, #2d1b69, #1a0f3d);
  color: #fff;
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.return-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.btn-image {
  width: 60px;
  height: 40px;
  object-fit: contain;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

// 战斗界面
.battle-arena {
  display: grid;
  grid-template-areas:
    "enemy enemy enemy"
    "log log log"
    "player player player"
    "actions actions actions";
  grid-template-rows: auto 1fr auto auto;
  gap: 20px;
  height: calc(100vh - 120px);
}

.enemy-section {
  grid-area: enemy;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 0, 0, 0.1);
  border: 2px solid rgba(255, 0, 0, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.enemy-info {
  flex: 1;
}

.enemy-name {
  font-size: 20px;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 5px;
}

.enemy-level {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.enemy-avatar {
  width: 80px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid rgba(255, 0, 0, 0.5);
  }
}

.battle-log {
  grid-area: log;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.log-title {
  font-size: 16px;
  font-weight: bold;
  color: #4ecdc4;
  margin-bottom: 10px;
  text-align: center;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  max-height: 200px;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;

  &.system {
    color: #4ecdc4;
  }

  &.player {
    color: #2ecc71;
  }

  &.enemy {
    color: #ff6b6b;
  }

  &.damage {
    color: #f39c12;
  }
}

.player-section {
  grid-area: player;
  display: flex;
  align-items: center;
  background: rgba(0, 255, 0, 0.1);
  border: 2px solid rgba(0, 255, 0, 0.3);
  border-radius: 12px;
  padding: 20px;
  gap: 20px;
}

.player-avatar {
  width: 80px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid rgba(0, 255, 0, 0.5);
  }
}

.player-info {
  flex: 1;
}

.player-name {
  font-size: 20px;
  font-weight: bold;
  color: #2ecc71;
  margin-bottom: 5px;
}

.player-level {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.health-bar, .mana-bar {
  position: relative;
  height: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 8px;
}

.bar-fill {
  height: 100%;
  transition: width 0.5s ease;

  &.enemy-hp {
    background: linear-gradient(90deg, #ff4444, #ff6666);
  }

  &.player-hp {
    background: linear-gradient(90deg, #2ecc71, #27ae60);
  }

  &.player-mp {
    background: linear-gradient(90deg, #3498db, #2980b9);
  }
}

.bar-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.battle-actions {
  grid-area: actions;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 120px;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.attack {
    background: #e74c3c;
    color: white;

    &:hover:not(:disabled) {
      background: #c0392b;
      transform: translateY(-2px);
    }
  }

  &.skill {
    background: #9b59b6;
    color: white;

    &:hover:not(:disabled) {
      background: #8e44ad;
      transform: translateY(-2px);
    }
  }

  &.item {
    background: #f39c12;
    color: white;

    &:hover:not(:disabled) {
      background: #e67e22;
      transform: translateY(-2px);
    }
  }

  &.flee {
    background: #95a5a6;
    color: white;

    &:hover:not(:disabled) {
      background: #7f8c8d;
      transform: translateY(-2px);
    }
  }
}

.action-menu {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  min-width: 300px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  color: #4ecdc4;
  margin-bottom: 15px;
  text-align: center;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(.disabled) {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.item-name {
  font-weight: 500;
}

.item-cost, .item-count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

// 敌人选择界面
.enemy-selection {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #4ecdc4;
  margin-bottom: 20px;
  text-align: center;
}

.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-text {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
}

.error-text {
  font-size: 16px;
  color: #ff6b6b;
  margin-bottom: 15px;
}

.retry-btn {
  padding: 10px 20px;
  background: #4ecdc4;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    background: #45b7aa;
    transform: translateY(-2px);
  }
}

.enemies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.enemy-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;

  &:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  }
}

.enemy-card-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

.enemy-card-name {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 5px;
}

.enemy-card-level {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.enemy-card-difficulty {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;

  &.very-easy {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    border: 1px solid #2ecc71;
  }

  &.easy {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
    border: 1px solid #3498db;
  }

  &.normal {
    background: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
    border: 1px solid #f1c40f;
  }

  &.hard {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
    border: 1px solid #f39c12;
  }

  &.very-hard {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid #e74c3c;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .battle-container {
    padding: 10px;
  }

  .header-section {
    margin-bottom: 15px;
    gap: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .btn-image {
    width: 50px;
    height: 35px;
  }

  .battle-arena {
    grid-template-areas:
      "enemy"
      "log"
      "player"
      "actions";
    grid-template-columns: 1fr;
    height: auto;
    gap: 15px;
  }

  .enemy-section, .player-section {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .battle-actions {
    flex-wrap: wrap;
    gap: 10px;
  }

  .action-btn {
    flex: 1;
    min-width: calc(50% - 5px);
    padding: 10px 16px;
    font-size: 14px;
  }

  .action-menu {
    bottom: 80px;
    left: 10px;
    right: 10px;
    transform: none;
    min-width: auto;
  }

  .enemies-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .battle-container {
    padding: 8px;
  }

  .enemy-section, .player-section {
    padding: 15px;
  }

  .enemy-avatar, .player-avatar {
    width: 60px;
    height: 60px;
  }

  .action-btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .battle-actions {
    flex-direction: column;
  }

  .enemies-grid {
    grid-template-columns: 1fr;
  }
}
</style>
