{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Battle.vue?vue&type=style&index=0&id=a4709880&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Battle.vue", "mtime": 1749718635742}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Battle.vue"], "names": [], "mappings": ";AAge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file": "Battle.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"battle-page\">\n      <!-- 返回按钮 -->\n      <div class=\"header-section\">\n        <button class=\"return-btn\" @click=\"goBack\">\n          <img src=\"/static/game/UI/anniu/fhui_2.png\" alt=\"返回\" class=\"btn-image\" />\n        </button>\n        <h2 class=\"page-title\">战斗系统</h2>\n      </div>\n      \n      <!-- 战斗状态显示 -->\n      <div v-if=\"battleState.inBattle\" class=\"battle-arena\">\n        <!-- 敌人信息 -->\n        <div class=\"enemy-section\">\n          <div class=\"enemy-info\">\n            <div class=\"enemy-name\">{{ battleState.enemy.name }}</div>\n            <div class=\"enemy-level\">等级 {{ battleState.enemy.level }}</div>\n            <div class=\"health-bar\">\n              <div class=\"bar-fill enemy-hp\" :style=\"{ width: enemyHpPercent + '%' }\"></div>\n              <div class=\"bar-text\">{{ battleState.enemy.hp }}/{{ battleState.enemy.maxHp }}</div>\n            </div>\n          </div>\n          <div class=\"enemy-avatar\">\n            <img :src=\"battleState.enemy.avatar || '/static/game/UI/ts/ts2.png'\" :alt=\"battleState.enemy.name\" />\n          </div>\n        </div>\n        \n        <!-- 战斗日志 -->\n        <div class=\"battle-log\">\n          <div class=\"log-title\">战斗记录</div>\n          <div class=\"log-content\">\n            <div \n              v-for=\"(log, index) in battleState.logs\" \n              :key=\"index\"\n              class=\"log-item\"\n              :class=\"log.type\"\n            >\n              {{ log.message }}\n            </div>\n          </div>\n        </div>\n        \n        <!-- 玩家信息 -->\n        <div class=\"player-section\">\n          <div class=\"player-avatar\">\n            <img :src=\"characterInfo.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"characterInfo.name\" />\n          </div>\n          <div class=\"player-info\">\n            <div class=\"player-name\">{{ characterInfo.name }}</div>\n            <div class=\"player-level\">等级 {{ characterInfo.level }}</div>\n            <div class=\"health-bar\">\n              <div class=\"bar-fill player-hp\" :style=\"{ width: playerHpPercent + '%' }\"></div>\n              <div class=\"bar-text\">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</div>\n            </div>\n            <div class=\"mana-bar\">\n              <div class=\"bar-fill player-mp\" :style=\"{ width: playerMpPercent + '%' }\"></div>\n              <div class=\"bar-text\">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 战斗操作 -->\n        <div class=\"battle-actions\">\n          <button \n            class=\"action-btn attack\"\n            @click=\"performAction('attack')\"\n            :disabled=\"isActionLoading || battleState.isPlayerTurn === false\"\n          >\n            普通攻击\n          </button>\n          \n          <button \n            class=\"action-btn skill\"\n            @click=\"showSkillMenu = !showSkillMenu\"\n            :disabled=\"isActionLoading || battleState.isPlayerTurn === false\"\n          >\n            使用技能\n          </button>\n          \n          <button \n            class=\"action-btn item\"\n            @click=\"showItemMenu = !showItemMenu\"\n            :disabled=\"isActionLoading || battleState.isPlayerTurn === false\"\n          >\n            使用道具\n          </button>\n          \n          <button \n            class=\"action-btn flee\"\n            @click=\"performAction('flee')\"\n            :disabled=\"isActionLoading\"\n          >\n            逃跑\n          </button>\n        </div>\n        \n        <!-- 技能菜单 -->\n        <div v-if=\"showSkillMenu\" class=\"action-menu skill-menu\">\n          <div class=\"menu-title\">选择技能</div>\n          <div class=\"menu-items\">\n            <div \n              v-for=\"skill in availableSkills\" \n              :key=\"skill.id\"\n              class=\"menu-item\"\n              @click=\"useSkill(skill)\"\n              :class=\"{ disabled: !canUseSkill(skill) }\"\n            >\n              <span class=\"item-name\">{{ skill.name }}</span>\n              <span class=\"item-cost\">{{ skill.manaCost }} MP</span>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 道具菜单 -->\n        <div v-if=\"showItemMenu\" class=\"action-menu item-menu\">\n          <div class=\"menu-title\">选择道具</div>\n          <div class=\"menu-items\">\n            <div \n              v-for=\"item in availableItems\" \n              :key=\"item.id\"\n              class=\"menu-item\"\n              @click=\"useItem(item)\"\n            >\n              <span class=\"item-name\">{{ item.name }}</span>\n              <span class=\"item-count\">x{{ item.quantity }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 非战斗状态 - 选择敌人 -->\n      <div v-else class=\"enemy-selection\">\n        <div class=\"section-title\">选择对手</div>\n        \n        <!-- 加载状态 -->\n        <div v-if=\"isLoading\" class=\"loading-container\">\n          <div class=\"loading-text\">加载中...</div>\n        </div>\n        \n        <!-- 错误状态 -->\n        <div v-else-if=\"error\" class=\"error-container\">\n          <div class=\"error-text\">{{ error }}</div>\n          <button class=\"retry-btn\" @click=\"fetchEnemies\">重试</button>\n        </div>\n        \n        <!-- 敌人列表 -->\n        <div v-else class=\"enemies-grid\">\n          <div \n            v-for=\"enemy in enemies\" \n            :key=\"enemy.id\"\n            class=\"enemy-card\"\n            @click=\"startBattle(enemy)\"\n          >\n            <div class=\"enemy-card-avatar\">\n              <img :src=\"enemy.avatar || '/static/game/UI/ts/ts2.png'\" :alt=\"enemy.name\" />\n            </div>\n            <div class=\"enemy-card-info\">\n              <div class=\"enemy-card-name\">{{ enemy.name }}</div>\n              <div class=\"enemy-card-level\">等级 {{ enemy.level }}</div>\n              <div class=\"enemy-card-difficulty\" :class=\"getDifficultyClass(enemy)\">\n                {{ getDifficultyText(enemy) }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport axios from 'axios'\nimport { API_BASE_URL } from '@/api/config.js'\nimport { ERROR_MESSAGES } from '@/api/constants.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      battleState: {\n        inBattle: false,\n        isPlayerTurn: true,\n        enemy: null,\n        logs: []\n      },\n      characterInfo: {\n        name: '',\n        level: 1,\n        hp: 100,\n        maxHp: 100,\n        mp: 50,\n        maxMp: 50,\n        avatar: ''\n      },\n      enemies: [],\n      availableSkills: [],\n      availableItems: [],\n      showSkillMenu: false,\n      showItemMenu: false,\n      isLoading: true,\n      error: null,\n      isActionLoading: false\n    }\n  },\n  computed: {\n    authToken() {\n      return this.$store.state.token || localStorage.getItem('authToken')\n    },\n    selectedCharacterId() {\n      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')\n    },\n    enemyHpPercent() {\n      if (!this.battleState.enemy) return 0\n      return Math.round((this.battleState.enemy.hp / this.battleState.enemy.maxHp) * 100)\n    },\n    playerHpPercent() {\n      return Math.round((this.characterInfo.hp / this.characterInfo.maxHp) * 100)\n    },\n    playerMpPercent() {\n      return Math.round((this.characterInfo.mp / this.characterInfo.maxMp) * 100)\n    }\n  },\n  created() {\n    // 检查认证状态\n    if (!this.authToken) {\n      logger.warn('Battle页面: 未找到认证token')\n      this.error = '请先登录'\n      return\n    }\n\n    // 检查角色选择状态\n    if (!this.selectedCharacterId) {\n      logger.warn('Battle页面: 未选择角色')\n      this.error = '请先选择角色'\n      return\n    }\n\n    this.fetchEnemies()\n    this.fetchCharacterInfo()\n    this.fetchBattleResources()\n  },\n  methods: {\n    goBack() {\n      if (this.battleState.inBattle) {\n        if (confirm('确定要退出战斗吗？')) {\n          this.endBattle()\n        }\n      } else {\n        this.$router.go(-1)\n      }\n    },\n    \n    async fetchEnemies() {\n      this.isLoading = true\n      this.error = null\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/battle/enemies`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.enemies = response.data.enemies || []\n        logger.info('获取敌人列表成功', this.enemies.length)\n      } catch (err) {\n        if (err.response && err.response.status === 404) {\n          this.error = '战斗API暂未实现，请等待后端开发完成'\n        } else {\n          this.error = err.response?.data?.message || err.message || ERROR_MESSAGES.UNKNOWN_ERROR\n        }\n        logger.warn('获取敌人列表失败', err.response?.status, err.message)\n      } finally {\n        this.isLoading = false\n      }\n    },\n    \n    async fetchCharacterInfo() {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.characterInfo = {\n          name: response.data.name || '',\n          level: response.data.level || 1,\n          hp: response.data.hp || 100,\n          maxHp: response.data.maxHp || 100,\n          mp: response.data.mp || 50,\n          maxMp: response.data.maxMp || 50,\n          avatar: response.data.avatar || ''\n        }\n      } catch (err) {\n        logger.error('获取角色信息失败', err)\n      }\n    },\n    \n    async fetchBattleResources() {\n      try {\n        // 获取可用技能\n        const skillsResponse = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/skills/battle`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        this.availableSkills = skillsResponse.data.skills || []\n        \n        // 获取可用道具\n        const itemsResponse = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/items/battle`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        this.availableItems = itemsResponse.data.items || []\n      } catch (err) {\n        logger.error('获取战斗资源失败', err)\n      }\n    },\n    \n    async startBattle(enemy) {\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/battle/start`,\n          { \n            character_id: this.selectedCharacterId,\n            enemy_id: enemy.id \n          },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.battleState = {\n          inBattle: true,\n          isPlayerTurn: true,\n          enemy: response.data.enemy,\n          logs: [{ message: `战斗开始！遭遇了 ${enemy.name}`, type: 'system' }]\n        }\n        \n        logger.info('开始战斗', enemy.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('开始战斗失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async performAction(action) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      this.showSkillMenu = false\n      this.showItemMenu = false\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/battle/action`,\n          { \n            action: action,\n            character_id: this.selectedCharacterId\n          },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.processBattleResult(response.data)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('战斗行动失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async useSkill(skill) {\n      if (!this.canUseSkill(skill)) return\n      \n      this.showSkillMenu = false\n      await this.performAction(`skill:${skill.id}`)\n    },\n    \n    async useItem(item) {\n      this.showItemMenu = false\n      await this.performAction(`item:${item.id}`)\n    },\n    \n    canUseSkill(skill) {\n      return this.characterInfo.mp >= skill.manaCost\n    },\n    \n    processBattleResult(result) {\n      // 更新战斗状态\n      if (result.enemy) {\n        this.battleState.enemy = result.enemy\n      }\n      \n      if (result.character) {\n        this.characterInfo.hp = result.character.hp\n        this.characterInfo.mp = result.character.mp\n      }\n      \n      // 添加战斗日志\n      if (result.logs) {\n        this.battleState.logs.push(...result.logs)\n      }\n      \n      // 检查战斗是否结束\n      if (result.battleEnd) {\n        this.endBattle(result.victory, result.rewards)\n      } else {\n        this.battleState.isPlayerTurn = result.isPlayerTurn\n      }\n    },\n    \n    endBattle(victory = false, rewards = null) {\n      if (victory) {\n        let message = '战斗胜利！'\n        if (rewards) {\n          message += ` 获得经验: ${rewards.exp || 0}, 金币: ${rewards.gold || 0}`\n        }\n        this.showToast(message, 'success')\n      }\n      \n      this.battleState = {\n        inBattle: false,\n        isPlayerTurn: true,\n        enemy: null,\n        logs: []\n      }\n      \n      // 刷新角色信息\n      this.fetchCharacterInfo()\n    },\n    \n    getDifficultyClass(enemy) {\n      const levelDiff = enemy.level - this.characterInfo.level\n      if (levelDiff <= -5) return 'very-easy'\n      if (levelDiff <= -2) return 'easy'\n      if (levelDiff <= 2) return 'normal'\n      if (levelDiff <= 5) return 'hard'\n      return 'very-hard'\n    },\n    \n    getDifficultyText(enemy) {\n      const levelDiff = enemy.level - this.characterInfo.level\n      if (levelDiff <= -5) return '非常简单'\n      if (levelDiff <= -2) return '简单'\n      if (levelDiff <= 2) return '普通'\n      if (levelDiff <= 5) return '困难'\n      return '非常困难'\n    },\n    \n    showToast(message, type = 'error') {\n      alert(message)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.battle-page {\n  padding: 15px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2d1b69, #1a0f3d);\n  color: #fff;\n}\n\n.header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  gap: 15px;\n}\n\n.return-btn {\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  padding: 0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.btn-image {\n  width: 60px;\n  height: 40px;\n  object-fit: contain;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #fff;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n// 战斗界面\n.battle-arena {\n  display: grid;\n  grid-template-areas:\n    \"enemy enemy enemy\"\n    \"log log log\"\n    \"player player player\"\n    \"actions actions actions\";\n  grid-template-rows: auto 1fr auto auto;\n  gap: 20px;\n  height: calc(100vh - 120px);\n}\n\n.enemy-section {\n  grid-area: enemy;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(255, 0, 0, 0.1);\n  border: 2px solid rgba(255, 0, 0, 0.3);\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.enemy-info {\n  flex: 1;\n}\n\n.enemy-name {\n  font-size: 20px;\n  font-weight: bold;\n  color: #ff6b6b;\n  margin-bottom: 5px;\n}\n\n.enemy-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 10px;\n}\n\n.enemy-avatar {\n  width: 80px;\n  height: 80px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 10px;\n    border: 2px solid rgba(255, 0, 0, 0.5);\n  }\n}\n\n.battle-log {\n  grid-area: log;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n}\n\n.log-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #4ecdc4;\n  margin-bottom: 10px;\n  text-align: center;\n}\n\n.log-content {\n  flex: 1;\n  overflow-y: auto;\n  max-height: 200px;\n}\n\n.log-item {\n  padding: 5px 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  font-size: 14px;\n\n  &.system {\n    color: #4ecdc4;\n  }\n\n  &.player {\n    color: #2ecc71;\n  }\n\n  &.enemy {\n    color: #ff6b6b;\n  }\n\n  &.damage {\n    color: #f39c12;\n  }\n}\n\n.player-section {\n  grid-area: player;\n  display: flex;\n  align-items: center;\n  background: rgba(0, 255, 0, 0.1);\n  border: 2px solid rgba(0, 255, 0, 0.3);\n  border-radius: 12px;\n  padding: 20px;\n  gap: 20px;\n}\n\n.player-avatar {\n  width: 80px;\n  height: 80px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 10px;\n    border: 2px solid rgba(0, 255, 0, 0.5);\n  }\n}\n\n.player-info {\n  flex: 1;\n}\n\n.player-name {\n  font-size: 20px;\n  font-weight: bold;\n  color: #2ecc71;\n  margin-bottom: 5px;\n}\n\n.player-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 10px;\n}\n\n.health-bar, .mana-bar {\n  position: relative;\n  height: 20px;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 8px;\n}\n\n.bar-fill {\n  height: 100%;\n  transition: width 0.5s ease;\n\n  &.enemy-hp {\n    background: linear-gradient(90deg, #ff4444, #ff6666);\n  }\n\n  &.player-hp {\n    background: linear-gradient(90deg, #2ecc71, #27ae60);\n  }\n\n  &.player-mp {\n    background: linear-gradient(90deg, #3498db, #2980b9);\n  }\n}\n\n.bar-text {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.battle-actions {\n  grid-area: actions;\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n}\n\n.action-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  min-width: 120px;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.attack {\n    background: #e74c3c;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #c0392b;\n      transform: translateY(-2px);\n    }\n  }\n\n  &.skill {\n    background: #9b59b6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #8e44ad;\n      transform: translateY(-2px);\n    }\n  }\n\n  &.item {\n    background: #f39c12;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #e67e22;\n      transform: translateY(-2px);\n    }\n  }\n\n  &.flee {\n    background: #95a5a6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #7f8c8d;\n      transform: translateY(-2px);\n    }\n  }\n}\n\n.action-menu {\n  position: absolute;\n  bottom: 120px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(0, 0, 0, 0.9);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  padding: 20px;\n  min-width: 300px;\n  max-height: 300px;\n  overflow-y: auto;\n  z-index: 100;\n}\n\n.menu-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #4ecdc4;\n  margin-bottom: 15px;\n  text-align: center;\n}\n\n.menu-items {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.menu-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover:not(.disabled) {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-1px);\n  }\n\n  &.disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.item-name {\n  font-weight: 500;\n}\n\n.item-cost, .item-count {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n// 敌人选择界面\n.enemy-selection {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.section-title {\n  font-size: 20px;\n  font-weight: bold;\n  color: #4ecdc4;\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.loading-container, .error-container {\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.loading-text {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.error-text {\n  font-size: 16px;\n  color: #ff6b6b;\n  margin-bottom: 15px;\n}\n\n.retry-btn {\n  padding: 10px 20px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n}\n\n.enemies-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.enemy-card {\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 12px;\n  padding: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.12);\n    transform: translateY(-3px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.enemy-card-avatar {\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 15px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 10px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n}\n\n.enemy-card-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 5px;\n}\n\n.enemy-card-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 10px;\n}\n\n.enemy-card-difficulty {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  display: inline-block;\n\n  &.very-easy {\n    background: rgba(46, 204, 113, 0.2);\n    color: #2ecc71;\n    border: 1px solid #2ecc71;\n  }\n\n  &.easy {\n    background: rgba(52, 152, 219, 0.2);\n    color: #3498db;\n    border: 1px solid #3498db;\n  }\n\n  &.normal {\n    background: rgba(241, 196, 15, 0.2);\n    color: #f1c40f;\n    border: 1px solid #f1c40f;\n  }\n\n  &.hard {\n    background: rgba(243, 156, 18, 0.2);\n    color: #f39c12;\n    border: 1px solid #f39c12;\n  }\n\n  &.very-hard {\n    background: rgba(231, 76, 60, 0.2);\n    color: #e74c3c;\n    border: 1px solid #e74c3c;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .battle-container {\n    padding: 10px;\n  }\n\n  .header-section {\n    margin-bottom: 15px;\n    gap: 10px;\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .btn-image {\n    width: 50px;\n    height: 35px;\n  }\n\n  .battle-arena {\n    grid-template-areas:\n      \"enemy\"\n      \"log\"\n      \"player\"\n      \"actions\";\n    grid-template-columns: 1fr;\n    height: auto;\n    gap: 15px;\n  }\n\n  .enemy-section, .player-section {\n    flex-direction: column;\n    text-align: center;\n    gap: 15px;\n  }\n\n  .battle-actions {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .action-btn {\n    flex: 1;\n    min-width: calc(50% - 5px);\n    padding: 10px 16px;\n    font-size: 14px;\n  }\n\n  .action-menu {\n    bottom: 80px;\n    left: 10px;\n    right: 10px;\n    transform: none;\n    min-width: auto;\n  }\n\n  .enemies-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 15px;\n  }\n}\n\n@media (max-width: 480px) {\n  .battle-container {\n    padding: 8px;\n  }\n\n  .enemy-section, .player-section {\n    padding: 15px;\n  }\n\n  .enemy-avatar, .player-avatar {\n    width: 60px;\n    height: 60px;\n  }\n\n  .action-btn {\n    width: 100%;\n    margin-bottom: 8px;\n  }\n\n  .battle-actions {\n    flex-direction: column;\n  }\n\n  .enemies-grid {\n    grid-template-columns: 1fr;\n  }\n}\n</style>\n"]}]}