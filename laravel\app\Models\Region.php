<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Region extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'level_range_min',
        'level_range_max',
        'danger_level',
        'is_pvp',
        'weather_enabled',
        'background_image',
        'map_image',
        'sort_order',
        'is_active',
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'level_range_min' => 'integer',
        'level_range_max' => 'integer',
        'danger_level' => 'integer',
        'is_pvp' => 'boolean',
        'weather_enabled' => 'boolean',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 地图类型常量
     */
    const TYPE_CITY = 'city';
    const TYPE_WILDERNESS = 'wilderness';
    const TYPE_DUNGEON = 'dungeon';
    const TYPE_INSTANCE = 'instance';
    const TYPE_SPECIAL = 'special';

    /**
     * 获取所有地图类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_CITY => '城市',
            self::TYPE_WILDERNESS => '野外',
            self::TYPE_DUNGEON => '副本',
            self::TYPE_INSTANCE => '实例',
            self::TYPE_SPECIAL => '特殊'
        ];
    }

    /**
     * 获取地图类型文本
     */
    public function getTypeTextAttribute()
    {
        $typeMap = self::getTypeOptions();
        return $typeMap[$this->type] ?? '未知';
    }

    /**
     * 获取等级范围文本
     */
    public function getLevelRangeAttribute()
    {
        return "{$this->level_range_min}-{$this->level_range_max}";
    }

    /**
     * 获取危险等级文本
     */
    public function getDangerLevelTextAttribute()
    {
        $dangerMap = [
            1 => '安全',
            2 => '低危',
            3 => '中危',
            4 => '高危',
            5 => '极危'
        ];

        return $dangerMap[$this->danger_level] ?? '未知';
    }

    /**
     * 作用域：只获取活跃的地图
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeOfType(Builder $query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按等级范围筛选
     */
    public function scopeForLevel(Builder $query, $level)
    {
        return $query->where('level_range_min', '<=', $level)
                    ->where('level_range_max', '>=', $level);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrdered(Builder $query)
    {
        return $query->orderBy('sort_order', 'desc')
                    ->orderBy('level_range_min', 'asc')
                    ->orderBy('created_at', 'asc');
    }

    /**
     * 关联：该地图的角色
     */
    public function characters()
    {
        return $this->hasMany(Character::class);
    }

    /**
     * 获取该地图的所有位置
     */
    public function locations()
    {
        return $this->hasMany(Location::class, 'region_id');
    }

    /**
     * 获取该地图的NPC
     */
    public function npcs()
    {
        return $this->hasMany(Character::class)->where('is_npc', true);
    }

    /**
     * 获取该地图的怪物
     */
    public function monsters()
    {
        return $this->hasMany(Monster::class);
    }

    /**
     * 获取该地图的资源点
     */
    public function resources()
    {
        return $this->hasMany(Resource::class);
    }
}
