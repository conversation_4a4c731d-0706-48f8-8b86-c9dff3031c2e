<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBankTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bank_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['deposit', 'withdraw'])->comment('交易类型：存款/取款');
            $table->enum('currency', ['silver', 'gold_ingot'])->comment('货币类型：银两/金砖');
            $table->integer('amount')->comment('交易金额');
            $table->integer('balance')->comment('交易后余额');
            $table->string('description')->nullable()->comment('交易描述');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bank_transactions');
    }
}