{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\RequestTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\RequestTest.vue", "mtime": 1749872688134}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getRegions", "emergencyCleanupStorage", "clearAllCookies", "fix431Error", "check431Risk", "minimalFetch", "logger", "name", "data", "isLoading", "testResult", "storageSize", "storageCount", "largestItem", "key", "size", "estimatedHeaders", "estimatedHeaderSize", "mounted", "refreshInfo", "methods", "testRegionRequest", "startTime", "Date", "now", "result", "endTime", "JSON", "stringify", "error", "code", "message", "needCleanup", "emergencyClean", "success", "clearCookies", "testDirectRequest", "fix431AndTest", "riskCheck", "hasRisk", "fixResult", "apiError", "minimalResult", "minimalError", "calculateStorageInfo", "calculateHeaderInfo", "totalSize", "count", "largest", "i", "localStorage", "length", "value", "getItem", "headers", "navigator", "userAgent", "window", "location", "href", "language", "_this$$store", "token", "$store", "state", "auth", "e", "Object", "keys", "reduce", "formatBytes", "bytes", "k", "sizes", "Math", "floor", "log", "parseFloat", "pow", "toFixed"], "sources": ["src/views/debug/RequestTest.vue"], "sourcesContent": ["<template>\n  <div class=\"request-test\">\n    <h2>请求测试工具</h2>\n    <p>用于测试和调试HTTP 431错误</p>\n\n    <div class=\"test-section\">\n      <h3>存储状态</h3>\n      <div class=\"storage-info\">\n        <p>总存储大小: {{ formatBytes(storageSize) }}</p>\n        <p>存储项目数: {{ storageCount }}</p>\n        <p>最大项目: {{ largestItem.key }} ({{ formatBytes(largestItem.size) }})</p>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>请求测试</h3>\n      <div class=\"test-buttons\">\n        <button @click=\"testRegionRequest\" :disabled=\"isLoading\">\n          {{ isLoading ? '测试中...' : '测试大区API' }}\n        </button>\n        <button @click=\"emergencyClean\" :disabled=\"isLoading\">\n          紧急清理存储\n        </button>\n        <button @click=\"clearCookies\" :disabled=\"isLoading\">\n          清理Cookies\n        </button>\n        <button @click=\"testDirectRequest\" :disabled=\"isLoading\">\n          直接请求测试\n        </button>\n        <button @click=\"fix431AndTest\" :disabled=\"isLoading\">\n          修复431错误并测试\n        </button>\n        <button @click=\"refreshInfo\">\n          刷新信息\n        </button>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试结果</h3>\n      <div class=\"result-area\">\n        <pre>{{ testResult }}</pre>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>请求头信息</h3>\n      <div class=\"headers-info\">\n        <p>预估请求头大小: {{ estimatedHeaderSize }} bytes</p>\n        <div class=\"headers-list\">\n          <div v-for=\"(value, key) in estimatedHeaders\" :key=\"key\" class=\"header-item\">\n            <span class=\"header-key\">{{ key }}:</span>\n            <span class=\"header-value\">{{ value.substring(0, 100) }}{{ value.length > 100 ? '...' : '' }}</span>\n            <span class=\"header-size\">({{ key.length + value.length }} bytes)</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getRegions } from '@/api/services/regionService.js'\nimport { emergencyCleanupStorage, clearAllCookies } from '@/utils/storage.js'\nimport { fix431Error, check431Risk, minimalFetch } from '@/utils/fix431Error.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'RequestTest',\n  data() {\n    return {\n      isLoading: false,\n      testResult: '点击\"测试大区API\"开始测试',\n      storageSize: 0,\n      storageCount: 0,\n      largestItem: { key: '', size: 0 },\n      estimatedHeaders: {},\n      estimatedHeaderSize: 0\n    }\n  },\n  mounted() {\n    this.refreshInfo()\n  },\n  methods: {\n    async testRegionRequest() {\n      this.isLoading = true\n      this.testResult = '正在测试大区API请求...'\n      \n      try {\n        const startTime = Date.now()\n        const result = await getRegions()\n        const endTime = Date.now()\n        \n        this.testResult = `✅ 请求成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(result, null, 2)}`\n      } catch (error) {\n        this.testResult = `❌ 请求失败！\n错误代码: ${error.code}\n错误信息: ${error.message}\n需要清理: ${error.needCleanup ? '是' : '否'}\n详细信息: ${JSON.stringify(error, null, 2)}`\n        \n        logger.error('[RequestTest] 测试失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async emergencyClean() {\n      this.isLoading = true\n      this.testResult = '正在执行紧急清理...'\n\n      try {\n        const success = emergencyCleanupStorage()\n        if (success) {\n          this.testResult = '✅ 紧急清理完成！'\n        } else {\n          this.testResult = '❌ 紧急清理失败！'\n        }\n      } catch (error) {\n        this.testResult = `❌ 紧急清理出错: ${error.message}`\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async clearCookies() {\n      this.isLoading = true\n      this.testResult = '正在清理Cookies...'\n\n      try {\n        const success = clearAllCookies()\n        if (success) {\n          this.testResult = '✅ Cookies清理完成！'\n        } else {\n          this.testResult = '❌ Cookies清理失败！'\n        }\n      } catch (error) {\n        this.testResult = `❌ 清理Cookies出错: ${error.message}`\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async testDirectRequest() {\n      this.isLoading = true\n      this.testResult = '正在执行直接请求测试...'\n\n      try {\n        const startTime = Date.now()\n\n        // 使用最小化的fetch请求\n        const data = await minimalFetch('/api/regions')\n\n        const endTime = Date.now()\n\n        this.testResult = `✅ 直接请求成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(data, null, 2)}`\n      } catch (error) {\n        this.testResult = `❌ 直接请求出错: ${error.message}`\n        logger.error('[RequestTest] 直接请求失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async fix431AndTest() {\n      this.isLoading = true\n      this.testResult = '正在修复431错误并测试...'\n\n      try {\n        // 1. 检查风险\n        const riskCheck = check431Risk()\n        this.testResult += `\\n\\n风险检查结果:\\n${JSON.stringify(riskCheck, null, 2)}`\n\n        // 2. 执行修复\n        if (riskCheck.hasRisk) {\n          this.testResult += '\\n\\n检测到风险，正在修复...'\n          const fixResult = fix431Error()\n          this.testResult += `\\n修复结果: ${fixResult ? '成功' : '失败'}`\n        }\n\n        // 3. 测试请求\n        this.testResult += '\\n\\n正在测试大区API...'\n        const startTime = Date.now()\n\n        try {\n          const result = await getRegions()\n          const endTime = Date.now()\n\n          this.testResult += `\\n\\n✅ 大区API测试成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(result, null, 2)}`\n        } catch (apiError) {\n          this.testResult += `\\n\\n❌ 大区API测试失败: ${apiError.message}`\n\n          // 尝试最小化请求\n          try {\n            this.testResult += '\\n\\n尝试最小化请求...'\n            const minimalResult = await minimalFetch('/api/regions')\n            this.testResult += `\\n\\n✅ 最小化请求成功！\n结果: ${JSON.stringify(minimalResult, null, 2)}`\n          } catch (minimalError) {\n            this.testResult += `\\n\\n❌ 最小化请求也失败: ${minimalError.message}`\n          }\n        }\n\n      } catch (error) {\n        this.testResult += `\\n\\n❌ 修复过程出错: ${error.message}`\n        logger.error('[RequestTest] 431修复失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    refreshInfo() {\n      this.calculateStorageInfo()\n      this.calculateHeaderInfo()\n    },\n\n    calculateStorageInfo() {\n      let totalSize = 0\n      let count = 0\n      let largest = { key: '', size: 0 }\n\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        const value = localStorage.getItem(key) || ''\n        const size = key.length + value.length\n        \n        totalSize += size\n        count++\n        \n        if (size > largest.size) {\n          largest = { key, size }\n        }\n      }\n\n      this.storageSize = totalSize\n      this.storageCount = count\n      this.largestItem = largest\n    },\n\n    calculateHeaderInfo() {\n      // 模拟请求头\n      const headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'User-Agent': navigator.userAgent,\n        'Referer': window.location.href,\n        'Accept-Language': navigator.language,\n        'Accept-Encoding': 'gzip, deflate, br',\n        'Connection': 'keep-alive',\n        'Cache-Control': 'no-cache'\n      }\n\n      // 添加可能的认证头\n      try {\n        const token = this.$store?.state?.auth?.token\n        if (token && typeof token === 'string') {\n          headers['Authorization'] = `Bearer ${token}`\n        }\n      } catch (e) {\n        // 忽略错误\n      }\n\n      this.estimatedHeaders = headers\n      this.estimatedHeaderSize = Object.keys(headers).reduce((size, key) => {\n        return size + key.length + (headers[key] || '').length\n      }, 0)\n    },\n\n    formatBytes(bytes) {\n      if (bytes === 0) return '0 Bytes'\n      const k = 1024\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.request-test {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background: #f9f9f9;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.storage-info p {\n  margin: 5px 0;\n  font-family: monospace;\n}\n\n.test-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.test-buttons button {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  background: #007bff;\n  color: white;\n  cursor: pointer;\n}\n\n.test-buttons button:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.test-buttons button:hover:not(:disabled) {\n  background: #0056b3;\n}\n\n.result-area {\n  background: #000;\n  color: #0f0;\n  padding: 15px;\n  border-radius: 4px;\n  font-family: monospace;\n  font-size: 12px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.headers-info p {\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.headers-list {\n  max-height: 200px;\n  overflow-y: auto;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  padding: 10px;\n  background: white;\n}\n\n.header-item {\n  display: flex;\n  margin-bottom: 5px;\n  font-family: monospace;\n  font-size: 12px;\n}\n\n.header-key {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 150px;\n}\n\n.header-value {\n  flex: 1;\n  margin: 0 10px;\n  color: #333;\n}\n\n.header-size {\n  color: #666;\n  font-size: 10px;\n}\n</style>\n"], "mappings": ";;;AA8DA,SAAAA,UAAA;AACA,SAAAC,uBAAA,EAAAC,eAAA;AACA,SAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,WAAA;MACAC,YAAA;MACAC,WAAA;QAAAC,GAAA;QAAAC,IAAA;MAAA;MACAC,gBAAA;MACAC,mBAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,MAAAC,kBAAA;MACA,KAAAZ,SAAA;MACA,KAAAC,UAAA;MAEA;QACA,MAAAY,SAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,MAAAC,MAAA,SAAAzB,UAAA;QACA,MAAA0B,OAAA,GAAAH,IAAA,CAAAC,GAAA;QAEA,KAAAd,UAAA;AACA,MAAAgB,OAAA,GAAAJ,SAAA;AACA,MAAAK,IAAA,CAAAC,SAAA,CAAAH,MAAA;MACA,SAAAI,KAAA;QACA,KAAAnB,UAAA;AACA,QAAAmB,KAAA,CAAAC,IAAA;AACA,QAAAD,KAAA,CAAAE,OAAA;AACA,QAAAF,KAAA,CAAAG,WAAA;AACA,QAAAL,IAAA,CAAAC,SAAA,CAAAC,KAAA;QAEAvB,MAAA,CAAAuB,KAAA,wBAAAA,KAAA;MACA;QACA,KAAApB,SAAA;QACA,KAAAU,WAAA;MACA;IACA;IAEA,MAAAc,eAAA;MACA,KAAAxB,SAAA;MACA,KAAAC,UAAA;MAEA;QACA,MAAAwB,OAAA,GAAAjC,uBAAA;QACA,IAAAiC,OAAA;UACA,KAAAxB,UAAA;QACA;UACA,KAAAA,UAAA;QACA;MACA,SAAAmB,KAAA;QACA,KAAAnB,UAAA,gBAAAmB,KAAA,CAAAE,OAAA;MACA;QACA,KAAAtB,SAAA;QACA,KAAAU,WAAA;MACA;IACA;IAEA,MAAAgB,aAAA;MACA,KAAA1B,SAAA;MACA,KAAAC,UAAA;MAEA;QACA,MAAAwB,OAAA,GAAAhC,eAAA;QACA,IAAAgC,OAAA;UACA,KAAAxB,UAAA;QACA;UACA,KAAAA,UAAA;QACA;MACA,SAAAmB,KAAA;QACA,KAAAnB,UAAA,qBAAAmB,KAAA,CAAAE,OAAA;MACA;QACA,KAAAtB,SAAA;QACA,KAAAU,WAAA;MACA;IACA;IAEA,MAAAiB,kBAAA;MACA,KAAA3B,SAAA;MACA,KAAAC,UAAA;MAEA;QACA,MAAAY,SAAA,GAAAC,IAAA,CAAAC,GAAA;;QAEA;QACA,MAAAhB,IAAA,SAAAH,YAAA;QAEA,MAAAqB,OAAA,GAAAH,IAAA,CAAAC,GAAA;QAEA,KAAAd,UAAA;AACA,MAAAgB,OAAA,GAAAJ,SAAA;AACA,MAAAK,IAAA,CAAAC,SAAA,CAAApB,IAAA;MACA,SAAAqB,KAAA;QACA,KAAAnB,UAAA,gBAAAmB,KAAA,CAAAE,OAAA;QACAzB,MAAA,CAAAuB,KAAA,0BAAAA,KAAA;MACA;QACA,KAAApB,SAAA;QACA,KAAAU,WAAA;MACA;IACA;IAEA,MAAAkB,cAAA;MACA,KAAA5B,SAAA;MACA,KAAAC,UAAA;MAEA;QACA;QACA,MAAA4B,SAAA,GAAAlC,YAAA;QACA,KAAAM,UAAA,oBAAAiB,IAAA,CAAAC,SAAA,CAAAU,SAAA;;QAEA;QACA,IAAAA,SAAA,CAAAC,OAAA;UACA,KAAA7B,UAAA;UACA,MAAA8B,SAAA,GAAArC,WAAA;UACA,KAAAO,UAAA,eAAA8B,SAAA;QACA;;QAEA;QACA,KAAA9B,UAAA;QACA,MAAAY,SAAA,GAAAC,IAAA,CAAAC,GAAA;QAEA;UACA,MAAAC,MAAA,SAAAzB,UAAA;UACA,MAAA0B,OAAA,GAAAH,IAAA,CAAAC,GAAA;UAEA,KAAAd,UAAA;AACA,MAAAgB,OAAA,GAAAJ,SAAA;AACA,MAAAK,IAAA,CAAAC,SAAA,CAAAH,MAAA;QACA,SAAAgB,QAAA;UACA,KAAA/B,UAAA,wBAAA+B,QAAA,CAAAV,OAAA;;UAEA;UACA;YACA,KAAArB,UAAA;YACA,MAAAgC,aAAA,SAAArC,YAAA;YACA,KAAAK,UAAA;AACA,MAAAiB,IAAA,CAAAC,SAAA,CAAAc,aAAA;UACA,SAAAC,YAAA;YACA,KAAAjC,UAAA,uBAAAiC,YAAA,CAAAZ,OAAA;UACA;QACA;MAEA,SAAAF,KAAA;QACA,KAAAnB,UAAA,qBAAAmB,KAAA,CAAAE,OAAA;QACAzB,MAAA,CAAAuB,KAAA,2BAAAA,KAAA;MACA;QACA,KAAApB,SAAA;QACA,KAAAU,WAAA;MACA;IACA;IAEAA,YAAA;MACA,KAAAyB,oBAAA;MACA,KAAAC,mBAAA;IACA;IAEAD,qBAAA;MACA,IAAAE,SAAA;MACA,IAAAC,KAAA;MACA,IAAAC,OAAA;QAAAlC,GAAA;QAAAC,IAAA;MAAA;MAEA,SAAAkC,CAAA,MAAAA,CAAA,GAAAC,YAAA,CAAAC,MAAA,EAAAF,CAAA;QACA,MAAAnC,GAAA,GAAAoC,YAAA,CAAApC,GAAA,CAAAmC,CAAA;QACA,MAAAG,KAAA,GAAAF,YAAA,CAAAG,OAAA,CAAAvC,GAAA;QACA,MAAAC,IAAA,GAAAD,GAAA,CAAAqC,MAAA,GAAAC,KAAA,CAAAD,MAAA;QAEAL,SAAA,IAAA/B,IAAA;QACAgC,KAAA;QAEA,IAAAhC,IAAA,GAAAiC,OAAA,CAAAjC,IAAA;UACAiC,OAAA;YAAAlC,GAAA;YAAAC;UAAA;QACA;MACA;MAEA,KAAAJ,WAAA,GAAAmC,SAAA;MACA,KAAAlC,YAAA,GAAAmC,KAAA;MACA,KAAAlC,WAAA,GAAAmC,OAAA;IACA;IAEAH,oBAAA;MACA;MACA,MAAAS,OAAA;QACA;QACA;QACA,cAAAC,SAAA,CAAAC,SAAA;QACA,WAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;QACA,mBAAAJ,SAAA,CAAAK,QAAA;QACA;QACA;QACA;MACA;;MAEA;MACA;QAAA,IAAAC,YAAA;QACA,MAAAC,KAAA,IAAAD,YAAA,QAAAE,MAAA,cAAAF,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAG,KAAA,cAAAH,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAI,IAAA,cAAAJ,YAAA,uBAAAA,YAAA,CAAAC,KAAA;QACA,IAAAA,KAAA,WAAAA,KAAA;UACAR,OAAA,8BAAAQ,KAAA;QACA;MACA,SAAAI,CAAA;QACA;MAAA;MAGA,KAAAlD,gBAAA,GAAAsC,OAAA;MACA,KAAArC,mBAAA,GAAAkD,MAAA,CAAAC,IAAA,CAAAd,OAAA,EAAAe,MAAA,EAAAtD,IAAA,EAAAD,GAAA;QACA,OAAAC,IAAA,GAAAD,GAAA,CAAAqC,MAAA,IAAAG,OAAA,CAAAxC,GAAA,SAAAqC,MAAA;MACA;IACA;IAEAmB,YAAAC,KAAA;MACA,IAAAA,KAAA;MACA,MAAAC,CAAA;MACA,MAAAC,KAAA;MACA,MAAAxB,CAAA,GAAAyB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,GAAA,CAAAL,KAAA,IAAAG,IAAA,CAAAE,GAAA,CAAAJ,CAAA;MACA,OAAAK,UAAA,EAAAN,KAAA,GAAAG,IAAA,CAAAI,GAAA,CAAAN,CAAA,EAAAvB,CAAA,GAAA8B,OAAA,aAAAN,KAAA,CAAAxB,CAAA;IACA;EACA;AACA", "ignoreList": []}]}