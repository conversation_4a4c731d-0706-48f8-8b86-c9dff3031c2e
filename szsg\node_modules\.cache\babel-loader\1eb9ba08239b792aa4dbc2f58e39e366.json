{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CreateCharacter.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CreateCharacter.vue", "mtime": 1749698812008}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBHYW1lTGF5b3V0IGZyb20gJ0AvbGF5b3V0cy9HYW1lTGF5b3V0LnZ1ZSc7CmltcG9ydCB7IG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IHsgcmVnaW9uU2VydmljZSB9IGZyb20gJ0AvYXBpJzsKaW1wb3J0IGxvZ2dlciBmcm9tICdAL3V0aWxzL2xvZ2dlcic7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQ3JlYXRlQ2hhcmFjdGVyJywKICBjb21wb25lbnRzOiB7CiAgICBHYW1lTGF5b3V0CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFJlZ2lvbjogbnVsbCwKICAgICAgaXNDcmVhdGluZzogZmFsc2UsCiAgICAgIHNlbGVjdGVkQXZhdGFyOiAwLAogICAgICBjaGFyYWN0ZXJEYXRhOiB7CiAgICAgICAgbmFtZTogJycsCiAgICAgICAgZ2VuZGVyOiAnbWFsZScsCiAgICAgICAgcHJvZmVzc2lvbjogJycKICAgICAgfSwKICAgICAgcHJvZmVzc2lvbnM6IFt7CiAgICAgICAgaWQ6ICd3YXJyaW9yJywKICAgICAgICBuYW1lOiAn5q2m5aOrJywKICAgICAgICBkZXNjcmlwdGlvbjogJ+atpuiJuumrmOW8uueahOaImOWjq++8jOaThemVv+i/kei6q+aQj+aWl++8jOWIgOWJkeeyvumAmicKICAgICAgfSwgewogICAgICAgIGlkOiAnc2Nob2xhcicsCiAgICAgICAgbmFtZTogJ+aWh+S6uicsCiAgICAgICAgZGVzY3JpcHRpb246ICfljZrlrablpJrmiY3nmoTlrabogIXvvIznsr7pgJror5for43mrYzotYvvvIzms5XmnK/pgKDor6Pmt7HljponCiAgICAgIH0sIHsKICAgICAgICBpZDogJ215c3RpYycsCiAgICAgICAgbmFtZTogJ+W8guS6uicsCiAgICAgICAgZGVzY3JpcHRpb246ICfnpZ7np5jojqvmtYvnmoTkv67ooYzogIXvvIzouqvmgIDlpYfmnK/vvIzog73lipvlnYfooaEnCiAgICAgIH1dCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzRm9ybVZhbGlkKCkgewogICAgICByZXR1cm4gdGhpcy5jaGFyYWN0ZXJEYXRhLm5hbWUudHJpbSgpLmxlbmd0aCA+PSAyICYmIHRoaXMuY2hhcmFjdGVyRGF0YS5nZW5kZXIgJiYgdGhpcy5jaGFyYWN0ZXJEYXRhLnByb2Zlc3Npb247CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgbG9nZ2VyLmRlYnVnKCdbQ3JlYXRlQ2hhcmFjdGVyXSDpobXpnaLliJ3lp4vljJYnKTsKCiAgICAvLyDojrflj5blvZPliY3pgInmi6nnmoTlpKfljLoKICAgIHRoaXMuY3VycmVudFJlZ2lvbiA9IHJlZ2lvblNlcnZpY2UuZ2V0Q3VycmVudFJlZ2lvbigpOwogICAgaWYgKCF0aGlzLmN1cnJlbnRSZWdpb24pIHsKICAgICAgdGhpcy5zaG93VG9hc3QoJ+ivt+WFiOmAieaLqeWkp+WMuicpOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3NldHVwL3JlZ2lvbi1zZWxlY3QnKTsKICAgICAgcmV0dXJuOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLi4ubWFwQWN0aW9ucygnY2hhcmFjdGVyJywgWydjcmVhdGVDaGFyYWN0ZXInXSksCiAgICBzZWxlY3RQcm9mZXNzaW9uKHByb2Zlc3Npb24pIHsKICAgICAgdGhpcy5jaGFyYWN0ZXJEYXRhLnByb2Zlc3Npb24gPSBwcm9mZXNzaW9uLmlkOwogICAgICBsb2dnZXIuZGVidWcoJ1tDcmVhdGVDaGFyYWN0ZXJdIOmAieaLqeiBjOS4mjonLCBwcm9mZXNzaW9uLm5hbWUpOwogICAgfSwKICAgIGdldEF2YWlsYWJsZUF2YXRhcnMoKSB7CiAgICAgIGNvbnN0IGdlbmRlciA9IHRoaXMuY2hhcmFjdGVyRGF0YS5nZW5kZXIgfHwgJ21hbGUnOwogICAgICBjb25zdCBwcm9mZXNzaW9uID0gdGhpcy5jaGFyYWN0ZXJEYXRhLnByb2Zlc3Npb24gfHwgJ3dhcnJpb3InOwogICAgICByZXR1cm4gW2Avc3RhdGljL2dhbWUvYXZhdGFycy8ke2dlbmRlcn1fJHtwcm9mZXNzaW9ufV8xLnBuZ2AsIGAvc3RhdGljL2dhbWUvYXZhdGFycy8ke2dlbmRlcn1fJHtwcm9mZXNzaW9ufV8yLnBuZ2AsIGAvc3RhdGljL2dhbWUvYXZhdGFycy8ke2dlbmRlcn1fJHtwcm9mZXNzaW9ufV8zLnBuZ2BdOwogICAgfSwKICAgIGFzeW5jIHN1Ym1pdENyZWF0ZUNoYXJhY3RlcigpIHsKICAgICAgaWYgKCF0aGlzLmlzRm9ybVZhbGlkKSB7CiAgICAgICAgdGhpcy5zaG93VG9hc3QoJ+ivt+WujOWWhOinkuiJsuS/oeaBrycpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmlzQ3JlYXRpbmcgPSB0cnVlOwogICAgICB0cnkgewogICAgICAgIHZhciBfdGhpcyRjdXJyZW50UmVnaW9uOwogICAgICAgIGNvbnN0IGNoYXJhY3RlckRhdGEgPSB7CiAgICAgICAgICBuYW1lOiB0aGlzLmNoYXJhY3RlckRhdGEubmFtZS50cmltKCksCiAgICAgICAgICBnZW5kZXI6IHRoaXMuY2hhcmFjdGVyRGF0YS5nZW5kZXIsCiAgICAgICAgICBwcm9mZXNzaW9uOiB0aGlzLmNoYXJhY3RlckRhdGEucHJvZmVzc2lvbiwKICAgICAgICAgIC8vIOebtOaOpeS9v+eUqHByb2Zlc3Npb27lrZfmrrUKICAgICAgICAgIHJlZ2lvbl9pZDogKChfdGhpcyRjdXJyZW50UmVnaW9uID0gdGhpcy5jdXJyZW50UmVnaW9uKSA9PT0gbnVsbCB8fCBfdGhpcyRjdXJyZW50UmVnaW9uID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpcyRjdXJyZW50UmVnaW9uLmlkKSB8fCAxIC8vIOaPkOS+m+m7mOiupOWAvAogICAgICAgIH07CiAgICAgICAgbG9nZ2VyLmRlYnVnKCdbQ3JlYXRlQ2hhcmFjdGVyXSDmj5DkuqTmlbDmja46JywgY2hhcmFjdGVyRGF0YSk7CiAgICAgICAgbG9nZ2VyLmRlYnVnKCdbQ3JlYXRlQ2hhcmFjdGVyXSDlvZPliY3ljLrln586JywgdGhpcy5jdXJyZW50UmVnaW9uKTsKICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmNyZWF0ZUNoYXJhY3RlcihjaGFyYWN0ZXJEYXRhKTsKICAgICAgICBpZiAocmVzdWx0ICYmIHJlc3VsdC5zdWNjZXNzKSB7CiAgICAgICAgICB0aGlzLnNob3dUb2FzdCgn6KeS6Imy5Yib5bu65oiQ5Yqf77yBJyk7CiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9zZXR1cC9jaGFyYWN0ZXItc2VsZWN0Jyk7CiAgICAgICAgICB9LCAxNTAwKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdmFyIF9yZXN1bHQkZXJyb3I7CiAgICAgICAgICB0aGlzLnNob3dUb2FzdCgocmVzdWx0ID09PSBudWxsIHx8IHJlc3VsdCA9PT0gdm9pZCAwIHx8IChfcmVzdWx0JGVycm9yID0gcmVzdWx0LmVycm9yKSA9PT0gbnVsbCB8fCBfcmVzdWx0JGVycm9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfcmVzdWx0JGVycm9yLm1lc3NhZ2UpIHx8ICfliJvlu7rop5LoibLlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgbG9nZ2VyLmVycm9yKCdbQ3JlYXRlQ2hhcmFjdGVyXSDliJvlu7rop5LoibLlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuc2hvd1RvYXN0KGVycm9yLm1lc3NhZ2UgfHwgJ+WIm+W7uuinkuiJsuWksei0pe+8jOivt+mHjeivlScpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaXNDcmVhdGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgZ29CYWNrKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3NldHVwL2NoYXJhY3Rlci1zZWxlY3QnKTsKICAgIH0sCiAgICBzaG93VG9hc3QobWVzc2FnZSkgewogICAgICBjb25zdCB0b2FzdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpOwogICAgICB0b2FzdC50ZXh0Q29udGVudCA9IG1lc3NhZ2U7CiAgICAgIHRvYXN0LnN0eWxlLmNzc1RleHQgPSBgCiAgICAgICAgcG9zaXRpb246IGZpeGVkOwogICAgICAgIHRvcDogNTAlOwogICAgICAgIGxlZnQ6IDUwJTsKICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTsKICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuOCk7CiAgICAgICAgY29sb3I6IHdoaXRlOwogICAgICAgIHBhZGRpbmc6IDEycHggMjBweDsKICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7CiAgICAgICAgei1pbmRleDogMTAwMDA7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBgOwogICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRvYXN0KTsKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZCh0b2FzdCk7CiAgICAgIH0sIDIwMDApOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["GameLayout", "mapActions", "regionService", "logger", "name", "components", "data", "currentRegion", "isCreating", "<PERSON><PERSON><PERSON><PERSON>", "characterData", "gender", "profession", "professions", "id", "description", "computed", "isFormValid", "trim", "length", "created", "debug", "getCurrentRegion", "showToast", "$router", "push", "methods", "selectProfession", "getAvailableAvatars", "submitCreateCharacter", "_this$currentRegion", "region_id", "result", "createCharacter", "success", "setTimeout", "_result$error", "error", "message", "goBack", "toast", "document", "createElement", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/setup/CreateCharacter.vue"], "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"create-character-container\">\n      <!-- 创建角色主界面 -->\n      <div class=\"character-creation-panel\">\n        <form @submit.prevent=\"submitCreateCharacter\" class=\"character-form\">\n          <!-- 名称输入区 -->\n          <div class=\"name-section\">\n            <span class=\"label-text\">名称:</span>\n            <input \n              v-model=\"characterData.name\"\n              type=\"text\" \n              class=\"name-input\"\n              maxlength=\"10\"\n              required\n            />\n          </div>\n\n          <!-- 性别选择区 -->\n          <div class=\"gender-section\">\n            <span class=\"label-text\">性别:</span>\n            <div class=\"gender-buttons\">\n              <button \n                type=\"button\"\n                class=\"gender-btn\"\n                :class=\"{ 'selected': characterData.gender === 'male' }\"\n                @click=\"characterData.gender = 'male'\"\n              >\n                男\n              </button>\n              <button \n                type=\"button\"\n                class=\"gender-btn\"\n                :class=\"{ 'selected': characterData.gender === 'female' }\"\n                @click=\"characterData.gender = 'female'\"\n              >\n                女\n              </button>\n            </div>\n          </div>\n\n          <!-- 职业选择区 -->\n          <div class=\"profession-section\">\n            <span class=\"label-text\">职业:</span>\n            <div class=\"profession-buttons\">\n              <button \n                v-for=\"profession in professions\" \n                :key=\"profession.id\"\n                type=\"button\"\n                class=\"profession-btn\"\n                :class=\"{ 'selected': characterData.profession === profession.id }\"\n                @click=\"selectProfession(profession)\"\n              >\n                {{ profession.name }}\n              </button>\n            </div>\n          </div>\n\n          <!-- 头像选择区 -->\n          <div class=\"avatar-section\">\n            <span class=\"label-text\">头像:</span>\n            <div class=\"avatar-grid\">\n              <div \n                v-for=\"(avatar, index) in getAvailableAvatars()\" \n                :key=\"index\"\n                class=\"avatar-option\"\n                :class=\"{ 'selected': selectedAvatar === index }\"\n                @click=\"selectedAvatar = index\"\n              >\n                <img :src=\"avatar\" :alt=\"`头像${index + 1}`\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- 提示文字 -->\n          <div class=\"hint-text\">\n            请选择在游戏中角色的性别\n          </div>\n\n        </form>\n\n        <!-- 操作按钮区域 -->\n        <div class=\"action-buttons\">\n          <button\n            type=\"button\"\n            @click=\"goBack\"\n            class=\"game-btn return-btn\"\n            :disabled=\"isCreating\"\n          >\n            返回\n          </button>\n          <button\n            type=\"button\"\n            @click=\"submitCreateCharacter\"\n            class=\"game-btn create-btn\"\n            :disabled=\"!isFormValid || isCreating\"\n          >\n            创建角色\n          </button>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapActions } from 'vuex'\nimport { regionService } from '@/api'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'CreateCharacter',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      currentRegion: null,\n      isCreating: false,\n      selectedAvatar: 0,\n      characterData: {\n        name: '',\n        gender: 'male',\n        profession: ''\n      },\n      professions: [\n        {\n          id: 'warrior',\n          name: '武士',\n          description: '武艺高强的战士，擅长近身搏斗，刀剑精通'\n        },\n        {\n          id: 'scholar',\n          name: '文人',\n          description: '博学多才的学者，精通诗词歌赋，法术造诣深厚'\n        },\n        {\n          id: 'mystic',\n          name: '异人',\n          description: '神秘莫测的修行者，身怀奇术，能力均衡'\n        }\n      ]\n    }\n  },\n  computed: {\n    isFormValid() {\n      return this.characterData.name.trim().length >= 2 &&\n             this.characterData.gender &&\n             this.characterData.profession\n    }\n  },\n  created() {\n    logger.debug('[CreateCharacter] 页面初始化')\n    \n    // 获取当前选择的大区\n    this.currentRegion = regionService.getCurrentRegion()\n    if (!this.currentRegion) {\n      this.showToast('请先选择大区')\n      this.$router.push('/setup/region-select')\n      return\n    }\n  },\n  methods: {\n    ...mapActions('character', ['createCharacter']),\n\n    selectProfession(profession) {\n      this.characterData.profession = profession.id\n      logger.debug('[CreateCharacter] 选择职业:', profession.name)\n    },\n\n    getAvailableAvatars() {\n      const gender = this.characterData.gender || 'male'\n      const profession = this.characterData.profession || 'warrior'\n      return [\n        `/static/game/avatars/${gender}_${profession}_1.png`,\n        `/static/game/avatars/${gender}_${profession}_2.png`,\n        `/static/game/avatars/${gender}_${profession}_3.png`\n      ]\n    },\n\n    async submitCreateCharacter() {\n      if (!this.isFormValid) {\n        this.showToast('请完善角色信息')\n        return\n      }\n\n      this.isCreating = true\n\n      try {\n        const characterData = {\n          name: this.characterData.name.trim(),\n          gender: this.characterData.gender,\n          profession: this.characterData.profession, // 直接使用profession字段\n          region_id: this.currentRegion?.id || 1 // 提供默认值\n        }\n\n        logger.debug('[CreateCharacter] 提交数据:', characterData)\n        logger.debug('[CreateCharacter] 当前区域:', this.currentRegion)\n\n        const result = await this.createCharacter(characterData)\n        \n        if (result && result.success) {\n          this.showToast('角色创建成功！')\n          setTimeout(() => {\n            this.$router.push('/setup/character-select')\n          }, 1500)\n        } else {\n          this.showToast(result?.error?.message || '创建角色失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[CreateCharacter] 创建角色失败:', error)\n        this.showToast(error.message || '创建角色失败，请重试')\n      } finally {\n        this.isCreating = false\n      }\n    },\n\n    goBack() {\n      this.$router.push('/setup/character-select')\n    },\n\n    showToast(message) {\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 全局重置和基础样式 */\n* {\n  box-sizing: border-box;\n}\n\nhtml, body {\n  margin: 0;\n  padding: 0;\n  overflow-x: hidden;\n}\n.create-character-container {\n  padding: 5px 15px;\n  max-width: 480px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  align-items: flex-start;\n  justify-content: center;\n  box-sizing: border-box;\n  padding-top: 10px;\n}\n\n.character-creation-panel {\n  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);\n  border: 3px solid #8b7355;\n  border-radius: 12px;\n  padding: 20px;\n  width: 100%;\n  max-width: 420px;\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);\n  box-sizing: border-box;\n  position: relative;\n  margin-top: -25px;\n}\n\n.character-form {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n/* 名称输入区 */\n.name-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.label-text {\n  color: #ffd700;\n  font-size: 16px;\n  font-weight: bold;\n  min-width: 55px;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.name-input {\n  flex: 1;\n  background: #ffff99;\n  border: 2px solid #8b7355;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #000;\n  outline: none;\n  transition: border-color 0.3s ease;\n}\n\n.name-input:focus {\n  border-color: #ffd700;\n  box-shadow: 0 0 3px rgba(255, 215, 0, 0.5);\n}\n\n/* 性别选择区 */\n.gender-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.gender-buttons {\n  display: flex;\n  gap: 20px;\n}\n\n.gender-btn {\n  background: transparent;\n  border: none;\n  color: #ffd700;\n  font-size: 18px;\n  font-weight: bold;\n  cursor: pointer;\n  padding: 8px 16px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.gender-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.05);\n}\n\n.gender-btn.selected {\n  background: rgba(255, 215, 0, 0.3);\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n/* 职业选择区 */\n.profession-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.profession-buttons {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.profession-btn {\n  background: transparent;\n  border: none;\n  color: #ffd700;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  padding: 8px 14px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.profession-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.05);\n}\n\n.profession-btn.selected {\n  background: rgba(255, 215, 0, 0.3);\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n/* 头像选择区 */\n.avatar-section {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.avatar-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 10px;\n}\n\n.avatar-option {\n  width: 65px;\n  height: 65px;\n  border: 2px solid #8b7355;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.avatar-option:hover {\n  border-color: #ffd700;\n  transform: scale(1.05);\n}\n\n.avatar-option.selected {\n  border-color: #ffd700;\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n.avatar-option img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 提示文字 */\n.hint-text {\n  background: rgba(0, 0, 0, 0.8);\n  color: #ffd700;\n  text-align: center;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 13px;\n  margin: 10px 0 5px 0;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 25px;\n  margin-top: 5px;\n  padding: 10px 0;\n  border-top: 1px solid #8b7355;\n}\n\n.game-btn {\n  background: linear-gradient(135deg, #8b7355 0%, #a0845c 50%, #8b7355 100%);\n  border: 2px solid #ffd700;\n  border-radius: 8px;\n  color: #ffd700;\n  font-size: 14px;\n  font-weight: bold;\n  padding: 10px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  min-width: 80px;\n}\n\n.game-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #a0845c 0%, #b8956b 50%, #a0845c 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);\n}\n\n.game-btn:active:not(:disabled) {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.game-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n  background: #666;\n  border-color: #999;\n  color: #ccc;\n}\n\n.return-btn {\n  background: linear-gradient(135deg, #6b4423 0%, #8b5a2b 50%, #6b4423 100%);\n}\n\n.return-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #8b5a2b 0%, #a0673a 50%, #8b5a2b 100%);\n}\n\n.create-btn {\n  background: linear-gradient(135deg, #2d5016 0%, #4a7c23 50%, #2d5016 100%);\n}\n\n.create-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #4a7c23 0%, #5e9c2e 50%, #4a7c23 100%);\n}\n\n@media (max-width: 768px) {\n  .create-character-container {\n    padding: 8px;\n    min-height: 100vh;\n  }\n\n  .character-creation-panel {\n    margin: 0;\n    padding: 18px;\n    max-width: 100%;\n    border-radius: 8px;\n  }\n\n  .character-form {\n    gap: 15px;\n  }\n\n  .name-section,\n  .gender-section,\n  .profession-section,\n  .avatar-section {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n    padding: 10px 0;\n  }\n\n  .label-text {\n    min-width: auto;\n    font-size: 15px;\n  }\n\n  .gender-buttons,\n  .profession-buttons {\n    gap: 15px;\n    flex-wrap: wrap;\n  }\n\n  .avatar-grid {\n    gap: 8px;\n  }\n\n  .avatar-option {\n    width: 55px;\n    height: 55px;\n  }\n\n  .action-buttons {\n    gap: 18px;\n    margin-top: 15px;\n    padding: 12px 0;\n  }\n\n  .game-btn {\n    font-size: 13px;\n    padding: 8px 16px;\n    min-width: 70px;\n  }\n}\n\n/* 针对小屏幕设备的额外优化 */\n@media (max-width: 480px) {\n  .create-character-container {\n    padding: 5px;\n    min-height: 100vh;\n  }\n\n  .character-creation-panel {\n    padding: 12px;\n    border-radius: 6px;\n  }\n\n  .character-form {\n    gap: 12px;\n  }\n\n  .name-section,\n  .gender-section,\n  .profession-section,\n  .avatar-section {\n    padding: 8px 0;\n  }\n\n  .label-text {\n    font-size: 14px;\n  }\n\n  .gender-btn {\n    font-size: 16px;\n    padding: 6px 12px;\n  }\n\n  .profession-btn {\n    font-size: 14px;\n    padding: 6px 10px;\n  }\n\n  .gender-buttons,\n  .profession-buttons {\n    gap: 10px;\n  }\n\n  .avatar-grid {\n    gap: 6px;\n  }\n\n  .avatar-option {\n    width: 48px;\n    height: 48px;\n  }\n\n  .action-buttons {\n    gap: 15px;\n    margin-top: 12px;\n    padding: 10px 0;\n  }\n\n  .game-btn {\n    font-size: 12px;\n    padding: 6px 12px;\n    min-width: 60px;\n  }\n}\n</style>\n"], "mappings": ";AA0GA,OAAAA,UAAA;AACA,SAAAC,UAAA;AACA,SAAAC,aAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;QACAN,IAAA;QACAO,MAAA;QACAC,UAAA;MACA;MACAC,WAAA,GACA;QACAC,EAAA;QACAV,IAAA;QACAW,WAAA;MACA,GACA;QACAD,EAAA;QACAV,IAAA;QACAW,WAAA;MACA,GACA;QACAD,EAAA;QACAV,IAAA;QACAW,WAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,YAAA;MACA,YAAAP,aAAA,CAAAN,IAAA,CAAAc,IAAA,GAAAC,MAAA,SACA,KAAAT,aAAA,CAAAC,MAAA,IACA,KAAAD,aAAA,CAAAE,UAAA;IACA;EACA;EACAQ,QAAA;IACAjB,MAAA,CAAAkB,KAAA;;IAEA;IACA,KAAAd,aAAA,GAAAL,aAAA,CAAAoB,gBAAA;IACA,UAAAf,aAAA;MACA,KAAAgB,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;MACA;IACA;EACA;EACAC,OAAA;IACA,GAAAzB,UAAA;IAEA0B,iBAAAf,UAAA;MACA,KAAAF,aAAA,CAAAE,UAAA,GAAAA,UAAA,CAAAE,EAAA;MACAX,MAAA,CAAAkB,KAAA,4BAAAT,UAAA,CAAAR,IAAA;IACA;IAEAwB,oBAAA;MACA,MAAAjB,MAAA,QAAAD,aAAA,CAAAC,MAAA;MACA,MAAAC,UAAA,QAAAF,aAAA,CAAAE,UAAA;MACA,QACA,wBAAAD,MAAA,IAAAC,UAAA,UACA,wBAAAD,MAAA,IAAAC,UAAA,UACA,wBAAAD,MAAA,IAAAC,UAAA,SACA;IACA;IAEA,MAAAiB,sBAAA;MACA,UAAAZ,WAAA;QACA,KAAAM,SAAA;QACA;MACA;MAEA,KAAAf,UAAA;MAEA;QAAA,IAAAsB,mBAAA;QACA,MAAApB,aAAA;UACAN,IAAA,OAAAM,aAAA,CAAAN,IAAA,CAAAc,IAAA;UACAP,MAAA,OAAAD,aAAA,CAAAC,MAAA;UACAC,UAAA,OAAAF,aAAA,CAAAE,UAAA;UAAA;UACAmB,SAAA,IAAAD,mBAAA,QAAAvB,aAAA,cAAAuB,mBAAA,uBAAAA,mBAAA,CAAAhB,EAAA;QACA;QAEAX,MAAA,CAAAkB,KAAA,4BAAAX,aAAA;QACAP,MAAA,CAAAkB,KAAA,iCAAAd,aAAA;QAEA,MAAAyB,MAAA,cAAAC,eAAA,CAAAvB,aAAA;QAEA,IAAAsB,MAAA,IAAAA,MAAA,CAAAE,OAAA;UACA,KAAAX,SAAA;UACAY,UAAA;YACA,KAAAX,OAAA,CAAAC,IAAA;UACA;QACA;UAAA,IAAAW,aAAA;UACA,KAAAb,SAAA,EAAAS,MAAA,aAAAA,MAAA,gBAAAI,aAAA,GAAAJ,MAAA,CAAAK,KAAA,cAAAD,aAAA,uBAAAA,aAAA,CAAAE,OAAA;QACA;MACA,SAAAD,KAAA;QACAlC,MAAA,CAAAkC,KAAA,8BAAAA,KAAA;QACA,KAAAd,SAAA,CAAAc,KAAA,CAAAC,OAAA;MACA;QACA,KAAA9B,UAAA;MACA;IACA;IAEA+B,OAAA;MACA,KAAAf,OAAA,CAAAC,IAAA;IACA;IAEAF,UAAAe,OAAA;MACA,MAAAE,KAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,KAAA,CAAAG,WAAA,GAAAL,OAAA;MACAE,KAAA,CAAAI,KAAA,CAAAC,OAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACAJ,QAAA,CAAAK,IAAA,CAAAC,WAAA,CAAAP,KAAA;MACAL,UAAA;QACAM,QAAA,CAAAK,IAAA,CAAAE,WAAA,CAAAR,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}