<?php

namespace App\Services;

use App\Events\ChatEvent;
use App\Models\ChatMessage;
use App\Models\Character;
use Illuminate\Support\Facades\Log;

class ChatSocketService
{
    /**
     * 发送消息到聊天频道
     *
     * @param int $characterId 发送者角色ID
     * @param string $channel 频道名称 (world, team, private, etc)
     * @param string $message 消息内容
     * @param int|null $receiverId 私聊接收者ID
     * @return ChatMessage
     */
    public function sendMessage($characterId, $channel, $message, $receiverId = null)
    {
        // 获取发送者角色信息
        $character = Character::findOrFail($characterId);

        // 创建聊天消息记录
        $chatMessage = new ChatMessage();
        $chatMessage->sender_id = $characterId;
        $chatMessage->receiver_id = $receiverId;
        $chatMessage->channel = $channel;
        $chatMessage->message = $message;
        $chatMessage->save();

        // 准备广播数据
        $messageData = [
            'id' => $chatMessage->id,
            'sender' => [
                'id' => $character->id,
                'name' => $character->name,
                'level' => $character->level,
                'avatar' => $character->avatar ?? null,
            ],
            'message' => $message,
            'timestamp' => $chatMessage->created_at,
        ];

        // 根据频道类型选择不同的广播方式
        switch ($channel) {
            case 'world':
                // 世界频道，广播给所有人
                event(new ChatEvent('world_message', [
                    'message' => $messageData
                ]));
                break;

            case 'team':
                // 获取角色所在的队伍ID
                $teamMember = $character->teamMember;
                if ($teamMember) {
                    $teamId = $teamMember->team_id;
                    // 队伍频道，只广播给队伍成员
                    event(new ChatEvent('team_message', [
                        'team_id' => $teamId,
                        'message' => $messageData
                    ]));
                } else {
                    // 如果不在队伍中，记录错误
                    Log::warning("Character {$characterId} tried to send team message but is not in a team.");
                }
                break;

            case 'private':
                // 私聊频道，只发送给特定接收者
                if ($receiverId) {
                    // 添加接收者信息到消息数据
                    $receiver = Character::find($receiverId);
                    if ($receiver) {
                        $messageData['receiver'] = [
                            'id' => $receiver->id,
                            'name' => $receiver->name,
                        ];

                        // 分别给发送者和接收者广播消息
                        event(new ChatEvent('private_message', [
                            'sender_id' => $characterId,
                            'receiver_id' => $receiverId,
                            'message' => $messageData
                        ]));
                    } else {
                        Log::warning("Private message receiver {$receiverId} not found.");
                    }
                } else {
                    Log::warning("Attempted to send private message without receiver ID.");
                }
                break;

            default:
                // 其他自定义频道
                event(new ChatEvent('channel_message', [
                    'channel' => $channel,
                    'message' => $messageData
                ]));
                break;
        }

        return $chatMessage;
    }

    /**
     * 获取特定频道的历史消息
     *
     * @param string $channel 频道名称
     * @param int|null $characterId 当前角色ID
     * @param int|null $receiverId 私聊接收者ID
     * @param int $limit 消息数量限制
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getChannelHistory($channel, $characterId = null, $receiverId = null, $limit = 50)
    {
        $query = ChatMessage::with('sender:id,name,level,avatar')
            ->where('channel', $channel)
            ->latest()
            ->limit($limit);

        // 如果是私聊，需要筛选发送者和接收者
        if ($channel === 'private' && $characterId && $receiverId) {
            $query->where(function($q) use ($characterId, $receiverId) {
                // 当前角色发送给接收者的
                $q->where(function($innerQ) use ($characterId, $receiverId) {
                    $innerQ->where('sender_id', $characterId)
                        ->where('receiver_id', $receiverId);
                })
                // 或接收者发送给当前角色的
                ->orWhere(function($innerQ) use ($characterId, $receiverId) {
                    $innerQ->where('sender_id', $receiverId)
                        ->where('receiver_id', $characterId);
                });
            });
        }
        // 如果是队伍频道，需要确保当前角色在队伍中
        else if ($channel === 'team' && $characterId) {
            $character = Character::find($characterId);
            if ($character && $character->teamMember) {
                $teamId = $character->teamMember->team_id;
                // 获取队伍所有成员
                $teamMemberIds = $character->teamMember->team->members->pluck('character_id')->toArray();
                // 只显示队伍成员发送的消息
                $query->whereIn('sender_id', $teamMemberIds);
            } else {
                // 如果不在队伍中，返回空集合
                return collect();
            }
        }

        // 查询并返回结果
        $messages = $query->get();

        // 格式化消息，增加额外的显示信息
        $formattedMessages = $messages->map(function($message) {
            return [
                'id' => $message->id,
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                    'level' => $message->sender->level,
                    'avatar' => $message->sender->avatar ?? null,
                ],
                'receiver_id' => $message->receiver_id,
                'message' => $message->message,
                'timestamp' => $message->created_at,
            ];
        });

        return $formattedMessages;
    }
}
