module.exports = {
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_URL || 'http://localhost:8000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''  // 移除前缀，使请求正确到达 Laravel 路由
        },
        logLevel: 'debug'  // 添加日志级别以帮助调试
      }
    },
    // 确保开发服务器热更新功能
    hot: true,
    // 显示编译错误
    overlay: {
      warnings: false,
      errors: true
    },
    // 自动打开浏览器
    open: false,
    // sockjs配置
    sockHost: 'localhost',
    disableHostCheck: true,
    public: 'localhost:8080'
  },
  css: {
    loaderOptions: {
      scss: {
        prependData: `@use "sass:color"; @use "@/assets/styles/variables.scss" as *;`
      }
    }
  },
  configureWebpack: {
    // 禁用代码分割，将所有代码打包进一个文件
    optimization: {
      splitChunks: false
    },
    output: {
      filename: '[name].js',
      chunkFilename: '[name].js'
    },
    performance: {
      hints: false
    }
  },
  chainWebpack: config => {
    // 禁用prefetch和preload，减少加载延迟
    config.plugins.delete('prefetch');
    config.plugins.delete('preload');
    
    // 设置静态资源处理
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|webp)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096, // 小于4kb的图片将转为base64内联到代码中
        fallback: {
          loader: 'file-loader',
          options: {
            name: 'img/[name].[hash:8].[ext]'
          }
        }
      });
  },
  // 开发环境下使用绝对路径，生产环境使用相对路径
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  // 配置静态资源目录
  assetsDir: 'static',
  transpileDependencies: []
} 