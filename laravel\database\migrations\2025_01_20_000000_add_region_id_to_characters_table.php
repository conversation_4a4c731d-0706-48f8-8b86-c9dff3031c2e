<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('characters')) {
        Schema::table('characters', function (Blueprint $table) {
            $table->integer('region_id')->default(1)->after('user_id');
            $table->index('region_id');
        });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('characters') && Schema::hasColumn('characters', 'region_id')) {
        Schema::table('characters', function (Blueprint $table) {
            $table->dropIndex(['region_id']);
            $table->dropColumn('region_id');
        });
        }
    }
};
