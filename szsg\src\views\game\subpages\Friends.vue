<template>
  <GameLayout>
    <div class="friends-page">
      <!-- 返回按钮 -->
      <div class="header-section">
        <button class="return-btn" @click="goBack">
          <img src="/static/game/UI/anniu/fhui_2.png" alt="返回" class="btn-image" />
        </button>
        <h2 class="page-title">好友系统</h2>
      </div>
      
      <!-- 功能标签 -->
      <div class="friends-tabs">
        <div 
          v-for="(tab, index) in friendsTabs" 
          :key="index"
          class="friends-tab"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-text">加载中...</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-text">{{ error }}</div>
        <div class="error-actions">
          <button v-if="error.includes('登录')" class="retry-btn" @click="goToLogin">前往登录</button>
          <button v-else-if="error.includes('角色')" class="retry-btn" @click="goToCharacterSelect">选择角色</button>
          <button v-else class="retry-btn" @click="fetchFriendsData">重试</button>
        </div>
      </div>
      
      <!-- 好友内容 -->
      <div v-else class="friends-content">
        <!-- 好友列表 -->
        <div v-if="currentTab === 0" class="friends-list">
          <div v-if="friends.length === 0" class="empty-tip">
            <span>暂无好友</span>
          </div>
          
          <div v-else class="friend-items">
            <div 
              v-for="friend in friends" 
              :key="friend.id"
              class="friend-item"
              :class="{ online: friend.isOnline }"
            >
              <div class="friend-avatar">
                <img :src="friend.avatar || '/static/game/UI/tx/male/tx1.png'" :alt="friend.name" />
                <div v-if="friend.isOnline" class="online-indicator"></div>
              </div>
              
              <div class="friend-info">
                <div class="friend-name">{{ friend.name }}</div>
                <div class="friend-level">等级 {{ friend.level }}</div>
                <div class="friend-status">{{ friend.isOnline ? '在线' : '离线' }}</div>
              </div>
              
              <div class="friend-actions">
                <button 
                  class="action-btn chat"
                  @click="chatWithFriend(friend)"
                  :disabled="!friend.isOnline"
                >
                  聊天
                </button>
                <button 
                  class="action-btn remove"
                  @click="removeFriend(friend)"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 好友申请 -->
        <div v-if="currentTab === 1" class="friend-requests">
          <div v-if="friendRequests.length === 0" class="empty-tip">
            <span>暂无好友申请</span>
          </div>
          
          <div v-else class="request-items">
            <div 
              v-for="request in friendRequests" 
              :key="request.id"
              class="request-item"
            >
              <div class="request-avatar">
                <img :src="request.avatar || '/static/game/UI/tx/male/tx1.png'" :alt="request.name" />
              </div>
              
              <div class="request-info">
                <div class="request-name">{{ request.name }}</div>
                <div class="request-level">等级 {{ request.level }}</div>
                <div class="request-time">{{ formatTime(request.requestTime) }}</div>
              </div>
              
              <div class="request-actions">
                <button 
                  class="action-btn accept"
                  @click="acceptFriendRequest(request)"
                  :disabled="isActionLoading"
                >
                  接受
                </button>
                <button 
                  class="action-btn reject"
                  @click="rejectFriendRequest(request)"
                  :disabled="isActionLoading"
                >
                  拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 添加好友 -->
        <div v-if="currentTab === 2" class="add-friend">
          <div class="search-section">
            <div class="search-input-group">
              <input 
                v-model="searchQuery"
                type="text"
                placeholder="输入玩家名称搜索"
                class="search-input"
                @keyup.enter="searchPlayers"
              />
              <button 
                class="search-btn"
                @click="searchPlayers"
                :disabled="isSearching || !searchQuery.trim()"
              >
                搜索
              </button>
            </div>
          </div>
          
          <div v-if="isSearching" class="searching-tip">
            <span>搜索中...</span>
          </div>
          
          <div v-else-if="searchResults.length === 0 && hasSearched" class="empty-tip">
            <span>未找到相关玩家</span>
          </div>
          
          <div v-else-if="searchResults.length > 0" class="search-results">
            <div 
              v-for="player in searchResults" 
              :key="player.id"
              class="search-result-item"
            >
              <div class="result-avatar">
                <img :src="player.avatar || '/static/game/UI/tx/male/tx1.png'" :alt="player.name" />
              </div>
              
              <div class="result-info">
                <div class="result-name">{{ player.name }}</div>
                <div class="result-level">等级 {{ player.level }}</div>
                <div class="result-status">{{ player.isOnline ? '在线' : '离线' }}</div>
              </div>
              
              <div class="result-actions">
                <button 
                  class="action-btn add"
                  @click="sendFriendRequest(player)"
                  :disabled="isActionLoading || isAlreadyFriend(player) || hasPendingRequest(player)"
                >
                  {{ getAddButtonText(player) }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import axios from 'axios'
import { API_BASE_URL } from '@/api/config.js'
import { ERROR_MESSAGES } from '@/api/constants.js'
import logger from '@/utils/logger'

export default {
  components: {
    GameLayout
  },
  data() {
    return {
      currentTab: 0,
      friendsTabs: [
        { name: '好友列表', type: 'friends' },
        { name: '好友申请', type: 'requests' },
        { name: '添加好友', type: 'add' }
      ],
      friends: [],
      friendRequests: [],
      searchQuery: '',
      searchResults: [],
      hasSearched: false,
      isLoading: true,
      isSearching: false,
      isActionLoading: false,
      error: null
    }
  },
  computed: {
    authToken() {
      return this.$store.state.token || localStorage.getItem('authToken')
    },
    selectedCharacterId() {
      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')
    }
  },
  created() {
    // 检查认证状态
    if (!this.authToken) {
      logger.warn('Friends页面: 未找到认证token')
      this.error = '请先登录'
      return
    }

    // 检查角色选择状态
    if (!this.selectedCharacterId) {
      logger.warn('Friends页面: 未选择角色')
      this.error = '请先选择角色'
      return
    }

    this.fetchFriendsData()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    
    switchTab(index) {
      this.currentTab = index
      logger.info('切换好友标签', this.friendsTabs[index].name)
      
      if (index === 1) {
        this.fetchFriendRequests()
      }
    },
    
    async fetchFriendsData() {
      this.isLoading = true
      this.error = null
      
      try {
        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friends`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        
        this.friends = response.data.friends || []
        logger.info('获取好友列表成功', this.friends.length)
      } catch (err) {
        this.error = err.message || ERROR_MESSAGES.UNKNOWN_ERROR
        this.showToast(this.error)
        logger.error('获取好友列表失败', err)
      } finally {
        this.isLoading = false
      }
    },
    
    async fetchFriendRequests() {
      try {
        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        
        this.friendRequests = response.data.requests || []
        logger.info('获取好友申请成功', this.friendRequests.length)
      } catch (err) {
        logger.error('获取好友申请失败', err)
      }
    },
    
    async searchPlayers() {
      if (!this.searchQuery.trim()) return
      
      this.isSearching = true
      this.hasSearched = false
      
      try {
        const response = await axios.get(`${API_BASE_URL}/players/search`, {
          params: { query: this.searchQuery.trim() },
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Accept': 'application/json'
          }
        })
        
        this.searchResults = response.data.players || []
        this.hasSearched = true
        logger.info('搜索玩家成功', this.searchResults.length)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('搜索玩家失败', err)
      } finally {
        this.isSearching = false
      }
    },
    
    async sendFriendRequest(player) {
      if (this.isActionLoading) return
      this.isActionLoading = true
      
      try {
        const response = await axios.post(
          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`,
          { target_player_id: player.id },
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Accept': 'application/json'
            }
          }
        )
        
        this.showToast(response.data.message || '好友申请已发送', 'success')
        logger.info('发送好友申请成功', player.name)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('发送好友申请失败', err)
      } finally {
        this.isActionLoading = false
      }
    },
    
    async acceptFriendRequest(request) {
      if (this.isActionLoading) return
      this.isActionLoading = true
      
      try {
        const response = await axios.post(
          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/accept`,
          {},
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Accept': 'application/json'
            }
          }
        )
        
        this.showToast(response.data.message || '已接受好友申请', 'success')
        await this.fetchFriendRequests()
        await this.fetchFriendsData()
        logger.info('接受好友申请成功', request.name)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('接受好友申请失败', err)
      } finally {
        this.isActionLoading = false
      }
    },
    
    async rejectFriendRequest(request) {
      if (this.isActionLoading) return
      this.isActionLoading = true
      
      try {
        const response = await axios.post(
          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/reject`,
          {},
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Accept': 'application/json'
            }
          }
        )
        
        this.showToast(response.data.message || '已拒绝好友申请', 'success')
        await this.fetchFriendRequests()
        logger.info('拒绝好友申请成功', request.name)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('拒绝好友申请失败', err)
      } finally {
        this.isActionLoading = false
      }
    },
    
    async removeFriend(friend) {
      if (!confirm(`确定要删除好友"${friend.name}"吗？`)) {
        return
      }
      
      this.isActionLoading = true
      
      try {
        const response = await axios.delete(
          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friends/${friend.id}`,
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Accept': 'application/json'
            }
          }
        )
        
        this.showToast(response.data.message || '已删除好友', 'success')
        await this.fetchFriendsData()
        logger.info('删除好友成功', friend.name)
      } catch (err) {
        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        logger.error('删除好友失败', err)
      } finally {
        this.isActionLoading = false
      }
    },
    
    chatWithFriend(friend) {
      this.showToast(`与${friend.name}的聊天功能开发中...`, 'info')
      // 这里可以实现私聊功能
    },
    
    isAlreadyFriend(player) {
      return this.friends.some(friend => friend.id === player.id)
    },
    
    hasPendingRequest(player) {
      return this.friendRequests.some(request => request.id === player.id)
    },
    
    getAddButtonText(player) {
      if (this.isAlreadyFriend(player)) return '已是好友'
      if (this.hasPendingRequest(player)) return '已申请'
      return '添加好友'
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return `${Math.floor(diff / 86400000)}天前`
    },
    
    showToast(message, type = 'error') {
      alert(message)
    },

    goToLogin() {
      this.$router.push('/login')
    },

    goToCharacterSelect() {
      this.$router.push('/setup/character-select')
    }
  }
}
</script>

<style lang="scss" scoped>
.friends-page {
  padding: 15px;
  min-height: 100vh;
  background: linear-gradient(135deg, #2d1b69, #1a0f3d);
  color: #fff;
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.return-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.btn-image {
  width: 60px;
  height: 40px;
  object-fit: contain;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.friends-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
}

.friends-tab {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  &.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-text {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
}

.error-text {
  font-size: 16px;
  color: #ff6b6b;
  margin-bottom: 15px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.retry-btn {
  padding: 10px 20px;
  background: #4ecdc4;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    background: #45b7aa;
    transform: translateY(-2px);
  }
}

.friends-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-tip {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 60px 20px;
  font-size: 16px;
}

// 好友列表样式
.friend-items, .request-items, .search-results {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.friend-item, .request-item, .search-result-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  }

  &.online {
    border-left: 4px solid #2ecc71;
  }
}

.friend-avatar, .request-avatar, .result-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  margin-right: 15px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #2ecc71;
  border: 2px solid #fff;
  border-radius: 50%;
}

.friend-info, .request-info, .result-info {
  flex: 1;
  margin-right: 15px;
}

.friend-name, .request-name, .result-name {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 4px;
}

.friend-level, .request-level, .result-level {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.friend-status, .result-status {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.request-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.friend-actions, .request-actions, .result-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.chat {
    background: #4ecdc4;
    color: white;

    &:hover:not(:disabled) {
      background: #45b7aa;
      transform: translateY(-1px);
    }
  }

  &.remove {
    background: #e74c3c;
    color: white;

    &:hover:not(:disabled) {
      background: #c0392b;
      transform: translateY(-1px);
    }
  }

  &.accept {
    background: #2ecc71;
    color: white;

    &:hover:not(:disabled) {
      background: #27ae60;
      transform: translateY(-1px);
    }
  }

  &.reject {
    background: #95a5a6;
    color: white;

    &:hover:not(:disabled) {
      background: #7f8c8d;
      transform: translateY(-1px);
    }
  }

  &.add {
    background: #3498db;
    color: white;

    &:hover:not(:disabled) {
      background: #2980b9;
      transform: translateY(-1px);
    }
  }
}

// 搜索区域样式
.search-section {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #fff;
  font-size: 16px;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
  }
}

.search-btn {
  padding: 12px 24px;
  background: #4ecdc4;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: #45b7aa;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.searching-tip {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 40px 20px;
  font-size: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .friends-page {
    padding: 10px;
  }

  .header-section {
    margin-bottom: 15px;
    gap: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .btn-image {
    width: 50px;
    height: 35px;
  }

  .friends-tabs {
    flex-wrap: wrap;
    gap: 6px;
  }

  .friends-tab {
    padding: 8px 16px;
    font-size: 14px;
  }

  .friend-item, .request-item, .search-result-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .friend-avatar, .request-avatar, .result-avatar {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .friend-info, .request-info, .result-info {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .friend-actions, .request-actions, .result-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-input, .search-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .friends-page {
    padding: 8px;
  }

  .friends-content {
    padding: 15px;
  }

  .friend-avatar, .request-avatar, .result-avatar {
    width: 50px;
    height: 50px;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
