<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * 运行数据填充
     */
    public function run(): void
    {
        // 创建超级管理员账号
        Admin::create([
            'name' => '超级管理员',
            'username' => 'admin',
            'password' => Hash::make('admin123'), // 默认密码，生产环境应当修改
            'role' => 'super_admin',
            'permissions' => json_encode([
                'characters' => ['view', 'create', 'edit', 'delete'],
                'battles' => ['view', 'create', 'edit', 'delete'],
                'items' => ['view', 'create', 'edit', 'delete'],
                'quests' => ['view', 'create', 'edit', 'delete'],
                'bank' => ['view', 'create', 'edit', 'delete'],
                'maps' => ['view', 'create', 'edit', 'delete'],
                'users' => ['view', 'create', 'edit', 'delete'],
                'settings' => ['view', 'edit'],
            ]),
        ]);

        // 创建普通管理员账号
        Admin::create([
            'name' => '游戏管理员',
            'username' => 'gameadmin',
            'password' => Hash::make('game123'), // 默认密码，生产环境应当修改
            'role' => 'admin',
            'permissions' => json_encode([
                'characters' => ['view', 'edit'],
                'battles' => ['view'],
                'items' => ['view', 'edit'],
                'quests' => ['view', 'edit'],
                'bank' => ['view'],
                'maps' => ['view'],
                'users' => ['view'],
                'settings' => ['view'],
            ]),
        ]);
    }
}
