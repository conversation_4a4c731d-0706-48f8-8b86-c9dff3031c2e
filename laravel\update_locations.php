<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "开始更新locations表的region_id字段...\n";

// 获取所有regions
$regions = DB::table('regions')->get();
echo "找到 " . count($regions) . " 个regions\n";

// 获取所有locations
$locations = DB::table('locations')->get();
echo "找到 " . count($locations) . " 个locations\n";

// 默认将所有location分配给第一个region
if ($regions->count() > 0) {
    $defaultRegionId = $regions->first()->id;
    echo "默认region_id: " . $defaultRegionId . "\n";

    // 更新所有locations的region_id
    $updated = DB::table('locations')
        ->whereNull('region_id')
        ->update(['region_id' => $defaultRegionId]);

    echo "更新了 " . $updated . " 个locations的region_id\n";
} else {
    echo "没有找到regions，无法更新locations\n";
}

// 验证更新结果
$locationsWithRegion = DB::table('locations')
    ->whereNotNull('region_id')
    ->count();

echo "现在有 " . $locationsWithRegion . " 个locations有region_id\n";

echo "更新完成！\n";
