<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Character;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BankTransactionTestSeeder extends Seeder
{
    /**
     * 为指定角色创建银行交易测试数据
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('创建银行交易测试数据...');

        // 获取所有角色
        $characters = Character::all();

        if ($characters->isEmpty()) {
            $this->command->error('没有找到角色数据，请先创建角色');
            return;
        }

        foreach ($characters as $character) {
            $this->command->info("为角色 {$character->name} 创建银行账户和交易记录");

            // 检查并创建银行账户
            $bankAccount = BankAccount::firstOrCreate(
                ['character_id' => $character->id],
                [
                    'silver' => 1000,
                    'gold_ingot' => 10
                ]
            );

            // 清除该角色的现有交易记录
            BankTransaction::where('character_id', $character->id)->delete();

            // 创建一系列银两存款记录
            $this->createTransactionSeries($character, $bankAccount, 'silver', 'deposit', 5);

            // 创建一系列银两取款记录
            $this->createTransactionSeries($character, $bankAccount, 'silver', 'withdraw', 3);

            // 创建一系列金砖存款记录
            $this->createTransactionSeries($character, $bankAccount, 'gold_ingot', 'deposit', 2);

            // 创建一系列金砖取款记录
            $this->createTransactionSeries($character, $bankAccount, 'gold_ingot', 'withdraw', 1);

            $this->command->info("已为角色 {$character->name} 创建完成交易记录");
        }

        $this->command->info('银行交易测试数据创建完成！');
    }

    /**
     * 创建一系列交易记录
     *
     * @param Character $character 角色
     * @param BankAccount $bankAccount 银行账户
     * @param string $currency 货币类型 (silver/gold_ingot)
     * @param string $type 交易类型 (deposit/withdraw)
     * @param int $count 记录数量
     * @return void
     */
    private function createTransactionSeries($character, $bankAccount, $currency, $type, $count)
    {
        $currencyLabel = $currency === 'silver' ? '银两' : '金砖';
        $typeLabel = $type === 'deposit' ? '存入' : '取出';

        $this->command->info("创建 {$count} 条 {$currencyLabel}{$typeLabel} 记录");

        // 初始余额
        $balance = $bankAccount->$currency;

        // 生成随机间隔的时间序列
        $timestamps = $this->generateTimeSequence($count);

        // 为每个时间点创建交易记录
        foreach ($timestamps as $index => $timestamp) {
            // 随机生成交易金额，范围根据货币类型而定
            if ($currency === 'silver') {
                $amount = rand(10, 100) * 10; // 100-1000之间的10的倍数
            } else {
                $amount = rand(1, 10); // 1-10之间的整数
            }

            // 计算余额变化
            if ($type === 'deposit') {
                $balance += $amount;
                $description = "测试 - 存入 {$amount} {$currencyLabel}";
            } else {
                // 确保取款不会超过余额
                $amount = min($amount, $balance);
                // 如果余额太少，设置一个最小值
                $amount = max($amount, 1);
                $balance -= $amount;
                $description = "测试 - 取出 {$amount} {$currencyLabel}";
            }

            // 创建交易记录
            try {
                BankTransaction::create([
                    'character_id' => $character->id,
                    'type' => $type,
                    'currency' => $currency,
                    'amount' => $amount,
                    'balance' => $balance,
                    'description' => $description,
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp
                ]);
            } catch (\Exception $e) {
                Log::error("创建交易记录失败: {$e->getMessage()}");
                $this->command->error("创建交易记录失败: {$e->getMessage()}");
            }
        }

        // 更新账户余额以匹配最终交易
        $bankAccount->$currency = $balance;
        $bankAccount->save();
    }

    /**
     * 生成指定数量的随机时间序列，并按时间降序排列
     *
     * @param int $count 时间点数量
     * @return array 时间戳数组
     */
    private function generateTimeSequence($count)
    {
        $timestamps = [];
        $now = time();

        for ($i = 0; $i < $count; $i++) {
            // 随机生成过去30天内的时间点
            $randomOffset = rand(1, 30) * 24 * 60 * 60; // 1-30天的随机秒数
            $timestamp = $now - $randomOffset;
            $timestamps[] = date('Y-m-d H:i:s', $timestamp);
        }

        // 按时间降序排序
        usort($timestamps, function($a, $b) {
            return strtotime($b) - strtotime($a);
        });

        return $timestamps;
    }
}
