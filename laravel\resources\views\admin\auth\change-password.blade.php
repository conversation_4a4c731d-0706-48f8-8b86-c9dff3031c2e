@extends('admin.layouts.app')

@section('title', '修改密码')

@section('breadcrumb')
<a><cite>修改密码</cite></a>
@endsection

@section('page-title', '修改密码')

@section('content')
<div class="layui-card">
  <div class="layui-card-body">
    <form class="layui-form" method="POST" action="{{ route('admin.change-password.post') }}">
      @csrf

      <div class="layui-form-item">
        <label class="layui-form-label">当前密码</label>
        <div class="layui-input-block">
          <input type="password" name="current_password" lay-verify="required" placeholder="请输入当前密码" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">新密码</label>
        <div class="layui-input-block">
          <input type="password" name="password" lay-verify="required|pwd" placeholder="请输入新密码，至少8个字符" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">确认密码</label>
        <div class="layui-input-block">
          <input type="password" name="password_confirmation" lay-verify="required|pwd|same" placeholder="请再次输入新密码" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-block">
          <button class="layui-btn" lay-submit lay-filter="formPassword">提交修改</button>
          <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
      </div>
    </form>
  </div>
</div>
@endsection

@section('js')
<script>
  layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    // 自定义验证规则
    form.verify({
      pwd: [
        /^.{8,}$/,
        '密码必须8位以上'
      ],
      same: function(value) {
        var password = $('input[name=password]').val();
        if (value !== password) {
          return '两次密码输入不一致';
        }
      }
    });

    // 提交表单
    form.on('submit(formPassword)', function(data){
      // 默认提交表单
      return true;
    });
  });
</script>
@endsection
