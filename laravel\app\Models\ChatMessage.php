<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'sender_id',
        'receiver_id',
        'channel',
        'message',
        'is_system'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_system' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取消息发送者
     */
    public function sender()
    {
        return $this->belongsTo(Character::class, 'sender_id');
    }

    /**
     * 获取私聊消息接收者
     */
    public function receiver()
    {
        return $this->belongsTo(Character::class, 'receiver_id');
    }

    /**
     * 作用域：获取特定频道的消息
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $channel
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * 作用域：仅获取特定时间段内的消息
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param \DateTime|string $startTime
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSince($query, $startTime)
    {
        return $query->where('created_at', '>=', $startTime);
    }

    /**
     * 作用域：获取两个角色之间的私聊消息
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $character1Id
     * @param int $character2Id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBetweenCharacters($query, $character1Id, $character2Id)
    {
        return $query->where(function($q) use ($character1Id, $character2Id) {
            $q->where(function($innerQ) use ($character1Id, $character2Id) {
                $innerQ->where('sender_id', $character1Id)
                      ->where('receiver_id', $character2Id);
            })->orWhere(function($innerQ) use ($character1Id, $character2Id) {
                $innerQ->where('sender_id', $character2Id)
                      ->where('receiver_id', $character1Id);
            });
        });
    }
}
