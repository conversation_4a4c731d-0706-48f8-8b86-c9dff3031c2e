@extends('admin.layouts.app')

@section('title', '战斗详情')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <a href="{{ route('admin.battles.index') }}" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        战斗详情 #{{ $battle->id }}
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <tbody>
                                <tr>
                                    <td width="100">战斗ID</td>
                                    <td>{{ $battle->id }}</td>
                                </tr>
                                <tr>
                                    <td>角色</td>
                                    <td>{{ $battle->character->name ?? ($battle->character_name ?? '未知') }}</td>
                                </tr>
                                <tr>
                                    <td>怪物</td>
                                    <td>{{ $battle->monster->name ?? ($battle->monster_name ?? '未知') }}</td>
                                </tr>
                                <tr>
                                    <td>地点</td>
                                    <td>{{ $battle->location->name ?? ($battle->location_name ?? '未知') }}</td>
                                </tr>
                                <tr>
                                    <td>战斗类型</td>
                                    <td>{{ $battle->battle_type ?? '普通战斗' }}</td>
                                </tr>
                                <tr>
                                    <td>状态</td>
                                    <td>
                                        @php
                                        $statusMap = [
                                            'ongoing' => '<span class="layui-badge layui-bg-blue">进行中</span>',
                                            'victory' => '<span class="layui-badge layui-bg-green">胜利</span>',
                                            'defeat' => '<span class="layui-badge layui-bg-red">失败</span>',
                                            'fled' => '<span class="layui-badge layui-bg-orange">逃跑</span>',
                                            // 兼容旧数据
                                            'win' => '<span class="layui-badge layui-bg-green">胜利</span>',
                                            'lose' => '<span class="layui-badge layui-bg-red">失败</span>',
                                            'escape' => '<span class="layui-badge layui-bg-orange">逃跑</span>',
                                            'active' => '<span class="layui-badge layui-bg-blue">进行中</span>',
                                        ];

                                        // 增强错误处理，确保即使对象没有status属性也不会报错
                                        $status = 'unknown';

                                        // 检查是否为对象，以及对象是否有status或result属性
                                        if (is_object($battle)) {
                                            if (property_exists($battle, 'status') && isset($battle->status)) {
                                                $status = $battle->status;
                                            } elseif (property_exists($battle, 'result') && isset($battle->result)) {
                                                $status = $battle->result;
                                            } elseif (property_exists($battle, 'type') && isset($battle->type)) {
                                                // 如果有type属性但没有status，可能是旧数据
                                                $status = 'unknown';
                                            }
                                        } elseif (is_array($battle)) {
                                            // 如果是数组，尝试使用数组访问方式
                                            if (isset($battle['status'])) {
                                                $status = $battle['status'];
                                            } elseif (isset($battle['result'])) {
                                                $status = $battle['result'];
                                            }
                                        }

                                        $statusDisplay = $statusMap[$status] ?? '<span class="layui-badge">未知</span>';
                                        @endphp
                                        {!! $statusDisplay !!}
                                    </td>
                                </tr>
                                <tr>
                                    <td>回合数</td>
                                    <td>{{ $battle->rounds ?? 0 }}</td>
                                </tr>
                                <tr>
                                    <td>开始时间</td>
                                    <td>{{ $battle->start_time ?? $battle->created_at }}</td>
                                </tr>
                                <tr>
                                    <td>结束时间</td>
                                    <td>{{ $battle->end_time ?? '未结束' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">战斗收益</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <tbody>
                                <tr>
                                    <td width="100">经验获得</td>
                                    <td>{{ $battle->exp_gained ?? 0 }}</td>
                                </tr>
                                <tr>
                                    <td>银两获得</td>
                                    <td>{{ $battle->gold_gained ?? ($battle->silver_gained ?? 0) }}</td>
                                </tr>
                                <tr>
                                    <td>物品获得</td>
                                    <td>
                                        @if(isset($battle->items_gained) && is_array($battle->items_gained) && count($battle->items_gained) > 0)
                                            <ul class="layui-text">
                                                @foreach($battle->items_gained as $item)
                                                    <li>
                                                        @if(is_array($item))
                                                            {{ $item['name'] ?? '未知物品' }} x {{ $item['quantity'] ?? 1 }}
                                                        @else
                                                            {{ $item }}
                                                        @endif
                                                    </li>
                                                @endforeach
                                            </ul>
                                        @else
                                            无
                                        @endif
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-header">战斗日志</div>
            <div class="layui-card-body">
                @if(isset($battle->battle_log) && is_array($battle->battle_log) && count($battle->battle_log) > 0)
                    <div class="layui-timeline">
                        @foreach($battle->battle_log as $log)
                            <div class="layui-timeline-item">
                                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                                <div class="layui-timeline-content layui-text">
                                    <h3 class="layui-timeline-title">回合 {{ $log['round'] ?? '?' }}</h3>
                                    <p>
                                        <strong>{{ $log['action'] ?? '未知动作' }}</strong><br>
                                        @if(isset($log['data']) && is_array($log['data']))
                                            @foreach($log['data'] as $key => $value)
                                                @if(is_array($value))
                                                    <strong>{{ $key }}:</strong> {{ json_encode($value, JSON_UNESCAPED_UNICODE) }}<br>
                                                @else
                                                    <strong>{{ $key }}:</strong> {{ $value }}<br>
                                                @endif
                                            @endforeach
                                        @endif
                                        <small class="layui-text-muted">{{ $log['timestamp'] ?? '' }}</small>
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="layui-text">
                        <p>无战斗日志记录</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
