<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CharacterVip extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'character_vip';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'character_id',
        'level',
        'exp',
        'daily_reward_claimed',
        'daily_reward_reset_at',
    ];

    /**
     * 需要进行类型转换的属性
     *
     * @var array
     */
    protected $casts = [
        'daily_reward_claimed' => 'boolean',
        'daily_reward_reset_at' => 'datetime',
    ];

    /**
     * 获取关联的角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 获取当前VIP等级信息
     */
    public function vipLevel()
    {
        return VipLevel::where('level', $this->level)->first();
    }

    /**
     * 获取下一个VIP等级信息
     */
    public function nextVipLevel()
    {
        return VipLevel::where('level', $this->level + 1)->first();
    }

    /**
     * 获取VIP奖励领取记录
     */
    public function rewardClaims()
    {
        return $this->hasMany(VipRewardClaim::class, 'character_id', 'character_id');
    }

    /**
     * 检查是否可以领取每日奖励
     */
    public function canClaimDailyReward()
    {
        if (!$this->daily_reward_claimed) {
            return true;
        }

        // 如果重置时间已过，可以重新领取
        if ($this->daily_reward_reset_at && Carbon::now()->gt($this->daily_reward_reset_at)) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否可以升级
     */
    public function canLevelUp()
    {
        $nextLevel = $this->nextVipLevel();
        if (!$nextLevel) {
            return false; // 已经是最高等级
        }

        return $this->exp >= $nextLevel->exp_required;
    }

    /**
     * 添加VIP经验
     */
    public function addExp($amount)
    {
        $this->exp += $amount;

        // 检查是否可以升级
        while ($this->canLevelUp()) {
            $nextLevel = $this->nextVipLevel();
            if (!$nextLevel) {
                break;
            }

            $this->level += 1;
        }

        $this->save();

        return $this;
    }

    /**
     * 领取每日奖励
     */
    public function claimDailyReward()
    {
        if (!$this->canClaimDailyReward()) {
            return false;
        }

        $this->daily_reward_claimed = true;
        $this->daily_reward_reset_at = Carbon::tomorrow()->startOfDay();
        $this->save();

        return true;
    }
}
