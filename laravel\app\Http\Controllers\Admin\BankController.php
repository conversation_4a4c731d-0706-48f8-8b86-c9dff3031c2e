<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BankController extends Controller
{
    /**
     * 显示钱庄账户管理页面
     */
    public function index()
    {
        try {
            // 获取账户统计数据
            $totalSilver = DB::table('characters')->sum('silver') ?? 0;
            $totalGoldIngot = DB::table('characters')->sum('gold_ingot') ?? 0;

            // 获取账户列表
            $accounts = DB::table('characters')
                ->select('id', 'name', 'silver', 'gold_ingot', 'updated_at')
                ->orderBy('silver', 'desc')
                ->orderBy('gold_ingot', 'desc')
                ->paginate(15);

            return view('admin.bank.index', compact('accounts', 'totalSilver', 'totalGoldIngot'));
        } catch (\Exception $e) {
            // 如果数据表不存在，返回空数据
            $totalSilver = 0;
            $totalGoldIngot = 0;
            $accounts = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.bank.index', compact('accounts', 'totalSilver', 'totalGoldIngot'));
        }
    }

    /**
     * 显示交易记录页面
     */
    public function transactions(Request $request)
    {
        try {
            $query = DB::table('bank_transactions')
                ->join('characters', 'bank_transactions.character_id', '=', 'characters.id')
                ->select(
                    'bank_transactions.*',
                    'characters.name as character_name'
                );

            // 过滤条件
            if ($request->has('type') && $request->type) {
                $query->where('bank_transactions.type', $request->type);
            }

            if ($request->has('currency') && $request->currency) {
                $query->where('bank_transactions.currency', $request->currency);
            }

            if ($request->has('character_id') && $request->character_id) {
                $query->where('bank_transactions.character_id', $request->character_id);
            }

            // 日期范围过滤
            if ($request->has('date_from') && $request->date_from) {
                $query->whereDate('bank_transactions.created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to) {
                $query->whereDate('bank_transactions.created_at', '<=', $request->date_to);
            }

            // 排序和分页
            $transactions = $query
                ->orderBy('bank_transactions.created_at', 'desc')
                ->paginate(15)
                ->appends($request->except('page'));

            // 获取所有角色，用于过滤器
            $characters = DB::table('characters')
                ->select('id', 'name')
                ->orderBy('name')
                ->get();

            return view('admin.bank.transactions', compact('transactions', 'characters'));
        } catch (\Exception $e) {
            // 如果数据表不存在，返回空数据
            $transactions = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );
            $characters = collect([]);

            return view('admin.bank.transactions', compact('transactions', 'characters'));
        }
    }
}
