<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class SettingController extends Controller
{
    /**
     * 显示基本设置页面
     */
    public function basic()
    {
        // 从数据库或缓存中获取系统设置
        $settings = $this->getSettings('basic');

        return view('admin.settings.basic', compact('settings'));
    }

    /**
     * 保存基本设置
     */
    public function saveBasic(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'maintenance_mode' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // 保存设置
        $this->saveSettings('basic', $request->except('_token'));

        return redirect()->route('admin.settings.basic')
            ->with('success', '基本设置已保存');
    }

    /**
     * 显示游戏设置页面
     */
    public function game()
    {
        // 从数据库或缓存中获取游戏设置
        $settings = $this->getSettings('game');

        return view('admin.settings.game', compact('settings'));
    }

    /**
     * 保存游戏设置
     */
    public function saveGame(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'initial_silver' => 'nullable|integer|min:0',
            'initial_health' => 'nullable|integer|min:1',
            'experience_rate' => 'nullable|numeric|min:0.1|max:10',
            'drop_rate' => 'nullable|numeric|min:0.1|max:10',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // 保存设置
        $this->saveSettings('game', $request->except('_token'));

        return redirect()->route('admin.settings.game')
            ->with('success', '游戏设置已保存');
    }

    /**
     * 从数据库或缓存中获取设置
     *
     * @param string $group 设置组
     * @return array
     */
    private function getSettings($group)
    {
        // 尝试从缓存获取
        $cacheKey = 'settings_' . $group;

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // 如果没有缓存，从数据库获取
        try {
            $settings = DB::table('settings')
                ->where('group', $group)
                ->pluck('value', 'key')
                ->toArray();

            // 如果设置为空，返回默认值
            if (empty($settings)) {
                $settings = $this->getDefaultSettings($group);
            }

            // 缓存设置
            Cache::put($cacheKey, $settings, now()->addHours(24));

            return $settings;
        } catch (\Exception $e) {
            // 如果数据库出错（例如表不存在），返回默认值
            return $this->getDefaultSettings($group);
        }
    }

    /**
     * 保存设置到数据库并更新缓存
     *
     * @param string $group 设置组
     * @param array $data 设置数据
     */
    private function saveSettings($group, $data)
    {
        try {
            // 确认settings表存在
            if (!$this->ensureSettingsTable()) {
                return false;
            }

            // 保存每项设置
            foreach ($data as $key => $value) {
                DB::table('settings')->updateOrInsert(
                    ['group' => $group, 'key' => $key],
                    ['value' => $value, 'updated_at' => now()]
                );
            }

            // 更新缓存
            $cacheKey = 'settings_' . $group;
            Cache::put($cacheKey, $data, now()->addHours(24));

            return true;
        } catch (\Exception $e) {
            // 处理错误
            return false;
        }
    }

    /**
     * 确保settings表存在
     *
     * @return bool
     */
    private function ensureSettingsTable()
    {
        try {
            // 检查表是否存在
            $tableExists = DB::getSchemaBuilder()->hasTable('settings');

            if (!$tableExists) {
                // 创建表
                DB::statement('
                    CREATE TABLE settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        `group` VARCHAR(100) NOT NULL,
                        `key` VARCHAR(100) NOT NULL,
                        value TEXT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        UNIQUE KEY group_key_unique (`group`, `key`)
                    )
                ');
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取默认设置
     *
     * @param string $group 设置组
     * @return array
     */
    private function getDefaultSettings($group)
    {
        $defaults = [
            'basic' => [
                'site_name' => '神之西游',
                'site_description' => '神之西游游戏管理系统',
                'maintenance_mode' => false,
            ],
            'game' => [
                'initial_silver' => 100,
                'initial_health' => 100,
                'experience_rate' => 1.0,
                'drop_rate' => 1.0,
            ],
        ];

        return $defaults[$group] ?? [];
    }
}
