{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CreateCharacter.vue?vue&type=style&index=0&id=fbe4a29e&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CreateCharacter.vue", "mtime": 1749698812008}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CreateCharacter.vue"], "names": [], "mappings": ";AAuPA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "CreateCharacter.vue", "sourceRoot": "src/views/setup", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"create-character-container\">\n      <!-- 创建角色主界面 -->\n      <div class=\"character-creation-panel\">\n        <form @submit.prevent=\"submitCreateCharacter\" class=\"character-form\">\n          <!-- 名称输入区 -->\n          <div class=\"name-section\">\n            <span class=\"label-text\">名称:</span>\n            <input \n              v-model=\"characterData.name\"\n              type=\"text\" \n              class=\"name-input\"\n              maxlength=\"10\"\n              required\n            />\n          </div>\n\n          <!-- 性别选择区 -->\n          <div class=\"gender-section\">\n            <span class=\"label-text\">性别:</span>\n            <div class=\"gender-buttons\">\n              <button \n                type=\"button\"\n                class=\"gender-btn\"\n                :class=\"{ 'selected': characterData.gender === 'male' }\"\n                @click=\"characterData.gender = 'male'\"\n              >\n                男\n              </button>\n              <button \n                type=\"button\"\n                class=\"gender-btn\"\n                :class=\"{ 'selected': characterData.gender === 'female' }\"\n                @click=\"characterData.gender = 'female'\"\n              >\n                女\n              </button>\n            </div>\n          </div>\n\n          <!-- 职业选择区 -->\n          <div class=\"profession-section\">\n            <span class=\"label-text\">职业:</span>\n            <div class=\"profession-buttons\">\n              <button \n                v-for=\"profession in professions\" \n                :key=\"profession.id\"\n                type=\"button\"\n                class=\"profession-btn\"\n                :class=\"{ 'selected': characterData.profession === profession.id }\"\n                @click=\"selectProfession(profession)\"\n              >\n                {{ profession.name }}\n              </button>\n            </div>\n          </div>\n\n          <!-- 头像选择区 -->\n          <div class=\"avatar-section\">\n            <span class=\"label-text\">头像:</span>\n            <div class=\"avatar-grid\">\n              <div \n                v-for=\"(avatar, index) in getAvailableAvatars()\" \n                :key=\"index\"\n                class=\"avatar-option\"\n                :class=\"{ 'selected': selectedAvatar === index }\"\n                @click=\"selectedAvatar = index\"\n              >\n                <img :src=\"avatar\" :alt=\"`头像${index + 1}`\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- 提示文字 -->\n          <div class=\"hint-text\">\n            请选择在游戏中角色的性别\n          </div>\n\n        </form>\n\n        <!-- 操作按钮区域 -->\n        <div class=\"action-buttons\">\n          <button\n            type=\"button\"\n            @click=\"goBack\"\n            class=\"game-btn return-btn\"\n            :disabled=\"isCreating\"\n          >\n            返回\n          </button>\n          <button\n            type=\"button\"\n            @click=\"submitCreateCharacter\"\n            class=\"game-btn create-btn\"\n            :disabled=\"!isFormValid || isCreating\"\n          >\n            创建角色\n          </button>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapActions } from 'vuex'\nimport { regionService } from '@/api'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'CreateCharacter',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      currentRegion: null,\n      isCreating: false,\n      selectedAvatar: 0,\n      characterData: {\n        name: '',\n        gender: 'male',\n        profession: ''\n      },\n      professions: [\n        {\n          id: 'warrior',\n          name: '武士',\n          description: '武艺高强的战士，擅长近身搏斗，刀剑精通'\n        },\n        {\n          id: 'scholar',\n          name: '文人',\n          description: '博学多才的学者，精通诗词歌赋，法术造诣深厚'\n        },\n        {\n          id: 'mystic',\n          name: '异人',\n          description: '神秘莫测的修行者，身怀奇术，能力均衡'\n        }\n      ]\n    }\n  },\n  computed: {\n    isFormValid() {\n      return this.characterData.name.trim().length >= 2 &&\n             this.characterData.gender &&\n             this.characterData.profession\n    }\n  },\n  created() {\n    logger.debug('[CreateCharacter] 页面初始化')\n    \n    // 获取当前选择的大区\n    this.currentRegion = regionService.getCurrentRegion()\n    if (!this.currentRegion) {\n      this.showToast('请先选择大区')\n      this.$router.push('/setup/region-select')\n      return\n    }\n  },\n  methods: {\n    ...mapActions('character', ['createCharacter']),\n\n    selectProfession(profession) {\n      this.characterData.profession = profession.id\n      logger.debug('[CreateCharacter] 选择职业:', profession.name)\n    },\n\n    getAvailableAvatars() {\n      const gender = this.characterData.gender || 'male'\n      const profession = this.characterData.profession || 'warrior'\n      return [\n        `/static/game/avatars/${gender}_${profession}_1.png`,\n        `/static/game/avatars/${gender}_${profession}_2.png`,\n        `/static/game/avatars/${gender}_${profession}_3.png`\n      ]\n    },\n\n    async submitCreateCharacter() {\n      if (!this.isFormValid) {\n        this.showToast('请完善角色信息')\n        return\n      }\n\n      this.isCreating = true\n\n      try {\n        const characterData = {\n          name: this.characterData.name.trim(),\n          gender: this.characterData.gender,\n          profession: this.characterData.profession, // 直接使用profession字段\n          region_id: this.currentRegion?.id || 1 // 提供默认值\n        }\n\n        logger.debug('[CreateCharacter] 提交数据:', characterData)\n        logger.debug('[CreateCharacter] 当前区域:', this.currentRegion)\n\n        const result = await this.createCharacter(characterData)\n        \n        if (result && result.success) {\n          this.showToast('角色创建成功！')\n          setTimeout(() => {\n            this.$router.push('/setup/character-select')\n          }, 1500)\n        } else {\n          this.showToast(result?.error?.message || '创建角色失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[CreateCharacter] 创建角色失败:', error)\n        this.showToast(error.message || '创建角色失败，请重试')\n      } finally {\n        this.isCreating = false\n      }\n    },\n\n    goBack() {\n      this.$router.push('/setup/character-select')\n    },\n\n    showToast(message) {\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 全局重置和基础样式 */\n* {\n  box-sizing: border-box;\n}\n\nhtml, body {\n  margin: 0;\n  padding: 0;\n  overflow-x: hidden;\n}\n.create-character-container {\n  padding: 5px 15px;\n  max-width: 480px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  align-items: flex-start;\n  justify-content: center;\n  box-sizing: border-box;\n  padding-top: 10px;\n}\n\n.character-creation-panel {\n  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);\n  border: 3px solid #8b7355;\n  border-radius: 12px;\n  padding: 20px;\n  width: 100%;\n  max-width: 420px;\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);\n  box-sizing: border-box;\n  position: relative;\n  margin-top: -25px;\n}\n\n.character-form {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n/* 名称输入区 */\n.name-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.label-text {\n  color: #ffd700;\n  font-size: 16px;\n  font-weight: bold;\n  min-width: 55px;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.name-input {\n  flex: 1;\n  background: #ffff99;\n  border: 2px solid #8b7355;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #000;\n  outline: none;\n  transition: border-color 0.3s ease;\n}\n\n.name-input:focus {\n  border-color: #ffd700;\n  box-shadow: 0 0 3px rgba(255, 215, 0, 0.5);\n}\n\n/* 性别选择区 */\n.gender-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.gender-buttons {\n  display: flex;\n  gap: 20px;\n}\n\n.gender-btn {\n  background: transparent;\n  border: none;\n  color: #ffd700;\n  font-size: 18px;\n  font-weight: bold;\n  cursor: pointer;\n  padding: 8px 16px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.gender-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.05);\n}\n\n.gender-btn.selected {\n  background: rgba(255, 215, 0, 0.3);\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n/* 职业选择区 */\n.profession-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.profession-buttons {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.profession-btn {\n  background: transparent;\n  border: none;\n  color: #ffd700;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  padding: 8px 14px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.profession-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.05);\n}\n\n.profession-btn.selected {\n  background: rgba(255, 215, 0, 0.3);\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n/* 头像选择区 */\n.avatar-section {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.avatar-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 10px;\n}\n\n.avatar-option {\n  width: 65px;\n  height: 65px;\n  border: 2px solid #8b7355;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.avatar-option:hover {\n  border-color: #ffd700;\n  transform: scale(1.05);\n}\n\n.avatar-option.selected {\n  border-color: #ffd700;\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n.avatar-option img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 提示文字 */\n.hint-text {\n  background: rgba(0, 0, 0, 0.8);\n  color: #ffd700;\n  text-align: center;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 13px;\n  margin: 10px 0 5px 0;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 25px;\n  margin-top: 5px;\n  padding: 10px 0;\n  border-top: 1px solid #8b7355;\n}\n\n.game-btn {\n  background: linear-gradient(135deg, #8b7355 0%, #a0845c 50%, #8b7355 100%);\n  border: 2px solid #ffd700;\n  border-radius: 8px;\n  color: #ffd700;\n  font-size: 14px;\n  font-weight: bold;\n  padding: 10px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  min-width: 80px;\n}\n\n.game-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #a0845c 0%, #b8956b 50%, #a0845c 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);\n}\n\n.game-btn:active:not(:disabled) {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.game-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n  background: #666;\n  border-color: #999;\n  color: #ccc;\n}\n\n.return-btn {\n  background: linear-gradient(135deg, #6b4423 0%, #8b5a2b 50%, #6b4423 100%);\n}\n\n.return-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #8b5a2b 0%, #a0673a 50%, #8b5a2b 100%);\n}\n\n.create-btn {\n  background: linear-gradient(135deg, #2d5016 0%, #4a7c23 50%, #2d5016 100%);\n}\n\n.create-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #4a7c23 0%, #5e9c2e 50%, #4a7c23 100%);\n}\n\n@media (max-width: 768px) {\n  .create-character-container {\n    padding: 8px;\n    min-height: 100vh;\n  }\n\n  .character-creation-panel {\n    margin: 0;\n    padding: 18px;\n    max-width: 100%;\n    border-radius: 8px;\n  }\n\n  .character-form {\n    gap: 15px;\n  }\n\n  .name-section,\n  .gender-section,\n  .profession-section,\n  .avatar-section {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n    padding: 10px 0;\n  }\n\n  .label-text {\n    min-width: auto;\n    font-size: 15px;\n  }\n\n  .gender-buttons,\n  .profession-buttons {\n    gap: 15px;\n    flex-wrap: wrap;\n  }\n\n  .avatar-grid {\n    gap: 8px;\n  }\n\n  .avatar-option {\n    width: 55px;\n    height: 55px;\n  }\n\n  .action-buttons {\n    gap: 18px;\n    margin-top: 15px;\n    padding: 12px 0;\n  }\n\n  .game-btn {\n    font-size: 13px;\n    padding: 8px 16px;\n    min-width: 70px;\n  }\n}\n\n/* 针对小屏幕设备的额外优化 */\n@media (max-width: 480px) {\n  .create-character-container {\n    padding: 5px;\n    min-height: 100vh;\n  }\n\n  .character-creation-panel {\n    padding: 12px;\n    border-radius: 6px;\n  }\n\n  .character-form {\n    gap: 12px;\n  }\n\n  .name-section,\n  .gender-section,\n  .profession-section,\n  .avatar-section {\n    padding: 8px 0;\n  }\n\n  .label-text {\n    font-size: 14px;\n  }\n\n  .gender-btn {\n    font-size: 16px;\n    padding: 6px 12px;\n  }\n\n  .profession-btn {\n    font-size: 14px;\n    padding: 6px 10px;\n  }\n\n  .gender-buttons,\n  .profession-buttons {\n    gap: 10px;\n  }\n\n  .avatar-grid {\n    gap: 6px;\n  }\n\n  .avatar-option {\n    width: 48px;\n    height: 48px;\n  }\n\n  .action-buttons {\n    gap: 15px;\n    margin-top: 12px;\n    padding: 10px 0;\n  }\n\n  .game-btn {\n    font-size: 12px;\n    padding: 6px 12px;\n    min-width: 60px;\n  }\n}\n</style>\n"]}]}