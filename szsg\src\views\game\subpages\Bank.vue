<template>
  <GameLayout>
    <div class="bank-page">
      <div class="bank-container">
        <!-- 钱庄标题 -->
        <div class="bank-header">
          <h1>钱庄</h1>
          <div class="character-status">
            <div class="status-box">
              <div class="status-item">
                <div class="status-label">银两:</div>
                <div class="status-value silver-value">{{ characterInfo.silver }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">金砖:</div>
                <div class="status-value gold-value">{{ characterInfo.gold }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">存款银两:</div>
                <div class="status-value silver-value">{{ accountInfo.silver }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">存款金砖:</div>
                <div class="status-value gold-value">{{ accountInfo.gold_ingot }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 选项卡导航 -->
        <div class="tabs-container">
          <div 
            v-for="tab in tabs" 
            :key="tab.id" 
            class="tab" 
            :class="{ 'active': activeTab === tab.id }"
            @click="activeTab = tab.id"
          >
            <span class="tab-name">{{ tab.name }}</span>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <!-- 存款 -->
          <div v-if="activeTab === 'deposit'" class="tab-content">
            <div class="operation-form">
              <div class="form-group">
                <label for="deposit-currency">货币类型:</label>
                <select id="deposit-currency" v-model="depositForm.currency" class="form-control">
                  <option value="silver">银两</option>
                  <option value="gold_ingot">金砖</option>
                </select>
              </div>
              <div class="form-group">
                <label for="deposit-amount">存款金额:</label>
                <input 
                  id="deposit-amount" 
                  type="number" 
                  v-model="depositForm.amount" 
                  class="form-control" 
                  min="1"
                  :max="getMaxDepositAmount()"
                />
              </div>
              <div class="form-actions">
                <button 
                  @click="deposit" 
                  class="action-button" 
                  :disabled="!canDeposit()"
                >
                  存款
                </button>
                <button @click="setMaxDepositAmount" class="max-button">最大</button>
              </div>
            </div>
          </div>

          <!-- 取款 -->
          <div v-if="activeTab === 'withdraw'" class="tab-content">
            <div class="operation-form">
              <div class="form-group">
                <label for="withdraw-currency">货币类型:</label>
                <select id="withdraw-currency" v-model="withdrawForm.currency" class="form-control">
                  <option value="silver">银两</option>
                  <option value="gold_ingot">金砖</option>
                </select>
              </div>
              <div class="form-group">
                <label for="withdraw-amount">取款金额:</label>
                <input 
                  id="withdraw-amount" 
                  type="number" 
                  v-model="withdrawForm.amount" 
                  class="form-control" 
                  min="1"
                  :max="getMaxWithdrawAmount()"
                />
              </div>
              <div class="form-actions">
                <button 
                  @click="withdraw" 
                  class="action-button" 
                  :disabled="!canWithdraw()"
                >
                  取款
                </button>
                <button @click="setMaxWithdrawAmount" class="max-button">最大</button>
              </div>
            </div>
          </div>

          <!-- 交易记录 -->
          <div v-if="activeTab === 'transactions'" class="tab-content">
            <div class="transaction-filters">
              <div class="filter-group">
                <label for="filter-currency">货币类型:</label>
                <select id="filter-currency" v-model="transactionFilters.currency" class="form-control" @change="loadTransactions">
                  <option value="">全部</option>
                  <option value="silver">银两</option>
                  <option value="gold_ingot">金砖</option>
                </select>
              </div>
              <div class="filter-group">
                <label for="filter-type">交易类型:</label>
                <select id="filter-type" v-model="transactionFilters.type" class="form-control" @change="loadTransactions">
                  <option value="">全部</option>
                  <option value="deposit">存款</option>
                  <option value="withdraw">取款</option>
                </select>
              </div>
            </div>

            <div class="transaction-list">
              <table v-if="formattedTransactions.length > 0">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>类型</th>
                    <th>货币</th>
                    <th>金额</th>
                    <th>余额</th>
                    <th>说明</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="transaction in formattedTransactions" :key="transaction.id">
                    <td>{{ formatDate(transaction.created_at) }}</td>
                    <td>{{ transaction.type === 'deposit' ? '存款' : '取款' }}</td>
                    <td>{{ transaction.currency === 'silver' ? '银两' : '金砖' }}</td>
                    <td>{{ transaction.amount }}</td>
                    <td>{{ transaction.balance }}</td>
                    <td>{{ transaction.description }}</td>
                  </tr>
                </tbody>
              </table>
              <div v-else class="no-transactions">
                暂无交易记录
              </div>
              
              <!-- 分页 -->
              <div v-if="pagination && pagination.total > 0" class="pagination">
                <button 
                  @click="changePage(pagination.current_page - 1)" 
                  :disabled="pagination.current_page <= 1"
                  class="pagination-button"
                >
                  上一页
                </button>
                <span class="page-info">{{ pagination.current_page }} / {{ pagination.last_page }}</span>
                <button 
                  @click="changePage(pagination.current_page + 1)" 
                  :disabled="pagination.current_page >= pagination.last_page"
                  class="pagination-button"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 返回按钮 -->
        <div class="bottom-actions">
          <button class="back-button" @click="$router.push('/game/main')">返回城镇</button>
        </div>

        <!-- 操作结果弹窗 -->
        <div v-if="showResult" class="result-modal">
          <div class="result-content" :class="{ 'error': resultError }">
            <h3>{{ resultError ? '操作失败' : '操作成功' }}</h3>
            <p>{{ resultMessage }}</p>
            <button @click="showResult = false">确定</button>
          </div>
        </div>

        <!-- 加载中和错误提示 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
          <div>加载中...</div>
        </div>
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue';
import bankService from '@/api/services/bankService';
import { showMessage } from '@/utils/message';
import logger from '@/utils/logger';
import { getCurrentCharacter } from '@/api/services/characterService';

export default {
  name: 'Bank',
  components: { GameLayout },
  data() {
    return {
      isLoading: true,
      error: null,
      activeTab: 'deposit', // 默认选中存款选项卡
      tabs: [
        {
          id: 'deposit',
          name: '存款'
        },
        {
          id: 'withdraw',
          name: '取款'
        },
        {
          id: 'transactions',
          name: '交易记录'
        }
      ],
      characterInfo: {
        id: null,
        name: '',
        silver: 0,
        gold: 0
      },
      accountInfo: {
        silver: 0,
        gold_ingot: 0
      },
      depositForm: {
        currency: 'silver',
        amount: 100
      },
      withdrawForm: {
        currency: 'silver',
        amount: 100
      },
      transactions: [],
      pagination: null,
      transactionFilters: {
        currency: '',
        type: '',
        page: 1,
        per_page: 10
      },
      showResult: false,
      resultError: false,
      resultMessage: ''
    };
  },
  created() {
    this.loadData();
  },
  watch: {
    activeTab(newTab) {
      if (newTab === 'transactions') {
        this.loadTransactions();
      }
    },
    'depositForm.currency'() {
      // 切换货币类型时，重置金额
      this.depositForm.amount = 100;
    },
    'withdrawForm.currency'() {
      // 切换货币类型时，重置金额
      this.withdrawForm.amount = 100;
    }
  },
  computed: {
    /**
     * 格式化交易记录，确保数据格式一致
     * 处理可能的数据格式不一致问题
     */
    formattedTransactions() {
      return this.transactions.map(transaction => {
        // 确保所有必要字段都存在，使用默认值代替可能缺失的字段
        return {
          id: transaction.id || Math.random().toString(36).substr(2, 9), // 生成随机ID如果缺失
          created_at: transaction.created_at || new Date().toISOString(),
          type: transaction.type || 'deposit',
          currency: transaction.currency || 'silver',
          amount: parseInt(transaction.amount) || 0,
          balance: parseInt(transaction.balance) || 0,
          description: transaction.description || this.generateDescription(transaction)
        };
      });
    }
  },
  methods: {
    /**
     * 加载初始数据
     */
    async loadData() {
      try {
        this.isLoading = true;
        this.error = null;
        
        // 获取当前角色信息
        const character = getCurrentCharacter();
        if (!character) {
          logger.error('[Bank] 未找到角色信息，尝试从LocalStorage获取');
          
          // 尝试直接从LocalStorage获取
          try {
            const characterId = localStorage.getItem('selectedCharacterId');
            const characterJson = localStorage.getItem('selectedCharacter');
            
            if (characterId && characterJson) {
              const savedCharacter = JSON.parse(characterJson);
              logger.debug('[Bank] 从LocalStorage获取到角色:', savedCharacter);
              
              this.characterInfo = {
                id: savedCharacter.id,
                name: savedCharacter.name,
                silver: savedCharacter.silver || 0,
                gold: savedCharacter.gold || 0
              };
            } else {
              throw new Error('未找到角色信息，请先选择角色');
            }
          } catch (e) {
            logger.error('[Bank] 从LocalStorage获取角色失败:', e);
            throw new Error('未找到角色信息，请先选择角色');
          }
        } else {
          // 正常获取到角色信息
          this.characterInfo = {
            id: character.id,
            name: character.name,
            silver: character.silver || 0,
            gold: character.gold || 0
          };
          
          logger.debug('[Bank] 获取到角色信息:', JSON.stringify(this.characterInfo));
        }
        
        // 从Vuex获取更详细的角色信息
        try {
          const characterStatus = this.$store?.state?.character?.characterStatus;
          if (characterStatus) {
            this.characterInfo.silver = characterStatus.silver || this.characterInfo.silver;
            this.characterInfo.gold = characterStatus.gold || this.characterInfo.gold;
            logger.debug('[Bank] 从Vuex更新后的角色信息:', JSON.stringify(this.characterInfo));
          } else {
            logger.warn('[Bank] 未从Vuex获取到角色状态，使用默认值');
          }
        } catch (stateError) {
          logger.error('[Bank] 从Vuex获取角色状态失败:', stateError);
        }
        
        // 银行账户信息初始化为默认值，防止API调用失败时没有数据
        this.accountInfo = {
          silver: 0,
          gold_ingot: 0
        };
        
        try {
          // 获取银行账户信息
          logger.debug('[Bank] 开始获取银行账户信息, ID:', this.characterInfo.id);
          const accountResponse = await bankService.getAccountInfo(this.characterInfo.id);
          logger.debug('[Bank] 银行账户响应:', JSON.stringify(accountResponse));
          
          // 处理可能的不同响应格式
          if (accountResponse) {
            // 检查不同的响应数据结构
            if (accountResponse.data && accountResponse.data.account) {
              // 标准格式: { data: { account: {...} } }
              this.accountInfo = accountResponse.data.account;
              logger.debug('[Bank] 获取到账户信息(标准格式):', JSON.stringify(this.accountInfo));
              
              // 更新角色信息
              if (accountResponse.data.character) {
                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.data.character));
                this.characterInfo.name = accountResponse.data.character.name || this.characterInfo.name;
                this.characterInfo.silver = accountResponse.data.character.silver || this.characterInfo.silver;
                this.characterInfo.gold = accountResponse.data.character.gold || this.characterInfo.gold;
                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));
              }
            } else if (accountResponse.account) {
              // 替代格式1: { account: {...} }
              this.accountInfo = accountResponse.account;
              logger.debug('[Bank] 获取到账户信息(替代格式1):', JSON.stringify(this.accountInfo));
              
              // 更新角色信息
              if (accountResponse.character) {
                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.character));
                this.characterInfo.name = accountResponse.character.name || this.characterInfo.name;
                this.characterInfo.silver = accountResponse.character.silver || this.characterInfo.silver;
                this.characterInfo.gold = accountResponse.character.gold || this.characterInfo.gold;
                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));
              }
            } else if (typeof accountResponse === 'object' && accountResponse !== null) {
              // 替代格式2: 响应本身就是账户对象
              // 检查是否至少有银两或金砖字段
              if ('silver' in accountResponse || 'gold_ingot' in accountResponse) {
                this.accountInfo = {
                  silver: accountResponse.silver || 0,
                  gold_ingot: accountResponse.gold_ingot || 0
                };
                logger.debug('[Bank] 获取到账户信息(替代格式2):', JSON.stringify(this.accountInfo));
                
                // 检查是否包含角色信息
                if ('name' in accountResponse || 'silver' in accountResponse || 'gold' in accountResponse) {
                  logger.debug('[Bank] 从API响应对象中获取角色信息');
                  if ('name' in accountResponse) this.characterInfo.name = accountResponse.name;
                  if ('silver' in accountResponse) this.characterInfo.silver = accountResponse.silver;
                  if ('gold' in accountResponse) this.characterInfo.gold = accountResponse.gold;
                  logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));
                }
              } else {
                throw new Error('获取银行账户信息失败: 响应数据格式无效');
              }
            } else {
              throw new Error('获取银行账户信息失败: 响应数据格式无效');
            }
          } else {
            throw new Error('获取银行账户信息失败: 无响应数据');
          }
        } catch (apiError) {
          // 记录API错误但不阻止页面加载，使用默认账户信息
          logger.error('[Bank] API调用失败:', apiError);
          logger.error('[Bank] API调用失败详情:', JSON.stringify(apiError));
          
          // 显示警告消息
          showMessage('获取银行账户信息失败，将使用临时数据显示', 'warning');
          
          // 默认值已初始化，无需重复设置
        } finally {
          // 无论API成功与否，都初始化表单
          this.depositForm.amount = Math.min(100, this.characterInfo.silver || 0);
          this.withdrawForm.amount = Math.min(100, this.accountInfo.silver || 0);
        }
        
        this.isLoading = false;
      } catch (error) {
        this.error = error.message || '加载数据失败，请刷新重试';
        this.isLoading = false;
        logger.error('[Bank] 加载数据失败:', error);
        
        // 如果是未找到角色信息，则跳转到角色选择页面
        if (error.message && error.message.includes('未找到角色信息')) {
          showMessage('未找到角色信息，请先选择角色', 'error');
          this.$router.push('/setup/character-select');
        }
      }
    },
    
    /**
     * 加载交易记录
     */
    async loadTransactions() {
      try {
        this.isLoading = true;
        this.error = null;
        
        // 如果角色信息不存在，不尝试加载交易记录
        if (!this.characterInfo || !this.characterInfo.id) {
          logger.warn('[Bank] 无法加载交易记录: 角色信息不存在');
          this.transactions = [];
          this.isLoading = false;
          return;
        }
        
        // 构建请求参数，只有当筛选值不为空字符串时才添加到请求参数中
        const params = {};
        if (this.transactionFilters.page) {
          params.page = this.transactionFilters.page;
        }
        if (this.transactionFilters.per_page) {
          params.per_page = this.transactionFilters.per_page;
        }
        // 只有当值不是空字符串时才添加筛选条件
        if (this.transactionFilters.currency && this.transactionFilters.currency !== '') {
          params.currency = this.transactionFilters.currency;
        }
        if (this.transactionFilters.type && this.transactionFilters.type !== '') {
          params.type = this.transactionFilters.type;
        }
        
        logger.debug('[Bank] 加载交易记录, 参数:', params);
        
        try {
          const response = await bankService.getTransactionHistory(this.characterInfo.id, params);
          logger.debug('[Bank] 交易记录响应:', JSON.stringify(response));
          
          // 处理响应结构
          if (response && response.data) {
            // 标准响应结构
            this.transactions = response.data.transactions || [];
            this.pagination = response.data.pagination || null;
            logger.debug('[Bank] 解析到交易记录:', this.transactions.length);
          } else if (response && Array.isArray(response)) {
            // 响应直接是交易数组
            this.transactions = response;
            logger.debug('[Bank] 解析到交易记录(数组格式):', this.transactions.length);
          } else if (response && Array.isArray(response.transactions)) {
            // 响应中有transactions数组
            this.transactions = response.transactions;
            this.pagination = response.pagination || null;
            logger.debug('[Bank] 解析到交易记录(嵌套数组):', this.transactions.length);
          } else if (response && response.success && Array.isArray(response.data)) {
            // success包装的数组
            this.transactions = response.data;
            logger.debug('[Bank] 解析到交易记录(success包装数组):', this.transactions.length);
          } else {
            // 无效响应，使用空数组
            logger.warn('[Bank] 无效的交易记录响应格式，使用空数组:', JSON.stringify(response));
            this.transactions = [];
            this.pagination = null;
          }
          
          // 检查交易记录是否有效
          if (this.transactions.length > 0) {
            logger.debug('[Bank] 交易记录首条样例:', JSON.stringify(this.transactions[0]));
          } else {
            logger.warn('[Bank] 未获取到交易记录，筛选条件:', this.transactionFilters);
          }
        } catch (apiError) {
          // API调用失败，使用空数组
          logger.error('[Bank] 交易记录API调用失败:', apiError);
          this.transactions = [];
          this.pagination = null;
          
          // 显示警告消息，但不中断加载过程
          showMessage('获取交易记录失败，将显示空记录', 'warning');
        }
        
        this.isLoading = false;
      } catch (error) {
        this.error = error.message || '加载交易记录失败，请刷新重试';
        this.isLoading = false;
        logger.error('[Bank] 加载交易记录失败:', error);
        
        // 确保有默认值
        this.transactions = this.transactions || [];
      }
    },
    
    /**
     * 存款操作
     */
    async deposit() {
      try {
        if (!this.canDeposit()) {
          return this.showResultMessage('无法进行存款操作', true);
        }
        
        this.isLoading = true;
        this.error = null;
        
        const response = await bankService.deposit(
          this.characterInfo.id,
          this.depositForm.currency,
          parseInt(this.depositForm.amount) || 0
        );
        
        // 更新本地数据
        if (response.account) {
          this.accountInfo = response.account;
          logger.debug('[Bank] 存款后更新账户信息:', JSON.stringify(this.accountInfo));
        }
        
        if (response.character) {
          // 确保数据类型正确转换
          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;
          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;
          logger.debug('[Bank] 存款后更新角色信息:', JSON.stringify(this.characterInfo));
        }
        
        this.isLoading = false;
        this.showResultMessage(response.message || '存款成功', false);
      } catch (error) {
        this.isLoading = false;
        this.showResultMessage(error.message || '存款失败，请重试', true);
        logger.error('[Bank] 存款操作失败:', error);
      }
    },
    
    /**
     * 取款操作
     */
    async withdraw() {
      try {
        if (!this.canWithdraw()) {
          return this.showResultMessage('无法进行取款操作', true);
        }
        
        this.isLoading = true;
        this.error = null;
        
        const response = await bankService.withdraw(
          this.characterInfo.id,
          this.withdrawForm.currency,
          parseInt(this.withdrawForm.amount) || 0
        );
        
        // 更新本地数据
        if (response.account) {
          this.accountInfo = response.account;
          logger.debug('[Bank] 取款后更新账户信息:', JSON.stringify(this.accountInfo));
        }
        
        if (response.character) {
          // 确保数据类型正确转换
          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;
          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;
          logger.debug('[Bank] 取款后更新角色信息:', JSON.stringify(this.characterInfo));
        }
        
        this.isLoading = false;
        this.showResultMessage(response.message || '取款成功', false);
      } catch (error) {
        this.isLoading = false;
        this.showResultMessage(error.message || '取款失败，请重试', true);
        logger.error('[Bank] 取款操作失败:', error);
      }
    },
    
    /**
     * 显示操作结果消息
     */
    showResultMessage(message, isError = false) {
      this.resultMessage = message;
      this.resultError = isError;
      this.showResult = true;
    },
    
    /**
     * 为缺失描述的交易记录生成默认描述
     */
    generateDescription(transaction) {
      const type = transaction.type === 'deposit' ? '存入' : '取出';
      const currency = transaction.currency === 'silver' ? '银两' : '金砖';
      const amount = transaction.amount || 0;
      return `${type} ${amount} ${currency}`;
    },
    
    /**
     * 格式化日期
     */
    formatDate(dateStr) {
      if (!dateStr) return '未知时间';
      
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '日期格式错误';
        
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } catch (e) {
        logger.error('[Bank] 日期格式化失败:', e);
        return '日期格式错误';
      }
    },
    
    /**
     * 切换分页
     */
    changePage(page) {
      if (page < 1 || (this.pagination && page > this.pagination.last_page)) {
        return;
      }
      
      this.transactionFilters.page = page;
      this.loadTransactions();
    },
    
    /**
     * 获取最大可存款金额
     */
    getMaxDepositAmount() {
      if (this.depositForm.currency === 'silver') {
        return this.characterInfo.silver;
      } else {
        return this.characterInfo.gold;
      }
    },
    
    /**
     * 获取最大可取款金额
     */
    getMaxWithdrawAmount() {
      if (this.withdrawForm.currency === 'silver') {
        return this.accountInfo.silver;
      } else {
        return this.accountInfo.gold_ingot;
      }
    },
    
    /**
     * 设置最大存款金额
     */
    setMaxDepositAmount() {
      this.depositForm.amount = this.getMaxDepositAmount();
    },
    
    /**
     * 设置最大取款金额
     */
    setMaxWithdrawAmount() {
      this.withdrawForm.amount = this.getMaxWithdrawAmount();
    },
    
    /**
     * 判断是否可以存款
     */
    canDeposit() {
      const amount = parseInt(this.depositForm.amount);
      if (!amount || amount <= 0) {
        return false;
      }
      
      if (this.depositForm.currency === 'silver') {
        return amount <= this.characterInfo.silver;
      } else {
        return amount <= this.characterInfo.gold;
      }
    },
    
    /**
     * 判断是否可以取款
     */
    canWithdraw() {
      const amount = parseInt(this.withdrawForm.amount);
      if (!amount || amount <= 0) {
        return false;
      }
      
      if (this.withdrawForm.currency === 'silver') {
        return amount <= this.accountInfo.silver;
      } else {
        return amount <= this.accountInfo.gold_ingot;
      }
    }
  }
};
</script>

<style scoped>
.bank-page {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.bank-container {
  background-color: rgba(0, 0, 20, 0.7);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 0 20px rgba(0, 0, 50, 0.5);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.bank-header {
  margin-bottom: 15px;
  padding: 12px;
  background-color: rgba(0, 0, 51, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(51, 51, 204, 0.3);
}

.bank-header h1 {
  margin: 0 0 10px 0;
  text-align: center;
  color: #ffcc00;
  font-size: 22px;
  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);
}

.character-status {
  margin-top: 10px;
}

.status-box {
  background-color: rgba(153, 0, 0, 0.2);
  border: 1px solid rgba(153, 0, 0, 0.5);
  border-radius: 5px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 3px 5px;
}

.status-label {
  color: #aaaaff;
  font-weight: bold;
}

.status-value {
  color: white;
  font-weight: bold;
}

.silver-value {
  color: #ffcc00;
}

.gold-value {
  color: #ff9900;
}

.tabs-container {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 2px solid rgba(51, 51, 204, 0.5);
}

.tab {
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  flex: 1;
  border-radius: 5px 5px 0 0;
  background-color: rgba(0, 0, 51, 0.3);
}

.tab:hover {
  background-color: rgba(51, 51, 204, 0.3);
}

.tab.active {
  background-color: rgba(51, 51, 204, 0.5);
  border: 1px solid rgba(51, 51, 204, 0.8);
  border-bottom: none;
}

.tab-name {
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 15px;
}

.tab-content {
  padding: 10px;
}

.operation-form {
  background-color: rgba(0, 0, 51, 0.3);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(51, 51, 204, 0.3);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #aaaaff;
  font-weight: bold;
}

.form-control {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid rgba(51, 51, 204, 0.5);
  background-color: rgba(0, 0, 20, 0.7);
  color: white;
  font-size: 16px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.action-button {
  flex: 3;
  padding: 12px;
  background-color: #3333cc;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.3s ease;
}

.action-button:hover:not(:disabled) {
  background-color: #4444dd;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.action-button:disabled {
  background-color: #666666;
  cursor: not-allowed;
}

.max-button {
  flex: 1;
  padding: 12px;
  background-color: #990000;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.max-button:hover {
  background-color: #cc0000;
}

.transaction-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-group {
  flex: 1;
}

.transaction-list {
  background-color: rgba(0, 0, 51, 0.3);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(51, 51, 204, 0.3);
  max-height: 400px;
  overflow-y: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead {
  position: sticky;
  top: 0;
  background-color: rgba(0, 0, 51, 0.8);
  z-index: 1;
}

th, td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid rgba(51, 51, 204, 0.3);
}

th {
  color: #aaaaff;
  font-weight: bold;
}

tr:hover {
  background-color: rgba(51, 51, 204, 0.1);
}

.no-transactions {
  text-align: center;
  padding: 20px;
  color: #aaaaaa;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  gap: 10px;
}

.pagination-button {
  padding: 8px 15px;
  background-color: #3333cc;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-button:hover:not(:disabled) {
  background-color: #4444dd;
}

.pagination-button:disabled {
  background-color: #666666;
  cursor: not-allowed;
}

.page-info {
  font-weight: bold;
}

.bottom-actions {
  margin-top: auto;
  text-align: center;
}

.back-button {
  padding: 10px 20px;
  background-color: #cc0000;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  font-weight: bold;
  font-size: 16px;
  letter-spacing: 1px;
}

.back-button:hover {
  background-color: #dd4444;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
}

.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  background-color: #000033;
  border: 2px solid #3333cc;
  border-radius: 6px;
  padding: 20px;
  width: 80%;
  max-width: 400px;
  text-align: center;
}

.result-content.error {
  border-color: #cc0000;
}

.result-content h3 {
  color: #ffcc00;
  margin-top: 0;
}

.result-content p {
  margin: 15px 0;
}

.result-content button {
  padding: 8px 20px;
  background-color: #3333cc;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.result-content button:hover {
  background-color: #4444dd;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(51, 51, 204, 0.3);
  border-top: 4px solid #3333cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: rgba(153, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  text-align: center;
}

/* 移动设备适配 */
@media (max-width: 480px) {
  .status-box {
    grid-template-columns: 1fr;
  }
  
  .transaction-filters {
    flex-direction: column;
    gap: 10px;
  }
  
  th, td {
    padding: 8px 5px;
    font-size: 14px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .action-button, .max-button {
    width: 100%;
  }
}
</style> 