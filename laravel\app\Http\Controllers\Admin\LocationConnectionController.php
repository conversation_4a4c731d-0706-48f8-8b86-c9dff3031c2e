<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Location;
use App\Models\LocationConnection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LocationConnectionController extends Controller
{
    /**
     * 显示位置连接列表
     */
    public function index()
    {
        $connections = LocationConnection::with(['fromLocation', 'toLocation'])
            ->orderBy('from_location_id')
            ->orderBy('to_location_id')
            ->paginate(20);

        $stats = [
            'total_connections' => LocationConnection::count(),
            'active_connections' => LocationConnection::where('is_active', true)->count(),
            'bidirectional_connections' => $this->getBidirectionalConnectionsCount(),
        ];

        return view('admin.location-connections.index', compact('connections', 'stats'));
    }

    /**
     * 显示创建连接表单
     */
    public function create()
    {
        $locations = Location::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.location-connections.create', compact('locations'));
    }

    /**
     * 保存新连接
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_location_id' => 'required|exists:locations,id',
            'to_location_id' => 'required|exists:locations,id|different:from_location_id',
            'distance' => 'required|integer|min:1',
            'time_cost' => 'required|integer|min:1',
            'silver_cost' => 'required|integer|min:0',
            'level_requirement' => 'required|integer|min:1',
            'is_bidirectional' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // 检查连接是否已存在
            $existingConnection = LocationConnection::where('from_location_id', $request->from_location_id)
                ->where('to_location_id', $request->to_location_id)
                ->first();

            if ($existingConnection) {
                return back()->withErrors(['error' => '该连接已存在']);
            }

            // 创建连接
            $connectionData = [
                'from_location_id' => $request->from_location_id,
                'to_location_id' => $request->to_location_id,
                'distance' => $request->distance,
                'time_cost' => $request->time_cost,
                'silver_cost' => $request->silver_cost,
                'level_requirement' => $request->level_requirement,
                'is_active' => true,
            ];

            LocationConnection::create($connectionData);

            // 如果是双向连接，创建反向连接
            if ($request->is_bidirectional) {
                $reverseConnectionData = $connectionData;
                $reverseConnectionData['from_location_id'] = $request->to_location_id;
                $reverseConnectionData['to_location_id'] = $request->from_location_id;

                LocationConnection::create($reverseConnectionData);
            }

            DB::commit();

            return redirect()->route('admin.location-connections.index')
                ->with('success', '位置连接创建成功');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => '创建失败：' . $e->getMessage()]);
        }
    }

    /**
     * 显示编辑连接表单
     */
    public function edit(LocationConnection $locationConnection)
    {
        $locations = Location::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.location-connections.edit', compact('locationConnection', 'locations'));
    }

    /**
     * 更新连接
     */
    public function update(Request $request, LocationConnection $locationConnection)
    {
        $request->validate([
            'distance' => 'required|integer|min:1',
            'time_cost' => 'required|integer|min:1',
            'silver_cost' => 'required|integer|min:0',
            'level_requirement' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        try {
            $locationConnection->update([
                'distance' => $request->distance,
                'time_cost' => $request->time_cost,
                'silver_cost' => $request->silver_cost,
                'level_requirement' => $request->level_requirement,
                'is_active' => $request->has('is_active'),
            ]);

            return redirect()->route('admin.location-connections.index')
                ->with('success', '位置连接更新成功');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除连接
     */
    public function destroy(LocationConnection $locationConnection)
    {
        try {
            $locationConnection->delete();

            return redirect()->route('admin.location-connections.index')
                ->with('success', '位置连接删除成功');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量创建双向连接
     */
    public function createBidirectional(Request $request)
    {
        $request->validate([
            'location_ids' => 'required|array|min:2',
            'location_ids.*' => 'exists:locations,id',
            'distance' => 'required|integer|min:1',
            'time_cost' => 'required|integer|min:1',
            'silver_cost' => 'required|integer|min:0',
            'level_requirement' => 'required|integer|min:1',
        ]);

        try {
            DB::beginTransaction();

            $locationIds = $request->location_ids;
            $connectionsCreated = 0;

            // 为每对位置创建双向连接
            for ($i = 0; $i < count($locationIds); $i++) {
                for ($j = $i + 1; $j < count($locationIds); $j++) {
                    $fromId = $locationIds[$i];
                    $toId = $locationIds[$j];

                    // 检查连接是否已存在
                    $exists = LocationConnection::where('from_location_id', $fromId)
                        ->where('to_location_id', $toId)
                        ->exists();

                    if (!$exists) {
                        // 创建正向连接
                        LocationConnection::create([
                            'from_location_id' => $fromId,
                            'to_location_id' => $toId,
                            'distance' => $request->distance,
                            'time_cost' => $request->time_cost,
                            'silver_cost' => $request->silver_cost,
                            'level_requirement' => $request->level_requirement,
                            'is_active' => true,
                        ]);

                        // 创建反向连接
                        LocationConnection::create([
                            'from_location_id' => $toId,
                            'to_location_id' => $fromId,
                            'distance' => $request->distance,
                            'time_cost' => $request->time_cost,
                            'silver_cost' => $request->silver_cost,
                            'level_requirement' => $request->level_requirement,
                            'is_active' => true,
                        ]);

                        $connectionsCreated += 2;
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "成功创建 {$connectionsCreated} 个连接"
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取双向连接数量
     */
    private function getBidirectionalConnectionsCount()
    {
        return LocationConnection::select('from_location_id', 'to_location_id')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('location_connections as lc2')
                    ->whereColumn('lc2.from_location_id', 'location_connections.to_location_id')
                    ->whereColumn('lc2.to_location_id', 'location_connections.from_location_id');
            })
            ->count() / 2; // 除以2因为每个双向连接被计算了两次
    }
}
