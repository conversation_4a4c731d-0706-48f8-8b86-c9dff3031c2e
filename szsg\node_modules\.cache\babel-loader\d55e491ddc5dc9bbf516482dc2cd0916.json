{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\vipService.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\vipService.js", "mtime": 1749722665195}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "logger", "store", "get", "post", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "_store$state$auth", "token", "state", "auth", "localStorage", "getItem", "Authorization", "getVipInfo", "response", "loading", "error", "level", "exp", "next_exp", "can_claim_reward", "privileges", "reward_history", "date", "desc", "can_claim_daily", "getVipLevels", "reward", "claimVipReward", "claimHistoryReward", "claimDailyReward", "claimWeeklyReward", "claimMonthlyReward", "addVipExp", "amount", "calculateVipExp", "Math", "floor"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/api/services/vipService.js"], "sourcesContent": ["import axios from 'axios'\r\nimport logger from '@/utils/logger'\r\nimport store from '@/store'\r\nimport { get, post } from '@/api/request'\r\n\r\n// 获取认证头信息的辅助函数\r\nconst getAuthHeader = () => {\r\n  const token = store.state.auth?.token || localStorage.getItem('token') || localStorage.getItem('authToken')\r\n  return token ? { Authorization: `Bear<PERSON> ${token}` } : {}\r\n}\r\n\r\n/**\r\n * 获取VIP信息\r\n * @returns {Promise<Object>} VIP信息\r\n */\r\nexport const getVipInfo = async () => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await get('vip/info', {}, { loading: false });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('获取VIP信息失败', error)\r\n    // 模拟数据，仅用于开发测试\r\n    return {\r\n      level: 0,\r\n      exp: 0,\r\n      next_exp: 500,\r\n      can_claim_reward: true,\r\n      privileges: [\r\n        '每日签到奖励增加50%',\r\n        '商城购买折扣10%',\r\n        '每日可额外领取100体力',\r\n        '战斗经验增加15%'\r\n      ],\r\n      reward_history: [\r\n        { level: 1, date: '2023-06-15', desc: '金砖x10，银两x1000，高级装备箱x1' },\r\n        { level: 2, date: '2023-07-20', desc: '金砖x20，银两x2000，稀有武器箱x1' },\r\n        { level: 3, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 4, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 5, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 6, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 7, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 8, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 9, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 10, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 11, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n        { level: 12, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },\r\n      ],\r\n      can_claim_daily: true\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * 获取VIP等级配置\r\n * @returns {Promise<Array>} VIP等级配置列表\r\n */\r\nexport const getVipLevels = async () => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await get('vip/levels', {}, { loading: false });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('获取VIP等级配置失败', error)\r\n    // 模拟数据，仅用于开发测试\r\n    return [\r\n      { level: 0, exp: 0, reward: '无', privileges: ['基础游戏功能'] },\r\n      { level: 1, exp: 100, reward: '金砖x10，银两x1000', privileges: ['每日签到奖励增加10%', '商城购买折扣5%'] },\r\n      { level: 2, exp: 300, reward: '金砖x20，银两x2000', privileges: ['每日签到奖励增加20%', '商城购买折扣7%', '每日可额外领取50体力'] },\r\n      { level: 3, exp: 500, reward: '金砖x30，银两x3000', privileges: ['每日签到奖励增加50%', '商城购买折扣10%', '每日可额外领取100体力', '战斗经验增加15%'] },\r\n      { level: 4, exp: 1000, reward: '金砖x50，银两x5000', privileges: ['每日签到奖励增加70%', '商城购买折扣12%', '每日可额外领取150体力', '战斗经验增加20%', '自动战斗功能'] },\r\n      { level: 5, exp: 2000, reward: '金砖x100，银两x10000', privileges: ['每日签到奖励增加100%', '商城购买折扣15%', '每日可额外领取200体力', '战斗经验增加25%', '自动战斗功能', '每日免费抽奖次数+1'] }\r\n    ]\r\n  }\r\n}\r\n\r\n/**\r\n * 领取VIP等级奖励\r\n * @returns {Promise<Object>} 领取结果\r\n */\r\nexport const claimVipReward = async () => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await post('vip/claim-reward', {}, { loading: true });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('领取VIP奖励失败', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 领取历史VIP奖励\r\n * @param {Number} level VIP等级\r\n * @returns {Promise<Object>} 领取结果\r\n */\r\nexport const claimHistoryReward = async (level) => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await post('vip/claim-history-reward', { level }, { loading: true });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('领取历史VIP奖励失败', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 领取每日VIP福利\r\n * @returns {Promise<Object>} 领取结果\r\n */\r\nexport const claimDailyReward = async () => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await post('vip/daily-reward', {}, { loading: true });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('领取每日VIP福利失败', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 领取每周VIP福利\r\n * @returns {Promise<Object>} 领取结果\r\n */\r\nexport const claimWeeklyReward = async () => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await post('vip/weekly-reward', {}, { loading: true });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('领取每周VIP福利失败', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 领取每月VIP福利\r\n * @returns {Promise<Object>} 领取结果\r\n */\r\nexport const claimMonthlyReward = async () => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await post('vip/monthly-reward', {}, { loading: true });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('领取每月VIP福利失败', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 添加VIP经验\r\n * @param {Number} amount 经验值\r\n * @returns {Promise<Object>} 添加结果\r\n */\r\nexport const addVipExp = async (amount) => {\r\n  try {\r\n    // 使用封装的请求方法\r\n    const response = await post('vip/add-exp', { amount }, { loading: true });\r\n    return response;\r\n  } catch (error) {\r\n    logger.error('添加VIP经验失败', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 计算VIP经验值\r\n * @param {Number} amount 充值金额\r\n * @returns {Number} VIP经验值\r\n */\r\nexport const calculateVipExp = (amount) => {\r\n  // 假设每充值1元获得10点VIP经验\r\n  return Math.floor(amount * 10)\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,GAAG,EAAEC,IAAI,QAAQ,eAAe;;AAEzC;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAA,IAAAC,iBAAA;EAC1B,MAAMC,KAAK,GAAG,EAAAD,iBAAA,GAAAJ,KAAK,CAACM,KAAK,CAACC,IAAI,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBC,KAAK,KAAIG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC3G,OAAOJ,KAAK,GAAG;IAAEK,aAAa,EAAE,UAAUL,KAAK;EAAG,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMM,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMX,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE;MAAEY,OAAO,EAAE;IAAM,CAAC,CAAC;IAC9D,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAChC;IACA,OAAO;MACLC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,GAAG;MACbC,gBAAgB,EAAE,IAAI;MACtBC,UAAU,EAAE,CACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,WAAW,CACZ;MACDC,cAAc,EAAE,CACd;QAAEL,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAwB,CAAC,EAC/D;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAwB,CAAC,EAC/D;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,CAAC;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACvD;QAAEP,KAAK,EAAE,EAAE;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACxD;QAAEP,KAAK,EAAE,EAAE;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,EACxD;QAAEP,KAAK,EAAE,EAAE;QAAEM,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAwB,CAAC,CACzD;MACDC,eAAe,EAAE;IACnB,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACF;IACA,MAAMZ,QAAQ,GAAG,MAAMX,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE;MAAEY,OAAO,EAAE;IAAM,CAAC,CAAC;IAChE,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAClC;IACA,OAAO,CACL;MAAEC,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAES,MAAM,EAAE,GAAG;MAAEN,UAAU,EAAE,CAAC,QAAQ;IAAE,CAAC,EACzD;MAAEJ,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,GAAG;MAAES,MAAM,EAAE,eAAe;MAAEN,UAAU,EAAE,CAAC,aAAa,EAAE,UAAU;IAAE,CAAC,EACxF;MAAEJ,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,GAAG;MAAES,MAAM,EAAE,eAAe;MAAEN,UAAU,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,aAAa;IAAE,CAAC,EACvG;MAAEJ,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,GAAG;MAAES,MAAM,EAAE,eAAe;MAAEN,UAAU,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW;IAAE,CAAC,EACtH;MAAEJ,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,IAAI;MAAES,MAAM,EAAE,eAAe;MAAEN,UAAU,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ;IAAE,CAAC,EACjI;MAAEJ,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,IAAI;MAAES,MAAM,EAAE,iBAAiB;MAAEN,UAAU,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY;IAAE,CAAC,CACnJ;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMO,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF;IACA,MAAMd,QAAQ,GAAG,MAAMV,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;IACtE,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAChC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,kBAAkB,GAAG,MAAOZ,KAAK,IAAK;EACjD,IAAI;IACF;IACA,MAAMH,QAAQ,GAAG,MAAMV,IAAI,CAAC,0BAA0B,EAAE;MAAEa;IAAM,CAAC,EAAE;MAAEF,OAAO,EAAE;IAAK,CAAC,CAAC;IACrF,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMc,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,IAAI;IACF;IACA,MAAMhB,QAAQ,GAAG,MAAMV,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;IACtE,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMe,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAI;IACF;IACA,MAAMjB,QAAQ,GAAG,MAAMV,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;IACvE,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMgB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF;IACA,MAAMlB,QAAQ,GAAG,MAAMV,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;IACxE,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiB,SAAS,GAAG,MAAOC,MAAM,IAAK;EACzC,IAAI;IACF;IACA,MAAMpB,QAAQ,GAAG,MAAMV,IAAI,CAAC,aAAa,EAAE;MAAE8B;IAAO,CAAC,EAAE;MAAEnB,OAAO,EAAE;IAAK,CAAC,CAAC;IACzE,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdf,MAAM,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAChC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,eAAe,GAAID,MAAM,IAAK;EACzC;EACA,OAAOE,IAAI,CAACC,KAAK,CAACH,MAAM,GAAG,EAAE,CAAC;AAChC,CAAC", "ignoreList": []}]}