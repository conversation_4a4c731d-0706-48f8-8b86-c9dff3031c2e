/**
 * API错误处理工具
 * 统一处理API调用中的错误，特别是404错误
 */
import { ERROR_MESSAGES } from '@/api/constants.js'
import logger from '@/utils/logger'

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} apiName - API名称，用于生成友好的错误消息
 * @returns {string} 处理后的错误消息
 */
export function handleApiError(error, apiName = 'API') {
  // 记录错误日志
  logger.warn(`${apiName}调用失败`, error.response?.status, error.message)
  
  // 处理404错误
  if (error.response && error.response.status === 404) {
    return `${apiName}暂未实现，请等待后端开发完成`
  }
  
  // 处理其他HTTP错误
  if (error.response) {
    const status = error.response.status
    const message = error.response.data?.message
    
    switch (status) {
      case 400:
        return message || '请求参数错误'
      case 401:
        return '未授权，请重新登录'
      case 403:
        return '权限不足'
      case 500:
        return '服务器内部错误'
      case 502:
        return '网关错误'
      case 503:
        return '服务暂不可用'
      default:
        return message || `请求失败 (${status})`
    }
  }
  
  // 处理网络错误
  if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
    return '网络连接失败，请检查网络设置'
  }
  
  // 处理超时错误
  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
    return '请求超时，请稍后重试'
  }
  
  // 默认错误消息
  return error.message || ERROR_MESSAGES.UNKNOWN_ERROR
}

/**
 * 创建API错误处理器
 * @param {string} apiName - API名称
 * @returns {Function} 错误处理函数
 */
export function createApiErrorHandler(apiName) {
  return (error) => handleApiError(error, apiName)
}

/**
 * 常用API错误处理器
 */
export const apiErrorHandlers = {
  bag: createApiErrorHandler('背包'),
  quest: createApiErrorHandler('任务'),
  skill: createApiErrorHandler('技能'),
  battle: createApiErrorHandler('战斗'),
  friend: createApiErrorHandler('好友'),
  equipment: createApiErrorHandler('装备'),
  market: createApiErrorHandler('市场'),
  character: createApiErrorHandler('角色')
}

/**
 * 检查是否为API未实现错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为404错误
 */
export function isApiNotImplemented(error) {
  return error.response && error.response.status === 404
}

/**
 * 获取友好的错误消息
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认消息
 * @returns {string} 友好的错误消息
 */
export function getFriendlyErrorMessage(error, defaultMessage = '操作失败') {
  if (isApiNotImplemented(error)) {
    return 'API暂未实现，请等待后端开发完成'
  }
  
  return error.response?.data?.message || error.message || defaultMessage
}
