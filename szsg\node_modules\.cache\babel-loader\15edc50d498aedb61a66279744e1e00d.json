{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue", "mtime": 1749703549914}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9jYWxTdG9yYWdlOiB7CiAgICAgICAgYXV0aFRva2VuOiBudWxsLAogICAgICAgIHRva2VuOiBudWxsCiAgICAgIH0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgc3RvcmVBdXRoKCkgewogICAgICB2YXIgX3RoaXMkJHN0b3JlJHN0YXRlJGF1LCBfdGhpcyQkc3RvcmUkc3RhdGUkYXUyLCBfdGhpcyQkc3RvcmUkc3RhdGUkYXUzOwogICAgICByZXR1cm4gewogICAgICAgIGlzQXV0aGVudGljYXRlZDogKChfdGhpcyQkc3RvcmUkc3RhdGUkYXUgPSB0aGlzLiRzdG9yZS5zdGF0ZS5hdXRoKSA9PT0gbnVsbCB8fCBfdGhpcyQkc3RvcmUkc3RhdGUkYXUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJCRzdG9yZSRzdGF0ZSRhdS5pc0F1dGhlbnRpY2F0ZWQpIHx8IGZhbHNlLAogICAgICAgIHRva2VuOiAoKF90aGlzJCRzdG9yZSRzdGF0ZSRhdTIgPSB0aGlzLiRzdG9yZS5zdGF0ZS5hdXRoKSA9PT0gbnVsbCB8fCBfdGhpcyQkc3RvcmUkc3RhdGUkYXUyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpcyQkc3RvcmUkc3RhdGUkYXUyLnRva2VuKSB8fCBudWxsLAogICAgICAgIHVzZXI6ICgoX3RoaXMkJHN0b3JlJHN0YXRlJGF1MyA9IHRoaXMuJHN0b3JlLnN0YXRlLmF1dGgpID09PSBudWxsIHx8IF90aGlzJCRzdG9yZSRzdGF0ZSRhdTMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJCRzdG9yZSRzdGF0ZSRhdTMudXNlcikgfHwgbnVsbAogICAgICB9OwogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMudXBkYXRlTG9jYWxTdG9yYWdlSW5mbygpOwogIH0sCiAgbWV0aG9kczogewogICAgdXBkYXRlTG9jYWxTdG9yYWdlSW5mbygpIHsKICAgICAgdGhpcy5sb2NhbFN0b3JhZ2UuYXV0aFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhUb2tlbicpOwogICAgICB0aGlzLmxvY2FsU3RvcmFnZS50b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpOwogICAgfSwKICAgIGNsZWFyQXV0aCgpIHsKICAgICAgLy8g5riF6ZmkIHN0b3JlCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhdXRoL2xvZ291dCcpOwoKICAgICAgLy8g5riF6ZmkIGxvY2FsU3RvcmFnZQogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aFRva2VuJyk7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd0b2tlbicpOwogICAgICB0aGlzLnVwZGF0ZUxvY2FsU3RvcmFnZUluZm8oKTsKICAgICAgYWxlcnQoJ+iupOivgeeKtuaAgeW3sua4hemZpCcpOwogICAgfSwKICAgIHNldFRlc3RBdXRoKCkgewogICAgICBjb25zdCB0ZXN0VG9rZW4gPSAndGVzdC10b2tlbi0nICsgRGF0ZS5ub3coKTsKICAgICAgY29uc3QgdGVzdFVzZXIgPSB7CiAgICAgICAgaWQ6IDEsCiAgICAgICAgbmFtZTogJ1Rlc3RVc2VyJywKICAgICAgICB1c2VybmFtZTogJ3Rlc3R1c2VyJwogICAgICB9OwoKICAgICAgLy8g6K6+572u5YiwIHN0b3JlCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhdXRoL2xvZ2luJywgewogICAgICAgIHRva2VuOiB0ZXN0VG9rZW4sCiAgICAgICAgdXNlckluZm86IHRlc3RVc2VyCiAgICAgIH0pOwoKICAgICAgLy8g6K6+572u5YiwIGxvY2FsU3RvcmFnZQogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYXV0aFRva2VuJywgdGVzdFRva2VuKTsKICAgICAgdGhpcy51cGRhdGVMb2NhbFN0b3JhZ2VJbmZvKCk7CiAgICAgIGFsZXJ0KCfmtYvor5XorqTor4HnirbmgIHlt7Lorr7nva4nKTsKICAgIH0sCiAgICByZWZyZXNoUGFnZSgpIHsKICAgICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpOwogICAgfSwKICAgIGdvVG9GcmllbmRzKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2dhbWUvZnJpZW5kcycpOwogICAgfSwKICAgIGdvVG9Mb2dpbigpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9sb2dpbicpOwogICAgfSwKICAgIGdvVG9NYWluKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2dhbWUvbWFpbicpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["data", "localStorage", "authToken", "token", "computed", "storeAuth", "_this$$store$state$au", "_this$$store$state$au2", "_this$$store$state$au3", "isAuthenticated", "$store", "state", "auth", "user", "created", "updateLocalStorageInfo", "methods", "getItem", "clearAuth", "dispatch", "removeItem", "alert", "setTestAuth", "testToken", "Date", "now", "testUser", "id", "name", "username", "userInfo", "setItem", "refreshPage", "window", "location", "reload", "goToFriends", "$router", "push", "goToLogin", "goToMain"], "sources": ["src/views/debug/AuthDebug.vue"], "sourcesContent": ["<template>\n  <div class=\"auth-debug\">\n    <h2>认证状态调试</h2>\n    \n    <div class=\"debug-section\">\n      <h3>Store 状态</h3>\n      <div class=\"debug-item\">\n        <strong>isAuthenticated:</strong> {{ storeAuth.isAuthenticated }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>token:</strong> {{ storeAuth.token ? '存在' : '不存在' }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>user:</strong> {{ storeAuth.user ? JSON.stringify(storeAuth.user) : '不存在' }}\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>LocalStorage</h3>\n      <div class=\"debug-item\">\n        <strong>authToken:</strong> {{ localStorage.authToken ? '存在' : '不存在' }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>token:</strong> {{ localStorage.token ? '存在' : '不存在' }}\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>操作</h3>\n      <button @click=\"clearAuth\" class=\"debug-btn\">清除认证状态</button>\n      <button @click=\"setTestAuth\" class=\"debug-btn\">设置测试认证</button>\n      <button @click=\"refreshPage\" class=\"debug-btn\">刷新页面</button>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>导航测试</h3>\n      <button @click=\"goToFriends\" class=\"debug-btn\">跳转到好友页面</button>\n      <button @click=\"goToLogin\" class=\"debug-btn\">跳转到登录页面</button>\n      <button @click=\"goToMain\" class=\"debug-btn\">跳转到游戏主页</button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      localStorage: {\n        authToken: null,\n        token: null\n      }\n    }\n  },\n  computed: {\n    storeAuth() {\n      return {\n        isAuthenticated: this.$store.state.auth?.isAuthenticated || false,\n        token: this.$store.state.auth?.token || null,\n        user: this.$store.state.auth?.user || null\n      }\n    }\n  },\n  created() {\n    this.updateLocalStorageInfo()\n  },\n  methods: {\n    updateLocalStorageInfo() {\n      this.localStorage.authToken = localStorage.getItem('authToken')\n      this.localStorage.token = localStorage.getItem('token')\n    },\n    \n    clearAuth() {\n      // 清除 store\n      this.$store.dispatch('auth/logout')\n      \n      // 清除 localStorage\n      localStorage.removeItem('authToken')\n      localStorage.removeItem('token')\n      \n      this.updateLocalStorageInfo()\n      alert('认证状态已清除')\n    },\n    \n    setTestAuth() {\n      const testToken = 'test-token-' + Date.now()\n      const testUser = {\n        id: 1,\n        name: 'TestUser',\n        username: 'testuser'\n      }\n      \n      // 设置到 store\n      this.$store.dispatch('auth/login', {\n        token: testToken,\n        userInfo: testUser\n      })\n      \n      // 设置到 localStorage\n      localStorage.setItem('authToken', testToken)\n      \n      this.updateLocalStorageInfo()\n      alert('测试认证状态已设置')\n    },\n    \n    refreshPage() {\n      window.location.reload()\n    },\n    \n    goToFriends() {\n      this.$router.push('/game/friends')\n    },\n    \n    goToLogin() {\n      this.$router.push('/login')\n    },\n    \n    goToMain() {\n      this.$router.push('/game/main')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.auth-debug {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n  font-family: Arial, sans-serif;\n}\n\n.debug-section {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  background: #f9f9f9;\n}\n\n.debug-section h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.debug-item {\n  margin-bottom: 10px;\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.debug-btn {\n  margin: 5px;\n  padding: 8px 16px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.debug-btn:hover {\n  background: #0056b3;\n}\n</style>\n"], "mappings": ";AA4CA;EACAA,KAAA;IACA;MACAC,YAAA;QACAC,SAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACA;QACAC,eAAA,IAAAH,qBAAA,QAAAI,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAN,qBAAA,uBAAAA,qBAAA,CAAAG,eAAA;QACAN,KAAA,IAAAI,sBAAA,QAAAG,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAL,sBAAA,uBAAAA,sBAAA,CAAAJ,KAAA;QACAU,IAAA,IAAAL,sBAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAJ,sBAAA,uBAAAA,sBAAA,CAAAK,IAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,sBAAA;EACA;EACAC,OAAA;IACAD,uBAAA;MACA,KAAAd,YAAA,CAAAC,SAAA,GAAAD,YAAA,CAAAgB,OAAA;MACA,KAAAhB,YAAA,CAAAE,KAAA,GAAAF,YAAA,CAAAgB,OAAA;IACA;IAEAC,UAAA;MACA;MACA,KAAAR,MAAA,CAAAS,QAAA;;MAEA;MACAlB,YAAA,CAAAmB,UAAA;MACAnB,YAAA,CAAAmB,UAAA;MAEA,KAAAL,sBAAA;MACAM,KAAA;IACA;IAEAC,YAAA;MACA,MAAAC,SAAA,mBAAAC,IAAA,CAAAC,GAAA;MACA,MAAAC,QAAA;QACAC,EAAA;QACAC,IAAA;QACAC,QAAA;MACA;;MAEA;MACA,KAAAnB,MAAA,CAAAS,QAAA;QACAhB,KAAA,EAAAoB,SAAA;QACAO,QAAA,EAAAJ;MACA;;MAEA;MACAzB,YAAA,CAAA8B,OAAA,cAAAR,SAAA;MAEA,KAAAR,sBAAA;MACAM,KAAA;IACA;IAEAW,YAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;IACA;IAEAC,YAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IAEAC,UAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IAEAE,SAAA;MACA,KAAAH,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}