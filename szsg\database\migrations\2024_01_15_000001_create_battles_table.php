<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('battles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('character_id');
            $table->unsignedBigInteger('monster_id');
            $table->string('location_id', 50)->nullable();
            $table->enum('battle_type', ['normal', 'boss', 'pvp'])->default('normal');
            $table->enum('status', ['ongoing', 'victory', 'defeat', 'fled'])->default('ongoing');
            $table->integer('rounds')->default(0);
            $table->timestamp('start_time')->useCurrent();
            $table->timestamp('end_time')->nullable();
            $table->integer('exp_gained')->default(0);
            $table->integer('gold_gained')->default(0);
            $table->json('items_gained')->nullable();
            $table->json('battle_log')->nullable();
            $table->timestamps();

            $table->foreign('character_id')->references('id')->on('characters')->onDelete('cascade');
            $table->index(['character_id', 'status']);
            $table->index(['location_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('battles');
    }
};
