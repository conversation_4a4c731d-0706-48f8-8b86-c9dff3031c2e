{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue?vue&type=template&id=1bd62dc5&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue", "mtime": 1749724641760}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}