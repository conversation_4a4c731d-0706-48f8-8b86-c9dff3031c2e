<template>
  <div class="auth-debug">
    <h2>认证状态调试</h2>
    
    <div class="debug-section">
      <h3>Store 状态</h3>
      <div class="debug-item">
        <strong>isAuthenticated:</strong> {{ storeAuth.isAuthenticated }}
      </div>
      <div class="debug-item">
        <strong>token:</strong> {{ storeAuth.token ? '存在' : '不存在' }}
      </div>
      <div class="debug-item">
        <strong>user:</strong> {{ storeAuth.user ? JSON.stringify(storeAuth.user) : '不存在' }}
      </div>
    </div>
    
    <div class="debug-section">
      <h3>LocalStorage</h3>
      <div class="debug-item">
        <strong>authToken:</strong> {{ localStorage.authToken ? '存在' : '不存在' }}
      </div>
      <div class="debug-item">
        <strong>token:</strong> {{ localStorage.token ? '存在' : '不存在' }}
      </div>
    </div>
    
    <div class="debug-section">
      <h3>操作</h3>
      <button @click="clearAuth" class="debug-btn">清除认证状态</button>
      <button @click="setTestAuth" class="debug-btn">设置测试认证</button>
      <button @click="refreshPage" class="debug-btn">刷新页面</button>
    </div>
    
    <div class="debug-section">
      <h3>导航测试</h3>
      <button @click="goToFriends" class="debug-btn">跳转到好友页面</button>
      <button @click="goToLogin" class="debug-btn">跳转到登录页面</button>
      <button @click="goToMain" class="debug-btn">跳转到游戏主页</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      localStorage: {
        authToken: null,
        token: null
      }
    }
  },
  computed: {
    storeAuth() {
      return {
        isAuthenticated: this.$store.state.auth?.isAuthenticated || false,
        token: this.$store.state.auth?.token || null,
        user: this.$store.state.auth?.user || null
      }
    }
  },
  created() {
    this.updateLocalStorageInfo()
  },
  methods: {
    updateLocalStorageInfo() {
      this.localStorage.authToken = localStorage.getItem('authToken')
      this.localStorage.token = localStorage.getItem('token')
    },
    
    clearAuth() {
      // 清除 store
      this.$store.dispatch('auth/logout')
      
      // 清除 localStorage
      localStorage.removeItem('authToken')
      localStorage.removeItem('token')
      
      this.updateLocalStorageInfo()
      alert('认证状态已清除')
    },
    
    setTestAuth() {
      const testToken = 'test-token-' + Date.now()
      const testUser = {
        id: 1,
        name: 'TestUser',
        username: 'testuser'
      }
      
      // 设置到 store
      this.$store.dispatch('auth/login', {
        token: testToken,
        userInfo: testUser
      })
      
      // 设置到 localStorage
      localStorage.setItem('authToken', testToken)
      
      this.updateLocalStorageInfo()
      alert('测试认证状态已设置')
    },
    
    refreshPage() {
      window.location.reload()
    },
    
    goToFriends() {
      this.$router.push('/game/friends')
    },
    
    goToLogin() {
      this.$router.push('/login')
    },
    
    goToMain() {
      this.$router.push('/game/main')
    }
  }
}
</script>

<style scoped>
.auth-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.debug-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #f9f9f9;
}

.debug-section h3 {
  margin-top: 0;
  color: #333;
}

.debug-item {
  margin-bottom: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.debug-btn {
  margin: 5px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-btn:hover {
  background: #0056b3;
}
</style>
