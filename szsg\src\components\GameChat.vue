<template>
    <div class="chat-container" :class="{ 'minimized': minimized, 'compact': compactMode }">
        <div class="chat-header" @click="toggleMinimize">
            <div class="chat-title-section">
                <span class="chat-title">聊天</span>
                <div class="chat-status" :class="{ 'connected': isConnected }" :title="isConnected ? '已连接' : '未连接'"></div>
            </div>
            <div class="chat-controls">
                <button v-if="!minimized" class="control-btn" @click.stop="clearMessages" title="清空消息">
                    <span class="icon">🗑</span>
                </button>
                <button v-if="!minimized" class="control-btn" @click.stop="toggleCompactMode" title="紧凑模式">
                    <span class="icon">{{ compactMode ? '📖' : '📄' }}</span>
                </button>
                <button class="control-btn minimize-btn" :title="minimized ? '展开聊天' : '收起聊天'">
                    <span class="icon">{{ minimized ? '⬆' : '⬇' }}</span>
                </button>
            </div>
        </div>

        <div v-if="!minimized" class="chat-body">
            <div class="chat-channels">
                <button
                    v-for="(channel, index) in channels"
                    :key="index"
                    class="channel-tab"
                    :class="{ active: currentChannelIndex === index }"
                    :data-icon="getChannelIcon(channel.id)"
                    @click="switchChannel(index)"
                >
                    <span class="channel-icon">{{ getChannelIcon(channel.id) }}</span>
                    <span class="channel-name">{{ channel.name }}</span>
                    <span v-if="channel.unread > 0" class="channel-badge">{{ channel.unread > 99 ? '99+' : channel.unread }}</span>
                </button>
            </div>

            <div class="chat-messages" ref="chatMessagesContainer">
                <template v-if="filteredMessages.length > 0">
                    <div
                        v-for="(msg, index) in filteredMessages"
                        :key="index"
                        class="chat-message"
                        :class="{
                            'system-message': msg.type === 'system',
                            'npc-message': msg.type === 'npc',
                            'player-message': msg.type === 'player',
                            'self-message': msg.isSelf,
                            'compact': compactMode
                        }"
                    >
                        <div v-if="msg.type !== 'system'" class="message-header">
                            <span class="message-sender" @click="handleSenderClick(msg)">{{ msg.sender }}</span>
                            <span v-if="msg.timestamp && !compactMode" class="message-time">{{ formatTime(msg.timestamp) }}</span>
                        </div>
                        <div class="message-content">{{ msg.content }}</div>
                        <div v-if="msg.timestamp && compactMode" class="message-time-compact">{{ formatTime(msg.timestamp) }}</div>
                    </div>
                </template>
                <div v-else class="empty-messages">
                    <div class="empty-icon">💬</div>
                    <div class="empty-text">暂无消息</div>
                    <div class="empty-hint">在下方输入框发送消息开始聊天</div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="input-wrapper">
                    <input
                        class="chat-input"
                        v-model="newMessage"
                        :placeholder="getInputPlaceholder()"
                        @keyup.enter="sendMessage"
                        @focus="onInputFocus"
                        @blur="onInputBlur"
                        :disabled="!isConnected"
                        maxlength="200"
                    />
                    <div class="input-counter" v-if="newMessage.length > 150">{{ newMessage.length }}/200</div>
                </div>
                <button
                    class="send-btn"
                    @click="sendMessage"
                    :disabled="!isConnected || !newMessage.trim()"
                    :title="!isConnected ? '未连接到聊天服务器' : '发送消息'"
                >
                    <span class="send-icon">📤</span>
                    <span class="send-text">发送</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import socketManager from '../utils/socketManager';

// 聊天频道配置
const CHANNELS = [
    { id: 'world', name: '世界', unread: 0 },
    { id: 'trade', name: '交易', unread: 0 },
    { id: 'team', name: '队伍', unread: 0 },
    { id: 'private', name: '私聊', unread: 0 }
];

export default {
    name: 'GameChat',
    props: {
        characterInfo: {
            type: Object,
            default: () => ({})
        },
        autoConnect: {
            type: Boolean,
            default: true
        },
        initialMinimized: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isConnected: false,
            channels: [...CHANNELS],
            currentChannelIndex: 0,
            messages: [],
            newMessage: '',
            minimized: this.initialMinimized,
            reconnecting: false,
            compactMode: false,
            inputFocused: false,
            lastMessageTime: null,
            messageHistory: [], // 消息历史记录，用于上下键切换
            historyIndex: -1
        };
    },
    computed: {
        currentChannel() {
            return this.channels[this.currentChannelIndex]?.id || 'world';
        },
        filteredMessages() {
            // 如果是私聊频道，显示所有私聊消息
            if (this.currentChannel === 'private') {
                return this.messages.filter(msg => msg.channel === 'private');
            }
            
            // 如果是队伍频道，根据characterInfo中的teamId过滤队伍消息
            if (this.currentChannel === 'team' && this.characterInfo.teamId) {
                return this.messages.filter(msg => 
                    msg.channel === 'team' && 
                    msg.teamId === this.characterInfo.teamId
                );
            }
            
            // 否则只显示当前频道的消息
            return this.messages.filter(msg => msg.channel === this.currentChannel);
        }
    },
    watch: {
        characterInfo: {
            handler(newInfo) {
                if (newInfo && newInfo.id && this.autoConnect) {
                    this.connectChat();
                }
            },
            immediate: true
        },
        currentChannelIndex() {
            // 切换频道时，重置未读消息数
            if (this.channels[this.currentChannelIndex]) {
                this.channels[this.currentChannelIndex].unread = 0;
            }
            
            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        }
    },
    mounted() {
        // 当前组件已挂载
        if (this.characterInfo?.id && this.autoConnect) {
            this.connectChat();
        }
    },
    beforeUnmount() {
        // 组件销毁前，移除所有事件监听器
        this.disconnectChat();
    },
    methods: {
        async connectChat() {
            if (!this.characterInfo?.id) {
                console.error('[GameChat] 无法连接聊天，缺少角色信息');
                return;
            }
            
            try {
                // 初始化Socket连接
                await socketManager.init();
                
                // 添加事件监听
                this.setupEventListeners();
                
                // 加入相关频道
                this.joinChannels();
                
                // 设置连接状态
                this.isConnected = true;
                this.reconnecting = false;
                
                // 添加系统消息
                this.addSystemMessage('已连接到聊天服务器');
                
            } catch (error) {
                console.error('[GameChat] 连接失败:', error);
                
                this.isConnected = false;
                if (!this.reconnecting) {
                    this.reconnecting = true;
                    this.addSystemMessage('连接失败，正在尝试重新连接...');
                }
            }
        },
        
        disconnectChat() {
            // 移除事件监听
            if (this.unsubscribers) {
                this.unsubscribers.forEach(unsubscribe => unsubscribe());
                this.unsubscribers = [];
            }
            
            // 断开socket连接
            socketManager.disconnect();
            
            // 更新状态
            this.isConnected = false;
        },
        
        setupEventListeners() {
            // 存储所有取消订阅的函数
            this.unsubscribers = [];
            
            // 监听连接事件
            this.unsubscribers.push(
                socketManager.subscribe('connect', () => {
                    this.isConnected = true;
                    this.reconnecting = false;
                })
            );
            
            // 监听断开连接事件
            this.unsubscribers.push(
                socketManager.subscribe('disconnect', (reason) => {
                    this.isConnected = false;
                    this.addSystemMessage(`连接已断开 (${reason})`);
                })
            );
            
            // 世界消息
            this.unsubscribers.push(
                socketManager.subscribe('world_message', (data) => {
                    this.handleChatMessage({
                        type: 'player',
                        channel: 'world',
                        sender: data.sender.name,
                        content: data.message,
                        timestamp: data.timestamp,
                        sender_id: data.sender.id,
                        isSelf: data.sender.id === this.characterInfo?.id
                    });
                })
            );
            
            // 队伍消息
            this.unsubscribers.push(
                socketManager.subscribe('team_message', (data) => {
                    this.handleChatMessage({
                        type: 'player',
                        channel: 'team',
                        teamId: data.team_id,
                        sender: data.message.sender.name,
                        content: data.message.message,
                        timestamp: data.message.timestamp,
                        sender_id: data.message.sender.id,
                        isSelf: data.message.sender.id === this.characterInfo?.id
                    });
                })
            );
            
            // 私聊消息
            this.unsubscribers.push(
                socketManager.subscribe('private_message', (data) => {
                    const isSelf = data.sender_id === this.characterInfo?.id;
                    const sender = isSelf ? this.characterInfo?.name : data.message.sender.name;
                    const receiverId = isSelf ? data.receiver_id : data.sender_id;
                    
                    this.handleChatMessage({
                        type: 'player',
                        channel: 'private',
                        sender,
                        content: data.message.message,
                        timestamp: data.message.timestamp,
                        sender_id: data.message.sender.id,
                        receiver_id: receiverId,
                        isSelf
                    });
                })
            );
            
            // 系统消息
            this.unsubscribers.push(
                socketManager.subscribe('system_message', (data) => {
                    this.addSystemMessage(data.message || '系统通知');
                })
            );
        },
        
        joinChannels() {
            // 加入世界频道
            socketManager.joinChannel('chat.world');
            
            // 如果角色有队伍，加入队伍频道
            if (this.characterInfo.teamId) {
                socketManager.joinChannel(`team.${this.characterInfo.teamId}`);
            }
            
            // 加入角色私聊频道
            socketManager.joinChannel(`chat.user.${this.characterInfo.id}`);
        },
        
        handleChatMessage(messageData) {
            // 添加到消息列表
            this.messages.push(messageData);
            
            // 控制消息列表大小，防止过长
            if (this.messages.length > 200) {
                this.messages = this.messages.slice(-150);
            }
            
            // 如果消息不是当前频道，更新未读计数
            if (messageData.channel !== this.currentChannel) {
                const channelIndex = this.channels.findIndex(c => c.id === messageData.channel);
                if (channelIndex !== -1) {
                    this.channels[channelIndex].unread++;
                }
            }
            
            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },
        
        addSystemMessage(content, timestamp = null) {
            this.messages.push({
                type: 'system',
                channel: 'system',
                sender: '系统',
                content,
                timestamp: timestamp || Date.now()
            });
            
            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },
        
        switchChannel(index) {
            this.currentChannelIndex = index;
        },
        
        sendMessage() {
            if (!this.isConnected) {
                this.addSystemMessage('未连接到聊天服务器，无法发送消息');
                return;
            }
            
            const message = this.newMessage.trim();
            if (!message) return;
            
            // 获取当前频道
            const channel = this.currentChannel;
            
            // 构建消息数据
            let messageOptions = {
                channel: channel,
                message: message,
                character_id: this.characterInfo.id
            };
            
            switch (channel) {
                case 'team':
                    if (!this.characterInfo.teamId) {
                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');
                        return;
                    }
                    messageOptions.team_id = this.characterInfo.teamId;
                    break;
                    
                case 'private':
                    // 私聊需要指定接收者
                    // TODO: 添加私聊目标选择功能
                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');
                    return;
            }
            
            // 使用socketManager的sendChatMessage方法
            socketManager.sendChatMessage(messageOptions);
            
            // 清空输入框
            this.newMessage = '';
        },
        
        handleSenderClick(msg) {
            // 点击发送者名称
            if (msg.type === 'player' && !msg.isSelf) {
                this.$emit('player-click', {
                    id: msg.sender_id,
                    name: msg.sender
                });
            }
        },
        
        scrollToBottom() {
            const container = this.$refs.chatMessagesContainer;
            if (container) {
                // 注意: uni-app环境中可能需要特殊处理
                if (container.$el) {
                    container.$el.scrollTop = container.$el.scrollHeight;
                } else if (container.scrollTop !== undefined) {
                    container.scrollTop = container.scrollHeight;
                }
            }
        },
        
        toggleMinimize() {
            this.minimized = !this.minimized;
            
            if (!this.minimized) {
                // 展开时，滚动到底部
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            }
        },
        
        formatTime(timestamp) {
            if (!timestamp) return '';

            const date = new Date(timestamp);
            const now = new Date();
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');

            // 如果是今天，只显示时间
            if (date.toDateString() === now.toDateString()) {
                return `${hours}:${minutes}`;
            }

            // 如果不是今天，显示月日和时间
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${month}-${day} ${hours}:${minutes}`;
        },

        // 新增方法
        toggleCompactMode() {
            this.compactMode = !this.compactMode;
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },

        clearMessages() {
            this.messages = [];
            this.addSystemMessage('消息已清空');
        },

        getInputPlaceholder() {
            const channel = this.currentChannel;
            const placeholders = {
                'world': '对所有人说...',
                'trade': '发布交易信息...',
                'team': '对队友说...',
                'private': '私聊消息...'
            };
            return placeholders[channel] || '输入消息...';
        },

        onInputFocus() {
            this.inputFocused = true;
        },

        onInputBlur() {
            this.inputFocused = false;
        },

        // 处理消息历史记录
        addToHistory(message) {
            if (message && message.trim()) {
                // 移除重复的消息
                const index = this.messageHistory.indexOf(message);
                if (index > -1) {
                    this.messageHistory.splice(index, 1);
                }

                // 添加到历史记录开头
                this.messageHistory.unshift(message);

                // 限制历史记录数量
                if (this.messageHistory.length > 20) {
                    this.messageHistory = this.messageHistory.slice(0, 20);
                }
            }
            this.historyIndex = -1;
        },

        // 获取频道图标
        getChannelIcon(channelId) {
            const icons = {
                'world': '🌍',
                'trade': '💰',
                'team': '👥',
                'private': '💬'
            };
            return icons[channelId] || '💬';
        },

        // 增强的消息发送
        sendMessage() {
            if (!this.isConnected) {
                this.addSystemMessage('未连接到聊天服务器，无法发送消息');
                return;
            }

            const message = this.newMessage.trim();
            if (!message) return;

            // 检查消息长度
            if (message.length > 200) {
                this.addSystemMessage('消息长度不能超过200个字符');
                return;
            }

            // 防止刷屏（限制发送频率）
            const now = Date.now();
            if (this.lastMessageTime && (now - this.lastMessageTime) < 1000) {
                this.addSystemMessage('发送消息过于频繁，请稍后再试');
                return;
            }
            this.lastMessageTime = now;

            // 添加到历史记录
            this.addToHistory(message);

            // 获取当前频道
            const channel = this.currentChannel;

            // 构建消息数据
            let messageOptions = {
                channel: channel,
                message: message,
                character_id: this.characterInfo.id
            };

            switch (channel) {
                case 'team':
                    if (!this.characterInfo.teamId) {
                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');
                        return;
                    }
                    messageOptions.team_id = this.characterInfo.teamId;
                    break;

                case 'private':
                    // 私聊需要指定接收者
                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');
                    return;
            }

            // 使用socketManager的sendChatMessage方法
            socketManager.sendChatMessage(messageOptions);

            // 清空输入框
            this.newMessage = '';
        }
    }
};
</script>

<style scoped>
.chat-container {
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(20,20,30,0.9) 100%);
    backdrop-filter: blur(15px);
    border-radius: 12px 12px 0 0;
    overflow: hidden;
    height: 420px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255,255,255,0.1);
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.chat-container.minimized {
    height: 48px;
}

.chat-container.compact {
    height: 320px;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(30,30,40,0.8) 100%);
    border-bottom: 1px solid rgba(255,255,255,0.15);
    height: 48px;
    box-sizing: border-box;
    cursor: pointer;
    user-select: none;
}

.chat-header:hover {
    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(30,30,40,0.9) 100%);
}

.chat-title-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-title {
    font-size: 14px;
    color: #fff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.chat-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.control-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #ccc;
}

.control-btn:hover {
    background: rgba(255,255,255,0.2);
    color: #fff;
    transform: scale(1.05);
}

.control-btn .icon {
    font-size: 12px;
}

.chat-status {
    width: 8px;
    height: 8px;
    background-color: #ff4444;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 0 8px rgba(255,68,68,0.5);
}

.chat-status.connected {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76,175,80,0.5);
}

.chat-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

.chat-channels {
    display: flex;
    flex-shrink: 0;
    background: linear-gradient(90deg, rgba(0,0,0,0.3) 0%, rgba(20,20,30,0.4) 100%);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 4px;
    gap: 2px;
}

.channel-tab {
    background: none;
    border: none;
    padding: 8px 16px;
    font-size: 12px;
    color: #aaa;
    border-radius: 6px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 0;
}

.channel-tab:hover {
    background: rgba(255,255,255,0.1);
    color: #ddd;
}

.channel-tab.active {
    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(232,81,28,0.3);
}

.channel-name {
    white-space: nowrap;
}

.channel-badge {
    background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);
    color: white;
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    box-shadow: 0 2px 4px rgba(255,68,68,0.3);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 8px 12px;
    background: rgba(0,0,0,0.1);
    scrollbar-width: thin;
    scrollbar-color: rgba(255,255,255,0.3) transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

.chat-message {
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 8px;
    background: rgba(255,255,255,0.02);
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.chat-message:hover {
    background: rgba(255,255,255,0.05);
}

.chat-message.compact {
    margin-bottom: 4px;
    padding: 4px 6px;
}

.message-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2px;
}

.message-sender {
    font-size: 11px;
    color: #4fc3f7;
    cursor: pointer;
    font-weight: 600;
    transition: color 0.2s ease;
}

.message-sender:hover {
    color: #29b6f6;
    text-decoration: underline;
}

.self-message .message-sender {
    color: #90CAF9;
}

.npc-message .message-sender {
    color: #a5d6a7;
}

.player-message {
    border-left-color: #4fc3f7;
}

.self-message {
    border-left-color: #90CAF9;
    background: rgba(144,202,249,0.05);
}

.npc-message {
    border-left-color: #a5d6a7;
}

.message-content {
    font-size: 12px;
    color: #f0f0f0;
    line-height: 1.4;
    word-break: break-word;
    margin: 2px 0;
}

.message-time {
    font-size: 10px;
    color: #888;
    opacity: 0.7;
}

.message-time-compact {
    font-size: 9px;
    color: #666;
    text-align: right;
    margin-top: 2px;
}

.system-message {
    border-left-color: #90caf9;
    background: rgba(144,202,249,0.08);
    font-style: italic;
}

.system-message .message-content {
    color: #90caf9;
    font-size: 11px;
}

.empty-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    opacity: 0.6;
    color: #aaa;
    text-align: center;
    padding: 20px;
}

.empty-icon {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.5;
}

.empty-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.empty-hint {
    font-size: 11px;
    opacity: 0.7;
}

.chat-input-container {
    display: flex;
    padding: 8px 12px;
    border-top: 1px solid rgba(255,255,255,0.15);
    gap: 8px;
    flex-shrink: 0;
    background: rgba(0,0,0,0.2);
}

.input-wrapper {
    flex: 1;
    position: relative;
}

.chat-input {
    width: 100%;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 8px;
    padding: 8px 12px;
    color: #fff;
    font-size: 12px;
    height: 36px;
    box-sizing: border-box;
    transition: all 0.2s ease;
    outline: none;
}

.chat-input:focus {
    background: rgba(255,255,255,0.15);
    border-color: #e8511c;
    box-shadow: 0 0 0 2px rgba(232,81,28,0.2);
}

.chat-input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.chat-input::placeholder {
    color: #aaa;
}

.input-counter {
    position: absolute;
    right: 8px;
    bottom: -16px;
    font-size: 9px;
    color: #888;
}

.send-btn {
    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);
    color: white;
    border: none;
    padding: 0 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    height: 36px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
}

.send-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d64516 0%, #e55a2b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(232,81,28,0.3);
}

.send-btn:active:not(:disabled) {
    transform: translateY(0);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.send-icon {
    font-size: 12px;
}

.send-text {
    font-size: 11px;
}

/* 响应式设计 */
/* 超小屏幕 (手机竖屏) */
@media (max-width: 480px) {
    .chat-container {
        height: 280px;
        border-radius: 8px 8px 0 0;
    }

    .chat-container.compact {
        height: 220px;
    }

    .chat-container.minimized {
        height: 40px;
    }

    .chat-header {
        padding: 4px 8px;
        height: 40px;
    }

    .chat-title {
        font-size: 11px;
    }

    .control-btn {
        width: 20px;
        height: 20px;
        gap: 2px;
    }

    .control-btn .icon {
        font-size: 10px;
    }

    .channel-tab {
        padding: 4px 8px;
        font-size: 10px;
        gap: 3px;
    }

    .channel-name {
        display: none; /* 超小屏幕隐藏频道名称，只显示图标 */
    }

    .channel-tab::before {
        content: attr(data-icon);
        font-size: 12px;
    }

    .chat-messages {
        padding: 4px 6px;
    }

    .chat-message {
        margin-bottom: 6px;
        padding: 4px 6px;
    }

    .message-sender {
        font-size: 10px;
    }

    .message-content {
        font-size: 11px;
    }

    .message-time {
        font-size: 9px;
    }

    .chat-input-container {
        padding: 4px 6px;
        gap: 6px;
    }

    .chat-input {
        height: 32px;
        font-size: 11px;
        padding: 6px 10px;
    }

    .send-btn {
        min-width: 45px;
        padding: 0 8px;
        height: 32px;
    }

    .send-text {
        display: none; /* 超小屏幕只显示图标 */
    }

    .empty-icon {
        font-size: 24px;
    }

    .empty-text {
        font-size: 12px;
    }

    .empty-hint {
        font-size: 10px;
    }
}

/* 小屏幕 (手机横屏/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
    .chat-container {
        height: 320px;
    }

    .chat-container.compact {
        height: 260px;
    }

    .chat-container.minimized {
        height: 44px;
    }

    .chat-header {
        padding: 6px 10px;
        height: 44px;
    }

    .chat-title {
        font-size: 12px;
    }

    .control-btn {
        width: 24px;
        height: 24px;
    }

    .channel-tab {
        padding: 6px 12px;
        font-size: 11px;
    }

    .chat-messages {
        padding: 6px 8px;
    }

    .message-sender {
        font-size: 10px;
    }

    .message-content {
        font-size: 11px;
    }

    .chat-input-container {
        padding: 6px 8px;
    }

    .chat-input {
        height: 34px;
        font-size: 11px;
    }

    .send-btn {
        min-width: 50px;
        padding: 0 12px;
        height: 34px;
    }
}

/* 中等屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
    .chat-container {
        height: 380px;
    }

    .chat-container.compact {
        height: 300px;
    }

    .chat-header {
        padding: 7px 11px;
    }

    .chat-title {
        font-size: 13px;
    }

    .control-btn {
        width: 26px;
        height: 26px;
    }

    .channel-tab {
        padding: 7px 14px;
        font-size: 11px;
    }

    .chat-messages {
        padding: 7px 10px;
    }

    .chat-input {
        height: 35px;
        font-size: 12px;
    }

    .send-btn {
        height: 35px;
    }
}

/* 大屏幕 (桌面) */
@media (min-width: 1025px) {
    .chat-container {
        height: 420px;
    }

    .chat-container.compact {
        height: 320px;
    }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
    .chat-container {
        height: 240px !important;
    }

    .chat-container.compact {
        height: 200px !important;
    }

    .chat-container.minimized {
        height: 36px !important;
    }

    .chat-header {
        height: 36px;
        padding: 4px 8px;
    }

    .chat-title {
        font-size: 11px;
    }

    .control-btn {
        width: 22px;
        height: 22px;
    }

    .channel-tab {
        padding: 4px 10px;
        font-size: 10px;
    }

    .chat-messages {
        padding: 4px 6px;
    }

    .chat-input-container {
        padding: 4px 6px;
    }

    .chat-input {
        height: 30px;
        font-size: 10px;
    }

    .send-btn {
        height: 30px;
        min-width: 40px;
        padding: 0 8px;
    }
}

/* 高分辨率屏幕适配 */
@media (min-width: 1440px) {
    .chat-container {
        height: 480px;
        max-width: 400px;
        margin: 0 auto;
    }

    .chat-container.compact {
        height: 380px;
    }

    .chat-title {
        font-size: 15px;
    }

    .control-btn {
        width: 30px;
        height: 30px;
    }

    .channel-tab {
        padding: 10px 18px;
        font-size: 13px;
    }

    .message-sender {
        font-size: 12px;
    }

    .message-content {
        font-size: 13px;
    }

    .chat-input {
        font-size: 13px;
        height: 38px;
    }

    .send-btn {
        height: 38px;
        font-size: 12px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .control-btn,
    .channel-tab,
    .send-btn {
        min-height: 44px; /* iOS建议的最小触摸目标 */
    }

    .control-btn:active {
        transform: scale(0.95);
        background: rgba(255,255,255,0.3);
    }

    .channel-tab:active {
        background: rgba(232,81,28,0.8);
    }

    .send-btn:active {
        transform: scale(0.98);
    }

    .message-sender {
        min-height: 32px;
        display: flex;
        align-items: center;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .chat-container {
        background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(15,15,25,0.95) 100%);
        border-color: rgba(255,255,255,0.15);
    }

    .chat-header {
        background: linear-gradient(90deg, rgba(0,0,0,0.8) 0%, rgba(20,20,30,0.9) 100%);
    }

    .chat-status.connected {
        box-shadow: 0 0 12px rgba(76,175,80,0.8);
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    .chat-container,
    .control-btn,
    .channel-tab,
    .chat-message,
    .send-btn {
        transition: none;
    }

    .chat-status {
        animation: none;
    }
}
</style> 