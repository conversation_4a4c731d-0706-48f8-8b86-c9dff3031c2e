
# EditorConfig defines and maintains consistent coding styles between different
# editors and IDEs: http://EditorConfig.org/
# Top-most EditorConfig file
root = true

# All files
[*.*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true
max_line_length = 80

[*.md]
indent_size = 2

[*.json]
indent_size = 2

[*.{yaml,yml}]
indent_size = 2

[vendor/grunt-template-jasmine-requirejs/**/*]
indent_size = 2
