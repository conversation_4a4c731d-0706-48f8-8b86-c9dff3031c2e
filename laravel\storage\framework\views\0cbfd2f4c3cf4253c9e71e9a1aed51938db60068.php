<?php $__env->startSection('title', '怪物管理'); ?>

<?php $__env->startSection('content'); ?>
<div class="layui-card">
    <div class="layui-card-header">
        怪物列表
        <a href="<?php echo e(route('admin.monsters.create')); ?>" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加怪物</a>
    </div>
    <div class="layui-card-body">
        <?php if(session('success')): ?>
        <div class="layui-alert layui-alert-success">
            <?php echo e(session('success')); ?>

        </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
        <div class="layui-alert layui-alert-danger">
            <?php echo e(session('error')); ?>

        </div>
        <?php endif; ?>

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>等级</th>
                    <th>生命值</th>
                    <th>攻击力</th>
                    <th>防御力</th>
                    <th>速度</th>
                    <th>经验奖励</th>
                    <th>银两奖励</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $monsters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $monster): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($monster->id); ?></td>
                    <td><?php echo e($monster->name); ?></td>
                    <td><?php echo e($monster->level); ?></td>
                    <td><?php echo e($monster->hp ?? 0); ?></td>
                    <td><?php echo e($monster->attack ?? 0); ?></td>
                    <td><?php echo e($monster->defense ?? 0); ?></td>
                    <td><?php echo e($monster->speed ?? 0); ?></td>
                    <td><?php echo e($monster->exp_reward ?? 0); ?></td>
                    <td><?php echo e($monster->silver_reward ?? 0); ?></td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="<?php echo e(route('admin.monsters.edit', $monster->id)); ?>" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteMonster(<?php echo e($monster->id); ?>, '<?php echo e($monster->name); ?>')">删除</button>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="10" class="layui-center">暂无怪物数据</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <?php echo e($monsters->links('admin.layouts.pagination')); ?>

    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    <?php echo csrf_field(); ?>
    <?php echo method_field('DELETE'); ?>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function deleteMonster(id, name) {
    layer.confirm('确定要删除怪物 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "<?php echo e(route('admin.monsters.destroy', '')); ?>/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\szxy\laravel\resources\views/admin/monsters/index.blade.php ENDPATH**/ ?>