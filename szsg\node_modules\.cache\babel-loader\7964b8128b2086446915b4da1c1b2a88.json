{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Battle.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Battle.vue", "mtime": 1749890584410}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GameLayout", "BattleAnimation", "battleService", "logger", "name", "components", "data", "battleId", "character", "id", "level", "avatar", "hp", "max_hp", "mp", "max_mp", "monster", "battleStatus", "canAct", "isProcessing", "battleLogs", "showLog", "showResult", "battleResult", "rewards", "animationManager", "computed", "characterHpPercent", "characterMpPercent", "monsterHpPercent", "monsterMpPercent", "battleFinished", "includes", "resultTitle", "resultClass", "mounted", "characterId", "monsterId", "locationId", "$route", "query", "debug", "error", "showToast", "$router", "push", "isAuthenticated", "$store", "state", "auth", "initBattle", "parseInt", "methods", "Error", "result", "startBattle", "battle_id", "battle", "updateBattleData", "errorMessage", "message", "response", "_error$response$data", "status", "request", "battleData", "can_act", "attack", "performAction", "success", "_result$data$action_r", "$refs", "battleAnimation", "playAttackAnimation", "action_results", "character_action", "charAction", "playDamageAnimation", "damage", "is_critical", "monster_action", "monsterAction", "setTimeout", "battle_end", "handleBattleEnd", "flee", "_result$data$action_r2", "flee_result", "endResult", "openItems", "toggleLog", "loadBattleLog", "getBattleLog", "battle_log", "formatLogMessage", "log", "action", "critical", "monsterDamage", "monsterCritical", "closeResult", "returnToMain", "$toast", "alert"], "sources": ["src/views/game/Battle.vue"], "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"battle-container\">\n      <!-- 怪物信息区域 -->\n      <div class=\"monster-section\">\n        <div class=\"monster-info\">\n          <img :src=\"monster.avatar || '/static/game/ui/default_monster.png'\" class=\"monster-avatar\" />\n          <div class=\"monster-details\">\n            <h3>{{ monster.name }} Lv.{{ monster.level }}</h3>\n            <div class=\"hp-bar\">\n              <div class=\"bar-fill hp-fill\" :style=\"{ width: monsterHpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ monster.hp }}/{{ monster.max_hp }}</span>\n            </div>\n            <div class=\"mp-bar\">\n              <div class=\"bar-fill mp-fill\" :style=\"{ width: monsterMpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ monster.mp }}/{{ monster.max_mp }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 战斗动画区域 -->\n      <div class=\"battle-animation-area\" ref=\"animationArea\">\n        <BattleAnimation ref=\"battleAnimation\" />\n      </div>\n\n      <!-- 角色信息区域 -->\n      <div class=\"character-section\">\n        <div class=\"character-info\">\n          <img :src=\"character.avatar || '/static/game/ui/default_avatar.png'\" class=\"character-avatar\" />\n          <div class=\"character-details\">\n            <h3>{{ character.name }} Lv.{{ character.level }}</h3>\n            <div class=\"hp-bar\">\n              <div class=\"bar-fill hp-fill\" :style=\"{ width: characterHpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ character.hp }}/{{ character.max_hp }}</span>\n            </div>\n            <div class=\"mp-bar\">\n              <div class=\"bar-fill mp-fill\" :style=\"{ width: characterMpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ character.mp }}/{{ character.max_mp }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 操作按钮区域 -->\n      <div class=\"action-buttons\">\n        <button \n          @click=\"attack\" \n          :disabled=\"!canAct\" \n          class=\"action-btn attack-btn\"\n        >\n          攻击\n        </button>\n        <button \n          @click=\"openItems\" \n          :disabled=\"!canAct\" \n          class=\"action-btn item-btn\"\n        >\n          物品\n        </button>\n        <button \n          @click=\"flee\" \n          :disabled=\"!canAct\" \n          class=\"action-btn flee-btn\"\n        >\n          逃跑\n        </button>\n        <button \n          @click=\"returnToMain\" \n          v-if=\"battleFinished\"\n          class=\"action-btn return-btn\"\n        >\n          返回\n        </button>\n      </div>\n\n      <!-- 战斗日志 -->\n      <div class=\"battle-log\" v-if=\"showLog\">\n        <div class=\"log-header\">\n          <h4>战斗记录</h4>\n          <button @click=\"toggleLog\" class=\"close-btn\">×</button>\n        </div>\n        <div class=\"log-content\">\n          <div \n            v-for=\"(log, index) in battleLogs\" \n            :key=\"index\"\n            class=\"log-entry\"\n          >\n            <span class=\"log-time\">第{{ log.round }}回合</span>\n            <span class=\"log-message\">{{ formatLogMessage(log) }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 战斗结果弹窗 -->\n      <div v-if=\"showResult\" class=\"battle-result-modal\">\n        <div class=\"result-content\">\n          <h3 :class=\"resultClass\">{{ resultTitle }}</h3>\n          <div v-if=\"battleResult === 'victory'\" class=\"rewards\">\n            <p>获得经验: {{ rewards.exp_gained }}</p>\n            <p>获得金币: {{ rewards.gold_gained }}</p>\n            <div v-if=\"rewards.items_gained && rewards.items_gained.length > 0\">\n              <p>获得物品:</p>\n              <ul>\n                <li v-for=\"item in rewards.items_gained\" :key=\"item.item_id\">\n                  {{ item.name }} × {{ item.quantity }}\n                </li>\n              </ul>\n            </div>\n          </div>\n          <button @click=\"closeResult\" class=\"confirm-btn\">确定</button>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport BattleAnimation from '@/components/game/BattleAnimation.vue'\nimport battleService from '@/api/services/battleService.js'\nimport logger from '@/utils/logger.js'\n\nexport default {\n  name: 'Battle',\n  components: {\n    GameLayout,\n    BattleAnimation\n  },\n  \n  data() {\n    return {\n      battleId: null,\n      character: {\n        id: null,\n        name: '',\n        level: 1,\n        avatar: '',\n        hp: 100,\n        max_hp: 100,\n        mp: 50,\n        max_mp: 50\n      },\n      monster: {\n        id: null,\n        name: '',\n        level: 1,\n        avatar: '',\n        hp: 100,\n        max_hp: 100,\n        mp: 50,\n        max_mp: 50\n      },\n      battleStatus: 'ongoing', // ongoing, victory, defeat, fled\n      canAct: true,\n      isProcessing: false,\n      battleLogs: [],\n      showLog: false,\n      showResult: false,\n      battleResult: null,\n      rewards: {},\n      animationManager: null\n    }\n  },\n\n  computed: {\n    characterHpPercent() {\n      return this.character.max_hp > 0 ? (this.character.hp / this.character.max_hp) * 100 : 0\n    },\n    \n    characterMpPercent() {\n      return this.character.max_mp > 0 ? (this.character.mp / this.character.max_mp) * 100 : 0\n    },\n    \n    monsterHpPercent() {\n      return this.monster.max_hp > 0 ? (this.monster.hp / this.monster.max_hp) * 100 : 0\n    },\n    \n    monsterMpPercent() {\n      return this.monster.max_mp > 0 ? (this.monster.mp / this.monster.max_mp) * 100 : 0\n    },\n    \n    battleFinished() {\n      return ['victory', 'defeat', 'fled'].includes(this.battleStatus)\n    },\n    \n    resultTitle() {\n      switch (this.battleResult) {\n        case 'victory': return '战斗胜利！'\n        case 'defeat': return '战斗失败！'\n        case 'fled': return '成功逃脱！'\n        default: return ''\n      }\n    },\n    \n    resultClass() {\n      switch (this.battleResult) {\n        case 'victory': return 'victory-text'\n        case 'defeat': return 'defeat-text'\n        case 'fled': return 'flee-text'\n        default: return ''\n      }\n    }\n  },\n\n  async mounted() {\n    try {\n      // 从路由参数获取战斗信息\n      const { characterId, monsterId, locationId } = this.$route.query\n      logger.debug('[Battle] 路由参数:', { characterId, monsterId, locationId })\n\n      if (!characterId || !monsterId) {\n        logger.error('[Battle] 缺少战斗参数')\n        this.showToast('缺少战斗参数')\n        this.$router.push('/game/main')\n        return\n      }\n\n      // 验证用户认证状态\n      const isAuthenticated = this.$store.state.auth.isAuthenticated\n      logger.debug('[Battle] 用户认证状态:', isAuthenticated)\n\n      if (!isAuthenticated) {\n        logger.error('[Battle] 用户未认证')\n        this.showToast('请先登录')\n        this.$router.push('/login')\n        return\n      }\n\n      // 开始战斗\n      await this.initBattle(parseInt(characterId), parseInt(monsterId), locationId)\n\n    } catch (error) {\n      logger.error('[Battle] 初始化战斗失败:', error)\n      this.showToast('初始化战斗失败')\n      this.$router.push('/game/main')\n    }\n  },\n\n  methods: {\n    // 初始化战斗\n    async initBattle(characterId, monsterId, locationId) {\n      try {\n        logger.debug('[Battle] 开始初始化战斗:', { characterId, monsterId, locationId })\n\n        // 验证参数\n        if (!characterId || !monsterId) {\n          throw new Error('缺少必要的战斗参数')\n        }\n\n        const result = await battleService.startBattle(characterId, monsterId, locationId)\n        logger.debug('[Battle] 战斗API响应:', result)\n\n        // 检查API响应结构 - battleService返回的是res.data，所以直接检查battle_id\n        if (result && result.battle_id && result.battle) {\n          this.battleId = result.battle_id\n          this.updateBattleData(result.battle)\n          this.showToast('战斗开始！')\n        } else {\n          // 显示更详细的错误信息\n          const errorMessage = result?.message || result?.error || '开始战斗失败'\n          logger.error('[Battle] 战斗失败详情:', result)\n          throw new Error(errorMessage)\n        }\n      } catch (error) {\n        logger.error('[Battle] 开始战斗失败:', error)\n        // 如果是网络错误，显示更友好的错误信息\n        if (error.response) {\n          logger.error('[Battle] HTTP错误:', error.response.status, error.response.data)\n          throw new Error(`服务器错误 (${error.response.status}): ${error.response.data?.message || '未知错误'}`)\n        } else if (error.request) {\n          logger.error('[Battle] 网络错误:', error.request)\n          throw new Error('网络连接失败，请检查网络连接')\n        } else {\n          throw error\n        }\n      }\n    },\n\n    // 更新战斗数据\n    updateBattleData(battleData) {\n      this.character = { ...battleData.character }\n      this.monster = { ...battleData.monster }\n      this.battleStatus = battleData.status\n      this.canAct = battleData.can_act\n      this.rewards = battleData.rewards || {}\n    },\n\n    // 攻击\n    async attack() {\n      if (!this.canAct || this.isProcessing) return\n      \n      try {\n        this.isProcessing = true\n        this.canAct = false\n        \n        const result = await battleService.performAction(this.battleId, this.character.id, 'attack')\n        \n        if (result.success) {\n          // 播放攻击动画\n          if (this.$refs.battleAnimation) {\n            this.$refs.battleAnimation.playAttackAnimation()\n            \n            // 处理角色攻击结果\n            if (result.data.action_results.character_action) {\n              const charAction = result.data.action_results.character_action\n              this.$refs.battleAnimation.playDamageAnimation(charAction.damage, charAction.is_critical)\n            }\n            \n            // 处理怪物反击\n            if (result.data.action_results.monster_action) {\n              const monsterAction = result.data.action_results.monster_action\n              setTimeout(() => {\n                this.$refs.battleAnimation.playDamageAnimation(monsterAction.damage, monsterAction.is_critical)\n              }, 1500)\n            }\n          }\n          \n          // 更新战斗数据\n          this.updateBattleData(result.data.battle)\n          \n          // 检查战斗是否结束\n          if (result.data.action_results.character_action?.battle_end) {\n            this.handleBattleEnd(result.data.action_results.character_action.battle_end)\n          }\n          \n        } else {\n          this.showToast(result.message || '攻击失败')\n        }\n      } catch (error) {\n        logger.error('[Battle] 攻击失败:', error)\n        this.showToast('攻击失败')\n      } finally {\n        this.isProcessing = false\n        if (this.battleStatus === 'ongoing') {\n          this.canAct = true\n        }\n      }\n    },\n\n    // 逃跑\n    async flee() {\n      if (!this.canAct || this.isProcessing) return\n      \n      try {\n        this.isProcessing = true\n        this.canAct = false\n        \n        const result = await battleService.performAction(this.battleId, this.character.id, 'flee')\n        \n        if (result.success) {\n          if (result.data.action_results.flee_result?.success) {\n            this.battleResult = 'fled'\n            this.battleStatus = 'fled'\n            this.showResult = true\n          } else {\n            this.showToast('逃跑失败！')\n            this.canAct = true\n          }\n        } else {\n          this.showToast(result.message || '逃跑失败')\n          this.canAct = true\n        }\n      } catch (error) {\n        logger.error('[Battle] 逃跑失败:', error)\n        this.showToast('逃跑失败')\n        this.canAct = true\n      } finally {\n        this.isProcessing = false\n      }\n    },\n\n    // 处理战斗结束\n    handleBattleEnd(endResult) {\n      this.battleResult = endResult.result\n      this.battleStatus = endResult.result\n      this.canAct = false\n      \n      if (endResult.rewards) {\n        this.rewards = endResult.rewards\n      }\n      \n      // 延迟显示结果，让动画播放完\n      setTimeout(() => {\n        this.showResult = true\n      }, 2000)\n    },\n\n    // 打开物品界面\n    openItems() {\n      // TODO: 实现物品使用界面\n      this.showToast('物品功能开发中...')\n    },\n\n    // 切换日志显示\n    toggleLog() {\n      this.showLog = !this.showLog\n      if (this.showLog) {\n        this.loadBattleLog()\n      }\n    },\n\n    // 加载战斗日志\n    async loadBattleLog() {\n      try {\n        const result = await battleService.getBattleLog(this.battleId)\n        if (result.success) {\n          this.battleLogs = result.data.battle_log || []\n        }\n      } catch (error) {\n        logger.error('[Battle] 加载战斗日志失败:', error)\n      }\n    },\n\n    // 格式化日志消息\n    formatLogMessage(log) {\n      switch (log.action) {\n        case 'battle_start':\n          return '战斗开始！'\n        case 'character_attack': {\n          const damage = log.data.damage || 0\n          const critical = log.data.is_critical ? '(暴击)' : ''\n          return `你对${this.monster.name}造成了${damage}点伤害${critical}`\n        }\n        case 'monster_attack': {\n          const monsterDamage = log.data.damage || 0\n          const monsterCritical = log.data.is_critical ? '(暴击)' : ''\n          return `${this.monster.name}对你造成了${monsterDamage}点伤害${monsterCritical}`\n        }\n        case 'battle_end':\n          return `战斗结束，结果：${log.data.result}`\n        default:\n          return log.action\n      }\n    },\n\n    // 关闭结果弹窗\n    closeResult() {\n      this.showResult = false\n      this.returnToMain()\n    },\n\n    // 返回主界面\n    returnToMain() {\n      this.$router.push('/game/main')\n    },\n\n    // 显示提示\n    showToast(message) {\n      // 使用全局提示组件\n      if (this.$toast) {\n        this.$toast(message)\n      } else {\n        alert(message)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.battle-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);\n  color: #fff;\n  font-family: 'Microsoft YaHei', sans-serif;\n}\n\n/* 怪物信息区域 */\n.monster-section {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(139, 69, 19, 0.3);\n  border-bottom: 2px solid #8B4513;\n}\n\n.monster-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 15px;\n  border-radius: 10px;\n  border: 2px solid #666;\n}\n\n.monster-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid #ff6b6b;\n  object-fit: cover;\n}\n\n.monster-details h3 {\n  margin: 0 0 10px 0;\n  color: #ff6b6b;\n  font-size: 18px;\n}\n\n/* 战斗动画区域 */\n.battle-animation-area {\n  flex: 2;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* 角色信息区域 */\n.character-section {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(0, 100, 0, 0.2);\n  border-top: 2px solid #228B22;\n}\n\n.character-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 15px;\n  border-radius: 10px;\n  border: 2px solid #666;\n}\n\n.character-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid #87ceeb;\n  object-fit: cover;\n}\n\n.character-details h3 {\n  margin: 0 0 10px 0;\n  color: #87ceeb;\n  font-size: 18px;\n}\n\n/* 血条和蓝条样式 */\n.hp-bar, .mp-bar {\n  position: relative;\n  width: 200px;\n  height: 20px;\n  background: #333;\n  border: 1px solid #666;\n  border-radius: 10px;\n  margin: 5px 0;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  transition: width 0.5s ease;\n  border-radius: 9px;\n}\n\n.hp-fill {\n  background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%);\n}\n\n.mp-fill {\n  background: linear-gradient(90deg, #4444ff 0%, #6666ff 100%);\n}\n\n.bar-text {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  border-top: 2px solid #666;\n}\n\n.action-btn {\n  padding: 12px 24px;\n  font-size: 16px;\n  font-weight: bold;\n  border: 2px solid;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  min-width: 80px;\n}\n\n.attack-btn {\n  border-color: #ff4444;\n  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);\n}\n\n.attack-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ff6666 0%, #ff4444 100%);\n  transform: translateY(-2px);\n}\n\n.item-btn {\n  border-color: #44ff44;\n  background: linear-gradient(135deg, #44ff44 0%, #33cc33 100%);\n}\n\n.item-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #66ff66 0%, #44ff44 100%);\n  transform: translateY(-2px);\n}\n\n.flee-btn {\n  border-color: #ffff44;\n  background: linear-gradient(135deg, #ffff44 0%, #cccc33 100%);\n  color: #333;\n}\n\n.flee-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ffff66 0%, #ffff44 100%);\n  transform: translateY(-2px);\n}\n\n.return-btn {\n  border-color: #888;\n  background: linear-gradient(135deg, #888 0%, #666 100%);\n}\n\n.return-btn:hover {\n  background: linear-gradient(135deg, #aaa 0%, #888 100%);\n  transform: translateY(-2px);\n}\n\n.action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 战斗日志 */\n.battle-log {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 400px;\n  max-height: 300px;\n  background: rgba(0, 0, 0, 0.9);\n  border: 2px solid #666;\n  border-radius: 10px;\n  z-index: 1000;\n}\n\n.log-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border-bottom: 1px solid #666;\n}\n\n.log-header h4 {\n  margin: 0;\n  color: #fff;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 20px;\n  cursor: pointer;\n  padding: 0;\n  width: 25px;\n  height: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.log-content {\n  max-height: 200px;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.log-entry {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.log-time {\n  color: #888;\n  min-width: 60px;\n}\n\n.log-message {\n  color: #fff;\n}\n\n/* 战斗结果弹窗 */\n.battle-result-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 2000;\n}\n\n.result-content {\n  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);\n  border: 3px solid #666;\n  border-radius: 15px;\n  padding: 30px;\n  text-align: center;\n  min-width: 300px;\n}\n\n.result-content h3 {\n  margin: 0 0 20px 0;\n  font-size: 24px;\n}\n\n.victory-text {\n  color: #44ff44;\n  text-shadow: 0 0 10px #44ff44;\n}\n\n.defeat-text {\n  color: #ff4444;\n  text-shadow: 0 0 10px #ff4444;\n}\n\n.flee-text {\n  color: #ffff44;\n  text-shadow: 0 0 10px #ffff44;\n}\n\n.rewards {\n  margin: 20px 0;\n  text-align: left;\n}\n\n.rewards p {\n  margin: 8px 0;\n  color: #fff;\n}\n\n.rewards ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rewards li {\n  color: #87ceeb;\n  margin: 5px 0;\n}\n\n.confirm-btn {\n  padding: 10px 30px;\n  font-size: 16px;\n  font-weight: bold;\n  background: linear-gradient(135deg, #4444ff 0%, #3333cc 100%);\n  color: #fff;\n  border: 2px solid #4444ff;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.confirm-btn:hover {\n  background: linear-gradient(135deg, #6666ff 0%, #4444ff 100%);\n  transform: translateY(-2px);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .monster-info, .character-info {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .monster-avatar, .character-avatar {\n    width: 60px;\n    height: 60px;\n  }\n\n  .hp-bar, .mp-bar {\n    width: 150px;\n  }\n\n  .action-buttons {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .action-btn {\n    padding: 10px 20px;\n    font-size: 14px;\n    min-width: 70px;\n  }\n\n  .battle-log {\n    width: 90%;\n    max-width: 350px;\n  }\n}\n</style>\n"], "mappings": ";;AAsHA,OAAAA,UAAA;AACA,OAAAC,eAAA;AACA,OAAAC,aAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,UAAA;IACAC;EACA;EAEAK,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;QACAC,EAAA;QACAL,IAAA;QACAM,KAAA;QACAC,MAAA;QACAC,EAAA;QACAC,MAAA;QACAC,EAAA;QACAC,MAAA;MACA;MACAC,OAAA;QACAP,EAAA;QACAL,IAAA;QACAM,KAAA;QACAC,MAAA;QACAC,EAAA;QACAC,MAAA;QACAC,EAAA;QACAC,MAAA;MACA;MACAE,YAAA;MAAA;MACAC,MAAA;MACAC,YAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,YAAA;MACAC,OAAA;MACAC,gBAAA;IACA;EACA;EAEAC,QAAA;IACAC,mBAAA;MACA,YAAAnB,SAAA,CAAAK,MAAA,YAAAL,SAAA,CAAAI,EAAA,QAAAJ,SAAA,CAAAK,MAAA;IACA;IAEAe,mBAAA;MACA,YAAApB,SAAA,CAAAO,MAAA,YAAAP,SAAA,CAAAM,EAAA,QAAAN,SAAA,CAAAO,MAAA;IACA;IAEAc,iBAAA;MACA,YAAAb,OAAA,CAAAH,MAAA,YAAAG,OAAA,CAAAJ,EAAA,QAAAI,OAAA,CAAAH,MAAA;IACA;IAEAiB,iBAAA;MACA,YAAAd,OAAA,CAAAD,MAAA,YAAAC,OAAA,CAAAF,EAAA,QAAAE,OAAA,CAAAD,MAAA;IACA;IAEAgB,eAAA;MACA,qCAAAC,QAAA,MAAAf,YAAA;IACA;IAEAgB,YAAA;MACA,aAAAV,YAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEAW,YAAA;MACA,aAAAX,YAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;EACA;EAEA,MAAAY,QAAA;IACA;MACA;MACA;QAAAC,WAAA;QAAAC,SAAA;QAAAC;MAAA,SAAAC,MAAA,CAAAC,KAAA;MACArC,MAAA,CAAAsC,KAAA;QAAAL,WAAA;QAAAC,SAAA;QAAAC;MAAA;MAEA,KAAAF,WAAA,KAAAC,SAAA;QACAlC,MAAA,CAAAuC,KAAA;QACA,KAAAC,SAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,MAAAC,eAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAH,eAAA;MACA3C,MAAA,CAAAsC,KAAA,qBAAAK,eAAA;MAEA,KAAAA,eAAA;QACA3C,MAAA,CAAAuC,KAAA;QACA,KAAAC,SAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,WAAAK,UAAA,CAAAC,QAAA,CAAAf,WAAA,GAAAe,QAAA,CAAAd,SAAA,GAAAC,UAAA;IAEA,SAAAI,KAAA;MACAvC,MAAA,CAAAuC,KAAA,sBAAAA,KAAA;MACA,KAAAC,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;EAEAO,OAAA;IACA;IACA,MAAAF,WAAAd,WAAA,EAAAC,SAAA,EAAAC,UAAA;MACA;QACAnC,MAAA,CAAAsC,KAAA;UAAAL,WAAA;UAAAC,SAAA;UAAAC;QAAA;;QAEA;QACA,KAAAF,WAAA,KAAAC,SAAA;UACA,UAAAgB,KAAA;QACA;QAEA,MAAAC,MAAA,SAAApD,aAAA,CAAAqD,WAAA,CAAAnB,WAAA,EAAAC,SAAA,EAAAC,UAAA;QACAnC,MAAA,CAAAsC,KAAA,sBAAAa,MAAA;;QAEA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAE,SAAA,IAAAF,MAAA,CAAAG,MAAA;UACA,KAAAlD,QAAA,GAAA+C,MAAA,CAAAE,SAAA;UACA,KAAAE,gBAAA,CAAAJ,MAAA,CAAAG,MAAA;UACA,KAAAd,SAAA;QACA;UACA;UACA,MAAAgB,YAAA,IAAAL,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAM,OAAA,MAAAN,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAZ,KAAA;UACAvC,MAAA,CAAAuC,KAAA,qBAAAY,MAAA;UACA,UAAAD,KAAA,CAAAM,YAAA;QACA;MACA,SAAAjB,KAAA;QACAvC,MAAA,CAAAuC,KAAA,qBAAAA,KAAA;QACA;QACA,IAAAA,KAAA,CAAAmB,QAAA;UAAA,IAAAC,oBAAA;UACA3D,MAAA,CAAAuC,KAAA,qBAAAA,KAAA,CAAAmB,QAAA,CAAAE,MAAA,EAAArB,KAAA,CAAAmB,QAAA,CAAAvD,IAAA;UACA,UAAA+C,KAAA,WAAAX,KAAA,CAAAmB,QAAA,CAAAE,MAAA,QAAAD,oBAAA,GAAApB,KAAA,CAAAmB,QAAA,CAAAvD,IAAA,cAAAwD,oBAAA,uBAAAA,oBAAA,CAAAF,OAAA;QACA,WAAAlB,KAAA,CAAAsB,OAAA;UACA7D,MAAA,CAAAuC,KAAA,mBAAAA,KAAA,CAAAsB,OAAA;UACA,UAAAX,KAAA;QACA;UACA,MAAAX,KAAA;QACA;MACA;IACA;IAEA;IACAgB,iBAAAO,UAAA;MACA,KAAAzD,SAAA;QAAA,GAAAyD,UAAA,CAAAzD;MAAA;MACA,KAAAQ,OAAA;QAAA,GAAAiD,UAAA,CAAAjD;MAAA;MACA,KAAAC,YAAA,GAAAgD,UAAA,CAAAF,MAAA;MACA,KAAA7C,MAAA,GAAA+C,UAAA,CAAAC,OAAA;MACA,KAAA1C,OAAA,GAAAyC,UAAA,CAAAzC,OAAA;IACA;IAEA;IACA,MAAA2C,OAAA;MACA,UAAAjD,MAAA,SAAAC,YAAA;MAEA;QACA,KAAAA,YAAA;QACA,KAAAD,MAAA;QAEA,MAAAoC,MAAA,SAAApD,aAAA,CAAAkE,aAAA,MAAA7D,QAAA,OAAAC,SAAA,CAAAC,EAAA;QAEA,IAAA6C,MAAA,CAAAe,OAAA;UAAA,IAAAC,qBAAA;UACA;UACA,SAAAC,KAAA,CAAAC,eAAA;YACA,KAAAD,KAAA,CAAAC,eAAA,CAAAC,mBAAA;;YAEA;YACA,IAAAnB,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAC,gBAAA;cACA,MAAAC,UAAA,GAAAtB,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAC,gBAAA;cACA,KAAAJ,KAAA,CAAAC,eAAA,CAAAK,mBAAA,CAAAD,UAAA,CAAAE,MAAA,EAAAF,UAAA,CAAAG,WAAA;YACA;;YAEA;YACA,IAAAzB,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAM,cAAA;cACA,MAAAC,aAAA,GAAA3B,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAM,cAAA;cACAE,UAAA;gBACA,KAAAX,KAAA,CAAAC,eAAA,CAAAK,mBAAA,CAAAI,aAAA,CAAAH,MAAA,EAAAG,aAAA,CAAAF,WAAA;cACA;YACA;UACA;;UAEA;UACA,KAAArB,gBAAA,CAAAJ,MAAA,CAAAhD,IAAA,CAAAmD,MAAA;;UAEA;UACA,KAAAa,qBAAA,GAAAhB,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAC,gBAAA,cAAAL,qBAAA,eAAAA,qBAAA,CAAAa,UAAA;YACA,KAAAC,eAAA,CAAA9B,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAC,gBAAA,CAAAQ,UAAA;UACA;QAEA;UACA,KAAAxC,SAAA,CAAAW,MAAA,CAAAM,OAAA;QACA;MACA,SAAAlB,KAAA;QACAvC,MAAA,CAAAuC,KAAA,mBAAAA,KAAA;QACA,KAAAC,SAAA;MACA;QACA,KAAAxB,YAAA;QACA,SAAAF,YAAA;UACA,KAAAC,MAAA;QACA;MACA;IACA;IAEA;IACA,MAAAmE,KAAA;MACA,UAAAnE,MAAA,SAAAC,YAAA;MAEA;QACA,KAAAA,YAAA;QACA,KAAAD,MAAA;QAEA,MAAAoC,MAAA,SAAApD,aAAA,CAAAkE,aAAA,MAAA7D,QAAA,OAAAC,SAAA,CAAAC,EAAA;QAEA,IAAA6C,MAAA,CAAAe,OAAA;UAAA,IAAAiB,sBAAA;UACA,KAAAA,sBAAA,GAAAhC,MAAA,CAAAhD,IAAA,CAAAoE,cAAA,CAAAa,WAAA,cAAAD,sBAAA,eAAAA,sBAAA,CAAAjB,OAAA;YACA,KAAA9C,YAAA;YACA,KAAAN,YAAA;YACA,KAAAK,UAAA;UACA;YACA,KAAAqB,SAAA;YACA,KAAAzB,MAAA;UACA;QACA;UACA,KAAAyB,SAAA,CAAAW,MAAA,CAAAM,OAAA;UACA,KAAA1C,MAAA;QACA;MACA,SAAAwB,KAAA;QACAvC,MAAA,CAAAuC,KAAA,mBAAAA,KAAA;QACA,KAAAC,SAAA;QACA,KAAAzB,MAAA;MACA;QACA,KAAAC,YAAA;MACA;IACA;IAEA;IACAiE,gBAAAI,SAAA;MACA,KAAAjE,YAAA,GAAAiE,SAAA,CAAAlC,MAAA;MACA,KAAArC,YAAA,GAAAuE,SAAA,CAAAlC,MAAA;MACA,KAAApC,MAAA;MAEA,IAAAsE,SAAA,CAAAhE,OAAA;QACA,KAAAA,OAAA,GAAAgE,SAAA,CAAAhE,OAAA;MACA;;MAEA;MACA0D,UAAA;QACA,KAAA5D,UAAA;MACA;IACA;IAEA;IACAmE,UAAA;MACA;MACA,KAAA9C,SAAA;IACA;IAEA;IACA+C,UAAA;MACA,KAAArE,OAAA,SAAAA,OAAA;MACA,SAAAA,OAAA;QACA,KAAAsE,aAAA;MACA;IACA;IAEA;IACA,MAAAA,cAAA;MACA;QACA,MAAArC,MAAA,SAAApD,aAAA,CAAA0F,YAAA,MAAArF,QAAA;QACA,IAAA+C,MAAA,CAAAe,OAAA;UACA,KAAAjD,UAAA,GAAAkC,MAAA,CAAAhD,IAAA,CAAAuF,UAAA;QACA;MACA,SAAAnD,KAAA;QACAvC,MAAA,CAAAuC,KAAA,uBAAAA,KAAA;MACA;IACA;IAEA;IACAoD,iBAAAC,GAAA;MACA,QAAAA,GAAA,CAAAC,MAAA;QACA;UACA;QACA;UAAA;YACA,MAAAlB,MAAA,GAAAiB,GAAA,CAAAzF,IAAA,CAAAwE,MAAA;YACA,MAAAmB,QAAA,GAAAF,GAAA,CAAAzF,IAAA,CAAAyE,WAAA;YACA,iBAAA/D,OAAA,CAAAZ,IAAA,MAAA0E,MAAA,MAAAmB,QAAA;UACA;QACA;UAAA;YACA,MAAAC,aAAA,GAAAH,GAAA,CAAAzF,IAAA,CAAAwE,MAAA;YACA,MAAAqB,eAAA,GAAAJ,GAAA,CAAAzF,IAAA,CAAAyE,WAAA;YACA,eAAA/D,OAAA,CAAAZ,IAAA,QAAA8F,aAAA,MAAAC,eAAA;UACA;QACA;UACA,kBAAAJ,GAAA,CAAAzF,IAAA,CAAAgD,MAAA;QACA;UACA,OAAAyC,GAAA,CAAAC,MAAA;MACA;IACA;IAEA;IACAI,YAAA;MACA,KAAA9E,UAAA;MACA,KAAA+E,YAAA;IACA;IAEA;IACAA,aAAA;MACA,KAAAzD,OAAA,CAAAC,IAAA;IACA;IAEA;IACAF,UAAAiB,OAAA;MACA;MACA,SAAA0C,MAAA;QACA,KAAAA,MAAA,CAAA1C,OAAA;MACA;QACA2C,KAAA,CAAA3C,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}