{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue?vue&type=template&id=1bd62dc5&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue", "mtime": 1749724641760}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "click", "goBack", "attrs", "src", "alt", "loading", "_v", "<PERSON><PERSON><PERSON><PERSON>", "_s", "errorMessage", "retryLoading", "class", "active", "currentMainTab", "$event", "vipIcon", "vipLevel", "vipExp", "vipNextExp", "style", "width", "vipPercent", "_l", "n", "key", "left", "canClaimReward", "disabled", "isClaiming", "claimVipReward", "_e", "vipRewardHistory", "length", "reward", "idx", "getVipIcon", "level", "date", "desc", "claimHistoryReward", "currentRewardTab", "canClaimDaily", "claimDailyReward", "Math", "max", "floor", "canClaimWeekly", "claimWeeklyReward", "canClaimMonthly", "claimMonthlyReward", "vipPrivileges", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/views/game/subpages/Vip.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"GameLayout\", [\n    _c(\"div\", { staticClass: \"vip-page\" }, [\n      _c(\"div\", { staticClass: \"back-button-container\" }, [\n        _c(\"div\", { staticClass: \"back-button\", on: { click: _vm.goBack } }, [\n          _c(\"img\", {\n            attrs: { src: \"/static/game/UI/anniu/fhui_.png\", alt: \"返回\" },\n          }),\n        ]),\n      ]),\n      _vm.loading\n        ? _c(\"div\", { staticClass: \"loading-container\" }, [\n            _c(\"div\", { staticClass: \"loading-spinner\" }),\n            _c(\"div\", { staticClass: \"loading-text\" }, [_vm._v(\"加载中...\")]),\n          ])\n        : _vm.hasError\n        ? _c(\"div\", { staticClass: \"error-container\" }, [\n            _c(\"div\", { staticClass: \"error-icon\" }, [_vm._v(\"!\")]),\n            _c(\"div\", { staticClass: \"error-text\" }, [\n              _vm._v(_vm._s(_vm.errorMessage)),\n            ]),\n            _c(\n              \"button\",\n              {\n                staticClass: \"pixel-button gold\",\n                on: { click: _vm.retryLoading },\n              },\n              [_vm._v(\"重新加载\")]\n            ),\n          ])\n        : _c(\"div\", [\n            _c(\"div\", { staticClass: \"main-tabs\" }, [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"main-tab\",\n                  class: { active: _vm.currentMainTab === \"vip\" },\n                  on: {\n                    click: function ($event) {\n                      _vm.currentMainTab = \"vip\"\n                    },\n                  },\n                },\n                [_c(\"span\", [_vm._v(\"VIP\")])]\n              ),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"main-tab\",\n                  class: { active: _vm.currentMainTab === \"benefits\" },\n                  on: {\n                    click: function ($event) {\n                      _vm.currentMainTab = \"benefits\"\n                    },\n                  },\n                },\n                [_c(\"span\", [_vm._v(\"VIP福利\")])]\n              ),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"main-tab\",\n                  class: { active: _vm.currentMainTab === \"privileges\" },\n                  on: {\n                    click: function ($event) {\n                      _vm.currentMainTab = \"privileges\"\n                    },\n                  },\n                },\n                [_c(\"span\", [_vm._v(\"VIP特权\")])]\n              ),\n            ]),\n            _vm.currentMainTab === \"vip\"\n              ? _c(\"div\", { staticClass: \"tab-content\" }, [\n                  _c(\"div\", { staticClass: \"vip-info pixel-border-box\" }, [\n                    _c(\"div\", { staticClass: \"vip-info-header\" }, [\n                      _c(\"img\", {\n                        staticClass: \"vip-icon\",\n                        attrs: { src: _vm.vipIcon, alt: \"VIP\" + _vm.vipLevel },\n                      }),\n                    ]),\n                    _c(\"div\", { staticClass: \"vip-exp-section\" }, [\n                      _c(\"div\", { staticClass: \"vip-exp\" }, [\n                        _vm._v(\n                          \" 当前经验：\" +\n                            _vm._s(_vm.vipExp) +\n                            \" / \" +\n                            _vm._s(_vm.vipNextExp) +\n                            \" \"\n                        ),\n                        _vm.vipLevel < 20\n                          ? _c(\"span\", { staticClass: \"vip-next-exp\" }, [\n                              _vm._v(\" ，还需 \"),\n                              _c(\"span\", { staticClass: \"highlight-text\" }, [\n                                _vm._v(_vm._s(_vm.vipNextExp - _vm.vipExp)),\n                              ]),\n                              _vm._v(\n                                \" 经验升级到 VIP\" +\n                                  _vm._s(_vm.vipLevel + 1) +\n                                  \" \"\n                              ),\n                            ])\n                          : _c(\"span\", { staticClass: \"vip-next-exp\" }, [\n                              _vm._v(\"（已满级）\"),\n                            ]),\n                      ]),\n                      _c(\"div\", { staticClass: \"vip-progress-container\" }, [\n                        _c(\"div\", { staticClass: \"vip-progress-bar\" }, [\n                          _c(\"div\", {\n                            staticClass: \"vip-progress\",\n                            style: { width: _vm.vipPercent + \"%\" },\n                          }),\n                          _c(\n                            \"div\",\n                            { staticClass: \"progress-stars\" },\n                            _vm._l(5, function (n) {\n                              return _c(\n                                \"span\",\n                                {\n                                  key: n,\n                                  staticClass: \"progress-star\",\n                                  style: { left: n * 20 - 10 + \"%\" },\n                                },\n                                [_vm._v(\"✦\")]\n                              )\n                            }),\n                            0\n                          ),\n                        ]),\n                        _c(\"div\", { staticClass: \"vip-progress-text\" }, [\n                          _vm._v(_vm._s(_vm.vipPercent) + \"%\"),\n                        ]),\n                      ]),\n                    ]),\n                    _vm.canClaimReward\n                      ? _c(\n                          \"button\",\n                          {\n                            staticClass: \"pixel-button gold shine-effect\",\n                            attrs: { disabled: _vm.isClaiming },\n                            on: { click: _vm.claimVipReward },\n                          },\n                          [\n                            _vm.isClaiming\n                              ? _c(\"span\", [_vm._v(\"领取中...\")])\n                              : _c(\"span\", [\n                                  _vm._v(\n                                    \"领取VIP\" + _vm._s(_vm.vipLevel) + \"奖励\"\n                                  ),\n                                ]),\n                          ]\n                        )\n                      : _vm._e(),\n                  ]),\n                  _vm.vipRewardHistory.length\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"vip-reward-history pixel-border-box\" },\n                        [\n                          _c(\"div\", { staticClass: \"section-header\" }, [\n                            _c(\"div\", { staticClass: \"vip-title-with-icon\" }, [\n                              _c(\"img\", {\n                                staticClass: \"vip-small-icon\",\n                                attrs: { src: _vm.vipIcon, alt: \"VIP图标\" },\n                              }),\n                              _c(\"h3\", [_vm._v(\"VIP成长奖励历史\")]),\n                            ]),\n                            _c(\"div\", { staticClass: \"section-decor\" }),\n                          ]),\n                          _c(\n                            \"div\",\n                            { staticClass: \"history-list\" },\n                            _vm._l(\n                              _vm.vipRewardHistory,\n                              function (reward, idx) {\n                                return _c(\n                                  \"div\",\n                                  { key: idx, staticClass: \"history-item\" },\n                                  [\n                                    _c(\"div\", { staticClass: \"history-info\" }, [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"history-vip-badge\" },\n                                        [\n                                          _c(\"img\", {\n                                            staticClass: \"history-vip-icon\",\n                                            attrs: {\n                                              src: _vm.getVipIcon(reward.level),\n                                              alt: \"VIP\" + reward.level,\n                                            },\n                                          }),\n                                        ]\n                                      ),\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"history-date\" },\n                                        [\n                                          _vm._v(\n                                            _vm._s(reward.date || \"未领取\")\n                                          ),\n                                        ]\n                                      ),\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"history-desc\" },\n                                        [_vm._v(_vm._s(reward.desc))]\n                                      ),\n                                    ]),\n                                    !reward.date\n                                      ? _c(\n                                          \"button\",\n                                          {\n                                            staticClass:\n                                              \"pixel-button small gold shine-effect\",\n                                            attrs: { disabled: _vm.isClaiming },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.claimHistoryReward(\n                                                  reward.level\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [_vm._v(\" 补领 \")]\n                                        )\n                                      : _vm._e(),\n                                  ]\n                                )\n                              }\n                            ),\n                            0\n                          ),\n                        ]\n                      )\n                    : _vm._e(),\n                ])\n              : _vm.currentMainTab === \"benefits\"\n              ? _c(\"div\", { staticClass: \"tab-content\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"vip-daily-reward pixel-border-box\" },\n                    [\n                      _c(\"div\", { staticClass: \"section-header\" }, [\n                        _c(\"div\", { staticClass: \"vip-title-with-icon\" }, [\n                          _c(\"img\", {\n                            staticClass: \"vip-small-icon\",\n                            attrs: { src: _vm.vipIcon, alt: \"VIP图标\" },\n                          }),\n                          _c(\"h3\", [_vm._v(\"VIP福利\")]),\n                        ]),\n                        _c(\"div\", { staticClass: \"section-decor\" }),\n                      ]),\n                      _c(\"div\", { staticClass: \"reward-tabs\" }, [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"reward-tab\",\n                            class: { active: _vm.currentRewardTab === \"daily\" },\n                            on: {\n                              click: function ($event) {\n                                _vm.currentRewardTab = \"daily\"\n                              },\n                            },\n                          },\n                          [_vm._v(\" 每日福利 \")]\n                        ),\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"reward-tab\",\n                            class: {\n                              active: _vm.currentRewardTab === \"weekly\",\n                            },\n                            on: {\n                              click: function ($event) {\n                                _vm.currentRewardTab = \"weekly\"\n                              },\n                            },\n                          },\n                          [_vm._v(\" 每周福利 \")]\n                        ),\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"reward-tab\",\n                            class: {\n                              active: _vm.currentRewardTab === \"monthly\",\n                            },\n                            on: {\n                              click: function ($event) {\n                                _vm.currentRewardTab = \"monthly\"\n                              },\n                            },\n                          },\n                          [_vm._v(\" 每月福利 \")]\n                        ),\n                      ]),\n                      _vm.currentRewardTab === \"daily\"\n                        ? _c(\"div\", { staticClass: \"daily-reward-content\" }, [\n                            _c(\"div\", { staticClass: \"reward-info-card\" }, [\n                              _c(\"ul\", { staticClass: \"reward-list\" }, [\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"金砖\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 10)),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"银两\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 1000)),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"体力\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 5)),\n                                  ]),\n                                ]),\n                              ]),\n                            ]),\n                            _c(\n                              \"button\",\n                              {\n                                staticClass: \"pixel-button\",\n                                class: _vm.canClaimDaily\n                                  ? \"gold shine-effect\"\n                                  : \"disabled\",\n                                attrs: {\n                                  disabled:\n                                    !_vm.canClaimDaily || _vm.isClaiming,\n                                },\n                                on: { click: _vm.claimDailyReward },\n                              },\n                              [\n                                _vm.isClaiming\n                                  ? _c(\"span\", [_vm._v(\"领取中...\")])\n                                  : _vm.canClaimDaily\n                                  ? _c(\"span\", [_vm._v(\"领取每日福利\")])\n                                  : _c(\"span\", [_vm._v(\"今日已领取\")]),\n                              ]\n                            ),\n                          ])\n                        : _vm.currentRewardTab === \"weekly\"\n                        ? _c(\"div\", { staticClass: \"daily-reward-content\" }, [\n                            _c(\"div\", { staticClass: \"reward-info-card\" }, [\n                              _c(\"ul\", { staticClass: \"reward-list\" }, [\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"金砖\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 30)),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"银两\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 3000)),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"高级装备箱\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\n                                      \"x\" +\n                                        _vm._s(\n                                          Math.max(\n                                            1,\n                                            Math.floor(_vm.vipLevel / 2)\n                                          )\n                                        )\n                                    ),\n                                  ]),\n                                ]),\n                              ]),\n                            ]),\n                            _c(\n                              \"button\",\n                              {\n                                staticClass: \"pixel-button\",\n                                class: _vm.canClaimWeekly\n                                  ? \"gold shine-effect\"\n                                  : \"disabled\",\n                                attrs: {\n                                  disabled:\n                                    !_vm.canClaimWeekly || _vm.isClaiming,\n                                },\n                                on: { click: _vm.claimWeeklyReward },\n                              },\n                              [\n                                _vm.isClaiming\n                                  ? _c(\"span\", [_vm._v(\"领取中...\")])\n                                  : _vm.canClaimWeekly\n                                  ? _c(\"span\", [_vm._v(\"领取每周福利\")])\n                                  : _c(\"span\", [_vm._v(\"本周已领取\")]),\n                              ]\n                            ),\n                          ])\n                        : _c(\"div\", { staticClass: \"daily-reward-content\" }, [\n                            _c(\"div\", { staticClass: \"reward-info-card\" }, [\n                              _c(\"ul\", { staticClass: \"reward-list\" }, [\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"金砖\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 100)),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"银两\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 10000)),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"稀有装备箱\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\n                                      \"x\" +\n                                        _vm._s(\n                                          Math.max(\n                                            1,\n                                            Math.floor(_vm.vipLevel / 3)\n                                          )\n                                        )\n                                    ),\n                                  ]),\n                                ]),\n                                _c(\"li\", { staticClass: \"reward-list-item\" }, [\n                                  _c(\"span\", { staticClass: \"reward-name\" }, [\n                                    _vm._v(\"仙将碎片\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"reward-amount\" }, [\n                                    _vm._v(\"x\" + _vm._s(_vm.vipLevel * 5)),\n                                  ]),\n                                ]),\n                              ]),\n                            ]),\n                            _c(\n                              \"button\",\n                              {\n                                staticClass: \"pixel-button\",\n                                class: _vm.canClaimMonthly\n                                  ? \"gold shine-effect\"\n                                  : \"disabled\",\n                                attrs: {\n                                  disabled:\n                                    !_vm.canClaimMonthly || _vm.isClaiming,\n                                },\n                                on: { click: _vm.claimMonthlyReward },\n                              },\n                              [\n                                _vm.isClaiming\n                                  ? _c(\"span\", [_vm._v(\"领取中...\")])\n                                  : _vm.canClaimMonthly\n                                  ? _c(\"span\", [_vm._v(\"领取每月福利\")])\n                                  : _c(\"span\", [_vm._v(\"本月已领取\")]),\n                              ]\n                            ),\n                          ]),\n                    ]\n                  ),\n                ])\n              : _c(\"div\", { staticClass: \"tab-content\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"vip-privileges pixel-border-box\" },\n                    [\n                      _c(\"div\", { staticClass: \"section-header\" }, [\n                        _c(\"div\", { staticClass: \"vip-title-with-icon\" }, [\n                          _c(\"img\", {\n                            staticClass: \"vip-small-icon\",\n                            attrs: { src: _vm.vipIcon, alt: \"VIP图标\" },\n                          }),\n                          _c(\"h3\", [\n                            _vm._v(\"VIP\" + _vm._s(_vm.vipLevel) + \"特权说明\"),\n                          ]),\n                        ]),\n                        _c(\"div\", { staticClass: \"section-decor\" }),\n                      ]),\n                      _c(\n                        \"ul\",\n                        { staticClass: \"privilege-list\" },\n                        _vm._l(_vm.vipPrivileges, function (desc, idx) {\n                          return _c(\n                            \"li\",\n                            { key: idx, staticClass: \"privilege-item\" },\n                            [\n                              _c(\"i\", { staticClass: \"privilege-icon\" }, [\n                                _vm._v(\"✓\"),\n                              ]),\n                              _c(\"span\", [_vm._v(_vm._s(desc))]),\n                            ]\n                          )\n                        }),\n                        0\n                      ),\n                    ]\n                  ),\n                ]),\n          ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,YAAY,EAAE,CACtBA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CAClDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE,aAAa;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM;IAAO;EAAE,CAAC,EAAE,CACnEL,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEC,GAAG,EAAE,iCAAiC;MAAEC,GAAG,EAAE;IAAK;EAC7D,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFT,GAAG,CAACU,OAAO,GACPT,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC/D,CAAC,GACFX,GAAG,CAACY,QAAQ,GACZX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACvDV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,YAAY,CAAC,CAAC,CACjC,CAAC,EACFb,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,mBAAmB;IAChCC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACe;IAAa;EAChC,CAAC,EACD,CAACf,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,GACFV,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBa,KAAK,EAAE;MAAEC,MAAM,EAAEjB,GAAG,CAACkB,cAAc,KAAK;IAAM,CAAC;IAC/Cd,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;QACvBnB,GAAG,CAACkB,cAAc,GAAG,KAAK;MAC5B;IACF;EACF,CAAC,EACD,CAACjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAC9B,CAAC,EACDV,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBa,KAAK,EAAE;MAAEC,MAAM,EAAEjB,GAAG,CAACkB,cAAc,KAAK;IAAW,CAAC;IACpDd,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;QACvBnB,GAAG,CAACkB,cAAc,GAAG,UAAU;MACjC;IACF;EACF,CAAC,EACD,CAACjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAChC,CAAC,EACDV,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBa,KAAK,EAAE;MAAEC,MAAM,EAAEjB,GAAG,CAACkB,cAAc,KAAK;IAAa,CAAC;IACtDd,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;QACvBnB,GAAG,CAACkB,cAAc,GAAG,YAAY;MACnC;IACF;EACF,CAAC,EACD,CAACjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAChC,CAAC,CACF,CAAC,EACFX,GAAG,CAACkB,cAAc,KAAK,KAAK,GACxBjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,UAAU;IACvBI,KAAK,EAAE;MAAEC,GAAG,EAAER,GAAG,CAACoB,OAAO;MAAEX,GAAG,EAAE,KAAK,GAAGT,GAAG,CAACqB;IAAS;EACvD,CAAC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,GAAG,CAACW,EAAE,CACJ,QAAQ,GACNX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACsB,MAAM,CAAC,GAClB,KAAK,GACLtB,GAAG,CAACa,EAAE,CAACb,GAAG,CAACuB,UAAU,CAAC,GACtB,GACJ,CAAC,EACDvB,GAAG,CAACqB,QAAQ,GAAG,EAAE,GACbpB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,EACfV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC5CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACuB,UAAU,GAAGvB,GAAG,CAACsB,MAAM,CAAC,CAAC,CAC5C,CAAC,EACFtB,GAAG,CAACW,EAAE,CACJ,YAAY,GACVX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,CAAC,CAAC,GACxB,GACJ,CAAC,CACF,CAAC,GACFpB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,CACP,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,cAAc;IAC3BqB,KAAK,EAAE;MAAEC,KAAK,EAAEzB,GAAG,CAAC0B,UAAU,GAAG;IAAI;EACvC,CAAC,CAAC,EACFzB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjCH,GAAG,CAAC2B,EAAE,CAAC,CAAC,EAAE,UAAUC,CAAC,EAAE;IACrB,OAAO3B,EAAE,CACP,MAAM,EACN;MACE4B,GAAG,EAAED,CAAC;MACNzB,WAAW,EAAE,eAAe;MAC5BqB,KAAK,EAAE;QAAEM,IAAI,EAAEF,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;MAAI;IACnC,CAAC,EACD,CAAC5B,GAAG,CAACW,EAAE,CAAC,GAAG,CAAC,CACd,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACa,EAAE,CAACb,GAAG,CAAC0B,UAAU,CAAC,GAAG,GAAG,CAAC,CACrC,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,GAAG,CAAC+B,cAAc,GACd9B,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,gCAAgC;IAC7CI,KAAK,EAAE;MAAEyB,QAAQ,EAAEhC,GAAG,CAACiC;IAAW,CAAC;IACnC7B,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACkC;IAAe;EAClC,CAAC,EACD,CACElC,GAAG,CAACiC,UAAU,GACVhC,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BV,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACW,EAAE,CACJ,OAAO,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,CAAC,GAAG,IACnC,CAAC,CACF,CAAC,CAEV,CAAC,GACDrB,GAAG,CAACmC,EAAE,CAAC,CAAC,CACb,CAAC,EACFnC,GAAG,CAACoC,gBAAgB,CAACC,MAAM,GACvBpC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsC,CAAC,EACtD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7BI,KAAK,EAAE;MAAEC,GAAG,EAAER,GAAG,CAACoB,OAAO;MAAEX,GAAG,EAAE;IAAQ;EAC1C,CAAC,CAAC,EACFR,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAChC,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,CAAC,CAC5C,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAAC2B,EAAE,CACJ3B,GAAG,CAACoC,gBAAgB,EACpB,UAAUE,MAAM,EAAEC,GAAG,EAAE;IACrB,OAAOtC,EAAE,CACP,KAAK,EACL;MAAE4B,GAAG,EAAEU,GAAG;MAAEpC,WAAW,EAAE;IAAe,CAAC,EACzC,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAoB,CAAC,EACpC,CACEF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,kBAAkB;MAC/BI,KAAK,EAAE;QACLC,GAAG,EAAER,GAAG,CAACwC,UAAU,CAACF,MAAM,CAACG,KAAK,CAAC;QACjChC,GAAG,EAAE,KAAK,GAAG6B,MAAM,CAACG;MACtB;IACF,CAAC,CAAC,CAEN,CAAC,EACDxC,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEH,GAAG,CAACW,EAAE,CACJX,GAAG,CAACa,EAAE,CAACyB,MAAM,CAACI,IAAI,IAAI,KAAK,CAC7B,CAAC,CAEL,CAAC,EACDzC,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CAACH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACa,EAAE,CAACyB,MAAM,CAACK,IAAI,CAAC,CAAC,CAC9B,CAAC,CACF,CAAC,EACF,CAACL,MAAM,CAACI,IAAI,GACRzC,EAAE,CACA,QAAQ,EACR;MACEE,WAAW,EACT,sCAAsC;MACxCI,KAAK,EAAE;QAAEyB,QAAQ,EAAEhC,GAAG,CAACiC;MAAW,CAAC;MACnC7B,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;UACvB,OAAOnB,GAAG,CAAC4C,kBAAkB,CAC3BN,MAAM,CAACG,KACT,CAAC;QACH;MACF;IACF,CAAC,EACD,CAACzC,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,GACDX,GAAG,CAACmC,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CACF,CAAC,EACD,CACF,CAAC,CAEL,CAAC,GACDnC,GAAG,CAACmC,EAAE,CAAC,CAAC,CACb,CAAC,GACFnC,GAAG,CAACkB,cAAc,KAAK,UAAU,GACjCjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAoC,CAAC,EACpD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7BI,KAAK,EAAE;MAAEC,GAAG,EAAER,GAAG,CAACoB,OAAO;MAAEX,GAAG,EAAE;IAAQ;EAC1C,CAAC,CAAC,EACFR,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC5B,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,CAAC,CAC5C,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,YAAY;IACzBa,KAAK,EAAE;MAAEC,MAAM,EAAEjB,GAAG,CAAC6C,gBAAgB,KAAK;IAAQ,CAAC;IACnDzC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;QACvBnB,GAAG,CAAC6C,gBAAgB,GAAG,OAAO;MAChC;IACF;EACF,CAAC,EACD,CAAC7C,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDV,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,YAAY;IACzBa,KAAK,EAAE;MACLC,MAAM,EAAEjB,GAAG,CAAC6C,gBAAgB,KAAK;IACnC,CAAC;IACDzC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;QACvBnB,GAAG,CAAC6C,gBAAgB,GAAG,QAAQ;MACjC;IACF;EACF,CAAC,EACD,CAAC7C,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDV,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,YAAY;IACzBa,KAAK,EAAE;MACLC,MAAM,EAAEjB,GAAG,CAAC6C,gBAAgB,KAAK;IACnC,CAAC;IACDzC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUc,MAAM,EAAE;QACvBnB,GAAG,CAAC6C,gBAAgB,GAAG,SAAS;MAClC;IACF;EACF,CAAC,EACD,CAAC7C,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,CAAC,EACFX,GAAG,CAAC6C,gBAAgB,KAAK,OAAO,GAC5B5C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,EAAE,CAAC,CAAC,CACxC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,IAAI,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,cAAc;IAC3Ba,KAAK,EAAEhB,GAAG,CAAC8C,aAAa,GACpB,mBAAmB,GACnB,UAAU;IACdvC,KAAK,EAAE;MACLyB,QAAQ,EACN,CAAChC,GAAG,CAAC8C,aAAa,IAAI9C,GAAG,CAACiC;IAC9B,CAAC;IACD7B,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAAC+C;IAAiB;EACpC,CAAC,EACD,CACE/C,GAAG,CAACiC,UAAU,GACVhC,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BX,GAAG,CAAC8C,aAAa,GACjB7C,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BV,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAErC,CAAC,CACF,CAAC,GACFX,GAAG,CAAC6C,gBAAgB,KAAK,QAAQ,GACjC5C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,EAAE,CAAC,CAAC,CACxC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,IAAI,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CACJ,GAAG,GACDX,GAAG,CAACa,EAAE,CACJmC,IAAI,CAACC,GAAG,CACN,CAAC,EACDD,IAAI,CAACE,KAAK,CAAClD,GAAG,CAACqB,QAAQ,GAAG,CAAC,CAC7B,CACF,CACJ,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,cAAc;IAC3Ba,KAAK,EAAEhB,GAAG,CAACmD,cAAc,GACrB,mBAAmB,GACnB,UAAU;IACd5C,KAAK,EAAE;MACLyB,QAAQ,EACN,CAAChC,GAAG,CAACmD,cAAc,IAAInD,GAAG,CAACiC;IAC/B,CAAC;IACD7B,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACoD;IAAkB;EACrC,CAAC,EACD,CACEpD,GAAG,CAACiC,UAAU,GACVhC,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BX,GAAG,CAACmD,cAAc,GAClBlD,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BV,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAErC,CAAC,CACF,CAAC,GACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,GAAG,CAAC,CAAC,CACzC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,KAAK,CAAC,CAAC,CAC3C,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CACJ,GAAG,GACDX,GAAG,CAACa,EAAE,CACJmC,IAAI,CAACC,GAAG,CACN,CAAC,EACDD,IAAI,CAACE,KAAK,CAAClD,GAAG,CAACqB,QAAQ,GAAG,CAAC,CAC7B,CACF,CACJ,CAAC,CACF,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,cAAc;IAC3Ba,KAAK,EAAEhB,GAAG,CAACqD,eAAe,GACtB,mBAAmB,GACnB,UAAU;IACd9C,KAAK,EAAE;MACLyB,QAAQ,EACN,CAAChC,GAAG,CAACqD,eAAe,IAAIrD,GAAG,CAACiC;IAChC,CAAC;IACD7B,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACsD;IAAmB;EACtC,CAAC,EACD,CACEtD,GAAG,CAACiC,UAAU,GACVhC,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BX,GAAG,CAACqD,eAAe,GACnBpD,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9BV,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAErC,CAAC,CACF,CAAC,CAEV,CAAC,CACF,CAAC,GACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAkC,CAAC,EAClD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7BI,KAAK,EAAE;MAAEC,GAAG,EAAER,GAAG,CAACoB,OAAO;MAAEX,GAAG,EAAE;IAAQ;EAC1C,CAAC,CAAC,EACFR,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACW,EAAE,CAAC,KAAK,GAAGX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACqB,QAAQ,CAAC,GAAG,MAAM,CAAC,CAC9C,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,CAAC,CAC5C,CAAC,EACFF,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjCH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACuD,aAAa,EAAE,UAAUZ,IAAI,EAAEJ,GAAG,EAAE;IAC7C,OAAOtC,EAAE,CACP,IAAI,EACJ;MAAE4B,GAAG,EAAEU,GAAG;MAAEpC,WAAW,EAAE;IAAiB,CAAC,EAC3C,CACEF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAACX,GAAG,CAACa,EAAE,CAAC8B,IAAI,CAAC,CAAC,CAAC,CAAC,CAEtC,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CAEL,CAAC,CACF,CAAC,CACP,CAAC,CACP,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIa,eAAe,GAAG,EAAE;AACxBzD,MAAM,CAAC0D,aAAa,GAAG,IAAI;AAE3B,SAAS1D,MAAM,EAAEyD,eAAe", "ignoreList": []}]}