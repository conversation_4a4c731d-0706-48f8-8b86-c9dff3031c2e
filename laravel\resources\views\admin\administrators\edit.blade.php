@extends('admin.layouts.app')

@section('title', '编辑管理员')

@section('breadcrumb')
<a href="{{ route('admin.administrators.index') }}">管理员列表</a>
<a><cite>编辑管理员</cite></a>
@endsection

@section('page-title', '编辑管理员')

@section('content')
<div class="layui-card">
  <div class="layui-card-body">
    <form class="layui-form" method="POST" action="{{ route('admin.administrators.update', $administrator->id) }}">
      @csrf
      @method('PUT')

      <div class="layui-form-item">
        <label class="layui-form-label">姓名</label>
        <div class="layui-input-block">
          <input type="text" name="name" value="{{ old('name', $administrator->name) }}" lay-verify="required" placeholder="请输入姓名" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">用户名</label>
        <div class="layui-input-block">
          <input type="text" name="username" value="{{ old('username', $administrator->username) }}" lay-verify="required" placeholder="请输入登录用户名" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">密码</label>
        <div class="layui-input-block">
          <input type="password" name="password" placeholder="留空表示不修改密码，至少8个字符" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">角色</label>
        <div class="layui-input-block">
          <input type="radio" name="role" value="admin" title="普通管理员" {{ $administrator->role == 'admin' ? 'checked' : '' }}>
          <input type="radio" name="role" value="super_admin" title="超级管理员" {{ $administrator->role == 'super_admin' ? 'checked' : '' }}>
        </div>
      </div>

      @php
        $permissions = json_decode($administrator->permissions, true) ?? [];
      @endphp

      <div class="layui-form-item">
        <label class="layui-form-label">权限设置</label>
        <div class="layui-input-block">
          <!-- 角色管理权限 -->
          <fieldset class="layui-elem-field">
            <legend>角色管理</legend>
            <div class="layui-field-box">
              <input type="checkbox" name="permissions[characters][view]" title="查看" value="1" lay-skin="primary" {{ in_array('view', $permissions['characters'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[characters][create]" title="创建" value="1" lay-skin="primary" {{ in_array('create', $permissions['characters'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[characters][edit]" title="编辑" value="1" lay-skin="primary" {{ in_array('edit', $permissions['characters'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[characters][delete]" title="删除" value="1" lay-skin="primary" {{ in_array('delete', $permissions['characters'] ?? []) ? 'checked' : '' }}>
            </div>
          </fieldset>

          <!-- 物品管理权限 -->
          <fieldset class="layui-elem-field">
            <legend>物品管理</legend>
            <div class="layui-field-box">
              <input type="checkbox" name="permissions[items][view]" title="查看" value="1" lay-skin="primary" {{ in_array('view', $permissions['items'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[items][create]" title="创建" value="1" lay-skin="primary" {{ in_array('create', $permissions['items'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[items][edit]" title="编辑" value="1" lay-skin="primary" {{ in_array('edit', $permissions['items'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[items][delete]" title="删除" value="1" lay-skin="primary" {{ in_array('delete', $permissions['items'] ?? []) ? 'checked' : '' }}>
            </div>
          </fieldset>

          <!-- 任务管理权限 -->
          <fieldset class="layui-elem-field">
            <legend>任务管理</legend>
            <div class="layui-field-box">
              <input type="checkbox" name="permissions[quests][view]" title="查看" value="1" lay-skin="primary" {{ in_array('view', $permissions['quests'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[quests][create]" title="创建" value="1" lay-skin="primary" {{ in_array('create', $permissions['quests'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[quests][edit]" title="编辑" value="1" lay-skin="primary" {{ in_array('edit', $permissions['quests'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[quests][delete]" title="删除" value="1" lay-skin="primary" {{ in_array('delete', $permissions['quests'] ?? []) ? 'checked' : '' }}>
            </div>
          </fieldset>

          <!-- 系统设置权限 -->
          <fieldset class="layui-elem-field">
            <legend>系统设置</legend>
            <div class="layui-field-box">
              <input type="checkbox" name="permissions[settings][view]" title="查看" value="1" lay-skin="primary" {{ in_array('view', $permissions['settings'] ?? []) ? 'checked' : '' }}>
              <input type="checkbox" name="permissions[settings][edit]" title="编辑" value="1" lay-skin="primary" {{ in_array('edit', $permissions['settings'] ?? []) ? 'checked' : '' }}>
            </div>
          </fieldset>
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-block">
          <button class="layui-btn" lay-submit lay-filter="formAdmin">保存修改</button>
          <a href="{{ route('admin.administrators.index') }}" class="layui-btn layui-btn-primary">返回</a>
        </div>
      </div>
    </form>
  </div>
</div>
@endsection

@section('js')
<script>
  layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    // 自定义验证规则
    form.verify({
      pwd: [
        /^.{8,}$/,
        '密码必须8位以上'
      ]
    });

    // 角色切换事件
    form.on('radio(role)', function(data){
      var role = data.value;
      if (role === 'super_admin') {
        // 超级管理员自动选择所有权限
        $('input[type="checkbox"]').prop('checked', true);
        form.render('checkbox');
      }
    });

    // 提交表单
    form.on('submit(formAdmin)', function(data){
      // 默认提交表单
      return true;
    });
  });
</script>
@endsection
