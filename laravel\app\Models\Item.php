<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Item extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'model',
        'type',
        'subtype',
        'quality',
        'level_requirement',
        'profession_requirement',
        'attack_bonus',
        'defense_bonus',
        'magic_attack_bonus',
        'magic_defense_bonus',
        'health_bonus',
        'mana_bonus',
        'speed_bonus',
        'accuracy_bonus',
        'dodge_bonus',
        'critical_bonus',
        'special_effects',
        'set_bonus',
        'element',
        'skills',
        'is_consumable',
        'is_stackable',
        'max_stack',
        'is_tradeable',
        'is_droppable',
        'is_sellable',
        'buy_price',
        'sell_price',
        'drop_rate',
        'durability',
        'max_durability',
        'is_craftable',
        'craft_materials',
        'is_enhanceable',
        'max_enhance_level',
        'enhance_materials',
        'health_restore',
        'mana_restore',
        'buff_effects',
        'effect_duration',
        'obtain_methods',
        'drop_monsters',
        'sold_by_npcs',
        'is_active',
        'is_rare',
        'available_from',
        'available_until',
    ];

    protected $casts = [
        'special_effects' => 'array',
        'set_bonus' => 'array',
        'skills' => 'array',
        'craft_materials' => 'array',
        'enhance_materials' => 'array',
        'buff_effects' => 'array',
        'obtain_methods' => 'array',
        'drop_monsters' => 'array',
        'sold_by_npcs' => 'array',
        'is_consumable' => 'boolean',
        'is_stackable' => 'boolean',
        'is_tradeable' => 'boolean',
        'is_droppable' => 'boolean',
        'is_sellable' => 'boolean',
        'is_craftable' => 'boolean',
        'is_enhanceable' => 'boolean',
        'is_active' => 'boolean',
        'is_rare' => 'boolean',
        'available_from' => 'datetime',
        'available_until' => 'datetime',
    ];

    /**
     * 获取拥有此物品的角色
     */
    public function characters()
    {
        return $this->belongsToMany(Character::class, 'character_items')
            ->withPivot('quantity', 'equipped', 'custom_stats')
            ->withTimestamps();
    }

    /**
     * 获取物品的品质颜色
     */
    public function getQualityColor()
    {
        $colors = [
            'common' => '#ffffff',
            'uncommon' => '#1eff00',
            'rare' => '#0070dd',
            'epic' => '#a335ee',
            'legendary' => '#ff8000',
            'mythic' => '#e6cc80',
        ];

        return $colors[$this->quality] ?? $colors['common'];
    }

    /**
     * 获取物品的总属性加成
     */
    public function getTotalStats()
    {
        return [
            'attack' => $this->attack_bonus,
            'defense' => $this->defense_bonus,
            'magic_attack' => $this->magic_attack_bonus,
            'magic_defense' => $this->magic_defense_bonus,
            'health' => $this->health_bonus,
            'mana' => $this->mana_bonus,
            'speed' => $this->speed_bonus,
            'accuracy' => $this->accuracy_bonus,
            'dodge' => $this->dodge_bonus,
            'critical' => $this->critical_bonus,
        ];
    }

    /**
     * 检查物品是否可用
     */
    public function isAvailable()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        if ($this->available_from && $now->lt($this->available_from)) {
            return false;
        }

        if ($this->available_until && $now->gt($this->available_until)) {
            return false;
        }

        return true;
    }

    /**
     * 检查角色是否满足使用条件
     */
    public function canUseBy($character)
    {
        if ($character->level < $this->level_requirement) {
            return false;
        }

        if ($this->profession_requirement !== 'none' &&
            $this->profession_requirement !== 'all' &&
            $character->profession !== $this->profession_requirement) {
            return false;
        }

        return true;
    }

    /**
     * 获取物品的完整信息
     */
    public function getFullInfo()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'icon' => $this->icon,
            'type' => $this->type,
            'subtype' => $this->subtype,
            'quality' => $this->quality,
            'quality_color' => $this->getQualityColor(),
            'level_requirement' => $this->level_requirement,
            'profession_requirement' => $this->profession_requirement,
            'stats' => $this->getTotalStats(),
            'special_effects' => $this->special_effects ?? [],
            'buy_price' => $this->buy_price,
            'sell_price' => $this->sell_price,
            'is_consumable' => $this->is_consumable,
            'is_stackable' => $this->is_stackable,
            'max_stack' => $this->max_stack,
            'durability' => $this->durability,
            'max_durability' => $this->max_durability,
        ];
    }

    /**
     * 根据类型获取物品
     */
    public static function getByType($type)
    {
        return static::where('type', $type)
            ->where('is_active', true)
            ->get();
    }

    /**
     * 获取武器
     */
    public static function getWeapons()
    {
        return static::where('type', 'weapon')
            ->where('is_active', true)
            ->get();
    }

    /**
     * 获取防具
     */
    public static function getArmor()
    {
        return static::where('type', 'armor')
            ->where('is_active', true)
            ->get();
    }

    /**
     * 获取消耗品
     */
    public static function getConsumables()
    {
        return static::where('type', 'consumable')
            ->where('is_active', true)
            ->get();
    }
}
