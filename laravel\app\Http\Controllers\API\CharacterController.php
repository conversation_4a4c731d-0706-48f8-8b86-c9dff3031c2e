<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Character;

class CharacterController extends Controller
{
    /**
     * 获取当前用户的所有角色
     */
    public function index(Request $request)
    {
        $query = Character::where('user_id', $request->user()->id);

        // 如果指定了大区ID，则过滤该大区的角色
        if ($request->has('region_id')) {
            $query->where('region_id', $request->region_id);
        }

        $characters = $query->get();

        return response()->json([
            'success' => true,
            'data' => $characters->map(function($character) {
                return [
                    'id' => $character->id,
                    'characterId' => $character->id,
                    'name' => $character->name,
                    'profession' => $character->profession,
                    'level' => $character->level,
                    'experience' => $character->experience,
                    'gender' => $character->gender,
                    'region_id' => $character->region_id,
                    'avatar' => '/static/game/avatars/' . $character->gender . '_' . $character->profession . '.png',
                    'created_at' => $character->created_at,
                    'updated_at' => $character->updated_at
                ];
            })
        ]);
    }

    /**
     * 创建新角色
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:20',
            'gender' => 'required|in:male,female',
            'profession' => 'required|in:warrior,scholar,mystic',
            'region_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first(),
                ]
            ], 422);
        }

        // 检查用户的角色数量是否达到上限
        $characterCount = Character::where('user_id', $request->user()->id)->count();
        if ($characterCount >= 3) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'LIMIT_EXCEEDED',
                    'message' => '角色数量已达上限（3个）',
                ]
            ], 422);
        }

        // 根据职业设置初始属性
        $attributes = $this->getInitialAttributes($request->profession);
        $stats = $this->calculateStats($attributes);

        $character = Character::create([
            'user_id' => $request->user()->id,
            'name' => $request->name,
            'gender' => $request->gender,
            'profession' => $request->profession,
            'level' => 1,
            'experience' => 0,
            'attributes' => $attributes,
            'equipment' => json_encode([]),
            'skills' => json_encode([]),
            'stats' => $stats,
            'region_id' => $request->region_id,
            'attribute_points' => 4,
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'characterId' => $character->id,
                'name' => $character->name,
                'gender' => $character->gender,
                'profession' => $character->profession,
                'level' => $character->level,
                'experience' => $character->experience,
                'attributes' => $character->attributes,
                'stats' => $character->stats,
            ]
        ], 201);
    }

    /**
     * 获取角色详情
     */
    public function show(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 返回角色详情
        return response()->json([
            'success' => true,
            'data' => [
                'characterId' => $character->id,
                'name' => $character->name,
                'gender' => $character->gender,
                'profession' => $character->profession,
                'level' => $character->level,
                'experience' => $character->experience,
                'attributes' => $character->attributes,
                'equipment' => $character->equipment,
                'skills' => $character->skills,
                'stats' => $character->stats,
            ]
        ]);
    }

    /**
     * 根据职业获取初始属性
     */
    private function getInitialAttributes($profession)
    {
        // 初始等级为1，所以所有基础属性都为1
        $baseAttributes = [
            'strength' => 1,
            'dexterity' => 1,
            'intelligence' => 1,
            'vitality' => 1,
        ];

        // 根据职业可以添加额外的初始属性加成
        switch ($profession) {
            case 'warrior':
                // 武士可以有额外的力量和体质
                $baseAttributes['strength'] += 0;
                $baseAttributes['vitality'] += 0;
                break;
            case 'scholar':
                // 文人可以有额外的智力
                $baseAttributes['intelligence'] += 0;
                break;
            case 'mystic':
                // 异人可以有额外的敏捷
                $baseAttributes['dexterity'] += 0;
                break;
        }

        return json_encode($baseAttributes);
    }

    /**
     * 计算战斗属性
     */
    private function calculateStats($attributes)
    {
        $attributes = json_decode($attributes, true);

        $stats = [
            'hp' => 100 + ($attributes['vitality'] * 10),
            'mp' => 50 + ($attributes['intelligence'] * 5),
            'attack' => 10 + round($attributes['strength'] * 0.8 + $attributes['dexterity'] * 0.2),
            'defense' => 5 + round($attributes['vitality'] * 0.6 + $attributes['strength'] * 0.4),
            'magicAttack' => 5 + round($attributes['intelligence'] * 0.9),
            'magicDefense' => 5 + round($attributes['intelligence'] * 0.5 + $attributes['vitality'] * 0.3),
            'speed' => 10 + round($attributes['dexterity'] * 0.8)
        ];

        return json_encode($stats);
    }

    /**
     * 获取角色状态信息
     */
    public function getStatus(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 获取角色属性和统计数据
        $attributes = $character->attributes ?? [];
        $stats = $character->stats ?? [];

        // 获取角色所在大区信息
        $region = $character->region;
        $regionName = $region ? $region->name : '未知大区';

        // 职业中文名称映射
        $classNames = [
            'warrior' => '武士',
            'scholar' => '文人',
            'mystic' => '异人',
        ];

        // 计算升级所需经验
        $expRequired = $character->calculateExpRequired($character->level);

        // 构建状态响应
        $status = [
            'id' => $character->id,
            'server_name' => $regionName,
            'career' => $character->level >= 2 ? '二转' : '一转',
            'level' => $character->level,
            'class_name' => $classNames[$character->profession] ?? '未知',
            'server_rank' => 16, // 示例数据
            'rank_name' => '白银一★☆', // 示例数据
            'title' => '神颜会龙王', // 示例数据
            'exp' => $character->experience,
            'exp_required' => $expRequired,
            'upgrade_rate' => 612.0, // 示例数据
            'stamina' => 325, // 示例数据
            'strength_status' => '开启中',
            'gold' => $character->gold ?? 0,
            'silver' => $character->silver ?? 20650,
            'energy' => $character->energy ?? 100,
            'position' => '无', // 示例数据
            'location' => '许昌', // 示例数据
            'property' => '草庐', // 示例数据
            'faction' => '神颜会', // 示例数据
            'defeated_count' => 0, // 示例数据
            'partner' => '无', // 示例数据
            'attribute_points' => $character->attribute_points,
            'deputies' => ['龙飞卫', '秦卫兵'], // 示例数据
            'mount' => '乌云踏雪', // 示例数据
            'hp' => $stats['hp'] ?? 16444,
            'max_hp' => $stats['hp'] ?? 16444,
            'mp' => $stats['mp'] ?? 7146,
            'max_mp' => $stats['mp'] ?? 7146,
            'attack' => $stats['attack'] ?? 19279,
            'speed' => $stats['speed'] ?? 147,
            'defense' => $stats['defense'] ?? 274,
            'weight' => 751, // 示例数据
            'max_weight' => 920, // 示例数据
            'info_status' => '关闭中',
            'talents' => ['反击', '暴击', '爆率'], // 示例数据
            'avatar_url' => '/static/game/UI/tx/' . $character->gender . '/tx1.png',
            'constitution' => $attributes['vitality'] ?? 0,
            'intelligence' => $attributes['intelligence'] ?? 0,
            'strength' => $attributes['strength'] ?? 0,
            'agility' => $attributes['dexterity'] ?? 0,
        ];

        return response()->json([
            'success' => true,
            'data' => $status
        ]);
    }

    /**
     * 更新角色属性点分配
     */
    public function updateAttributes(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'constitution' => 'required|integer|min:0|max:200',
            'intelligence' => 'required|integer|min:0|max:200',
            'strength' => 'required|integer|min:0|max:200',
            'agility' => 'required|integer|min:0|max:200',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()->all(),
                    'request_data' => $request->all()
                ]
            ], 422);
        }

        // 准备属性数据 - 转换前端参数名称为后端参数名称
        $newAttributes = [
            'vitality' => $request->constitution,
            'intelligence' => $request->intelligence,
            'strength' => $request->strength,
            'dexterity' => $request->agility,
        ];

        // 使用模型方法更新属性（包含属性点验证和扣除）
        $success = $character->updateAttributes($newAttributes);

        if (!$success) {
            // 正确计算需要的额外属性点
            $baseAttributes = $character->calculateBaseAttributes($character->level);
            $extraPointsNeeded = [
                'vitality' => max($newAttributes['vitality'], $baseAttributes['vitality']) - $baseAttributes['vitality'],
                'intelligence' => max($newAttributes['intelligence'], $baseAttributes['intelligence']) - $baseAttributes['intelligence'],
                'strength' => max($newAttributes['strength'], $baseAttributes['strength']) - $baseAttributes['strength'],
                'dexterity' => max($newAttributes['dexterity'], $baseAttributes['dexterity']) - $baseAttributes['dexterity']
            ];
            $totalRequired = array_sum($extraPointsNeeded);
            $totalAvailable = $character->getTotalAvailableAttributePoints();

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INSUFFICIENT_POINTS',
                    'message' => '属性点不足',
                    'details' => [
                        'required_points' => $totalRequired,
                        'available_points' => $character->attribute_points,
                        'total_available_points' => $totalAvailable,
                        'current_level' => $character->level,
                        'base_attributes' => $baseAttributes,
                        'extra_points_needed' => $extraPointsNeeded,
                        'current_attributes' => $character->attributes,
                        'requested_attributes' => $newAttributes
                    ]
                ]
            ], 422);
        }

        // 重新加载角色数据
        $character->refresh();

        // 获取更新后的属性和统计数据
        $attributes = $character->attributes ?? [];
        $stats = $character->stats ?? [];

        return response()->json([
            'success' => true,
            'data' => [
                'attribute_points' => $character->attribute_points,
                'attributes' => [
                    'constitution' => $attributes['vitality'] ?? 0,
                    'intelligence' => $attributes['intelligence'] ?? 0,
                    'strength' => $attributes['strength'] ?? 0,
                    'agility' => $attributes['dexterity'] ?? 0
                ],
                'stats' => $stats
            ]
        ]);
    }

    /**
     * 重置角色属性点分配
     */
    public function resetAttributes(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 重置属性点分配
        $success = $character->resetAttributes();

        if (!$success) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'RESET_FAILED',
                    'message' => '重置属性点失败',
                ]
            ], 500);
        }

        // 重新加载角色数据
        $character->refresh();

        // 获取重置后的属性和统计数据
        $attributes = $character->attributes ?? [];
        $stats = $character->stats ?? [];

        return response()->json([
            'success' => true,
            'data' => [
                'attribute_points' => $character->attribute_points,
                'total_available_points' => $character->getTotalAvailableAttributePoints(),
                'attributes' => [
                    'constitution' => $attributes['vitality'] ?? 0,
                    'intelligence' => $attributes['intelligence'] ?? 0,
                    'strength' => $attributes['strength'] ?? 0,
                    'agility' => $attributes['dexterity'] ?? 0
                ],
                'stats' => $stats
            ]
        ]);
    }

    /**
     * 获取角色经验信息
     */
    public function getExperience(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 计算升级所需经验
        $expRequired = $character->calculateExpRequired($character->level);

        return response()->json([
            'success' => true,
            'data' => [
                'level' => $character->level,
                'exp' => $character->experience,
                'exp_required' => $expRequired,
                'attribute_points' => $character->attribute_points ?? 0
            ]
        ]);
    }

    /**
     * 添加角色经验值
     */
    public function addExperience(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'amount' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first(),
                ]
            ], 422);
        }

        // 使用模型方法添加经验
        $result = $character->addExperience($request->amount);

        // 计算新的升级所需经验
        $expRequired = $character->calculateExpRequired($character->level);

        // 重新加载角色数据以获取最新的属性
        $character->refresh();

        return response()->json([
            'success' => true,
            'data' => [
                'level' => $character->level,
                'exp' => $character->experience,
                'exp_required' => $expRequired,
                'attribute_points' => $character->attribute_points,
                'level_up' => $result['level_up'],
                'old_level' => $result['old_level'],
                'new_level' => $result['new_level'],
                'rewards' => $result['rewards'] ?? [],
                'stats' => $character->stats,
                'gold' => $character->gold,
                'silver' => $character->silver,
                'energy' => $character->energy,
                'skills' => $character->skills
            ]
        ]);
    }

    /**
     * 计算升级所需经验
     */
    private function calculateExpRequired($level)
    {
        // 新的经验计算公式：基础经验 * 等级 * (1 + 等级/10)
        // 这样低级时经验需求较少，高级时增长更快
        $baseExp = 1000;
        return floor($baseExp * $level * (1 + $level / 10));
    }
}
