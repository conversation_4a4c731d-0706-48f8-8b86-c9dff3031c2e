@extends('admin.layouts.app')

@section('title', '添加地图')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <a href="{{ route('admin.maps.index') }}" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        添加新地图
    </div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.maps.store') }}">
            @csrf

            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" required lay-verify="required" placeholder="请输入地图名称" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">代码</label>
                <div class="layui-input-block">
                    <input type="text" name="code" value="{{ old('code') }}" required lay-verify="required" placeholder="请输入地图代码" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入地图描述" class="layui-textarea">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">地图类型</label>
                <div class="layui-input-block">
                    <select name="type" lay-verify="required">
                        <option value="city" {{ old('type') == 'city' ? 'selected' : '' }}>城市</option>
                        <option value="wilderness" {{ old('type') == 'wilderness' ? 'selected' : '' }}>野外</option>
                        <option value="dungeon" {{ old('type') == 'dungeon' ? 'selected' : '' }}>副本</option>
                        <option value="instance" {{ old('type') == 'instance' ? 'selected' : '' }}>实例</option>
                        <option value="special" {{ old('type') == 'special' ? 'selected' : '' }}>特殊</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级范围</label>
                <div class="layui-input-inline" style="width: 80px;">
                    <input type="number" name="level_range_min" value="{{ old('level_range_min', 1) }}" required lay-verify="required|number" placeholder="最低" class="layui-input">
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline" style="width: 80px;">
                    <input type="number" name="level_range_max" value="{{ old('level_range_max', 10) }}" required lay-verify="required|number" placeholder="最高" class="layui-input">
                </div>
            </div>

            <input type="hidden" name="danger_level" value="1">

            <div class="layui-form-item">
                <label class="layui-form-label">排序权重</label>
                <div class="layui-input-inline">
                    <input type="number" name="sort_order" value="{{ old('sort_order', 0) }}" required lay-verify="required|number" placeholder="请输入排序权重" class="layui-input">
                </div>
                <div class="layui-form-mid layui-text-em">数值越大排序越靠前</div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_pvp" value="1" title="允许PVP" {{ old('is_pvp') ? 'checked' : '' }}>
                </div>
            </div>

            <input type="hidden" name="weather_enabled" value="0">

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_active" value="1" title="启用" {{ old('is_active', true) ? 'checked' : '' }}>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit>保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 表单验证
    form.verify({
        required: function(value) {
            if (!value) {
                return '必填项不能为空';
            }
        },
        number: function(value) {
            if (!/^\d+(\.\d+)?$/.test(value)) {
                return '必须是数字';
            }
        }
    });
});
</script>
@endsection
