@extends('admin.layouts.app')

@section('title', '添加地图位置')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <a href="{{ route('admin.maps.index') }}" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        添加新地图位置
    </div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.maps.store') }}">
            @csrf
            <!-- 默认使用龙宫区域 -->
            <input type="hidden" name="region_id" value="5">

            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" required lay-verify="required" placeholder="请输入位置名称" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">X坐标</label>
                <div class="layui-input-block">
                    <input type="number" name="x" value="{{ old('x', 0) }}" required lay-verify="required|number" placeholder="请输入X坐标" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">Y坐标</label>
                <div class="layui-input-block">
                    <input type="number" name="y" value="{{ old('y', 0) }}" required lay-verify="required|number" placeholder="请输入Y坐标" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入位置描述" class="layui-textarea">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">位置类型</label>
                <div class="layui-input-block">
                    <select name="type" lay-verify="required">
                        <option value="">请选择位置类型</option>
                        @foreach($typeOptions as $value => $label)
                        <option value="{{ $value }}" {{ old('type') == $value ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级要求</label>
                <div class="layui-input-block">
                    <input type="number" name="level_requirement" value="{{ old('level_requirement', 1) }}" required lay-verify="required|number" placeholder="请输入等级要求" class="layui-input">
                    <div class="layui-form-mid layui-text-em">角色需要达到的最低等级</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_safe" value="1" title="安全区" {{ old('is_safe') ? 'checked' : '' }}>
                    <div class="layui-form-mid layui-text-em">安全区内不能进行战斗</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_active" value="1" title="启用" {{ old('is_active', true) ? 'checked' : '' }}>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit>保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('js')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 表单验证
    form.verify({
        required: function(value) {
            if (!value) {
                return '必填项不能为空';
            }
        },
        number: function(value) {
            if (value && !/^\d+$/.test(value)) {
                return '必须是正整数';
            }
        }
    });

    // 表单提交
    form.on('submit', function(data){
        // 检查必填字段
        if (!data.field.name) {
            layer.msg('请输入位置名称');
            return false;
        }
        if (!data.field.type) {
            layer.msg('请选择位置类型');
            return false;
        }
        if (!data.field.x && data.field.x !== '0') {
            layer.msg('请输入X坐标');
            return false;
        }
        if (!data.field.y && data.field.y !== '0') {
            layer.msg('请输入Y坐标');
            return false;
        }
        if (!data.field.level_requirement) {
            layer.msg('请输入等级要求');
            return false;
        }

        return true;
    });
});
</script>
@endsection
