{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\StorageCleanup.vue?vue&type=style&index=0&id=41f838c2&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\StorageCleanup.vue", "mtime": 1749868572976}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["StorageCleanup.vue"], "names": [], "mappings": ";AAkKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "StorageCleanup.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"storage-cleanup\">\n    <div class=\"header\">\n      <h2>存储空间清理</h2>\n      <p>如果遇到\"请求头过大\"错误，可以使用此工具清理存储空间</p>\n    </div>\n\n    <div class=\"storage-info\">\n      <h3>当前存储状态</h3>\n      <div class=\"info-item\">\n        <span>总存储大小:</span>\n        <span>{{ formatBytes(storageInfo.currentSize) }}</span>\n      </div>\n      <div class=\"info-item\">\n        <span>存储项目数量:</span>\n        <span>{{ storageInfo.keys.length }}</span>\n      </div>\n    </div>\n\n    <div class=\"storage-items\">\n      <h3>存储项目详情</h3>\n      <div class=\"item-list\">\n        <div \n          v-for=\"item in storageItems\" \n          :key=\"item.key\"\n          class=\"storage-item\"\n          :class=\"{ 'large-item': item.size > 50000 }\"\n        >\n          <div class=\"item-info\">\n            <span class=\"item-key\">{{ item.key }}</span>\n            <span class=\"item-size\">{{ formatBytes(item.size) }}</span>\n          </div>\n          <button \n            @click=\"removeItem(item.key)\"\n            class=\"remove-btn\"\n            :disabled=\"item.key === 'szxy-game-state'\"\n          >\n            删除\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"actions\">\n      <button @click=\"clearExpiredCache\" class=\"action-btn\">清理过期缓存</button>\n      <button @click=\"clearAllCache\" class=\"action-btn warning\">清理所有缓存</button>\n      <button @click=\"clearAllStorage\" class=\"action-btn danger\">清空所有存储</button>\n      <button @click=\"refreshInfo\" class=\"action-btn\">刷新信息</button>\n    </div>\n\n    <div class=\"tips\">\n      <h3>使用提示</h3>\n      <ul>\n        <li>如果遇到HTTP 431错误，建议先点击\"清理过期缓存\"</li>\n        <li>红色标记的项目是大型存储项目（>50KB），可能导致请求头过大</li>\n        <li>\"szxy-game-state\"是游戏核心状态，不建议删除</li>\n        <li>清理后需要重新登录和选择大区</li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getStorageInfoSync, clearExpiredCache, cleanupLargeStorageItems } from '@/utils/storage.js'\n\nexport default {\n  name: 'StorageCleanup',\n  data() {\n    return {\n      storageInfo: {\n        keys: [],\n        currentSize: 0\n      },\n      storageItems: []\n    }\n  },\n  mounted() {\n    this.refreshInfo()\n  },\n  methods: {\n    refreshInfo() {\n      this.storageInfo = getStorageInfoSync()\n      this.storageItems = this.getStorageItems()\n    },\n\n    getStorageItems() {\n      const items = []\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        const value = localStorage.getItem(key) || ''\n        items.push({\n          key,\n          size: key.length + value.length,\n          value: value.substring(0, 100) + (value.length > 100 ? '...' : '')\n        })\n      }\n      return items.sort((a, b) => b.size - a.size)\n    },\n\n    formatBytes(bytes) {\n      if (bytes === 0) return '0 Bytes'\n      const k = 1024\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    },\n\n    removeItem(key) {\n      if (confirm(`确定要删除存储项目 \"${key}\" 吗？`)) {\n        localStorage.removeItem(key)\n        this.refreshInfo()\n        this.$message?.success?.('删除成功')\n      }\n    },\n\n    clearExpiredCache() {\n      try {\n        clearExpiredCache()\n        this.refreshInfo()\n        this.$message?.success?.('过期缓存清理完成')\n      } catch (error) {\n        this.$message?.error?.('清理失败: ' + error.message)\n      }\n    },\n\n    clearAllCache() {\n      if (confirm('确定要清理所有缓存吗？这将删除所有以\"SZXY_CACHE_\"开头的存储项目。')) {\n        try {\n          const keys = []\n          for (let i = 0; i < localStorage.length; i++) {\n            const key = localStorage.key(i)\n            if (key && key.startsWith('SZXY_CACHE_')) {\n              keys.push(key)\n            }\n          }\n          keys.forEach(key => localStorage.removeItem(key))\n          this.refreshInfo()\n          this.$message?.success?.(`清理了 ${keys.length} 个缓存项目`)\n        } catch (error) {\n          this.$message?.error?.('清理失败: ' + error.message)\n        }\n      }\n    },\n\n    clearAllStorage() {\n      if (confirm('确定要清空所有本地存储吗？这将删除所有游戏数据，需要重新登录！')) {\n        if (confirm('再次确认：这将删除所有本地数据，包括登录状态、游戏设置等！')) {\n          localStorage.clear()\n          this.refreshInfo()\n          this.$message?.success?.('所有存储已清空')\n          // 3秒后刷新页面\n          setTimeout(() => {\n            window.location.reload()\n          }, 3000)\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.storage-cleanup {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.header {\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.header h2 {\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.storage-info {\n  background: #f5f5f5;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.storage-info h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.storage-items {\n  margin-bottom: 30px;\n}\n\n.storage-items h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.item-list {\n  max-height: 400px;\n  overflow-y: auto;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n\n.storage-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.storage-item.large-item {\n  background-color: #fff5f5;\n  border-left: 4px solid #ff4757;\n}\n\n.item-info {\n  flex: 1;\n  display: flex;\n  justify-content: space-between;\n  margin-right: 15px;\n}\n\n.item-key {\n  font-weight: bold;\n  color: #333;\n}\n\n.item-size {\n  color: #666;\n  font-size: 12px;\n}\n\n.remove-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 5px 10px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n.remove-btn:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.actions {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-bottom: 30px;\n}\n\n.action-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  background: #007bff;\n  color: white;\n}\n\n.action-btn.warning {\n  background: #ffc107;\n  color: #333;\n}\n\n.action-btn.danger {\n  background: #dc3545;\n}\n\n.action-btn:hover {\n  opacity: 0.8;\n}\n\n.tips {\n  background: #e7f3ff;\n  padding: 15px;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n}\n\n.tips h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.tips ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.tips li {\n  margin-bottom: 5px;\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}