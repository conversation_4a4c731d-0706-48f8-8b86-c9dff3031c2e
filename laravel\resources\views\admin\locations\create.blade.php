@extends('admin.layouts.app')

@section('title', '添加位置')

@section('content')
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加位置</div>
        <div class="layui-card-body">
            <form class="layui-form" action="{{ route('admin.maps.locations.save', $map->id) }}" method="POST" lay-filter="location-form">
                @csrf

                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="locations[0][name]" lay-verify="required" placeholder="请输入位置名称" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea name="locations[0][description]" placeholder="请输入描述" class="layui-textarea"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">类型</label>
                    <div class="layui-input-block">
                        <select name="locations[0][type]" lay-verify="required">
                            <option value="town">城镇</option>
                            <option value="dungeon">副本</option>
                            <option value="market">市场</option>
                            <option value="inn">酒馆</option>
                            <option value="temple">寺庙</option>
                            <option value="wilderness">野外</option>
                            <option value="quest">任务点</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">坐标 X</label>
                    <div class="layui-input-inline">
                        <input type="number" name="locations[0][x]" lay-verify="required|number" placeholder="X 坐标" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">像素位置</div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">坐标 Y</label>
                    <div class="layui-input-inline">
                        <input type="number" name="locations[0][y]" lay-verify="required|number" placeholder="Y 坐标" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">像素位置</div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">等级要求</label>
                    <div class="layui-input-inline">
                        <input type="number" name="locations[0][level_requirement]" lay-verify="required|number" value="1" min="1" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item" pane>
                    <label class="layui-form-label">是否安全区</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="locations[0][is_safe]" lay-skin="switch" lay-text="是|否" checked>
                    </div>
                </div>

                <div class="layui-form-item" pane>
                    <label class="layui-form-label">是否启用</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="locations[0][is_active]" lay-skin="switch" lay-text="是|否" checked>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="saveBtn">保存</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;
    var $ = layui.jquery;

    // 表单提交
    form.on('submit(saveBtn)', function(data){
        var index = parent.layer.getFrameIndex(window.name);

        $.ajax({
            url: data.form.action,
            type: 'POST',
            data: data.field,
            dataType: 'json',
            success: function(res){
                if(res.success){
                    layer.msg('保存成功', {icon: 1});
                    setTimeout(function(){
                        parent.layer.close(index);
                        parent.location.reload();
                    }, 1000);
                }else{
                    layer.msg('保存失败：' + res.message, {icon: 2});
                }
            },
            error: function(xhr){
                var errors = xhr.responseJSON;
                if(errors && errors.errors){
                    var errorMsg = '';
                    for(var key in errors.errors){
                        errorMsg += errors.errors[key][0] + '<br>';
                    }
                    layer.msg(errorMsg, {icon: 2});
                }else{
                    layer.msg('保存失败，请稍后再试', {icon: 2});
                }
            }
        });

        return false;
    });
});
</script>
@endsection
