{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue?vue&type=style&index=0&id=0012e22c&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue", "mtime": 1750347852991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5nYW1lLWNvbnRhaW5lciB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogMTAwJTsKICB3aWR0aDogMTAwJTsKICBjb2xvcjogI2ZmZDcwMDsKICBvdmVyZmxvdzogaGlkZGVuOwogIGJhY2tncm91bmQ6IHVybCgnL3N0YXRpYy9nYW1lL1VJL2JnL21haW5fYmcucG5nJykgY2VudGVyIGNlbnRlciBuby1yZXBlYXQ7CiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjsKICAKICAmOjpiZWZvcmUgewogICAgY29udGVudDogJyc7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDA7CiAgICBsZWZ0OiAwOwogICAgcmlnaHQ6IDA7CiAgICBib3R0b206IDA7CiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDAsIDAsIDUwLCAwLjQpLCByZ2JhKDUwLCAwLCAwLCAwLjQpKTsKICAgIHotaW5kZXg6IDA7CiAgfQp9CgoubWFpbi1jb250ZW50IHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiA4cHg7CiAgaGVpZ2h0OiAxMDAlOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICB6LWluZGV4OiAxOwogIHBhZGRpbmc6IDhweDsKfQoKLnRvcC1zZWN0aW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogOHB4OwogIGhlaWdodDogMzIwcHg7CiAgZmxleC1zaHJpbms6IDA7CgogIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogICAgaGVpZ2h0OiAyODBweDsKICAgIGdhcDogNnB4OwogIH0KCiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7CiAgICBoZWlnaHQ6IDI0MHB4OwogICAgZ2FwOiA0cHg7CiAgfQp9CgouYm90dG9tLXNlY3Rpb24gewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDhweDsKICBmbGV4OiAxOwogIG1pbi1oZWlnaHQ6IDA7CgogIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogICAgZ2FwOiA2cHg7CiAgfQoKICBAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsKICAgIGdhcDogNHB4OwogIH0KfQoKLmxlZnQtcGFuZWwsIC5yaWdodC1wYW5lbCB7CiAgYmFja2dyb3VuZDogbm9uZTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYm9yZGVyLXJhZGl1czogMDsKICBvdmVyZmxvdzogaGlkZGVuOwogIGJveC1zaGFkb3c6IG5vbmU7CiAgYm9yZGVyOiBub25lOwp9CgoubGVmdC1wYW5lbCB7CiAgd2lkdGg6IDEyMHB4OwogIG1pbi13aWR0aDogMTAwcHg7CiAgYmFja2dyb3VuZDogbm9uZTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYm9yZGVyLXJhZGl1czogMDsKICBvdmVyZmxvdzogaGlkZGVuOwogIGJveC1zaGFkb3c6IG5vbmU7CiAgYm9yZGVyOiBub25lOwp9CgoucGl4ZWwtYXZhdGFyLWJhcjIgewogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlOwogIAogICY6aG92ZXIgewogICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsKICB9CiAgCiAgJjphY3RpdmUgewogICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk4KTsKICB9Cn0KCi5yaWdodC1wYW5lbCB7CiAgZmxleDogMTsKICBtaW4td2lkdGg6IDA7Cn0KCi5wYW5lbC1oZWFkZXIgewogIGJhY2tncm91bmQ6IG5vbmU7CiAgY29sb3I6IGluaGVyaXQ7CiAgcGFkZGluZzogMDsKICBmb250LXdlaWdodDogbm9ybWFsOwogIGZvbnQtc2l6ZTogaW5oZXJpdDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgdGV4dC1zaGFkb3c6IG5vbmU7CiAgYm9yZGVyLWJvdHRvbTogbm9uZTsKfQoKLmNoYXJhY3Rlci1pbmZvIHsKICBmbGV4OiAxOwogIHBhZGRpbmc6IDZweDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiA2cHg7CiAgYmFja2dyb3VuZDogbm9uZTsKfQoKLmluZm8tZGV0YWlscyB7CiAgZmxleDogMTsKfQoKLmNoYXJhY3Rlci1uYW1lIHsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNmZmQ3MDA7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDFweDsKICB0ZXh0LXNoYWRvdzogbm9uZTsKfQoKLmNoYXJhY3Rlci1sZXZlbCB7CiAgZm9udC1zaXplOiAxMHB4OwogIGNvbG9yOiAjZmZkNzAwOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiA2cHg7CiAgdGV4dC1zaGFkb3c6IG5vbmU7Cn0KCi5zdGF0cyB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogMXB4Owp9Cgouc3RhdC1pdGVtIHsKICBmb250LXNpemU6IDEwcHg7CiAgY29sb3I6ICNmZmQ3MDA7CiAgcGFkZGluZzogMXB4IDA7CiAgdGV4dC1zaGFkb3c6IG5vbmU7Cn0KCi5hdmF0YXItc2VjdGlvbiwgLnN0YXR1cy1iYXJzIHsKICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7Cn0KCi5wYW5lbC1jb250ZW50IHsKICBmbGV4OiAxOwogIHBhZGRpbmc6IDE1cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBiYWNrZ3JvdW5kOiBub25lOwp9CgoubnBjLWNvbnRlbnQsCi5ucGMtY29udGVudCAqIHsKICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7CiAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50OwogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Owp9CgoubnBjLXRleHQtbGlzdCB7CiAgbGlzdC1zdHlsZTogbm9uZTsKICBwYWRkaW5nOiAwOwogIG1hcmdpbjogMDsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlcjogbm9uZTsKfQoKLm5wYy10ZXh0LWxpc3QgbGkgewogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjODg4ICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7CiAgcGFkZGluZzogMnB4IDAgIWltcG9ydGFudDsKfQoKLm5wYy1hY3Rpb24tYnRuIHsKICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7CiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7CiAgY29sb3I6IGluaGVyaXQgIWltcG9ydGFudDsKICBmb250OiBpbmhlcml0ICFpbXBvcnRhbnQ7CiAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50OwogIGJvcmRlci1yYWRpdXM6IDAgIWltcG9ydGFudDsKICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7CiAgbWFyZ2luLWxlZnQ6IDRweCAhaW1wb3J0YW50OwogIGN1cnNvcjogcG9pbnRlcjsKfQoKLm5wYy1hY3Rpb24tYnRuOmhvdmVyLAoubnBjLWFjdGlvbi1idG46YWN0aXZlIHsKICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7CiAgY29sb3I6IGluaGVyaXQgIWltcG9ydGFudDsKICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7CiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7CiAgb3V0bGluZTogbm9uZSAhaW1wb3J0YW50Owp9CgouY2hhcmFjdGVyLWNvbnRlbnQgewogIGhlaWdodDogYXV0bzsKICBwYWRkaW5nOiAwOwogIG92ZXJmbG93OiB2aXNpYmxlOwogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm94LXNoYWRvdzogbm9uZTsKfQoKLmNoYXJhY3Rlci1jYXJkIHsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDA7CiAgcGFkZGluZzogMDsKICBib3gtc2hhZG93OiBub25lOwogIGJvcmRlcjogbm9uZTsKfQoKLmNoYXJhY3Rlci1hdmF0YXItc2VjdGlvbiwKLmNoYXJhY3Rlci1iYXNpYy1pbmZvLAouY2hhcmFjdGVyLXByb2Zlc3Npb24sCi5jaGFyYWN0ZXItYmFycywKLmNoYXJhY3Rlci1zdGF0cywKLmNoYXJhY3Rlci13ZWFsdGgsCi5jaGFyYWN0ZXItZXhwIHsKICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7CiAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50OwogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50OwogIGJvcmRlci1yYWRpdXM6IDAgIWltcG9ydGFudDsKICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7Cn0KCi5jaGFyYWN0ZXItYXZhdGFyLWltZyB7CiAgd2lkdGg6IDYwcHg7CiAgaGVpZ2h0OiA2MHB4OwogIGJvcmRlci1yYWRpdXM6IDEwJTsKICBib3JkZXI6IDJweCBzb2xpZCAjZmZkNzAwOwogIG9iamVjdC1maXQ6IGNvdmVyOwogIGJveC1zaGFkb3c6IG5vbmU7Cn0KCi5jaGFyYWN0ZXItbmFtZSB7CiAgbWFyZ2luOiAwIDAgNHB4IDA7CiAgZm9udC1zaXplOiAxOHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjZmZkNzAwOwogIHRleHQtc2hhZG93OiBub25lOwp9CgouY2hhcmFjdGVyLXByb2Zlc3Npb24gewogIG1hcmdpbjogMDsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICNmZmQ3MDA7CiAgdGV4dC1zaGFkb3c6IG5vbmU7Cn0KCi5jaGFyYWN0ZXItYmFycyB7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKfQoKLmJhci1pdGVtIHsKICBtYXJnaW4tYm90dG9tOiA4cHg7Cn0KCi5iYXItbGFiZWwgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogNHB4OwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogI2ZmZDcwMDsKICB0ZXh0LXNoYWRvdzogbm9uZTsKfQoKLmJhci12YWx1ZSB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5wcm9ncmVzcy1iYXIgewogIGhlaWdodDogOHB4OwogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogMDsKICBvdmVyZmxvdzogaGlkZGVuOwogIGJvcmRlcjogMXB4IHNvbGlkICMwODhiZTI7Cn0KCi5wcm9ncmVzcy1maWxsIHsKICBoZWlnaHQ6IDEwMCU7CiAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOwp9CgouaHAtYmFyIC5wcm9ncmVzcy1maWxsIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICNmZjAwMDAsICNmZjMzMzMpOwp9CgoubXAtYmFyIC5wcm9ncmVzcy1maWxsIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMwODhiZTIsICM0ZWFkZjUpOwp9CgouY2hhcmFjdGVyLXN0YXRzIHsKICBtYXJnaW4tYm90dG9tOiAxNnB4Owp9Cgouc3RhdHMtZ3JpZCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7CiAgZ2FwOiA4cHg7Cn0KCi5zdGF0LWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogNnB4IDhweDsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3JkZXI6IDFweCBzb2xpZCAjMDg4YmUyOwp9Cgouc3RhdC1sYWJlbCB7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjZmZkNzAwOwogIHRleHQtc2hhZG93OiBub25lOwp9Cgouc3RhdC12YWx1ZSB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNmZmQ3MDA7CiAgdGV4dC1zaGFkb3c6IG5vbmU7Cn0KCi5jaGFyYWN0ZXItd2VhbHRoIHsKICBtYXJnaW4tYm90dG9tOiAxNnB4Owp9Cgoud2VhbHRoLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDhweDsKICBwYWRkaW5nOiA4cHg7CiAgYmFja2dyb3VuZDogbm9uZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm9yZGVyOiAxcHggc29saWQgIzA4OGJlMjsKICBtYXJnaW4tYm90dG9tOiA2cHg7Cn0KCi53ZWFsdGgtbGFiZWwgewogIGZsZXg6IDE7CiAgZm9udC1zaXplOiAxM3B4OwogIGNvbG9yOiAjZmZkNzAwOwogIHRleHQtc2hhZG93OiBub25lOwp9Cgoud2VhbHRoLXZhbHVlIHsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogI2ZmZDcwMDsKICB0ZXh0LXNoYWRvdzogbm9uZTsKfQoKLmNoYXJhY3Rlci1leHAgewogIHBhZGRpbmc6IDhweDsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3JkZXI6IDFweCBzb2xpZCAjMDg4YmUyOwp9CgouZXhwLWxhYmVsIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGZvbnQtc2l6ZTogMTNweDsKICBjb2xvcjogI2ZmZDcwMDsKICB0ZXh0LXNoYWRvdzogbm9uZTsKfQoKLmV4cC12YWx1ZSB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNmZmQ3MDA7CiAgdGV4dC1zaGFkb3c6IG5vbmU7Cn0KCi5kZXRhaWwtaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiA4cHggMTJweDsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3JkZXI6IDFweCBzb2xpZCAjMDg4YmUyOwoKICAubGFiZWwgewogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICBjb2xvcjogI2ZmZDcwMDsKICAgIHRleHQtc2hhZG93OiBub25lOwogIH0KCiAgLnZhbHVlIHsKICAgIGNvbG9yOiAjZmZkNzAwOwogICAgdGV4dC1zaGFkb3c6IG5vbmU7CiAgfQp9CgouZmFjaWxpdGllcy1jb250ZW50IHsKICBoZWlnaHQ6IDEwMCU7CiAgYmFja2dyb3VuZDogbm9uZSAhaW1wb3J0YW50Owp9CgouZmFjaWxpdHktbGlzdCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogOHB4Owp9CgouZmFjaWxpdHktaXRlbSB7CiAgcGFkZGluZzogOHB4IDA7CiAgYmFja2dyb3VuZDogbm9uZTsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogMDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKfQoKLm1vdmUtY29udGVudCB7CiAgaGVpZ2h0OiAxMDAlOwogIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDsKfQoKLmxvY2F0aW9uLWxpc3QgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDNweDsKfQoKLmxvY2F0aW9uLWl0ZW0gewogIHBhZGRpbmc6IDZweDsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiAwOwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwp9CgouZnVuY3Rpb25zLWNvbnRlbnQgewogIGhlaWdodDogMTAwJTsKICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7Cn0KCi5mdW5jdGlvbi1saXN0IHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiA4cHg7Cn0KCi5mdW5jdGlvbi1pdGVtIHsKICBwYWRkaW5nOiAxMnB4OwogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyOiAxcHggc29saWQgIzA4OGJlMjsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CgogICY6aG92ZXIgewogICAgYmFja2dyb3VuZDogbm9uZTsKICAgIGJvcmRlci1jb2xvcjogI2ZmZDcwMDsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKICAgIGJveC1zaGFkb3c6IG5vbmU7CiAgfQoKICAuZnVuY3Rpb24tbmFtZSB7CiAgICBkaXNwbGF5OiBibG9jazsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgY29sb3I6ICNmZmQ3MDA7CiAgICBtYXJnaW4tYm90dG9tOiA0cHg7CiAgICB0ZXh0LXNoYWRvdzogbm9uZTsKICB9CgogIC5mdW5jdGlvbi1kZXNjIHsKICAgIGRpc3BsYXk6IGJsb2NrOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgY29sb3I6ICNjY2M7CiAgfQp9CgouZnVuY3Rpb24tYmFyIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogOHB4OwogIGhlaWdodDogNTBweDsKICBwYWRkaW5nOiAwOwogIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDsKICBib3JkZXItcmFkaXVzOiAwOwogIGJvcmRlcjogbm9uZTsKICBmbGV4LXNocmluazogMDsKICBvdmVyZmxvdzogdmlzaWJsZTsKfQoKLmZ1bmN0aW9uLWJ0biB7CiAgZmxleDogMTsKICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsKICBib3JkZXI6IG5vbmU7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgbWluLXdpZHRoOiAwOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBwYWRkaW5nOiAycHg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwoKICAmOmhvdmVyIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICAgIGJhY2tncm91bmQ6IG5vbmU7CiAgfQoKICAmOmFjdGl2ZSB7CiAgICBiYWNrZ3JvdW5kOiBub25lOwogICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTsKICB9CgogICYuYWN0aXZlIHsKICAgIGJhY2tncm91bmQ6IG5vbmU7CiAgICAmOjphZnRlciB7CiAgICAgIGNvbnRlbnQ6ICcnOwogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIGJvdHRvbTogLTNweDsKICAgICAgbGVmdDogMjUlOwogICAgICByaWdodDogMjUlOwogICAgICBoZWlnaHQ6IDNweDsKICAgICAgYmFja2dyb3VuZDogI2ZmZDcwMDsKICAgICAgYm9yZGVyLXJhZGl1czogM3B4OwogICAgfQogIH0KfQoKLmZ1bmN0aW9uLWJ0bi1pbWFnZSB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIG9iamVjdC1maXQ6IGNvbnRhaW47CiAgbWF4LXdpZHRoOiAxMDAlOwogIG1heC1oZWlnaHQ6IDEwMCU7Cn0KCi5vbmxpbmUtcGxheWVycyB7CiAgYm9yZGVyOiBub25lOwogIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDsKICBoZWlnaHQ6IDcwcHg7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGJvcmRlci1yYWRpdXM6IDA7CiAgYm94LXNoYWRvdzogbm9uZTsKICBmbGV4LXNocmluazogMDsKfQoKLnNlY3Rpb24tdGl0bGUgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzA4NDA3YSwgIzA4OGJlMik7CiAgY29sb3I6ICNmZmQ3MDA7CiAgcGFkZGluZzogNnB4IDEycHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgZm9udC1zaXplOiAxM3B4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICB0ZXh0LXNoYWRvdzogbm9uZTsKICBib3JkZXItYm90dG9tOiBub25lOwoKICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAgIHBhZGRpbmc6IDVweCAxMHB4OwogICAgZm9udC1zaXplOiAxMnB4OwogIH0KCiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7CiAgICBwYWRkaW5nOiA0cHggOHB4OwogICAgZm9udC1zaXplOiAxMXB4OwogIH0KfQoKLnBsYXllcnMtYXZhdGFycyB7CiAgZmxleDogMTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogOHB4IDEycHg7CiAgZ2FwOiA4cHg7CiAgb3ZlcmZsb3cteDogYXV0bzsKICBiYWNrZ3JvdW5kOiBub25lOwoKICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAgIHBhZGRpbmc6IDZweCAxMHB4OwogICAgZ2FwOiA2cHg7CiAgfQoKICBAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsKICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICBnYXA6IDRweDsKICB9Cn0KCi5wbGF5ZXItYXZhdGFyIHsKICB3aWR0aDogNDBweDsKICBoZWlnaHQ6IDQwcHg7CiAgYm9yZGVyLXJhZGl1czogMTAlOwogIGJvcmRlcjogMnB4IHNvbGlkICNmZmQ3MDA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBmbGV4LXNocmluazogMDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICBib3gtc2hhZG93OiBub25lOwoKICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAgIHdpZHRoOiAzNXB4OwogICAgaGVpZ2h0OiAzNXB4OwogIH0KCiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7CiAgICB3aWR0aDogMzBweDsKICAgIGhlaWdodDogMzBweDsKICB9CgogIGltZyB7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMTAwJTsKICAgIG9iamVjdC1maXQ6IGNvdmVyOwogIH0KCiAgJjpob3ZlciB7CiAgICBib3JkZXItY29sb3I6ICMwODhiZTI7CiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7CiAgICBib3gtc2hhZG93OiBub25lOwogIH0KfQoKLmNoYXQtc2VjdGlvbiB7CiAgYm9yZGVyOiBub25lOwogIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDsKICBoZWlnaHQ6IDQyMHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBib3JkZXItcmFkaXVzOiAwOwogIGJveC1zaGFkb3c6IG5vbmU7CiAgZmxleC1zaHJpbms6IDA7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi8qIOiBiuWkqeWMuuWfn+eOsOWcqOS9v+eUqEdhbWVDaGF057uE5Lu277yM56e76Zmk5pen55qE5qC35byPICovCgpAbWVkaWEgKG9yaWVudGF0aW9uOiBsYW5kc2NhcGUpIGFuZCAobWF4LWhlaWdodDogNjAwcHgpIHsKICAudG9wLXNlY3Rpb24gewogICAgaGVpZ2h0OiAyMDBweDsKICB9CgogIC5vbmxpbmUtcGxheWVycyB7CiAgICBoZWlnaHQ6IDYwcHg7CiAgfQoKICAuY2hhdC1zZWN0aW9uIHsKICAgIGhlaWdodDogMzAwcHg7CiAgfQoKICAuZnVuY3Rpb24tYmFyIHsKICAgIGhlaWdodDogNDBweDsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiAzMjBweCkgewogIC5tYWluLWNvbnRlbnQgewogICAgcGFkZGluZzogNHB4OwogICAgZ2FwOiA0cHg7CiAgfQoKICAubGVmdC1wYW5lbCB7CiAgICB3aWR0aDogMTQwcHg7CiAgICBtaW4td2lkdGg6IDEyMHB4OwogIH0KCiAgLnBsYXllci1hdmF0YXIgewogICAgd2lkdGg6IDI1cHg7CiAgICBoZWlnaHQ6IDI1cHg7CiAgfQoKICAuZnVuY3Rpb24tYnRuIHsKICAgIGZvbnQtc2l6ZTogMTBweDsKICB9CgogIC5wYW5lbC1oZWFkZXIgewogICAgZm9udC1zaXplOiAxMHB4OwogICAgcGFkZGluZzogM3B4IDZweDsKICB9Cn0KCkBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIHsKICAubWFpbi1jb250ZW50IHsKICAgIG1heC13aWR0aDogMTAwMHB4OwogICAgbWFyZ2luOiAwIGF1dG87CiAgICBwYWRkaW5nOiAyMHB4OwogIH0KCiAgLnRvcC1zZWN0aW9uIHsKICAgIGhlaWdodDogMzUwcHg7CiAgfQoKICAubGVmdC1wYW5lbCB7CiAgICB3aWR0aDogMzIwcHg7CiAgfQoKICAucGxheWVyLWF2YXRhciB7CiAgICB3aWR0aDogMTAwcHg7CiAgICBoZWlnaHQ6IDEwMHB4OwogIH0KCiAgLmZ1bmN0aW9uLWJhciB7CiAgICBoZWlnaHQ6IDYwcHg7CiAgfQoKICAuZnVuY3Rpb24tYnRuIHsKICAgIGZvbnQtc2l6ZTogMTZweDsKICB9CgogIC5vbmxpbmUtcGxheWVycyB7CiAgICBoZWlnaHQ6IDEwMHB4OwogIH0KCiAgLmNoYXQtc2VjdGlvbiB7CiAgICBoZWlnaHQ6IDQyMHB4OwogIH0KfQoKQG1lZGlhIChob3Zlcjogbm9uZSkgYW5kIChwb2ludGVyOiBjb2Fyc2UpIHsKICAuZnVuY3Rpb24tYnRuLCAuY2hhdC10YWIsIC5ucGMtaXRlbSwgLnBsYXllci1hdmF0YXIgewogICAgbWluLWhlaWdodDogNDRweDsKICB9CgogIC5mdW5jdGlvbi1idG46YWN0aXZlIHsKICAgIGJhY2tncm91bmQ6ICMwODhiZTI7CiAgICB0cmFuc2Zvcm06IHNjYWxlKDAuOTUpOwogIH0KCiAgLmNoYXQtdGFiOmFjdGl2ZSB7CiAgICBiYWNrZ3JvdW5kOiAjMDg4YmUyOwogIH0KfQoKLm5wYy1hY3Rpb25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOwogIG1hcmdpbi10b3A6IDEwcHg7Cn0KLm5wYy1hY3Rpb24tYnRuIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMwODQwN2EsICMwODhiZTIpOwogIGNvbG9yOiAjZmZkNzAwOwogIGJvcmRlcjogbm9uZTsKICBwYWRkaW5nOiA4cHggMTZweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIHRleHQtc2hhZG93OiBub25lOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7Cn0KLm5wYy1hY3Rpb24tYnRuOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMwODQwN2EsICMwODhiZTIpOwogIHRyYW5zZm9ybTogbm9uZTsKfQoubnBjLWFjdGlvbi1idG46YWN0aXZlIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMwODQwN2EsICMwODhiZTIpOwogIHRyYW5zZm9ybTogbm9uZTsKfQoKLnBpeGVsLWF2YXRhci1iYXIyIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgd2lkdGg6IDE4MHB4OwogIGhlaWdodDogNjBweDsKICBtYXJnaW46IDAgYXV0byA4cHggYXV0bzsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsKfQoucGl4ZWwtYmctaW1nMiB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGxlZnQ6IDA7CiAgdG9wOiAwOwogIHdpZHRoOiAxODBweDsKICBoZWlnaHQ6IDYwcHg7CiAgei1pbmRleDogMDsKICBwb2ludGVyLWV2ZW50czogbm9uZTsKfQoucGl4ZWwtYXZhdGFyLWltZzIgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBsZWZ0OiA2cHg7CiAgYm90dG9tOiA2cHg7CiAgd2lkdGg6IDM4cHg7CiAgaGVpZ2h0OiAzOHB4OwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBib3JkZXI6IDJweCBzb2xpZCAjZmZkNzAwOwogIGJhY2tncm91bmQ6ICMyMjI7CiAgei1pbmRleDogMjsKICBvYmplY3QtZml0OiBjb3ZlcjsKICBib3gtc2hhZG93OiBub25lOwp9Ci5waXhlbC1uYW1lMiB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGxlZnQ6IDA7CiAgdG9wOiA0cHg7CiAgd2lkdGg6IDE4MHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogI2ZmZjsKICBmb250LXNpemU6IDE4cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgdGV4dC1zaGFkb3c6IG5vbmU7CiAgei1pbmRleDogMzsKICBwb2ludGVyLWV2ZW50czogbm9uZTsKfQoucGl4ZWwtYmFyczIgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBsZWZ0OiA1NHB4OwogIHRvcDogMjZweDsKICB3aWR0aDogMTE2cHg7CiAgei1pbmRleDogMjsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiA0cHg7Cn0KLnBpeGVsLWJhcjIgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTJweDsKICBiYWNrZ3JvdW5kOiAjMjIyOwogIGJvcmRlcjogMS41cHggc29saWQgI2ZmZDcwMDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBtYXJnaW4tYm90dG9tOiAycHg7Cn0KLnBpeGVsLWJhci1pbm5lcjIgewogIGhlaWdodDogMTAwJTsKICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoucGl4ZWwtaHAtaW5uZXIyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICNmZjNjM2MsICNmZmIwM2MpOwp9Ci5waXhlbC1tcC1pbm5lcjIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzNjN2NmZiwgI2IwZTBmZik7Cn0KCiogewogIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsKfQpidXR0b24sIFt0eXBlPSJidXR0b24iXSwgW3R5cGU9InN1Ym1pdCJdLCBbdHlwZT0icmVzZXQiXSwgLmZ1bmN0aW9uLWJ0biwgLm5wYy1hY3Rpb24tYnRuLCAuY2hhdC10YWIsIC5wbGF5ZXItYXZhdGFyLCAubG9jYXRpb24taXRlbSwgLmZhY2lsaXR5LWl0ZW0sIC5mdW5jdGlvbi1pdGVtLCAucGFuZWwtaGVhZGVyLCAuc2VjdGlvbi10aXRsZSwgLnN0YXQtaXRlbSwgLmNoYXJhY3Rlci1jYXJkLCAuY2hhcmFjdGVyLWNvbnRlbnQsIC5wYW5lbC1jb250ZW50LCAucmlnaHQtcGFuZWwsIC5sZWZ0LXBhbmVsIHsKICBvdXRsaW5lOiBub25lICFpbXBvcnRhbnQ7CiAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50OwogIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDsKfQpidXR0b246YWN0aXZlLCBidXR0b246Zm9jdXMsIC5mdW5jdGlvbi1idG46YWN0aXZlLCAuZnVuY3Rpb24tYnRuOmZvY3VzLCAubnBjLWFjdGlvbi1idG46YWN0aXZlLCAubnBjLWFjdGlvbi1idG46Zm9jdXMsIC5jaGF0LXRhYjphY3RpdmUsIC5jaGF0LXRhYjpmb2N1cywgLnBsYXllci1hdmF0YXI6YWN0aXZlLCAucGxheWVyLWF2YXRhcjpmb2N1cywgLmxvY2F0aW9uLWl0ZW06YWN0aXZlLCAubG9jYXRpb24taXRlbTpmb2N1cywgLmZhY2lsaXR5LWl0ZW06YWN0aXZlLCAuZmFjaWxpdHktaXRlbTpmb2N1cywgLmZ1bmN0aW9uLWl0ZW06YWN0aXZlLCAuZnVuY3Rpb24taXRlbTpmb2N1cywgLmxvY2F0aW9uLWl0ZW0tc2ltcGxlOmFjdGl2ZSwgLmxvY2F0aW9uLWl0ZW0tc2ltcGxlOmZvY3VzIHsKICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7CiAgb3V0bGluZTogbm9uZSAhaW1wb3J0YW50OwogIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDsKICBjb2xvcjogaW5oZXJpdCAhaW1wb3J0YW50Owp9CgovKiDpmLLmraLomZrmi5/plK7nm5jlvLnlh7ogKi8KKiB7CiAgLXdlYmtpdC11c2VyLXNlbGVjdDogbm9uZSAhaW1wb3J0YW50OwogIC1tb3otdXNlci1zZWxlY3Q6IG5vbmUgIWltcG9ydGFudDsKICAtbXMtdXNlci1zZWxlY3Q6IG5vbmUgIWltcG9ydGFudDsKICB1c2VyLXNlbGVjdDogbm9uZSAhaW1wb3J0YW50OwogIC13ZWJraXQtdG91Y2gtY2FsbG91dDogbm9uZSAhaW1wb3J0YW50OwogIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsKfQoKLyog56aB55So5omA5pyJ5YWD57Sg55qE54Sm54K5ICovCio6Zm9jdXMgewogIG91dGxpbmU6IG5vbmUgIWltcG9ydGFudDsKICAtd2Via2l0LXRhcC1oaWdobGlnaHQtY29sb3I6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7Cn0KCi8qIOa3u+WKoOWFreWuq+agvOW4g+WxgOagt+W8jyAqLwouc2l4LWdyaWQgewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgMWZyKTsKICBncmlkLXRlbXBsYXRlLXJvd3M6IHJlcGVhdCgyLCAxZnIpOwogIGdhcDogNHB4OwogIGp1c3RpZnktaXRlbXM6IGNlbnRlcjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9Cgouc2l4LWdyaWQgLmZhY2lsaXR5LWl0ZW0gewogIHdpZHRoOiAxMDAlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNmZmQ3MDA7CiAgYmFja2dyb3VuZDogbm9uZTsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogMDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgcGFkZGluZzogOHB4IDA7CiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjJzOwp9Cgouc2l4LWdyaWQgLmZhY2lsaXR5LWl0ZW06YWN0aXZlIHsKICBiYWNrZ3JvdW5kOiBub25lOwp9CgovKiDmt7vliqDlip/og73lrqvmoLzluIPlsYDmoLflvI8gKi8KLmZ1bmN0aW9ucy1ncmlkIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7CiAgZ3JpZC10ZW1wbGF0ZS1yb3dzOiByZXBlYXQoNCwgMWZyKTsKICBnYXA6IDRweDsKICBqdXN0aWZ5LWl0ZW1zOiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmZ1bmN0aW9ucy1ncmlkIC5mdW5jdGlvbi1pdGVtIHsKICB3aWR0aDogMTAwJTsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgZm9udC1zaXplOiAxNXB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjZmZkNzAwOwogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDA7CiAgY3Vyc29yOiBwb2ludGVyOwogIHBhZGRpbmc6IDhweCAwOwogIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4yczsKfQoKLmZ1bmN0aW9ucy1ncmlkIC5mdW5jdGlvbi1pdGVtOmFjdGl2ZSB7CiAgYmFja2dyb3VuZDogbm9uZTsKfQoKLnBpeGVsLWluZm8tYm94IHsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDUwLCAwLjcpOwogIGJvcmRlcjogMXB4IHNvbGlkICM1NTU1ZmY7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIHBhZGRpbmc6IDhweDsKICBtYXJnaW4tdG9wOiA4cHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjZmZmZmZmOwp9CgoucGl4ZWwtcm93IHsKICBtYXJnaW4tYm90dG9tOiA0cHg7CiAgbGluZS1oZWlnaHQ6IDEuMjsKfQoKLnBpeGVsLWxhYmVsLWdvbGQgewogIGNvbG9yOiAjZmZjYzAwOwp9CgoucGl4ZWwtdmFsdWUtZ29sZCB7CiAgY29sb3I6ICNmZmNjMDA7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5waXhlbC1leHAtbGFiZWwgewogIG1hcmdpbi10b3A6IDZweDsKICBjb2xvcjogIzAwZmZmZjsKfQoKLnBpeGVsLWV4cC1iYXIgewogIGhlaWdodDogNnB4OwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgNTAsIDAuNSk7CiAgYm9yZGVyOiAxcHggc29saWQgIzU1NTVmZjsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgbWFyZ2luLXRvcDogMnB4OwogIG1hcmdpbi1ib3R0b206IDZweDsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLnBpeGVsLWV4cC1pbm5lciB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGhlaWdodDogMTAwJTsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsICMwMGZmZmYsICMwMGFhZmYpOwogIGxlZnQ6IDA7CiAgdG9wOiAwOwp9CgovKiBOUEPlkozmgKrnianmmL7npLrmoLflvI8gKi8KLmVudGl0eS1zZWN0aW9uIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9Cgouc2VjdGlvbi1oZWFkZXIgewogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogI2Q0YWYzNzsKICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIHBhZGRpbmc6IDVweCAxMHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMjEyLCAxNzUsIDU1LCAwLjEpOwogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgI2Q0YWYzNzsKICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjgpOwp9CgouZW50aXR5LWxpc3QgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDhweDsKfQoKLmVudGl0eS1yb3cgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgcGFkZGluZzogOHB4IDEycHg7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjMpOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKfQoKLmVudGl0eS1yb3c6aG92ZXIgewogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTsKICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsKfQoKLm5wYy1yb3c6aG92ZXIgewogIGJvcmRlci1jb2xvcjogcmdiYSgxMzUsIDIwNiwgMjM1LCAwLjUpOwp9CgoubW9uc3Rlci1yb3c6aG92ZXIgewogIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDEwNywgMTA3LCAwLjUpOwp9CgouZW50aXR5LWxlZnQgewogIGZsZXg6IDE7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKfQoKLmVudGl0eS1yaWdodCB7CiAgZmxleC1zaHJpbms6IDA7Cn0KCi5lbnRpdHktbmFtZSB7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjZmZmOwogIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZTsKfQoKLmVudGl0eS1uYW1lLmNsaWNrYWJsZTpob3ZlciB7CiAgY29sb3I6ICM4N2NlZWI7Cn0KCi5tb25zdGVyLW5hbWUgewogIGNvbG9yOiAjZmY2YjZiOwp9CgoubW9uc3Rlci1uYW1lLmNsaWNrYWJsZTpob3ZlciB7CiAgY29sb3I6ICNmZjhhOGE7Cn0KCi5lbnRpdHktbGV2ZWwgewogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogI2ZmZDcwMDsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjE1LCAwLCAwLjIpOwogIHBhZGRpbmc6IDJweCA2cHg7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpOwogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB3aWR0aDogZml0LWNvbnRlbnQ7Cn0KCi5lbnRpdHktc2VydmljZXMgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwogIGdhcDogNHB4OwogIG1hcmdpbi10b3A6IDJweDsKfQoKLnNlcnZpY2UtdGFnIHsKICBmb250LXNpemU6IDEwcHg7CiAgY29sb3I6ICM4N2NlZWI7CiAgYmFja2dyb3VuZDogcmdiYSgxMzUsIDIwNiwgMjM1LCAwLjIpOwogIHBhZGRpbmc6IDFweCA0cHg7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMTM1LCAyMDYsIDIzNSwgMC4zKTsKfQoKLmVudGl0eS10eXBlIHsKICBmb250LXNpemU6IDExcHg7CiAgY29sb3I6ICNjY2M7CiAgZm9udC1zdHlsZTogaXRhbGljOwp9CgouYWN0aW9uLWJ0biB7CiAgcGFkZGluZzogNnB4IDEycHg7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBmb250LXNpemU6IDEycHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgbWluLXdpZHRoOiA2MHB4Owp9CgoubnBjLWFjdGlvbi1idG4gewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM4N2NlZWIsICM1ZjllYTApOwogIGNvbG9yOiB3aGl0ZTsKICBib3JkZXI6IDFweCBzb2xpZCAjODdjZWViOwp9CgoubnBjLWFjdGlvbi1idG46aG92ZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1ZjllYTAsICM0NjgyYjQpOwogIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMTM1LCAyMDYsIDIzNSwgMC41KTsKfQoKLmJhdHRsZS1idG4gewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjQ3NTcsICNmZjM3NDIpOwogIGNvbG9yOiB3aGl0ZTsKICBib3JkZXI6IDFweCBzb2xpZCAjZmY2YjZiOwp9CgouYmF0dGxlLWJ0bjpob3ZlciB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmMzc0MiwgI2ZmMmYzYSk7CiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgyNTUsIDcxLCA4NywgMC41KTsKfQoKLmVtcHR5LXN0YXRlIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNDBweCAyMHB4OwogIGNvbG9yOiAjNjY2Owp9CgouZW1wdHktbWVzc2FnZSB7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtc3R5bGU6IGl0YWxpYzsKfQoKLyog5a6e5L2T5L+h5oGv5by556qX5qC35byPICovCi5lbnRpdHktbW9kYWwtb3ZlcmxheSB7CiAgcG9zaXRpb246IGZpeGVkOwogIHRvcDogMDsKICBsZWZ0OiAwOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNyk7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIHotaW5kZXg6IDEwMDA7CiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDVweCk7Cn0KCi5lbnRpdHktbW9kYWwgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyYzNlNTAsICMzNDQ5NWUpOwogIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgYm9yZGVyOiAycHggc29saWQgI2Q0YWYzNzsKICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuNSk7CiAgbWF4LXdpZHRoOiA1MDBweDsKICB3aWR0aDogOTAlOwogIG1heC1oZWlnaHQ6IDgwdmg7CiAgb3ZlcmZsb3cteTogYXV0bzsKfQoKLm1vZGFsLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgYmFja2dyb3VuZDogcmdiYSgyMTIsIDE3NSwgNTUsIDAuMSk7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMjEyLCAxNzUsIDU1LCAwLjMpOwp9CgoubW9kYWwtaGVhZGVyIGgzIHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICNkNGFmMzc7CiAgZm9udC1zaXplOiAxOHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIHRleHQtc2hhZG93OiAxcHggMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuOCk7Cn0KCi5jbG9zZS1idG4gewogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyOiBub25lOwogIGNvbG9yOiAjZmZmOwogIGZvbnQtc2l6ZTogMjRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgcGFkZGluZzogMDsKICB3aWR0aDogMzBweDsKICBoZWlnaHQ6IDMwcHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGJvcmRlci1yYWRpdXM6IDUwJTsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwp9CgouY2xvc2UtYnRuOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7CiAgY29sb3I6ICNmZjZiNmI7Cn0KCi5tb2RhbC1jb250ZW50IHsKICBwYWRkaW5nOiAyMHB4Owp9CgouaW5mby1yb3cgewogIGRpc3BsYXk6IGZsZXg7CiAgbWFyZ2luLWJvdHRvbTogMTJweDsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKfQoKLmluZm8tcm93LmRlc2NyaXB0aW9uIHsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogNXB4Owp9CgouaW5mby1sYWJlbCB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNkNGFmMzc7CiAgbWluLXdpZHRoOiA4MHB4OwogIG1hcmdpbi1yaWdodDogMTBweDsKfQoKLmluZm8tdmFsdWUgewogIGNvbG9yOiAjZmZmOwogIGZsZXg6IDE7Cn0KCi50aHJlYXQtbGV2ZWwgewogIGNvbG9yOiAjZmY2YjZiOwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9Cgouc3RhdHMtc2VjdGlvbiB7CiAgbWFyZ2luLXRvcDogMTVweDsKICBwYWRkaW5nLXRvcDogMTVweDsKICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOwp9Cgouc3RhdHMtdGl0bGUgewogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjZDRhZjM3OwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgZm9udC1zaXplOiAxNHB4Owp9Cgouc3RhdHMtZ3JpZCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7CiAgZ2FwOiA4cHg7Cn0KCi5zdGF0LWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIHBhZGRpbmc6IDRweCA4cHg7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjMpOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLnN0YXQtbGFiZWwgewogIGNvbG9yOiAjY2NjOwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLnN0YXQtdmFsdWUgewogIGNvbG9yOiAjZmZmOwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLyog56e75Yqo5Yqf6IO95qC35byPICovCi5tb3ZlLWNvbnRlbnQgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDE1cHg7CiAgaGVpZ2h0OiAxMDAlOwogIG92ZXJmbG93LXk6IGF1dG87CiAgcGFkZGluZzogMTBweDsKfQoKCgoubG9jYXRpb24tZGVzY3JpcHRpb24gewogIGZvbnQtc2l6ZTogMTFweDsKICBjb2xvcjogI2NjY2NjYzsKICBsaW5lLWhlaWdodDogMS40Owp9CgoubW92ZW1lbnQtb3B0aW9ucyB7CiAgZmxleDogMTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAxMnB4Owp9Cgouc2VjdGlvbi10aXRsZSB7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjODhjY2ZmOwogIG1hcmdpbi1ib3R0b206IDhweDsKICBwYWRkaW5nLWJvdHRvbTogNHB4Owp9CgoubG9jYXRpb24tbGlzdCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogNnB4Owp9CgoubG9jYXRpb24taXRlbSB7CiAgcGFkZGluZzogMTJweDsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMik7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDRweDsKfQoKLmxvY2F0aW9uLWl0ZW06aG92ZXI6bm90KC5kaXNhYmxlZCkgewogIGJhY2tncm91bmQ6IG5vbmU7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwp9CgoubG9jYXRpb24taXRlbS5kaXNhYmxlZCB7CiAgb3BhY2l0eTogMC41OwogIGN1cnNvcjogbm90LWFsbG93ZWQ7Cn0KCi8qIOeugOWMlueahOWcsOWbvuWIl+ihqOagt+W8jyAqLwoubG9jYXRpb24taXRlbS1zaW1wbGUgewogIHBhZGRpbmc6IDhweCAxMnB4OwogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjNDQ0OwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOwogIGNvbG9yOiAjZmZkNzAwOwogIGZvbnQtc2l6ZTogMTRweDsKICB0ZXh0LWFsaWduOiBsZWZ0OwogIGRpc3BsYXk6IGJsb2NrOwogIHdpZHRoOiAxMDAlOwp9CgoubG9jYXRpb24taXRlbS1zaW1wbGU6aG92ZXI6bm90KC5kaXNhYmxlZCkgewogIGJhY2tncm91bmQ6IG5vbmU7CiAgY29sb3I6ICNmZmZmZmY7Cn0KCi5sb2NhdGlvbi1pdGVtLXNpbXBsZS5kaXNhYmxlZCB7CiAgb3BhY2l0eTogMC41OwogIGN1cnNvcjogbm90LWFsbG93ZWQ7Cn0KCi5sb2NhdGlvbi1pdGVtLXNpbXBsZTpsYXN0LWNoaWxkIHsKICBib3JkZXItYm90dG9tOiBub25lOwp9CgoKCgoKCgoubm8tbG9jYXRpb25zLCAubG9hZGluZy1sb2NhdGlvbnMgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4OwogIGNvbG9yOiAjODg4ODg4OwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLmhpc3RvcnktbGlzdCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogNHB4Owp9CgovKiDlm5vooYzluIPlsYDmoLflvI8gKi8KLmZvdXItcm93LWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGhlaWdodDogMTAwJTsKICBnYXA6IDA7Cn0KCi5lbnRpdHktcm93LWNvbnRhaW5lciB7CiAgZmxleDogMTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMnB4IDhweDsKICBtaW4taGVpZ2h0OiAyMHB4Owp9CgouZW50aXR5LWxpc3QtaG9yaXpvbnRhbCB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDE1cHg7CiAgd2lkdGg6IDEwMCU7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmVudGl0eS1pdGVtLWNvbXBhY3QgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDhweDsKICBmbGV4OiAxOwp9CgouZW50aXR5LWl0ZW0tY29tcGFjdCAuZW50aXR5LW5hbWUgewogIGZvbnQtc2l6ZTogMTJweDsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogI2ZmZjsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogY29sb3IgMC4zcyBlYXNlOwogIGZsZXg6IDE7CiAgbGluZS1oZWlnaHQ6IDEuMjsKfQoKLmVudGl0eS1pdGVtLWNvbXBhY3QgLmVudGl0eS1uYW1lOmhvdmVyIHsKICBjb2xvcjogIzg3Y2VlYjsKfQoKLmVudGl0eS1pdGVtLWNvbXBhY3QgLm1vbnN0ZXItbmFtZSB7CiAgY29sb3I6ICNmZjZiNmI7Cn0KCi5lbnRpdHktaXRlbS1jb21wYWN0IC5tb25zdGVyLW5hbWU6aG92ZXIgewogIGNvbG9yOiAjZmY4YThhOwp9CgouYWN0aW9uLWJ0bi1jb21wYWN0IHsKICBwYWRkaW5nOiA0cHggOHB4OwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgZm9udC1zaXplOiAxMXB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwogIG1pbi13aWR0aDogNDBweDsKfQoKLmFjdGlvbi1idG4tY29tcGFjdC5ucGMtYWN0aW9uLWJ0biB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzg3Y2VlYiwgIzVmOWVhMCk7CiAgY29sb3I6IHdoaXRlOwogIGJvcmRlcjogMXB4IHNvbGlkICM4N2NlZWI7Cn0KCi5hY3Rpb24tYnRuLWNvbXBhY3QubnBjLWFjdGlvbi1idG46aG92ZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1ZjllYTAsICM0NjgyYjQpOwogIGJveC1zaGFkb3c6IDAgMCA4cHggcmdiYSgxMzUsIDIwNiwgMjM1LCAwLjQpOwp9CgouYWN0aW9uLWJ0bi1jb21wYWN0LmJhdHRsZS1idG4gewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjQ3NTcsICNmZjM3NDIpOwogIGNvbG9yOiB3aGl0ZTsKICBib3JkZXI6IDFweCBzb2xpZCAjZmY2YjZiOwp9CgouYWN0aW9uLWJ0bi1jb21wYWN0LmJhdHRsZS1idG46aG92ZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjM3NDIsICNmZjJmM2EpOwogIGJveC1zaGFkb3c6IDAgMCA4cHggcmdiYSgyNTUsIDcxLCA4NywgMC40KTsKfQoKLmVtcHR5LXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKfQoKLmVtcHR5LXRleHQgewogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTFweDsKICBmb250LXN0eWxlOiBpdGFsaWM7CiAgbGluZS1oZWlnaHQ6IDEuMTsKfQoKLmRpdmlkZXItbGluZSB7CiAgaGVpZ2h0OiAxcHg7CiAgYmFja2dyb3VuZDogIzY2NjsKICBtYXJnaW46IDAgOHB4OwogIGJvcmRlcjogbm9uZTsKfQoKLyog5Y2V6KGM5a6e5L2T5pi+56S65qC35byPICovCi5lbnRpdHktcm93LXNpbmdsZSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICB3aWR0aDogMTAwJTsKICBnYXA6IDEycHg7Cn0KCi5lbnRpdHktaW5mbyB7CiAgZmxleDogMTsKICBtaW4td2lkdGg6IDA7CiAgY3Vyc29yOiBwb2ludGVyOwp9CgouZW50aXR5LWluZm8gLmVudGl0eS1uYW1lIHsKICBmb250LXNpemU6IDEycHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNmZmY7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgbWF4LXdpZHRoOiAxNTBweDsKICBkaXNwbGF5OiBibG9jazsKICBsaW5lLWhlaWdodDogMS4yOwp9CgouZW50aXR5LWluZm8gLmVudGl0eS1uYW1lOmhvdmVyIHsKICBjb2xvcjogIzg3Y2VlYjsKfQoKLmVudGl0eS1pbmZvIC5tb25zdGVyLW5hbWUgewogIGNvbG9yOiAjZmY2YjZiOwp9CgouZW50aXR5LWluZm8gLm1vbnN0ZXItbmFtZTpob3ZlciB7CiAgY29sb3I6ICNmZjhhOGE7Cn0KCi8qIOS9jee9ruihjOaYvuekuuagt+W8jyAqLwoubG9jYXRpb24tcm93LWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDhweCAxMnB4OwogIG1pbi1oZWlnaHQ6IDQwcHg7Cn0KCi5sb2NhdGlvbi1yb3ctc2luZ2xlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgd2lkdGg6IDEwMCU7CiAgZ2FwOiAxMnB4Owp9CgoubG9jYXRpb24taW5mbyB7CiAgZmxleDogMTsKICBtaW4td2lkdGg6IDA7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRleHQtYWxpZ246IGNlbnRlcjsKfQoKLmxvY2F0aW9uLWluZm8uZGlzYWJsZWQgewogIGN1cnNvcjogbm90LWFsbG93ZWQ7CiAgb3BhY2l0eTogMC41Owp9CgoubG9jYXRpb24taW5mbyAubG9jYXRpb24tbmFtZSB7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjZmZkNzAwOwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBjb2xvciAwLjNzIGVhc2U7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIGRpc3BsYXk6IGJsb2NrOwp9CgoubG9jYXRpb24taW5mbyAubG9jYXRpb24tbmFtZTpob3ZlciB7CiAgY29sb3I6ICM4N2NlZWI7Cn0KCi5sb2NhdGlvbi1pbmZvLmRpc2FibGVkIC5sb2NhdGlvbi1uYW1lIHsKICBjb2xvcjogIzY2NjsKfQoKLmxvY2F0aW9uLWluZm8uZGlzYWJsZWQgLmxvY2F0aW9uLW5hbWU6aG92ZXIgewogIGNvbG9yOiAjNjY2Owp9Cgo="}, {"version": 3, "sources": ["Main.vue"], "names": [], "mappings": ";AAupCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "Main.vue", "sourceRoot": "src/views/game", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"game-container\">\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <!-- 上半部分：左右分栏 -->\n        <div class=\"top-section\">\n          <!-- 左侧：个人信息 -->\n          <div class=\"left-panel\">\n            <div class=\"pixel-avatar-bar2\" @click=\"openCharacterStatus\">\n              <!-- <img class=\"pixel-bg-img2\" src=\"/static/game/UI/bj/tbu.png\" alt=\"头像背景\" /> -->\n              <img class=\"pixel-avatar-img2\" :src=\"characterInfo.avatar\" :alt=\"characterInfo.name\" />\n              <div class=\"pixel-name2\">{{ characterInfo.name }}</div>\n              <div class=\"pixel-bars2\">\n                <div class=\"pixel-bar2 pixel-hp2\">\n                  <div class=\"pixel-bar-inner2 pixel-hp-inner2\" :style=\"{width: hpPercent + '%'}\"></div>\n                </div>\n                <div class=\"pixel-bar2 pixel-mp2\">\n                  <div class=\"pixel-bar-inner2 pixel-mp-inner2\" :style=\"{width: mpPercent + '%'}\"></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"pixel-info-box\">\n              <div class=\"pixel-row\">职业: {{ characterInfo.profession }}</div>\n              <div class=\"pixel-row\">等级: {{ characterInfo.level }}</div>\n              <div class=\"pixel-row\">银两: {{ characterInfo.silver }}</div>\n              <div class=\"pixel-row\"><span class=\"pixel-label-gold\">金砖:</span><span class=\"pixel-value-gold\">{{ characterInfo.gold }}</span></div>\n              <div class=\"pixel-row pixel-exp-label\">经验: {{ characterInfo.exp }}/{{ characterInfo.expRequired }}</div>\n              <div class=\"pixel-exp-bar\">\n                <div class=\"pixel-exp-inner\" :style=\"{width: expPercent + '%'}\"></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 右侧：动态内容区域 -->\n          <div class=\"right-panel\">\n            <div class=\"panel-header\">{{ getCurrentPanelTitle() }}</div>\n            <div class=\"panel-content\">\n              <!-- 人物功能内容：显示当前地图的NPC和怪物 -->\n              <div v-if=\"!currentFunction || currentFunction === 'character'\" class=\"npc-content\">\n                <div class=\"pixel-border-box\">\n                  <!-- 四行布局容器 -->\n                  <div class=\"four-row-container\">\n                    <!-- 第一行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(0)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(0), getEntityForRow(0).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(0).type === 'monster' }\">\n                            {{ getEntityForRow(0).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(0).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(0))\"\n                        >\n                          {{ getEntityForRow(0).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第二行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(1)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(1), getEntityForRow(1).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(1).type === 'monster' }\">\n                            {{ getEntityForRow(1).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(1).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(1))\"\n                        >\n                          {{ getEntityForRow(1).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第三行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(2)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(2), getEntityForRow(2).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(2).type === 'monster' }\">\n                            {{ getEntityForRow(2).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(2).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(2))\"\n                        >\n                          {{ getEntityForRow(2).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第四行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(3)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(3), getEntityForRow(3).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(3).type === 'monster' }\">\n                            {{ getEntityForRow(3).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(3).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(3))\"\n                        >\n                          {{ getEntityForRow(3).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 实体信息弹窗 -->\n              <div v-if=\"showEntityModal\" class=\"entity-modal-overlay\" @click=\"closeEntityModal\">\n                <div class=\"entity-modal\" @click.stop>\n                  <div class=\"modal-header\">\n                    <h3>{{ selectedEntity.name }}</h3>\n                    <button class=\"close-btn\" @click=\"closeEntityModal\">×</button>\n                  </div>\n                  <div class=\"modal-content\">\n                    <div v-if=\"selectedEntityType === 'npc'\" class=\"npc-info\">\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">称号：</span>\n                        <span class=\"info-value\">{{ selectedEntity.title || '无' }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">等级：</span>\n                        <span class=\"info-value\">{{ selectedEntity.level }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">类型：</span>\n                        <span class=\"info-value\">{{ getNpcTypeText(selectedEntity.type) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">阵营：</span>\n                        <span class=\"info-value\">{{ getFactionText(selectedEntity.faction) }}</span>\n                      </div>\n                      <div v-if=\"selectedEntity.services && selectedEntity.services.length > 0\" class=\"info-row\">\n                        <span class=\"info-label\">服务：</span>\n                        <span class=\"info-value\">{{ selectedEntity.services.join(', ') }}</span>\n                      </div>\n                      <div class=\"info-row description\">\n                        <span class=\"info-label\">描述：</span>\n                        <span class=\"info-value\">{{ selectedEntity.description || '暂无描述' }}</span>\n                      </div>\n                    </div>\n                    <div v-else-if=\"selectedEntityType === 'monster'\" class=\"monster-info\">\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">称号：</span>\n                        <span class=\"info-value\">{{ selectedEntity.title || '无' }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">等级：</span>\n                        <span class=\"info-value\">{{ selectedEntity.level }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">类型：</span>\n                        <span class=\"info-value\">{{ getMonsterTypeText(selectedEntity.type) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">元素：</span>\n                        <span class=\"info-value\">{{ getElementText(selectedEntity.element) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">体型：</span>\n                        <span class=\"info-value\">{{ getSizeText(selectedEntity.size) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">威胁等级：</span>\n                        <span class=\"info-value threat-level\">{{ selectedEntity.threat_level || 1 }}</span>\n                      </div>\n                      <div v-if=\"selectedEntity.stats\" class=\"stats-section\">\n                        <div class=\"stats-title\">属性</div>\n                        <div class=\"stats-grid\">\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">生命：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.health }}/{{ selectedEntity.stats.max_health }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">攻击：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.attack }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">防御：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.defense }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">速度：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.speed }}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div class=\"info-row description\">\n                        <span class=\"info-label\">描述：</span>\n                        <span class=\"info-value\">{{ selectedEntity.description || '暂无描述' }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 设施功能内容 -->\n              <div v-else-if=\"currentFunction === 'equipment'\" class=\"facilities-content\">\n                <div class=\"facility-list six-grid\">\n                  <div class=\"facility-item\" @click=\"selectFacility('clinic')\">\n                    <span class=\"facility-name\">医馆</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('bank')\">\n                    <span class=\"facility-name\">钱庄</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('posthouse')\">\n                    <span class=\"facility-name\">馆驿</span>\n                    </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('market')\">\n                    <span class=\"facility-name\">市场</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('square')\">\n                    <span class=\"facility-name\">广场</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('government')\">\n                    <span class=\"facility-name\">官府</span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 移动功能内容 -->\n              <div v-else-if=\"currentFunction === 'move'\" class=\"move-content\">\n                <div class=\"pixel-border-box\">\n                  <!-- 四行位置布局容器 -->\n                  <div class=\"four-row-container\">\n                    <!-- 第一行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(0)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(0))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(0).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第二行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(1)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(1))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(1).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第三行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(2)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(2))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(2).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第四行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(3)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(3))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(3).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 功能菜单内容 -->\n              <div v-else-if=\"currentFunction === 'functions'\" class=\"functions-content\">\n                <div class=\"function-list functions-grid\">\n                  <div class=\"function-item\" @click=\"openFunction('status')\">\n                    <span class=\"function-name\">状态</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('items')\">\n                    <span class=\"function-name\">物品</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('immortal')\">\n                    <span class=\"function-name\">仙将</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('team')\">\n                    <span class=\"function-name\">组队</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('ranking')\">\n                    <span class=\"function-name\">排行</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('friends')\">\n                    <span class=\"function-name\">好友</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('mail')\">\n                    <span class=\"function-name\">邮件</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('quest')\">\n                    <span class=\"function-name\">任务</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('arena')\">\n                    <span class=\"function-name\">擂台</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('guild')\">\n                    <span class=\"function-name\">帮派</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('training')\">\n                    <span class=\"function-name\">训练</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('treasury')\">\n                    <span class=\"function-name\">宝库</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('notice')\">\n                    <span class=\"function-name\">公告</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('vip')\">\n                    <span class=\"function-name\">VIP</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('strategy')\">\n                    <span class=\"function-name\">攻略</span>\n                </div>\n                  <div class=\"function-item\" @click=\"openFunction('logout')\">\n                    <span class=\"function-name\">登出</span>\n              </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 下半部分：功能区域 -->\n        <div class=\"bottom-section\">\n          <!-- 中间：功能按钮栏 -->\n          <div class=\"function-bar\">\n            <div\n              v-for=\"(func, index) in mainFunctions\"\n              :key=\"index\"\n              class=\"function-btn\"\n              :class=\"{ 'active': currentFunction === func.action }\"\n              @click=\"handleFunction(func.action)\"\n            >\n              <img :src=\"func.image\" :alt=\"func.name\" class=\"function-btn-image\" />\n            </div>\n          </div>\n\n          <!-- 在线玩家头像区域 -->\n          <div class=\"online-players\">\n            <div class=\"section-title\">在线玩家头像</div>\n            <div class=\"players-avatars\">\n              <div\n                v-for=\"(player, index) in onlinePlayers\"\n                :key=\"index\"\n                class=\"player-avatar\"\n              >\n                <img :src=\"player.avatar\" :alt=\"player.name\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- 底部：聊天组件区域 -->\n          <div class=\"chat-section\">\n            <GameChat\n              :character-info=\"characterInfo\"\n              :auto-connect=\"true\"\n              :initial-minimized=\"false\"\n              @message-sent=\"onChatMessageSent\"\n              @channel-switched=\"onChatChannelSwitched\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport GameChat from '@/components/GameChat.vue'\nimport logger from '@/utils/logger'\nimport { getCharacterDetail, getCurrentCharacter, getCharacterStatus } from '@/api/services/characterService'\nimport { showMessage } from '@/utils/message'\n\nexport default {\n  name: 'Main',\n  components: {\n    GameLayout,\n    GameChat\n  },\n  data() {\n    return {\n      currentFunction: null, // 保持为null，默认显示NPC和怪物\n      characterInfo: {\n        name: '',\n        avatar: '',\n        profession: '',\n        silver: 0,\n        gold: 0,\n        expNeeded: 0,\n        hp: 0,\n        maxHp: 0,\n        mp: 0,\n        maxMp: 0,\n        level: 1,\n        exp: 0,\n        expRequired: 1000,\n        attributePoints: 0,\n        constitution: 0,\n        intelligence: 0,\n        strength: 0,\n        agility: 0,\n        attack: 0,\n        defense: 0,\n        speed: 0\n      },\n      npcList: [\n        {\n          name: '村长',\n          type: 'npc',\n          description: '村庄的领导者，可以接受任务',\n          level: 50,\n          avatar: '/static/game/UI/tx/npc/village_chief.png',\n          services: ['任务', '信息']\n        },\n        {\n          name: '武器商人',\n          type: 'npc',\n          description: '出售各种武器装备',\n          level: 30,\n          avatar: '/static/game/UI/tx/npc/weapon_merchant.png',\n          services: ['武器', '装备']\n        },\n        {\n          name: '药剂师',\n          type: 'npc',\n          description: '出售恢复药剂和魔法药水',\n          level: 25,\n          avatar: '/static/game/UI/tx/npc/alchemist.png',\n          services: ['药剂', '治疗']\n        },\n        {\n          name: '铁匠',\n          type: 'npc',\n          description: '可以强化和修理装备',\n          level: 40,\n          avatar: '/static/game/UI/tx/npc/blacksmith.png',\n          services: ['强化', '修理']\n        }\n      ],\n      mainFunctions: [\n        { name: '人物', action: 'character', image: '/static/game/UI/anniu/sc_gn_1.png' },\n        { name: '设施', action: 'equipment', image: '/static/game/UI/anniu/sc_gn_2.png' },\n        { name: '移动', action: 'move', image: '/static/game/UI/anniu/sc_gn_3.png' },\n        { name: '功能', action: 'functions', image: '/static/game/UI/anniu/sc_gn_4.png' }\n      ],\n      onlinePlayers: [\n        { name: '玩家1', avatar: '/static/game/UI/tx/male/tx2.png' },\n        { name: '玩家2', avatar: '/static/game/UI/tx/male/tx3.png' },\n        { name: '玩家3', avatar: '/static/game/UI/tx/male/tx4.png' },\n        { name: '玩家4', avatar: '/static/game/UI/tx/male/tx5.png' },\n        { name: '玩家5', avatar: '/static/game/UI/tx/male/tx6.png' }\n      ],\n\n      // 地图相关数据\n      loadingLocations: false,\n\n      // 实体信息弹窗\n      showEntityModal: false,\n      selectedEntity: null,\n      selectedEntityType: null\n    }\n  },\n  computed: {\n    hpPercent() {\n      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100\n    },\n    mpPercent() {\n      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100\n    },\n    expPercent() {\n      return (this.characterInfo.exp / this.characterInfo.expRequired) * 100\n    },\n\n    // 地图相关计算属性\n    availableLocations() {\n      return this.$store.state.map.availableLocations || []\n    },\n\n    isMoving() {\n      return this.$store.state.map.loading.moving\n    },\n\n    canMove() {\n      return this.$store.getters['map/canMove']\n    },\n\n    // 当前地图的NPC列表\n    currentLocationNpcs() {\n      try {\n        const currentLocation = this.$store.state.map.currentLocation;\n        return currentLocation.npcs || [];\n      } catch (error) {\n        logger.error('[Main] 获取当前位置NPC失败:', error);\n        return [];\n      }\n    },\n\n    // 当前地图的怪物列表\n    currentLocationMonsters() {\n      try {\n        const currentLocation = this.$store.state.map.currentLocation;\n        return currentLocation.monsters || [];\n      } catch (error) {\n        logger.error('[Main] 获取当前位置怪物失败:', error);\n        return [];\n      }\n    },\n\n    // 合并所有实体（NPC和怪物）\n    allEntities() {\n      const entities = [];\n\n      try {\n        // 添加NPC\n        if (this.currentLocationNpcs && Array.isArray(this.currentLocationNpcs)) {\n          this.currentLocationNpcs.forEach(npc => {\n            entities.push({\n              ...npc,\n              type: 'npc'\n            });\n          });\n        }\n\n        // 添加怪物\n        if (this.currentLocationMonsters && Array.isArray(this.currentLocationMonsters)) {\n          this.currentLocationMonsters.forEach(monster => {\n            entities.push({\n              ...monster,\n              type: 'monster'\n            });\n          });\n        }\n      } catch (error) {\n        logger.error('[Main] 获取实体列表失败:', error);\n      }\n\n      return entities;\n    }\n  },\n  methods: {\n    openCharacterStatus() {\n      logger.info('打开角色状态页面')\n      \n      // 在跳转前初始化角色状态数据\n      this.$store.dispatch('character/initCurrentCharacter').then(character => {\n        if (character) {\n          this.$store.dispatch('character/loadCharacterStatus').then(() => {\n            // 确保状态已加载完成，然后跳转\n            logger.debug('[Main] 角色状态加载成功，准备跳转到角色状态页面');\n            \n            // 强制刷新一次角色信息，确保数据最新\n            this.loadCharacterInfo().then(() => {\n              this.$router.push('/game/character-status');\n            });\n          }).catch(error => {\n            logger.error('[Main] 加载角色状态失败:', error);\n            showMessage('加载角色状态失败', 'error');\n            // 即使加载失败也跳转，CharacterStatus组件会处理错误情况\n            this.$router.push('/game/character-status');\n          });\n        } else {\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n        }\n      });\n    },\n    selectNpc(npc) {\n      logger.info('选择NPC/野怪', npc.name)\n\n      if (npc.type === 'npc') {\n        // NPC交互逻辑\n        this.handleNpcInteraction(npc)\n      } else if (npc.type === 'monster') {\n        // 怪物战斗逻辑\n        this.handleMonsterEncounter(npc)\n      }\n    },\n\n    handleNpcInteraction(npc) {\n      // 西游记主题的NPC对话\n      switch(npc.name) {\n        case '城隍爷':\n          this.showToast(`与${npc.name}对话：施主，此地乃东胜神洲，有何贵干？`)\n          break\n        case '仙界商人':\n          this.showToast(`与${npc.name}对话：仙友，我这里有各种法宝，要不要看看？`)\n          setTimeout(() => {\n            this.$router.push('/game/market')\n          }, 2000)\n          break\n        case '猴族长老':\n          this.showToast(`与${npc.name}对话：小猴子，想学武艺吗？花果山可是修炼的好地方！`)\n          break\n        case '洞府守护':\n          this.showToast(`与${npc.name}对话：水帘洞乃美猴王洞府，此处有上古秘籍！`)\n          break\n        case '得道高僧':\n          this.showToast(`与${npc.name}对话：阿弥陀佛，施主与佛有缘，可愿听贫僧讲经？`)\n          break\n        case '罗汉':\n          this.showToast(`与${npc.name}对话：善哉善哉，施主来到灵山，可是为求真经？`)\n          break\n        case '凡间官员':\n          this.showToast(`与${npc.name}对话：这位侠客，南瞻部洲最近不太平，小心山贼！`)\n          break\n        case '东海龙王':\n          this.showToast(`与${npc.name}对话：何方神圣闯入龙宫？若是有缘人，可赐你神兵利器！`)\n          setTimeout(() => {\n            this.$router.push('/game/market')\n          }, 2000)\n          break\n        case '虾兵':\n          this.showToast(`与${npc.name}对话：龙王有令，闲杂人等不得入内！`)\n          break\n        default:\n          this.showToast(`与${npc.name}对话：施主，贫道这厢有礼了！`)\n      }\n    },\n\n    handleMonsterEncounter(monster) {\n      // 西游记主题的怪物遭遇\n      let encounterMessage = '';\n\n      switch(monster.name) {\n        case '灵猴':\n          encounterMessage = `一只${monster.name}从树上跳下，似乎想要与你切磋武艺！`;\n          break\n        case '山魈':\n          encounterMessage = `${monster.name}从山林中现身，凶神恶煞地盯着你！`;\n          break\n        case '水灵':\n          encounterMessage = `洞中的${monster.name}感受到了你的气息，化作人形挡住去路！`;\n          break\n        case '护法金刚':\n          encounterMessage = `${monster.name}金光闪闪，威严地说：\"欲入灵山，先过我这一关！\"`;\n          break\n        case '山贼':\n          encounterMessage = `一群${monster.name}拦路抢劫：\"此路是我开，此树是我栽！\"`;\n          break\n        case '蟹将':\n          encounterMessage = `龙宫${monster.name}挥舞着巨钳：\"胆敢擅闯龙宫，受死！\"`;\n          break\n        default:\n          encounterMessage = `遭遇了${monster.name}，看起来来者不善！`;\n      }\n\n      this.showToast(`${encounterMessage}（等级${monster.level}）准备战斗！`)\n\n      // 获取当前角色和位置信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      const currentLocation = this.$store.state.map.currentLocation;\n\n      if (!currentCharacter || !currentCharacter.id) {\n        this.showToast('角色信息错误，无法开始战斗', 'error');\n        return;\n      }\n\n      setTimeout(() => {\n        // 传递战斗所需的参数\n        this.$router.push({\n          path: '/game/battle',\n          query: {\n            characterId: currentCharacter.id,\n            monsterId: monster.id,\n            locationId: currentLocation?.id || null\n          }\n        });\n      }, 2000)\n    },\n\n    // 获取指定行的实体（为后台管理预留接口）\n    getEntityForRow(rowIndex) {\n      try {\n        // 目前简单按顺序分配，后续可通过后台配置\n        const entities = this.allEntities || [];\n        return entities[rowIndex] || null;\n      } catch (error) {\n        logger.error('[Main] 获取行实体失败:', error);\n        return null;\n      }\n    },\n\n    // 统一的实体操作方法\n    handleEntityAction(entity) {\n      if (entity.type === 'npc') {\n        this.handleNpcInteraction(entity);\n      } else if (entity.type === 'monster') {\n        this.handleMonsterEncounter(entity);\n      }\n    },\n\n    // 获取指定行的位置（为后台管理预留接口）\n    getLocationForRow(rowIndex) {\n      try {\n        // 目前简单按顺序分配，后续可通过后台配置\n        const locations = this.availableLocations || [];\n        return locations[rowIndex] || null;\n      } catch (error) {\n        logger.error('[Main] 获取行位置失败:', error);\n        return null;\n      }\n    },\n    handleFunction(action) {\n      logger.info('点击功能', action)\n      if (this.currentFunction === action) {\n        // 如果当前功能已经是选中状态，不做任何操作\n        return;\n      } else {\n        // 设置为新的功能\n        this.currentFunction = action\n      }\n    },\n    getCurrentPanelTitle() {\n      switch(this.currentFunction) {\n        case 'character':\n          return '人物信息'\n        case 'equipment':\n          return '设施列表'\n        case 'move':\n          return '移动地点'\n        case 'functions':\n          return '功能菜单'\n        default:\n          return ''\n      }\n    },\n    // 设施相关方法\n    selectFacility(facility) {\n      logger.info('选择设施', facility)\n      switch(facility) {\n        case 'clinic':\n          this.$router.push('/game/clinic');\n          break;\n        case 'bank':\n          this.$router.push('/game/bank');\n          break;\n        case 'posthouse':\n          this.$router.push('/game/posthouse');\n          break;\n        case 'market':\n          this.$router.push('/game/market');\n          break;\n        case 'square':\n          this.$router.push('/game/square');\n          break;\n        case 'government':\n          this.$router.push('/game/government');\n          break;\n      }\n    },\n    // 移动相关方法\n    async moveToLocationDirectly(location) {\n      logger.info('[Main] 直接移动到位置:', location);\n\n      // 检查角色信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      if (!currentCharacter || !currentCharacter.id) {\n        logger.error('[Main] 移动失败: 未找到角色信息');\n        this.showToast('请先选择角色', 'error');\n        return;\n      }\n\n      // 检查是否正在移动\n      if (this.isMoving) {\n        this.showToast('正在移动中，请稍候', 'warning');\n        return;\n      }\n\n      try {\n        await this.$store.dispatch('map/moveToLocation', location.id);\n        this.showToast(`成功移动到${location.name}`, 'success');\n\n        // 重新加载当前位置的NPC和怪物\n        await this.loadLocationEntities();\n      } catch (error) {\n        logger.error('[Main] 移动失败:', error);\n        this.showToast('移动失败: ' + error.message, 'error');\n      }\n    },\n\n\n\n\n\n    async loadLocationEntities() {\n      try {\n        const currentCharacter = this.$store.getters['character/currentCharacter'];\n        const currentLocation = this.$store.state.map.currentLocation;\n\n        if (currentCharacter && currentLocation.id) {\n          logger.debug('[Main] 当前位置实体信息:', {\n            location: currentLocation.name,\n            npcs: currentLocation.npcs?.length || 0,\n            monsters: currentLocation.monsters?.length || 0\n          });\n\n          // 触发Vue的响应式更新\n          this.$forceUpdate();\n        }\n      } catch (error) {\n        logger.error('[Main] 加载位置实体失败:', error);\n      }\n    },\n\n\n\n\n\n    // 兼容旧的移动方法\n    moveToLocation(location) {\n      logger.info('[Main] 兼容性移动方法调用:', location);\n\n      // 如果是字符串，转换为位置对象\n      if (typeof location === 'string') {\n        const locationObj = {\n          id: location,\n          name: location,\n          type: location\n        };\n        this.moveToLocationDirectly(locationObj);\n      } else {\n        this.moveToLocationDirectly(location);\n      }\n    },\n    // 功能菜单相关方法\n    openFunction(func) {\n      logger.info('打开功能', func)\n      // 根据功能类型跳转到对应页面\n      switch(func) {\n        case 'status':\n          this.$router.push('/game/status')\n          break\n        case 'items':\n          this.$router.push('/game/items')\n          break\n        case 'immortal':\n          this.$router.push('/game/immortal')\n          break\n        case 'team':\n          this.$router.push('/game/team')\n          break\n        case 'ranking':\n          this.$router.push('/game/ranking')\n          break\n        case 'friends':\n          this.$router.push('/game/friends')\n          break\n        case 'mail':\n          this.$router.push('/game/mail')\n          break\n        case 'quest':\n          this.$router.push('/game/quest')\n          break\n        case 'arena':\n          this.$router.push('/game/arena')\n          break\n        case 'guild':\n          this.$router.push('/game/guild')\n          break\n        case 'training':\n          this.$router.push('/game/training')\n          break\n        case 'treasury':\n          this.$router.push('/game/treasury')\n          break\n        case 'notice':\n          this.$router.push('/game/notice')\n          break\n        case 'vip':\n          this.$router.push('/game/vip')\n          break\n        case 'strategy':\n          this.$router.push('/game/strategy')\n          break\n        case 'logout':\n          this.$router.push('/game/logout')\n          break\n      }\n    },\n    // 聊天组件事件处理\n    onChatMessageSent(messageData) {\n      logger.info('[Main] 聊天消息已发送:', messageData);\n    },\n\n    onChatChannelSwitched(channelData) {\n      logger.info('[Main] 聊天频道已切换:', channelData);\n    },\n\n    showToast(message, type = 'info') {\n      // 简单的提示实现，使用logger代替alert避免弹出框\n      logger.info(`[Toast ${type}]:`, message)\n    },\n\n    // 实体信息弹窗相关方法\n    showEntityInfo(entity, type) {\n      this.selectedEntity = entity;\n      this.selectedEntityType = type;\n      this.showEntityModal = true;\n      logger.info(`[Main] 显示${type}信息:`, entity.name);\n    },\n\n    closeEntityModal() {\n      this.showEntityModal = false;\n      this.selectedEntity = null;\n      this.selectedEntityType = null;\n    },\n\n    // 获取类型文本的方法\n    getNpcTypeText(type) {\n      const typeMap = {\n        'merchant': '商人',\n        'quest_giver': '任务发布者',\n        'trainer': '训练师',\n        'guard': '守卫',\n        'official': '官员',\n        'immortal': '仙人',\n        'monk': '僧人',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n\n    getMonsterTypeText(type) {\n      const typeMap = {\n        'beast': '野兽',\n        'demon': '妖魔',\n        'spirit': '精灵',\n        'undead': '不死族',\n        'dragon': '龙族',\n        'immortal': '仙族',\n        'elemental': '元素',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n\n    getFactionText(faction) {\n      const factionMap = {\n        'heaven': '天庭',\n        'buddhist': '佛门',\n        'mortal': '凡间',\n        'demon': '妖魔',\n        'dragon': '龙族',\n        'neutral': '中立'\n      };\n      return factionMap[faction] || faction;\n    },\n\n    getElementText(element) {\n      const elementMap = {\n        'none': '无',\n        'fire': '火',\n        'water': '水',\n        'earth': '土',\n        'wind': '风',\n        'thunder': '雷',\n        'ice': '冰',\n        'light': '光',\n        'dark': '暗'\n      };\n      return elementMap[element] || element;\n    },\n\n    getSizeText(size) {\n      const sizeMap = {\n        'tiny': '微小',\n        'small': '小型',\n        'medium': '中型',\n        'large': '大型',\n        'huge': '巨型',\n        'giant': '超巨型'\n      };\n      return sizeMap[size] || size;\n    },\n\n    getDefaultAvatar(type) {\n      if (type === 'npc') {\n        return '/static/game/UI/tx/npc/default.png'\n      } else {\n        return '/static/game/UI/tx/monster/default.png'\n    }\n  },\n    async loadCharacterInfo() {\n      try {\n        // 获取当前角色基本信息\n    const current = getCurrentCharacter();\n        if (!current || !current.id) {\n          logger.error('[Main] 未找到当前角色信息');\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n          return;\n        }\n\n        // 将角色信息设置到store中\n        await this.$store.dispatch('character/selectCharacter', current);\n        logger.debug('[Main] 角色信息已设置到store:', current);\n\n        // 获取角色详情\n        try {\n          const detailResponse = await getCharacterDetail(current.id);\n          if (detailResponse && detailResponse.data) {\n            const detail = detailResponse.data;\n            \n            // 职业英文到中文的映射\n            const professionMap = {\n              'warrior': '武士',\n              'scholar': '文人',\n              'mystic': '异人'\n            };\n            \n            // 更新角色基本信息\n        this.characterInfo = {\n          ...this.characterInfo,\n              name: detail.name || current.name,\n              avatar: detail.avatar || `/static/game/UI/tx/${detail.gender || 'male'}/tx1.png`,\n              profession: professionMap[detail.profession] || detail.profession || '未知',\n              gold: detail.gold || 0,\n              silver: detail.silver || 0,\n              level: detail.level || 1\n        };\n          }\n        } catch (detailError) {\n          logger.error('[Main] 获取角色详情失败:', detailError);\n    }\n\n        // 获取角色状态信息\n        try {\n          const statusResponse = await getCharacterStatus(current.id);\n          if (statusResponse && statusResponse.data) {\n            const status = statusResponse.data;\n            \n            // 更新角色状态信息\n            this.characterInfo = {\n              ...this.characterInfo,\n              hp: parseInt(status.hp || 0),\n              maxHp: parseInt(status.max_hp || 100),\n              mp: parseInt(status.mp || 0),\n              maxMp: parseInt(status.max_mp || 100),\n              exp: parseInt(status.exp || 0),\n              expRequired: parseInt(status.exp_required || 1000),\n              attributePoints: parseInt(status.attribute_points || 0),\n              constitution: parseInt(status.constitution || 0),\n              intelligence: parseInt(status.intelligence || 0),\n              strength: parseInt(status.strength || 0),\n              agility: parseInt(status.agility || 0),\n              attack: parseInt(status.attack || 0),\n              defense: parseInt(status.defense || 0),\n              speed: parseInt(status.speed || 0)\n            };\n            \n            // 调试输出\n            logger.debug('[Main] 角色状态已更新:', this.characterInfo);\n          }\n        } catch (statusError) {\n          logger.error('[Main] 获取角色状态失败:', statusError);\n        }\n      } catch (error) {\n        logger.error('[Main] 加载角色信息失败:', error);\n        showMessage('加载角色信息失败', 'error');\n      }\n      \n      // 返回Promise以支持链式调用\n      return Promise.resolve();\n    },\n  },\n  async mounted() {\n    try {\n      // 加载角色信息\n      await this.loadCharacterInfo();\n\n      // 检查用户是否已登录且有角色信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      const isAuthenticated = this.$store.state.auth.isAuthenticated;\n\n      logger.debug('[Main] 挂载后状态检查:', {\n        isAuthenticated,\n        hasCharacter: !!currentCharacter,\n        characterId: currentCharacter?.id\n      });\n\n      if (isAuthenticated && currentCharacter && currentCharacter.id) {\n        // 初始化地图数据\n        try {\n          await this.$store.dispatch('map/initializeMap');\n          logger.info('[Main] 地图数据初始化完成');\n        } catch (error) {\n          logger.error('[Main] 地图数据初始化失败:', error);\n          // 地图初始化失败不影响主要功能，只记录错误\n        }\n      } else {\n        logger.warn('[Main] 用户未登录或无角色信息，跳过地图初始化');\n      }\n    } catch (error) {\n      logger.error('[Main] 组件挂载过程中发生错误:', error);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.game-container {\n  position: relative;\n  height: 100%;\n  width: 100%;\n  color: #ffd700;\n  overflow: hidden;\n  background: url('/static/game/UI/bg/main_bg.png') center center no-repeat;\n  background-size: cover;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(0, 0, 50, 0.4), rgba(50, 0, 0, 0.4));\n    z-index: 0;\n  }\n}\n\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n  padding: 8px;\n}\n\n.top-section {\n  display: flex;\n  gap: 8px;\n  height: 320px;\n  flex-shrink: 0;\n\n  @media (max-width: 768px) {\n    height: 280px;\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    height: 240px;\n    gap: 4px;\n  }\n}\n\n.bottom-section {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-height: 0;\n\n  @media (max-width: 768px) {\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    gap: 4px;\n  }\n}\n\n.left-panel, .right-panel {\n  background: none;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  border: none;\n}\n\n.left-panel {\n  width: 120px;\n  min-width: 100px;\n  background: none;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  border: none;\n}\n\n.pixel-avatar-bar2 {\n  cursor: pointer;\n  transition: transform 0.2s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.right-panel {\n  flex: 1;\n  min-width: 0;\n}\n\n.panel-header {\n  background: none;\n  color: inherit;\n  padding: 0;\n  font-weight: normal;\n  font-size: inherit;\n  text-align: center;\n  text-shadow: none;\n  border-bottom: none;\n}\n\n.character-info {\n  flex: 1;\n  padding: 6px;\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  background: none;\n}\n\n.info-details {\n  flex: 1;\n}\n\n.character-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 1px;\n  text-shadow: none;\n}\n\n.character-level {\n  font-size: 10px;\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 6px;\n  text-shadow: none;\n}\n\n.stats {\n  display: flex;\n  flex-direction: column;\n  gap: 1px;\n}\n\n.stat-item {\n  font-size: 10px;\n  color: #ffd700;\n  padding: 1px 0;\n  text-shadow: none;\n}\n\n.avatar-section, .status-bars {\n  display: none !important;\n}\n\n.panel-content {\n  flex: 1;\n  padding: 15px;\n  overflow-y: auto;\n  background: none;\n}\n\n.npc-content,\n.npc-content * {\n  background: none !important;\n  box-shadow: none !important;\n  border: none !important;\n}\n\n.npc-text-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  background: none;\n  border: none;\n}\n\n.npc-text-list li {\n  border-bottom: 1px solid #888 !important;\n  margin: 0 !important;\n  padding: 2px 0 !important;\n}\n\n.npc-action-btn {\n  background: none !important;\n  border: none !important;\n  color: inherit !important;\n  font: inherit !important;\n  box-shadow: none !important;\n  border-radius: 0 !important;\n  padding: 0 !important;\n  margin-left: 4px !important;\n  cursor: pointer;\n}\n\n.npc-action-btn:hover,\n.npc-action-btn:active {\n  background: none !important;\n  color: inherit !important;\n  box-shadow: none !important;\n  border: none !important;\n  outline: none !important;\n}\n\n.character-content {\n  height: auto;\n  padding: 0;\n  overflow: visible;\n  background: none;\n  box-shadow: none;\n}\n\n.character-card {\n  background: none;\n  border-radius: 0;\n  padding: 0;\n  box-shadow: none;\n  border: none;\n}\n\n.character-avatar-section,\n.character-basic-info,\n.character-profession,\n.character-bars,\n.character-stats,\n.character-wealth,\n.character-exp {\n  background: none !important;\n  box-shadow: none !important;\n  border: none !important;\n  border-radius: 0 !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n\n.character-avatar-img {\n  width: 60px;\n  height: 60px;\n  border-radius: 10%;\n  border: 2px solid #ffd700;\n  object-fit: cover;\n  box-shadow: none;\n}\n\n.character-name {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-profession {\n  margin: 0;\n  font-size: 14px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-bars {\n  margin-bottom: 16px;\n}\n\n.bar-item {\n  margin-bottom: 8px;\n}\n\n.bar-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n  font-size: 12px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.bar-value {\n  font-weight: bold;\n}\n\n.progress-bar {\n  height: 8px;\n  background: none;\n  border-radius: 0;\n  overflow: hidden;\n  border: 1px solid #088be2;\n}\n\n.progress-fill {\n  height: 100%;\n  transition: width 0.3s ease;\n}\n\n.hp-bar .progress-fill {\n  background: linear-gradient(90deg, #ff0000, #ff3333);\n}\n\n.mp-bar .progress-fill {\n  background: linear-gradient(90deg, #088be2, #4eadf5);\n}\n\n.character-stats {\n  margin-bottom: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.stat-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-wealth {\n  margin-bottom: 16px;\n}\n\n.wealth-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n  margin-bottom: 6px;\n}\n\n.wealth-label {\n  flex: 1;\n  font-size: 13px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.wealth-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-exp {\n  padding: 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n}\n\n.exp-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 13px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.exp-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n\n  .label {\n    font-weight: bold;\n    color: #ffd700;\n    text-shadow: none;\n  }\n\n  .value {\n    color: #ffd700;\n    text-shadow: none;\n  }\n}\n\n.facilities-content {\n  height: 100%;\n  background: none !important;\n}\n\n.facility-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.facility-item {\n  padding: 8px 0;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.move-content {\n  height: 100%;\n  background: none !important;\n}\n\n.location-list {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n}\n\n.location-item {\n  padding: 6px;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.functions-content {\n  height: 100%;\n  background: none !important;\n}\n\n.function-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.function-item {\n  padding: 12px;\n  background: none;\n  border: 1px solid #088be2;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: none;\n    border-color: #ffd700;\n    transform: translateY(-1px);\n    box-shadow: none;\n  }\n\n  .function-name {\n    display: block;\n    font-weight: bold;\n    color: #ffd700;\n    margin-bottom: 4px;\n    text-shadow: none;\n  }\n\n  .function-desc {\n    display: block;\n    font-size: 12px;\n    color: #ccc;\n  }\n}\n\n.function-bar {\n  display: flex;\n  gap: 8px;\n  height: 50px;\n  padding: 0;\n  background: none !important;\n  border-radius: 0;\n  border: none;\n  flex-shrink: 0;\n  overflow: visible;\n}\n\n.function-btn {\n  flex: 1;\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2px;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-2px);\n    background: none;\n  }\n\n  &:active {\n    background: none;\n    transform: scale(0.95);\n  }\n\n  &.active {\n    background: none;\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: -3px;\n      left: 25%;\n      right: 25%;\n      height: 3px;\n      background: #ffd700;\n      border-radius: 3px;\n    }\n  }\n}\n\n.function-btn-image {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  max-width: 100%;\n  max-height: 100%;\n}\n\n.online-players {\n  border: none;\n  background: none !important;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  box-shadow: none;\n  flex-shrink: 0;\n}\n\n.section-title {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  color: #ffd700;\n  padding: 6px 12px;\n  font-weight: bold;\n  font-size: 13px;\n  text-align: center;\n  text-shadow: none;\n  border-bottom: none;\n\n  @media (max-width: 768px) {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n}\n\n.players-avatars {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  gap: 8px;\n  overflow-x: auto;\n  background: none;\n\n  @media (max-width: 768px) {\n    padding: 6px 10px;\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 4px 8px;\n    gap: 4px;\n  }\n}\n\n.player-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 10%;\n  border: 2px solid #ffd700;\n  overflow: hidden;\n  flex-shrink: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: none;\n\n  @media (max-width: 768px) {\n    width: 35px;\n    height: 35px;\n  }\n\n  @media (max-width: 480px) {\n    width: 30px;\n    height: 30px;\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n\n  &:hover {\n    border-color: #088be2;\n    transform: scale(1.1);\n    box-shadow: none;\n  }\n}\n\n.chat-section {\n  border: none;\n  background: none !important;\n  height: 420px;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  box-shadow: none;\n  flex-shrink: 0;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 聊天区域现在使用GameChat组件，移除旧的样式 */\n\n@media (orientation: landscape) and (max-height: 600px) {\n  .top-section {\n    height: 200px;\n  }\n\n  .online-players {\n    height: 60px;\n  }\n\n  .chat-section {\n    height: 300px;\n  }\n\n  .function-bar {\n    height: 40px;\n  }\n}\n\n@media (max-width: 320px) {\n  .main-content {\n    padding: 4px;\n    gap: 4px;\n  }\n\n  .left-panel {\n    width: 140px;\n    min-width: 120px;\n  }\n\n  .player-avatar {\n    width: 25px;\n    height: 25px;\n  }\n\n  .function-btn {\n    font-size: 10px;\n  }\n\n  .panel-header {\n    font-size: 10px;\n    padding: 3px 6px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .main-content {\n    max-width: 1000px;\n    margin: 0 auto;\n    padding: 20px;\n  }\n\n  .top-section {\n    height: 350px;\n  }\n\n  .left-panel {\n    width: 320px;\n  }\n\n  .player-avatar {\n    width: 100px;\n    height: 100px;\n  }\n\n  .function-bar {\n    height: 60px;\n  }\n\n  .function-btn {\n    font-size: 16px;\n  }\n\n  .online-players {\n    height: 100px;\n  }\n\n  .chat-section {\n    height: 420px;\n  }\n}\n\n@media (hover: none) and (pointer: coarse) {\n  .function-btn, .chat-tab, .npc-item, .player-avatar {\n    min-height: 44px;\n  }\n\n  .function-btn:active {\n    background: #088be2;\n    transform: scale(0.95);\n  }\n\n  .chat-tab:active {\n    background: #088be2;\n  }\n}\n\n.npc-actions {\n  display: flex;\n  justify-content: space-around;\n  margin-top: 10px;\n}\n.npc-action-btn {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  color: #ffd700;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n  text-shadow: none;\n  transition: all 0.3s ease;\n}\n.npc-action-btn:hover {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  transform: none;\n}\n.npc-action-btn:active {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  transform: none;\n}\n\n.pixel-avatar-bar2 {\n  position: relative;\n  width: 180px;\n  height: 60px;\n  margin: 0 auto 8px auto;\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n}\n.pixel-bg-img2 {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 180px;\n  height: 60px;\n  z-index: 0;\n  pointer-events: none;\n}\n.pixel-avatar-img2 {\n  position: absolute;\n  left: 6px;\n  bottom: 6px;\n  width: 38px;\n  height: 38px;\n  border-radius: 6px;\n  border: 2px solid #ffd700;\n  background: #222;\n  z-index: 2;\n  object-fit: cover;\n  box-shadow: none;\n}\n.pixel-name2 {\n  position: absolute;\n  left: 0;\n  top: 4px;\n  width: 180px;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: none;\n  z-index: 3;\n  pointer-events: none;\n}\n.pixel-bars2 {\n  position: absolute;\n  left: 54px;\n  top: 26px;\n  width: 116px;\n  z-index: 2;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.pixel-bar2 {\n  width: 100%;\n  height: 12px;\n  background: #222;\n  border: 1.5px solid #ffd700;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 2px;\n}\n.pixel-bar-inner2 {\n  height: 100%;\n  transition: width 0.3s;\n  border-radius: 4px;\n}\n.pixel-hp-inner2 {\n  background: linear-gradient(90deg, #ff3c3c, #ffb03c);\n}\n.pixel-mp-inner2 {\n  background: linear-gradient(90deg, #3c7cff, #b0e0ff);\n}\n\n* {\n  -webkit-tap-highlight-color: transparent !important;\n}\nbutton, [type=\"button\"], [type=\"submit\"], [type=\"reset\"], .function-btn, .npc-action-btn, .chat-tab, .player-avatar, .location-item, .facility-item, .function-item, .panel-header, .section-title, .stat-item, .character-card, .character-content, .panel-content, .right-panel, .left-panel {\n  outline: none !important;\n  box-shadow: none !important;\n  background: none !important;\n}\nbutton:active, button:focus, .function-btn:active, .function-btn:focus, .npc-action-btn:active, .npc-action-btn:focus, .chat-tab:active, .chat-tab:focus, .player-avatar:active, .player-avatar:focus, .location-item:active, .location-item:focus, .facility-item:active, .facility-item:focus, .function-item:active, .function-item:focus, .location-item-simple:active, .location-item-simple:focus {\n  background: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n  color: inherit !important;\n}\n\n/* 防止虚拟键盘弹出 */\n* {\n  -webkit-user-select: none !important;\n  -moz-user-select: none !important;\n  -ms-user-select: none !important;\n  user-select: none !important;\n  -webkit-touch-callout: none !important;\n  -webkit-tap-highlight-color: transparent !important;\n}\n\n/* 禁用所有元素的焦点 */\n*:focus {\n  outline: none !important;\n  -webkit-tap-highlight-color: transparent !important;\n}\n\n/* 添加六宫格布局样式 */\n.six-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  grid-template-rows: repeat(2, 1fr);\n  gap: 4px;\n  justify-items: center;\n  align-items: center;\n}\n\n.six-grid .facility-item {\n  width: 100%;\n  text-align: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: #ffd700;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  padding: 8px 0;\n  transition: background 0.2s;\n}\n\n.six-grid .facility-item:active {\n  background: none;\n}\n\n/* 添加功能宫格布局样式 */\n.functions-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  grid-template-rows: repeat(4, 1fr);\n  gap: 4px;\n  justify-items: center;\n  align-items: center;\n}\n\n.functions-grid .function-item {\n  width: 100%;\n  text-align: center;\n  font-size: 15px;\n  font-weight: bold;\n  color: #ffd700;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  padding: 8px 0;\n  transition: background 0.2s;\n}\n\n.functions-grid .function-item:active {\n  background: none;\n}\n\n.pixel-info-box {\n  background: rgba(0, 0, 50, 0.7);\n  border: 1px solid #5555ff;\n  border-radius: 4px;\n  padding: 8px;\n  margin-top: 8px;\n  font-size: 12px;\n  color: #ffffff;\n}\n\n.pixel-row {\n  margin-bottom: 4px;\n  line-height: 1.2;\n}\n\n.pixel-label-gold {\n  color: #ffcc00;\n}\n\n.pixel-value-gold {\n  color: #ffcc00;\n  font-weight: bold;\n}\n\n.pixel-exp-label {\n  margin-top: 6px;\n  color: #00ffff;\n}\n\n.pixel-exp-bar {\n  height: 6px;\n  background: rgba(0, 0, 50, 0.5);\n  border: 1px solid #5555ff;\n  border-radius: 3px;\n  margin-top: 2px;\n  margin-bottom: 6px;\n  position: relative;\n  overflow: hidden;\n}\n\n.pixel-exp-inner {\n  position: absolute;\n  height: 100%;\n  background: linear-gradient(to right, #00ffff, #00aaff);\n  left: 0;\n  top: 0;\n}\n\n/* NPC和怪物显示样式 */\n.entity-section {\n  margin-bottom: 20px;\n}\n\n.section-header {\n  font-size: 16px;\n  font-weight: bold;\n  color: #d4af37;\n  margin-bottom: 10px;\n  padding: 5px 10px;\n  background: rgba(212, 175, 55, 0.1);\n  border-left: 3px solid #d4af37;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.entity-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.entity-row {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 8px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.entity-row:hover {\n  background: rgba(0, 0, 0, 0.5);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n\n.npc-row:hover {\n  border-color: rgba(135, 206, 235, 0.5);\n}\n\n.monster-row:hover {\n  border-color: rgba(255, 107, 107, 0.5);\n}\n\n.entity-left {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  cursor: pointer;\n}\n\n.entity-right {\n  flex-shrink: 0;\n}\n\n.entity-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #fff;\n  transition: color 0.3s ease;\n}\n\n.entity-name.clickable:hover {\n  color: #87ceeb;\n}\n\n.monster-name {\n  color: #ff6b6b;\n}\n\n.monster-name.clickable:hover {\n  color: #ff8a8a;\n}\n\n.entity-level {\n  font-size: 12px;\n  color: #ffd700;\n  background: rgba(255, 215, 0, 0.2);\n  padding: 2px 6px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  display: inline-block;\n  width: fit-content;\n}\n\n.entity-services {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 2px;\n}\n\n.service-tag {\n  font-size: 10px;\n  color: #87ceeb;\n  background: rgba(135, 206, 235, 0.2);\n  padding: 1px 4px;\n  border-radius: 8px;\n  border: 1px solid rgba(135, 206, 235, 0.3);\n}\n\n.entity-type {\n  font-size: 11px;\n  color: #ccc;\n  font-style: italic;\n}\n\n.action-btn {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 60px;\n}\n\n.npc-action-btn {\n  background: linear-gradient(135deg, #87ceeb, #5f9ea0);\n  color: white;\n  border: 1px solid #87ceeb;\n}\n\n.npc-action-btn:hover {\n  background: linear-gradient(135deg, #5f9ea0, #4682b4);\n  box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);\n}\n\n.battle-btn {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: white;\n  border: 1px solid #ff6b6b;\n}\n\n.battle-btn:hover {\n  background: linear-gradient(135deg, #ff3742, #ff2f3a);\n  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n  color: #666;\n}\n\n.empty-message {\n  font-size: 14px;\n  font-style: italic;\n}\n\n/* 实体信息弹窗样式 */\n.entity-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.entity-modal {\n  background: linear-gradient(135deg, #2c3e50, #34495e);\n  border-radius: 12px;\n  border: 2px solid #d4af37;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n  max-width: 500px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 15px 20px;\n  background: rgba(212, 175, 55, 0.1);\n  border-bottom: 1px solid rgba(212, 175, 55, 0.3);\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #d4af37;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: #ff6b6b;\n}\n\n.modal-content {\n  padding: 20px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 12px;\n  align-items: flex-start;\n}\n\n.info-row.description {\n  flex-direction: column;\n  gap: 5px;\n}\n\n.info-label {\n  font-weight: bold;\n  color: #d4af37;\n  min-width: 80px;\n  margin-right: 10px;\n}\n\n.info-value {\n  color: #fff;\n  flex: 1;\n}\n\n.threat-level {\n  color: #ff6b6b;\n  font-weight: bold;\n}\n\n.stats-section {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.stats-title {\n  font-weight: bold;\n  color: #d4af37;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 4px 8px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n}\n\n.stat-label {\n  color: #ccc;\n  font-size: 12px;\n}\n\n.stat-value {\n  color: #fff;\n  font-weight: bold;\n  font-size: 12px;\n}\n\n/* 移动功能样式 */\n.move-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n\n\n.location-description {\n  font-size: 11px;\n  color: #cccccc;\n  line-height: 1.4;\n}\n\n.movement-options {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.section-title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #88ccff;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n}\n\n.location-list {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.location-item {\n  padding: 12px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.location-item:hover:not(.disabled) {\n  background: none;\n  transform: translateY(-1px);\n}\n\n.location-item.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 简化的地图列表样式 */\n.location-item-simple {\n  padding: 8px 12px;\n  background: none;\n  border: none;\n  border-bottom: 1px solid #444;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  color: #ffd700;\n  font-size: 14px;\n  text-align: left;\n  display: block;\n  width: 100%;\n}\n\n.location-item-simple:hover:not(.disabled) {\n  background: none;\n  color: #ffffff;\n}\n\n.location-item-simple.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.location-item-simple:last-child {\n  border-bottom: none;\n}\n\n\n\n\n\n\n\n.no-locations, .loading-locations {\n  text-align: center;\n  padding: 20px;\n  color: #888888;\n  font-size: 12px;\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n/* 四行布局样式 */\n.four-row-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 0;\n}\n\n.entity-row-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: 2px 8px;\n  min-height: 20px;\n}\n\n.entity-list-horizontal {\n  display: flex;\n  gap: 15px;\n  width: 100%;\n  align-items: center;\n}\n\n.entity-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex: 1;\n}\n\n.entity-item-compact .entity-name {\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  flex: 1;\n  line-height: 1.2;\n}\n\n.entity-item-compact .entity-name:hover {\n  color: #87ceeb;\n}\n\n.entity-item-compact .monster-name {\n  color: #ff6b6b;\n}\n\n.entity-item-compact .monster-name:hover {\n  color: #ff8a8a;\n}\n\n.action-btn-compact {\n  padding: 4px 8px;\n  border: none;\n  border-radius: 3px;\n  font-size: 11px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 40px;\n}\n\n.action-btn-compact.npc-action-btn {\n  background: linear-gradient(135deg, #87ceeb, #5f9ea0);\n  color: white;\n  border: 1px solid #87ceeb;\n}\n\n.action-btn-compact.npc-action-btn:hover {\n  background: linear-gradient(135deg, #5f9ea0, #4682b4);\n  box-shadow: 0 0 8px rgba(135, 206, 235, 0.4);\n}\n\n.action-btn-compact.battle-btn {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: white;\n  border: 1px solid #ff6b6b;\n}\n\n.action-btn-compact.battle-btn:hover {\n  background: linear-gradient(135deg, #ff3742, #ff2f3a);\n  box-shadow: 0 0 8px rgba(255, 71, 87, 0.4);\n}\n\n.empty-row {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.empty-text {\n  color: #666;\n  font-size: 11px;\n  font-style: italic;\n  line-height: 1.1;\n}\n\n.divider-line {\n  height: 1px;\n  background: #666;\n  margin: 0 8px;\n  border: none;\n}\n\n/* 单行实体显示样式 */\n.entity-row-single {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  gap: 12px;\n}\n\n.entity-info {\n  flex: 1;\n  min-width: 0;\n  cursor: pointer;\n}\n\n.entity-info .entity-name {\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 150px;\n  display: block;\n  line-height: 1.2;\n}\n\n.entity-info .entity-name:hover {\n  color: #87ceeb;\n}\n\n.entity-info .monster-name {\n  color: #ff6b6b;\n}\n\n.entity-info .monster-name:hover {\n  color: #ff8a8a;\n}\n\n/* 位置行显示样式 */\n.location-row-container {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  min-height: 40px;\n}\n\n.location-row-single {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  gap: 12px;\n}\n\n.location-info {\n  flex: 1;\n  min-width: 0;\n  cursor: pointer;\n  text-align: center;\n}\n\n.location-info.disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n\n.location-info .location-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #ffd700;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.location-info .location-name:hover {\n  color: #87ceeb;\n}\n\n.location-info.disabled .location-name {\n  color: #666;\n}\n\n.location-info.disabled .location-name:hover {\n  color: #666;\n}\n\n</style>\n\n"]}]}