<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\LocationConnection;

echo "=== 修复等级要求 ===\n";

// 获取所有连接并降低等级要求
$connections = LocationConnection::all();

foreach ($connections as $connection) {
    $oldLevel = $connection->level_requirement;
    
    // 将所有等级要求降低到1级，让1级角色可以访问所有地点
    $connection->level_requirement = 1;
    $connection->save();
    
    echo "连接 {$connection->fromLocation->name} → {$connection->toLocation->name}: 等级要求从 {$oldLevel} 降低到 1\n";
}

echo "\n=== 修复完成 ===\n";
echo "所有位置现在都可以被1级角色访问\n";
