<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

class AuthController extends Controller
{
    /**
     * 用户注册
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:255|unique:users,name',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first(),
                ]
            ], 422);
        }

        $user = User::create([
            'name' => $request->username,
            'password' => Hash::make($request->password),
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'userId' => $user->id,
                'username' => $user->name,
            ]
        ]);
    }

    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first(),
                ]
            ], 422);
        }

        if (!Auth::attempt(['name' => $request->username, 'password' => $request->password])) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UNAUTHORIZED',
                    'message' => '用户名或密码错误',
                ]
            ], 401);
        }

        $user = Auth::user();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'data' => [
                'userId' => $user->id,
                'username' => $user->name,
                'token' => $token, // 同时返回token到响应体
            ]
        ])->withCookie(cookie('token', $token, 60 * 24, null, null, false, true)); // HttpOnly cookie
    }

    /**
     * 用户登出
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => '登出成功'
        ])->withCookie(cookie()->forget('token'));
    }

    /**
     * 获取当前用户信息
     */
    public function me(Request $request)
    {
        $user = $request->user();

        return response()->json([
            'success' => true,
            'data' => [
                'userId' => $user->id,
                'username' => $user->name,
                'roles' => $user->roles->pluck('name'),
                'createdAt' => $user->created_at,
            ]
        ]);
    }
}