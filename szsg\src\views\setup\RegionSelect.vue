<template>
  <GameLayout
    page-type="region-select"
    custom-title=" "
  >
    <div class="region-select-container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载大区列表...</p>
      </div>

      <!-- 错误提示 -->
      <div v-if="error && !isLoading" class="error-container">
        <div class="error-message">
          <i class="error-icon">⚠️</i>
          <p>{{ error }}</p>
          <div class="error-actions">
            <button @click="loadRegionList" class="retry-btn">重试</button>
            <button v-if="showCleanupButton" @click="goToCleanup" class="cleanup-btn">清理存储</button>
          </div>
        </div>
      </div>

      <!-- 大区列表 -->
      <div v-if="!isLoading && !error" class="regions-container">

        <div class="regions-list">
          <div
            v-for="region in regions"
            :key="region.id"
            class="region-item"
            :class="{
              'selected': selectedRegion?.id === region.id,
              'maintenance': !region.isActive,
              'busy': region.isPvp
            }"
            @click="selectRegionLocal(region)"
          >
            <div class="region-name-section">
              <h3 class="region-name">{{ region.name }}</h3>
              <div class="region-status" :class="region.isActive ? 'online' : 'maintenance'">
                {{ region.isActive ? '正常' : '维护中' }}
              </div>
            </div>

          </div>
        </div>

        <!-- 分页信息 -->
        <div class="pagination-info">
          第1/1页
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <div
            @click="goBack"
            @mousedown="handleBackMouseDown"
            @mouseup="handleBackMouseUp"
            @mouseleave="handleBackMouseUp"
            class="btn-back-image"
            :class="{ 'pressed': isBackPressed }"
          >
            <img
              :src="getBackButtonImage()"
              alt="返回"
              draggable="false"
            />
          </div>
          <div
            @click="confirmSelection"
            @mousedown="handleEnterMouseDown"
            @mouseup="handleEnterMouseUp"
            @mouseleave="handleEnterMouseUp"
            class="btn-enter-image"
            :class="{ 'disabled': !selectedRegion, 'pressed': isEnterPressed }"
          >
            <img
              :src="getEnterButtonImage()"
              alt="进入游戏"
              draggable="false"
            />
          </div>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import { mapState, mapActions } from 'vuex'
import logger from '@/utils/logger'

export default {
  name: 'RegionSelect',
  components: {
    GameLayout
  },
  data() {
    return {
      selectedRegion: null,
      isLoading: false,
      error: null,
      isEnterPressed: false,
      isBackPressed: false,
      showCleanupButton: false
    }
  },
  computed: {
    ...mapState('game', ['regions'])
  },
  async created() {
    logger.debug('[RegionSelect] 页面初始化')
    await this.loadRegionList()
  },
  methods: {
    ...mapActions('game', ['loadRegions', 'selectRegion', 'loadRecommendedRegions']),

    async loadRegionList() {
      this.isLoading = true
      this.error = null

      try {
        await this.loadRegions()
        if (this.regions.length === 0) {
          this.error = '暂无可用大区'
        }
      } catch (error) {
        logger.error('[RegionSelect] 加载大区失败:', error)
          this.error = error.message || '加载大区失败，请重试'
        this.showCleanupButton = false
      } finally {
        this.isLoading = false
      }
    },

    selectRegionLocal(region) {
      this.selectedRegion = region
      logger.debug('[RegionSelect] 选择大区:', region.name)
    },

    async confirmSelection() {
      if (!this.selectedRegion) {
        this.showToast('请先选择一个大区')
        return
      }

      try {
        const success = await this.selectRegion(this.selectedRegion)
        if (success) {
          this.showToast('大区选择成功')
          // 跳转到角色选择页面
          this.$router.push('/setup/character-select')
        } else {
          this.showToast('选择大区失败，请重试')
        }
      } catch (error) {
        logger.error('[RegionSelect] 确认选择失败:', error)
        this.showToast('选择大区失败，请重试')
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    goToCleanup() {
      this.$router.push('/debug/storage')
    },

    getStatusText(isActive) {
      return isActive ? '正常' : '维护中';
    },

    handleEnterMouseDown() {
      if (this.selectedRegion) {
        this.isEnterPressed = true
      }
    },

    handleEnterMouseUp() {
      this.isEnterPressed = false
    },

    getEnterButtonImage() {
      if (!this.selectedRegion) {
        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片
      }
      return this.isEnterPressed
        ? '/static/game/UI/anniu/jr_4.png'
        : '/static/game/UI/anniu/jr_3.png'
    },

    handleBackMouseDown() {
      this.isBackPressed = true
    },

    handleBackMouseUp() {
      this.isBackPressed = false
    },

    getBackButtonImage() {
      return this.isBackPressed
        ? '/static/game/UI/anniu/fhui_.png'
        : '/static/game/UI/anniu/fhui_2.png'
    },

    showToast(message) {
      // 简单的提示实现
      const toast = document.createElement('div')
      toast.textContent = message
      toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
      `
      document.body.appendChild(toast)
      setTimeout(() => {
        document.body.removeChild(toast)
      }, 2000)
    }
  }
}
</script>

<style scoped>
.region-select-container {
  padding: 30px 20px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.region-select-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.page-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.title-background {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.title-image {
  max-width: 100%;
  height: auto;
  display: block;
}

.page-subtitle {
  font-size: 16px;
  color: #ccc;
  margin: 0;
  font-style: italic;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 300px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #ccc;
  font-size: 16px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 300px;
}

.error-message {
  text-align: center;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  padding: 30px;
  border-radius: 10px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

.retry-btn {
  padding: 8px 16px;
  background: #d4af37;
  color: #000;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background: #b8941f;
}

.cleanup-btn {
  padding: 8px 16px;
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cleanup-btn:hover {
  background: #ff5252;
}

.regions-container {
  flex: 1;
}

.regions-header {
  text-align: center;
  margin-bottom: 40px;
}

.regions-title {
  font-size: 28px;
  color: #d4af37;
  margin: 0 0 10px 0;
  text-shadow: 0 0 8px rgba(212, 175, 55, 0.4);
  font-weight: bold;
}

.regions-subtitle {
  font-size: 16px;
  color: #ccc;
  font-style: italic;
}

.regions-list {
  max-width: 600px;
  margin: 0 auto 30px auto;
}

.region-item {
  position: relative;
  height: 60px;
  margin-bottom: 12px;
  border: 3px solid #4a5568;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.region-item:hover {
  border-color: #d4af37;
}

.region-item.selected {
  border-color: #d4af37;
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
}

.region-item.maintenance {
  opacity: 0.6;
  border-color: #666;
}

.region-item.busy {
  border-color: #ff9800;
}

.region-name-section {
  position: relative;
  z-index: 3;
  padding: 0 16px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.8);
  height: 100%;
  border-radius: 5px;
}

.region-name {
  font-size: 20px;
  color: #d4af37;
  margin: 0;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.region-status {
  font-size: 12px;
  padding: 3px 6px;
  border-radius: 3px;
  font-weight: bold;
}

.region-status.online {
  background: #4caf50;
  color: white;
}

.region-status.busy {
  background: #ff9800;
  color: white;
}

.region-status.maintenance {
  background: #f44336;
  color: white;
}

.region-status.offline {
  background: #666;
  color: white;
}

.pagination-info {
  text-align: center;
  margin-bottom: 20px;
  color: #d4af37;
  font-size: 16px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 40px 0;
  margin-top: 20px;
}

.btn-back-image {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-back-image img {
  max-width: 100%;
  height: auto;
  display: block;
  transition: all 0.1s ease;
}

.btn-back-image:hover img {
  transform: scale(1.05);
}

.btn-back-image.pressed img {
  transform: scale(0.95);
}

.btn-enter-image {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-enter-image img {
  max-width: 100%;
  height: auto;
  display: block;
  transition: all 0.1s ease;
}

.btn-enter-image:hover:not(.disabled) img {
  transform: scale(1.05);
}

.btn-enter-image.pressed img {
  transform: scale(0.95);
}

.btn-enter-image.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.btn-enter-image.disabled img {
  filter: grayscale(100%);
}

.page-footer {
  text-align: center;
  margin-top: 40px;
  padding: 20px 0;
}

.footer-text {
  font-size: 14px;
  color: #888;
  font-style: italic;
}
</style>
