{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Battle.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Battle.vue", "mtime": 1749718635742}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GameLayout", "axios", "API_BASE_URL", "ERROR_MESSAGES", "logger", "components", "data", "battleState", "inBattle", "isPlayerTurn", "enemy", "logs", "characterInfo", "name", "level", "hp", "maxHp", "mp", "maxMp", "avatar", "enemies", "availableSkills", "availableItems", "showSkillMenu", "showItemMenu", "isLoading", "error", "isActionLoading", "computed", "authToken", "$store", "state", "token", "localStorage", "getItem", "selectedCharacterId", "getters", "enemyHpPercent", "Math", "round", "playerHpPercent", "playerMpPercent", "created", "warn", "fetchEnemies", "fetchCharacterInfo", "fetchBattleResources", "methods", "goBack", "confirm", "endBattle", "$router", "go", "response", "get", "headers", "info", "length", "err", "_err$response2", "status", "_err$response", "message", "UNKNOWN_ERROR", "skillsResponse", "skills", "itemsResponse", "items", "startBattle", "post", "character_id", "enemy_id", "id", "type", "_err$response3", "showToast", "performAction", "action", "processBattleResult", "_err$response4", "useSkill", "skill", "canUseSkill", "useItem", "item", "manaCost", "result", "character", "push", "battleEnd", "victory", "rewards", "exp", "gold", "getDifficultyClass", "levelDiff", "getDifficultyText", "alert"], "sources": ["src/views/game/subpages/Battle.vue"], "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"battle-page\">\n      <!-- 返回按钮 -->\n      <div class=\"header-section\">\n        <button class=\"return-btn\" @click=\"goBack\">\n          <img src=\"/static/game/UI/anniu/fhui_2.png\" alt=\"返回\" class=\"btn-image\" />\n        </button>\n        <h2 class=\"page-title\">战斗系统</h2>\n      </div>\n      \n      <!-- 战斗状态显示 -->\n      <div v-if=\"battleState.inBattle\" class=\"battle-arena\">\n        <!-- 敌人信息 -->\n        <div class=\"enemy-section\">\n          <div class=\"enemy-info\">\n            <div class=\"enemy-name\">{{ battleState.enemy.name }}</div>\n            <div class=\"enemy-level\">等级 {{ battleState.enemy.level }}</div>\n            <div class=\"health-bar\">\n              <div class=\"bar-fill enemy-hp\" :style=\"{ width: enemyHpPercent + '%' }\"></div>\n              <div class=\"bar-text\">{{ battleState.enemy.hp }}/{{ battleState.enemy.maxHp }}</div>\n            </div>\n          </div>\n          <div class=\"enemy-avatar\">\n            <img :src=\"battleState.enemy.avatar || '/static/game/UI/ts/ts2.png'\" :alt=\"battleState.enemy.name\" />\n          </div>\n        </div>\n        \n        <!-- 战斗日志 -->\n        <div class=\"battle-log\">\n          <div class=\"log-title\">战斗记录</div>\n          <div class=\"log-content\">\n            <div \n              v-for=\"(log, index) in battleState.logs\" \n              :key=\"index\"\n              class=\"log-item\"\n              :class=\"log.type\"\n            >\n              {{ log.message }}\n            </div>\n          </div>\n        </div>\n        \n        <!-- 玩家信息 -->\n        <div class=\"player-section\">\n          <div class=\"player-avatar\">\n            <img :src=\"characterInfo.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"characterInfo.name\" />\n          </div>\n          <div class=\"player-info\">\n            <div class=\"player-name\">{{ characterInfo.name }}</div>\n            <div class=\"player-level\">等级 {{ characterInfo.level }}</div>\n            <div class=\"health-bar\">\n              <div class=\"bar-fill player-hp\" :style=\"{ width: playerHpPercent + '%' }\"></div>\n              <div class=\"bar-text\">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</div>\n            </div>\n            <div class=\"mana-bar\">\n              <div class=\"bar-fill player-mp\" :style=\"{ width: playerMpPercent + '%' }\"></div>\n              <div class=\"bar-text\">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 战斗操作 -->\n        <div class=\"battle-actions\">\n          <button \n            class=\"action-btn attack\"\n            @click=\"performAction('attack')\"\n            :disabled=\"isActionLoading || battleState.isPlayerTurn === false\"\n          >\n            普通攻击\n          </button>\n          \n          <button \n            class=\"action-btn skill\"\n            @click=\"showSkillMenu = !showSkillMenu\"\n            :disabled=\"isActionLoading || battleState.isPlayerTurn === false\"\n          >\n            使用技能\n          </button>\n          \n          <button \n            class=\"action-btn item\"\n            @click=\"showItemMenu = !showItemMenu\"\n            :disabled=\"isActionLoading || battleState.isPlayerTurn === false\"\n          >\n            使用道具\n          </button>\n          \n          <button \n            class=\"action-btn flee\"\n            @click=\"performAction('flee')\"\n            :disabled=\"isActionLoading\"\n          >\n            逃跑\n          </button>\n        </div>\n        \n        <!-- 技能菜单 -->\n        <div v-if=\"showSkillMenu\" class=\"action-menu skill-menu\">\n          <div class=\"menu-title\">选择技能</div>\n          <div class=\"menu-items\">\n            <div \n              v-for=\"skill in availableSkills\" \n              :key=\"skill.id\"\n              class=\"menu-item\"\n              @click=\"useSkill(skill)\"\n              :class=\"{ disabled: !canUseSkill(skill) }\"\n            >\n              <span class=\"item-name\">{{ skill.name }}</span>\n              <span class=\"item-cost\">{{ skill.manaCost }} MP</span>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 道具菜单 -->\n        <div v-if=\"showItemMenu\" class=\"action-menu item-menu\">\n          <div class=\"menu-title\">选择道具</div>\n          <div class=\"menu-items\">\n            <div \n              v-for=\"item in availableItems\" \n              :key=\"item.id\"\n              class=\"menu-item\"\n              @click=\"useItem(item)\"\n            >\n              <span class=\"item-name\">{{ item.name }}</span>\n              <span class=\"item-count\">x{{ item.quantity }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 非战斗状态 - 选择敌人 -->\n      <div v-else class=\"enemy-selection\">\n        <div class=\"section-title\">选择对手</div>\n        \n        <!-- 加载状态 -->\n        <div v-if=\"isLoading\" class=\"loading-container\">\n          <div class=\"loading-text\">加载中...</div>\n        </div>\n        \n        <!-- 错误状态 -->\n        <div v-else-if=\"error\" class=\"error-container\">\n          <div class=\"error-text\">{{ error }}</div>\n          <button class=\"retry-btn\" @click=\"fetchEnemies\">重试</button>\n        </div>\n        \n        <!-- 敌人列表 -->\n        <div v-else class=\"enemies-grid\">\n          <div \n            v-for=\"enemy in enemies\" \n            :key=\"enemy.id\"\n            class=\"enemy-card\"\n            @click=\"startBattle(enemy)\"\n          >\n            <div class=\"enemy-card-avatar\">\n              <img :src=\"enemy.avatar || '/static/game/UI/ts/ts2.png'\" :alt=\"enemy.name\" />\n            </div>\n            <div class=\"enemy-card-info\">\n              <div class=\"enemy-card-name\">{{ enemy.name }}</div>\n              <div class=\"enemy-card-level\">等级 {{ enemy.level }}</div>\n              <div class=\"enemy-card-difficulty\" :class=\"getDifficultyClass(enemy)\">\n                {{ getDifficultyText(enemy) }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport axios from 'axios'\nimport { API_BASE_URL } from '@/api/config.js'\nimport { ERROR_MESSAGES } from '@/api/constants.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      battleState: {\n        inBattle: false,\n        isPlayerTurn: true,\n        enemy: null,\n        logs: []\n      },\n      characterInfo: {\n        name: '',\n        level: 1,\n        hp: 100,\n        maxHp: 100,\n        mp: 50,\n        maxMp: 50,\n        avatar: ''\n      },\n      enemies: [],\n      availableSkills: [],\n      availableItems: [],\n      showSkillMenu: false,\n      showItemMenu: false,\n      isLoading: true,\n      error: null,\n      isActionLoading: false\n    }\n  },\n  computed: {\n    authToken() {\n      return this.$store.state.token || localStorage.getItem('authToken')\n    },\n    selectedCharacterId() {\n      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')\n    },\n    enemyHpPercent() {\n      if (!this.battleState.enemy) return 0\n      return Math.round((this.battleState.enemy.hp / this.battleState.enemy.maxHp) * 100)\n    },\n    playerHpPercent() {\n      return Math.round((this.characterInfo.hp / this.characterInfo.maxHp) * 100)\n    },\n    playerMpPercent() {\n      return Math.round((this.characterInfo.mp / this.characterInfo.maxMp) * 100)\n    }\n  },\n  created() {\n    // 检查认证状态\n    if (!this.authToken) {\n      logger.warn('Battle页面: 未找到认证token')\n      this.error = '请先登录'\n      return\n    }\n\n    // 检查角色选择状态\n    if (!this.selectedCharacterId) {\n      logger.warn('Battle页面: 未选择角色')\n      this.error = '请先选择角色'\n      return\n    }\n\n    this.fetchEnemies()\n    this.fetchCharacterInfo()\n    this.fetchBattleResources()\n  },\n  methods: {\n    goBack() {\n      if (this.battleState.inBattle) {\n        if (confirm('确定要退出战斗吗？')) {\n          this.endBattle()\n        }\n      } else {\n        this.$router.go(-1)\n      }\n    },\n    \n    async fetchEnemies() {\n      this.isLoading = true\n      this.error = null\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/battle/enemies`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.enemies = response.data.enemies || []\n        logger.info('获取敌人列表成功', this.enemies.length)\n      } catch (err) {\n        if (err.response && err.response.status === 404) {\n          this.error = '战斗API暂未实现，请等待后端开发完成'\n        } else {\n          this.error = err.response?.data?.message || err.message || ERROR_MESSAGES.UNKNOWN_ERROR\n        }\n        logger.warn('获取敌人列表失败', err.response?.status, err.message)\n      } finally {\n        this.isLoading = false\n      }\n    },\n    \n    async fetchCharacterInfo() {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.characterInfo = {\n          name: response.data.name || '',\n          level: response.data.level || 1,\n          hp: response.data.hp || 100,\n          maxHp: response.data.maxHp || 100,\n          mp: response.data.mp || 50,\n          maxMp: response.data.maxMp || 50,\n          avatar: response.data.avatar || ''\n        }\n      } catch (err) {\n        logger.error('获取角色信息失败', err)\n      }\n    },\n    \n    async fetchBattleResources() {\n      try {\n        // 获取可用技能\n        const skillsResponse = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/skills/battle`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        this.availableSkills = skillsResponse.data.skills || []\n        \n        // 获取可用道具\n        const itemsResponse = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/items/battle`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        this.availableItems = itemsResponse.data.items || []\n      } catch (err) {\n        logger.error('获取战斗资源失败', err)\n      }\n    },\n    \n    async startBattle(enemy) {\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/battle/start`,\n          { \n            character_id: this.selectedCharacterId,\n            enemy_id: enemy.id \n          },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.battleState = {\n          inBattle: true,\n          isPlayerTurn: true,\n          enemy: response.data.enemy,\n          logs: [{ message: `战斗开始！遭遇了 ${enemy.name}`, type: 'system' }]\n        }\n        \n        logger.info('开始战斗', enemy.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('开始战斗失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async performAction(action) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      this.showSkillMenu = false\n      this.showItemMenu = false\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/battle/action`,\n          { \n            action: action,\n            character_id: this.selectedCharacterId\n          },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.processBattleResult(response.data)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('战斗行动失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async useSkill(skill) {\n      if (!this.canUseSkill(skill)) return\n      \n      this.showSkillMenu = false\n      await this.performAction(`skill:${skill.id}`)\n    },\n    \n    async useItem(item) {\n      this.showItemMenu = false\n      await this.performAction(`item:${item.id}`)\n    },\n    \n    canUseSkill(skill) {\n      return this.characterInfo.mp >= skill.manaCost\n    },\n    \n    processBattleResult(result) {\n      // 更新战斗状态\n      if (result.enemy) {\n        this.battleState.enemy = result.enemy\n      }\n      \n      if (result.character) {\n        this.characterInfo.hp = result.character.hp\n        this.characterInfo.mp = result.character.mp\n      }\n      \n      // 添加战斗日志\n      if (result.logs) {\n        this.battleState.logs.push(...result.logs)\n      }\n      \n      // 检查战斗是否结束\n      if (result.battleEnd) {\n        this.endBattle(result.victory, result.rewards)\n      } else {\n        this.battleState.isPlayerTurn = result.isPlayerTurn\n      }\n    },\n    \n    endBattle(victory = false, rewards = null) {\n      if (victory) {\n        let message = '战斗胜利！'\n        if (rewards) {\n          message += ` 获得经验: ${rewards.exp || 0}, 金币: ${rewards.gold || 0}`\n        }\n        this.showToast(message, 'success')\n      }\n      \n      this.battleState = {\n        inBattle: false,\n        isPlayerTurn: true,\n        enemy: null,\n        logs: []\n      }\n      \n      // 刷新角色信息\n      this.fetchCharacterInfo()\n    },\n    \n    getDifficultyClass(enemy) {\n      const levelDiff = enemy.level - this.characterInfo.level\n      if (levelDiff <= -5) return 'very-easy'\n      if (levelDiff <= -2) return 'easy'\n      if (levelDiff <= 2) return 'normal'\n      if (levelDiff <= 5) return 'hard'\n      return 'very-hard'\n    },\n    \n    getDifficultyText(enemy) {\n      const levelDiff = enemy.level - this.characterInfo.level\n      if (levelDiff <= -5) return '非常简单'\n      if (levelDiff <= -2) return '简单'\n      if (levelDiff <= 2) return '普通'\n      if (levelDiff <= 5) return '困难'\n      return '非常困难'\n    },\n    \n    showToast(message, type = 'error') {\n      alert(message)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.battle-page {\n  padding: 15px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2d1b69, #1a0f3d);\n  color: #fff;\n}\n\n.header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  gap: 15px;\n}\n\n.return-btn {\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  padding: 0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.btn-image {\n  width: 60px;\n  height: 40px;\n  object-fit: contain;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #fff;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n// 战斗界面\n.battle-arena {\n  display: grid;\n  grid-template-areas:\n    \"enemy enemy enemy\"\n    \"log log log\"\n    \"player player player\"\n    \"actions actions actions\";\n  grid-template-rows: auto 1fr auto auto;\n  gap: 20px;\n  height: calc(100vh - 120px);\n}\n\n.enemy-section {\n  grid-area: enemy;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(255, 0, 0, 0.1);\n  border: 2px solid rgba(255, 0, 0, 0.3);\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.enemy-info {\n  flex: 1;\n}\n\n.enemy-name {\n  font-size: 20px;\n  font-weight: bold;\n  color: #ff6b6b;\n  margin-bottom: 5px;\n}\n\n.enemy-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 10px;\n}\n\n.enemy-avatar {\n  width: 80px;\n  height: 80px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 10px;\n    border: 2px solid rgba(255, 0, 0, 0.5);\n  }\n}\n\n.battle-log {\n  grid-area: log;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n}\n\n.log-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #4ecdc4;\n  margin-bottom: 10px;\n  text-align: center;\n}\n\n.log-content {\n  flex: 1;\n  overflow-y: auto;\n  max-height: 200px;\n}\n\n.log-item {\n  padding: 5px 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  font-size: 14px;\n\n  &.system {\n    color: #4ecdc4;\n  }\n\n  &.player {\n    color: #2ecc71;\n  }\n\n  &.enemy {\n    color: #ff6b6b;\n  }\n\n  &.damage {\n    color: #f39c12;\n  }\n}\n\n.player-section {\n  grid-area: player;\n  display: flex;\n  align-items: center;\n  background: rgba(0, 255, 0, 0.1);\n  border: 2px solid rgba(0, 255, 0, 0.3);\n  border-radius: 12px;\n  padding: 20px;\n  gap: 20px;\n}\n\n.player-avatar {\n  width: 80px;\n  height: 80px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 10px;\n    border: 2px solid rgba(0, 255, 0, 0.5);\n  }\n}\n\n.player-info {\n  flex: 1;\n}\n\n.player-name {\n  font-size: 20px;\n  font-weight: bold;\n  color: #2ecc71;\n  margin-bottom: 5px;\n}\n\n.player-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 10px;\n}\n\n.health-bar, .mana-bar {\n  position: relative;\n  height: 20px;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 8px;\n}\n\n.bar-fill {\n  height: 100%;\n  transition: width 0.5s ease;\n\n  &.enemy-hp {\n    background: linear-gradient(90deg, #ff4444, #ff6666);\n  }\n\n  &.player-hp {\n    background: linear-gradient(90deg, #2ecc71, #27ae60);\n  }\n\n  &.player-mp {\n    background: linear-gradient(90deg, #3498db, #2980b9);\n  }\n}\n\n.bar-text {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.battle-actions {\n  grid-area: actions;\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n}\n\n.action-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  min-width: 120px;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.attack {\n    background: #e74c3c;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #c0392b;\n      transform: translateY(-2px);\n    }\n  }\n\n  &.skill {\n    background: #9b59b6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #8e44ad;\n      transform: translateY(-2px);\n    }\n  }\n\n  &.item {\n    background: #f39c12;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #e67e22;\n      transform: translateY(-2px);\n    }\n  }\n\n  &.flee {\n    background: #95a5a6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #7f8c8d;\n      transform: translateY(-2px);\n    }\n  }\n}\n\n.action-menu {\n  position: absolute;\n  bottom: 120px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(0, 0, 0, 0.9);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  padding: 20px;\n  min-width: 300px;\n  max-height: 300px;\n  overflow-y: auto;\n  z-index: 100;\n}\n\n.menu-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #4ecdc4;\n  margin-bottom: 15px;\n  text-align: center;\n}\n\n.menu-items {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.menu-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover:not(.disabled) {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-1px);\n  }\n\n  &.disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.item-name {\n  font-weight: 500;\n}\n\n.item-cost, .item-count {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n// 敌人选择界面\n.enemy-selection {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.section-title {\n  font-size: 20px;\n  font-weight: bold;\n  color: #4ecdc4;\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.loading-container, .error-container {\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.loading-text {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.error-text {\n  font-size: 16px;\n  color: #ff6b6b;\n  margin-bottom: 15px;\n}\n\n.retry-btn {\n  padding: 10px 20px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n}\n\n.enemies-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.enemy-card {\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 12px;\n  padding: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.12);\n    transform: translateY(-3px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.enemy-card-avatar {\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 15px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 10px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n}\n\n.enemy-card-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 5px;\n}\n\n.enemy-card-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 10px;\n}\n\n.enemy-card-difficulty {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  display: inline-block;\n\n  &.very-easy {\n    background: rgba(46, 204, 113, 0.2);\n    color: #2ecc71;\n    border: 1px solid #2ecc71;\n  }\n\n  &.easy {\n    background: rgba(52, 152, 219, 0.2);\n    color: #3498db;\n    border: 1px solid #3498db;\n  }\n\n  &.normal {\n    background: rgba(241, 196, 15, 0.2);\n    color: #f1c40f;\n    border: 1px solid #f1c40f;\n  }\n\n  &.hard {\n    background: rgba(243, 156, 18, 0.2);\n    color: #f39c12;\n    border: 1px solid #f39c12;\n  }\n\n  &.very-hard {\n    background: rgba(231, 76, 60, 0.2);\n    color: #e74c3c;\n    border: 1px solid #e74c3c;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .battle-container {\n    padding: 10px;\n  }\n\n  .header-section {\n    margin-bottom: 15px;\n    gap: 10px;\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .btn-image {\n    width: 50px;\n    height: 35px;\n  }\n\n  .battle-arena {\n    grid-template-areas:\n      \"enemy\"\n      \"log\"\n      \"player\"\n      \"actions\";\n    grid-template-columns: 1fr;\n    height: auto;\n    gap: 15px;\n  }\n\n  .enemy-section, .player-section {\n    flex-direction: column;\n    text-align: center;\n    gap: 15px;\n  }\n\n  .battle-actions {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .action-btn {\n    flex: 1;\n    min-width: calc(50% - 5px);\n    padding: 10px 16px;\n    font-size: 14px;\n  }\n\n  .action-menu {\n    bottom: 80px;\n    left: 10px;\n    right: 10px;\n    transform: none;\n    min-width: auto;\n  }\n\n  .enemies-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 15px;\n  }\n}\n\n@media (max-width: 480px) {\n  .battle-container {\n    padding: 8px;\n  }\n\n  .enemy-section, .player-section {\n    padding: 15px;\n  }\n\n  .enemy-avatar, .player-avatar {\n    width: 60px;\n    height: 60px;\n  }\n\n  .action-btn {\n    width: 100%;\n    margin-bottom: 8px;\n  }\n\n  .battle-actions {\n    flex-direction: column;\n  }\n\n  .enemies-grid {\n    grid-template-columns: 1fr;\n  }\n}\n</style>\n"], "mappings": ";AA4KA,OAAAA,UAAA;AACA,OAAAC,KAAA;AACA,SAAAC,YAAA;AACA,SAAAC,cAAA;AACA,OAAAC,MAAA;AAEA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;QACAC,QAAA;QACAC,YAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,aAAA;QACAC,IAAA;QACAC,KAAA;QACAC,EAAA;QACAC,KAAA;QACAC,EAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,eAAA;MACAC,cAAA;MACAC,aAAA;MACAC,YAAA;MACAC,SAAA;MACAC,KAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,KAAA,IAAAC,YAAA,CAAAC,OAAA;IACA;IACAC,oBAAA;MACA,YAAAL,MAAA,CAAAM,OAAA,6BAAAH,YAAA,CAAAC,OAAA;IACA;IACAG,eAAA;MACA,UAAA9B,WAAA,CAAAG,KAAA;MACA,OAAA4B,IAAA,CAAAC,KAAA,MAAAhC,WAAA,CAAAG,KAAA,CAAAK,EAAA,QAAAR,WAAA,CAAAG,KAAA,CAAAM,KAAA;IACA;IACAwB,gBAAA;MACA,OAAAF,IAAA,CAAAC,KAAA,MAAA3B,aAAA,CAAAG,EAAA,QAAAH,aAAA,CAAAI,KAAA;IACA;IACAyB,gBAAA;MACA,OAAAH,IAAA,CAAAC,KAAA,MAAA3B,aAAA,CAAAK,EAAA,QAAAL,aAAA,CAAAM,KAAA;IACA;EACA;EACAwB,QAAA;IACA;IACA,UAAAb,SAAA;MACAzB,MAAA,CAAAuC,IAAA;MACA,KAAAjB,KAAA;MACA;IACA;;IAEA;IACA,UAAAS,mBAAA;MACA/B,MAAA,CAAAuC,IAAA;MACA,KAAAjB,KAAA;MACA;IACA;IAEA,KAAAkB,YAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,oBAAA;EACA;EACAC,OAAA;IACAC,OAAA;MACA,SAAAzC,WAAA,CAAAC,QAAA;QACA,IAAAyC,OAAA;UACA,KAAAC,SAAA;QACA;MACA;QACA,KAAAC,OAAA,CAAAC,EAAA;MACA;IACA;IAEA,MAAAR,aAAA;MACA,KAAAnB,SAAA;MACA,KAAAC,KAAA;MAEA;QACA,MAAA2B,QAAA,SAAApD,KAAA,CAAAqD,GAAA,IAAApD,YAAA;UACAqD,OAAA;YACA,gCAAA1B,SAAA;YACA;UACA;QACA;QAEA,KAAAT,OAAA,GAAAiC,QAAA,CAAA/C,IAAA,CAAAc,OAAA;QACAhB,MAAA,CAAAoD,IAAA,kBAAApC,OAAA,CAAAqC,MAAA;MACA,SAAAC,GAAA;QAAA,IAAAC,cAAA;QACA,IAAAD,GAAA,CAAAL,QAAA,IAAAK,GAAA,CAAAL,QAAA,CAAAO,MAAA;UACA,KAAAlC,KAAA;QACA;UAAA,IAAAmC,aAAA;UACA,KAAAnC,KAAA,KAAAmC,aAAA,GAAAH,GAAA,CAAAL,QAAA,cAAAQ,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAAvD,IAAA,cAAAuD,aAAA,uBAAAA,aAAA,CAAAC,OAAA,KAAAJ,GAAA,CAAAI,OAAA,IAAA3D,cAAA,CAAA4D,aAAA;QACA;QACA3D,MAAA,CAAAuC,IAAA,cAAAgB,cAAA,GAAAD,GAAA,CAAAL,QAAA,cAAAM,cAAA,uBAAAA,cAAA,CAAAC,MAAA,EAAAF,GAAA,CAAAI,OAAA;MACA;QACA,KAAArC,SAAA;MACA;IACA;IAEA,MAAAoB,mBAAA;MACA;QACA,MAAAQ,QAAA,SAAApD,KAAA,CAAAqD,GAAA,IAAApD,YAAA,oBAAAiC,mBAAA;UACAoB,OAAA;YACA,gCAAA1B,SAAA;YACA;UACA;QACA;QAEA,KAAAjB,aAAA;UACAC,IAAA,EAAAwC,QAAA,CAAA/C,IAAA,CAAAO,IAAA;UACAC,KAAA,EAAAuC,QAAA,CAAA/C,IAAA,CAAAQ,KAAA;UACAC,EAAA,EAAAsC,QAAA,CAAA/C,IAAA,CAAAS,EAAA;UACAC,KAAA,EAAAqC,QAAA,CAAA/C,IAAA,CAAAU,KAAA;UACAC,EAAA,EAAAoC,QAAA,CAAA/C,IAAA,CAAAW,EAAA;UACAC,KAAA,EAAAmC,QAAA,CAAA/C,IAAA,CAAAY,KAAA;UACAC,MAAA,EAAAkC,QAAA,CAAA/C,IAAA,CAAAa,MAAA;QACA;MACA,SAAAuC,GAAA;QACAtD,MAAA,CAAAsB,KAAA,aAAAgC,GAAA;MACA;IACA;IAEA,MAAAZ,qBAAA;MACA;QACA;QACA,MAAAkB,cAAA,SAAA/D,KAAA,CAAAqD,GAAA,IAAApD,YAAA,oBAAAiC,mBAAA;UACAoB,OAAA;YACA,gCAAA1B,SAAA;YACA;UACA;QACA;QACA,KAAAR,eAAA,GAAA2C,cAAA,CAAA1D,IAAA,CAAA2D,MAAA;;QAEA;QACA,MAAAC,aAAA,SAAAjE,KAAA,CAAAqD,GAAA,IAAApD,YAAA,oBAAAiC,mBAAA;UACAoB,OAAA;YACA,gCAAA1B,SAAA;YACA;UACA;QACA;QACA,KAAAP,cAAA,GAAA4C,aAAA,CAAA5D,IAAA,CAAA6D,KAAA;MACA,SAAAT,GAAA;QACAtD,MAAA,CAAAsB,KAAA,aAAAgC,GAAA;MACA;IACA;IAEA,MAAAU,YAAA1D,KAAA;MACA,KAAAiB,eAAA;MAEA;QACA,MAAA0B,QAAA,SAAApD,KAAA,CAAAoE,IAAA,CACA,GAAAnE,YAAA,iBACA;UACAoE,YAAA,OAAAnC,mBAAA;UACAoC,QAAA,EAAA7D,KAAA,CAAA8D;QACA,GACA;UACAjB,OAAA;YACA,gCAAA1B,SAAA;YACA;UACA;QACA,CACA;QAEA,KAAAtB,WAAA;UACAC,QAAA;UACAC,YAAA;UACAC,KAAA,EAAA2C,QAAA,CAAA/C,IAAA,CAAAI,KAAA;UACAC,IAAA;YAAAmD,OAAA,cAAApD,KAAA,CAAAG,IAAA;YAAA4D,IAAA;UAAA;QACA;QAEArE,MAAA,CAAAoD,IAAA,SAAA9C,KAAA,CAAAG,IAAA;MACA,SAAA6C,GAAA;QAAA,IAAAgB,cAAA;QACA,KAAAC,SAAA,GAAAD,cAAA,GAAAhB,GAAA,CAAAL,QAAA,cAAAqB,cAAA,gBAAAA,cAAA,GAAAA,cAAA,CAAApE,IAAA,cAAAoE,cAAA,uBAAAA,cAAA,CAAAZ,OAAA,KAAA3D,cAAA,CAAA4D,aAAA;QACA3D,MAAA,CAAAsB,KAAA,WAAAgC,GAAA;MACA;QACA,KAAA/B,eAAA;MACA;IACA;IAEA,MAAAiD,cAAAC,MAAA;MACA,SAAAlD,eAAA;MACA,KAAAA,eAAA;MACA,KAAAJ,aAAA;MACA,KAAAC,YAAA;MAEA;QACA,MAAA6B,QAAA,SAAApD,KAAA,CAAAoE,IAAA,CACA,GAAAnE,YAAA,kBACA;UACA2E,MAAA,EAAAA,MAAA;UACAP,YAAA,OAAAnC;QACA,GACA;UACAoB,OAAA;YACA,gCAAA1B,SAAA;YACA;UACA;QACA,CACA;QAEA,KAAAiD,mBAAA,CAAAzB,QAAA,CAAA/C,IAAA;MACA,SAAAoD,GAAA;QAAA,IAAAqB,cAAA;QACA,KAAAJ,SAAA,GAAAI,cAAA,GAAArB,GAAA,CAAAL,QAAA,cAAA0B,cAAA,gBAAAA,cAAA,GAAAA,cAAA,CAAAzE,IAAA,cAAAyE,cAAA,uBAAAA,cAAA,CAAAjB,OAAA,KAAA3D,cAAA,CAAA4D,aAAA;QACA3D,MAAA,CAAAsB,KAAA,WAAAgC,GAAA;MACA;QACA,KAAA/B,eAAA;MACA;IACA;IAEA,MAAAqD,SAAAC,KAAA;MACA,UAAAC,WAAA,CAAAD,KAAA;MAEA,KAAA1D,aAAA;MACA,WAAAqD,aAAA,UAAAK,KAAA,CAAAT,EAAA;IACA;IAEA,MAAAW,QAAAC,IAAA;MACA,KAAA5D,YAAA;MACA,WAAAoD,aAAA,SAAAQ,IAAA,CAAAZ,EAAA;IACA;IAEAU,YAAAD,KAAA;MACA,YAAArE,aAAA,CAAAK,EAAA,IAAAgE,KAAA,CAAAI,QAAA;IACA;IAEAP,oBAAAQ,MAAA;MACA;MACA,IAAAA,MAAA,CAAA5E,KAAA;QACA,KAAAH,WAAA,CAAAG,KAAA,GAAA4E,MAAA,CAAA5E,KAAA;MACA;MAEA,IAAA4E,MAAA,CAAAC,SAAA;QACA,KAAA3E,aAAA,CAAAG,EAAA,GAAAuE,MAAA,CAAAC,SAAA,CAAAxE,EAAA;QACA,KAAAH,aAAA,CAAAK,EAAA,GAAAqE,MAAA,CAAAC,SAAA,CAAAtE,EAAA;MACA;;MAEA;MACA,IAAAqE,MAAA,CAAA3E,IAAA;QACA,KAAAJ,WAAA,CAAAI,IAAA,CAAA6E,IAAA,IAAAF,MAAA,CAAA3E,IAAA;MACA;;MAEA;MACA,IAAA2E,MAAA,CAAAG,SAAA;QACA,KAAAvC,SAAA,CAAAoC,MAAA,CAAAI,OAAA,EAAAJ,MAAA,CAAAK,OAAA;MACA;QACA,KAAApF,WAAA,CAAAE,YAAA,GAAA6E,MAAA,CAAA7E,YAAA;MACA;IACA;IAEAyC,UAAAwC,OAAA,UAAAC,OAAA;MACA,IAAAD,OAAA;QACA,IAAA5B,OAAA;QACA,IAAA6B,OAAA;UACA7B,OAAA,cAAA6B,OAAA,CAAAC,GAAA,cAAAD,OAAA,CAAAE,IAAA;QACA;QACA,KAAAlB,SAAA,CAAAb,OAAA;MACA;MAEA,KAAAvD,WAAA;QACAC,QAAA;QACAC,YAAA;QACAC,KAAA;QACAC,IAAA;MACA;;MAEA;MACA,KAAAkC,kBAAA;IACA;IAEAiD,mBAAApF,KAAA;MACA,MAAAqF,SAAA,GAAArF,KAAA,CAAAI,KAAA,QAAAF,aAAA,CAAAE,KAAA;MACA,IAAAiF,SAAA;MACA,IAAAA,SAAA;MACA,IAAAA,SAAA;MACA,IAAAA,SAAA;MACA;IACA;IAEAC,kBAAAtF,KAAA;MACA,MAAAqF,SAAA,GAAArF,KAAA,CAAAI,KAAA,QAAAF,aAAA,CAAAE,KAAA;MACA,IAAAiF,SAAA;MACA,IAAAA,SAAA;MACA,IAAAA,SAAA;MACA,IAAAA,SAAA;MACA;IACA;IAEApB,UAAAb,OAAA,EAAAW,IAAA;MACAwB,KAAA,CAAAnC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}