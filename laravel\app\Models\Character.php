<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Character extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'name',
        'gender',
        'profession',
        'level',
        'experience',
        'attributes',
        'equipment',
        'skills',
        'stats',
        'region_id',
        'gold',
        'silver',
        'energy',
        'attribute_points',
    ];

    /**
     * 应该被转换为原生类型的属性
     */
    protected $casts = [
        'attributes' => 'array',
        'equipment' => 'array',
        'skills' => 'array',
        'stats' => 'array',
    ];

    /**
     * 获取该角色所属的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取角色的物品（背包）
     */
    public function items()
    {
        return $this->hasMany(CharacterItem::class);
    }

    /**
     * 获取角色的队伍成员记录
     */
    public function teamMember()
    {
        return $this->hasOne(TeamMember::class);
    }

    /**
     * 获取角色当前所在的队伍
     */
    public function team()
    {
        return $this->hasOneThrough(
            Team::class,
            TeamMember::class,
            'character_id', // TeamMember表的外键
            'id', // Team表的本地键
            'id', // Character表的本地键
            'team_id' // TeamMember表的外键
        );
    }

    /**
     * 获取角色作为队长的队伍
     */
    public function leadingTeam()
    {
        return $this->hasOne(Team::class, 'leader_id');
    }

    /**
     * 获取角色收到的队伍邀请
     */
    public function teamInvites()
    {
        return $this->hasMany(TeamInvite::class, 'character_id');
    }

    /**
     * 获取角色发送的队伍邀请
     */
    public function sentTeamInvites()
    {
        return $this->hasMany(TeamInvite::class, 'inviter_id');
    }

    /**
     * 获取角色的VIP信息
     */
    public function vip()
    {
        return $this->hasOne(CharacterVip::class);
    }

    /**
     * 获取角色的VIP奖励领取记录
     */
    public function vipRewardClaims()
    {
        return $this->hasMany(VipRewardClaim::class);
    }

    /**
     * 获取角色所在的大区
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 添加金砖
     *
     * @param int $amount
     * @return $this
     */
    public function addGold($amount)
    {
        $this->gold = ($this->gold ?? 0) + $amount;
        return $this;
    }

    /**
     * 添加银两
     *
     * @param int $amount
     * @return $this
     */
    public function addSilver($amount)
    {
        $this->silver = ($this->silver ?? 0) + $amount;
        return $this;
    }

    /**
     * 添加体力
     *
     * @param int $amount
     * @return $this
     */
    public function addEnergy($amount)
    {
        $this->energy = ($this->energy ?? 0) + $amount;
        return $this;
    }

    /**
     * 添加VIP经验
     *
     * @param int $amount
     * @return CharacterVip
     */
    public function addVipExp($amount)
    {
        $vip = CharacterVip::firstOrCreate(
            ['character_id' => $this->id],
            ['level' => 0, 'exp' => 0]
        );

        return $vip->addExp($amount);
    }

    /**
     * 添加经验值并处理升级
     *
     * @param int $amount
     * @return array 包含是否升级等信息的数组
     */
    public function addExperience($amount)
    {
        $this->experience = ($this->experience ?? 0) + $amount;
        $levelUp = false;
        $oldLevel = $this->level;
        $levelUpRewards = [];

        // 计算升级所需经验
        $expRequired = $this->calculateExpRequired($this->level);

        // 检查是否升级
        while ($this->experience >= $expRequired) {
            // 扣除升级所需经验
            $this->experience -= $expRequired;

            // 升级
            $this->level += 1;

            // 获取升级奖励
            $rewards = $this->getLevelUpRewards($this->level);
            $levelUpRewards[] = $rewards;

            // 应用升级奖励
            $this->applyLevelUpRewards($rewards);

            // 更新升级所需经验
            $expRequired = $this->calculateExpRequired($this->level);

            $levelUp = true;
        }

        // 更新基础属性（与等级一致）
        if ($levelUp) {
            $this->updateBaseAttributes();
        }

        // 重新计算战斗属性
        $this->recalculateStats();

        // 保存角色数据
        $this->save();

        return [
            'level_up' => $levelUp,
            'old_level' => $oldLevel,
            'new_level' => $this->level,
            'exp' => $this->experience,
            'exp_required' => $expRequired,
            'attribute_points' => $this->attribute_points,
            'rewards' => $levelUpRewards
        ];
    }

    /**
     * 升级时自动增加基础属性
     */
    private function updateBaseAttributes()
    {
        // 获取当前属性
        $attributes = $this->attributes;

        // 确保 $attributes 是一个数组
        if (!is_array($attributes)) {
            // 如果是 JSON 字符串，尝试解码
            if (is_string($attributes)) {
                $attributes = json_decode($attributes, true);
            }

            // 如果解码失败或为空，初始化为空数组
            if (!is_array($attributes)) {
                $attributes = [];
            }
        }

        // 升级时自动增加基础属性，根据职业特色分配
        $attributeGrowth = $this->getAttributeGrowthPerLevel();

        // 自动增加基础属性（每级固定成长）
        $attributes['vitality'] = ($attributes['vitality'] ?? 0) + $attributeGrowth['vitality'];
        $attributes['intelligence'] = ($attributes['intelligence'] ?? 0) + $attributeGrowth['intelligence'];
        $attributes['strength'] = ($attributes['strength'] ?? 0) + $attributeGrowth['strength'];
        $attributes['dexterity'] = ($attributes['dexterity'] ?? 0) + $attributeGrowth['dexterity'];

        // 使用fillable方式设置JSON字段
        $this->fill(['attributes' => $attributes]);
    }

    /**
     * 获取每级自动属性成长（基础成长）
     */
    private function getAttributeGrowthPerLevel()
    {
        switch ($this->profession) {
            case 'warrior':
                // 武士：力量特化
                return [
                    'vitality' => 1,      // 每级自动+1体质
                    'strength' => 2,      // 每级自动+2力量
                    'intelligence' => 1,  // 每级自动+1智力
                    'dexterity' => 1      // 每级自动+1敏捷
                ];

            case 'scholar':
                // 文人：体质特化
                return [
                    'vitality' => 2,      // 每级自动+2体质
                    'strength' => 1,      // 每级自动+1力量
                    'intelligence' => 1,  // 每级自动+1智力
                    'dexterity' => 1      // 每级自动+1敏捷
                ];

            case 'mystic':
                // 异人：智力特化
                return [
                    'vitality' => 1,      // 每级自动+1体质
                    'strength' => 1,      // 每级自动+1力量
                    'intelligence' => 2,  // 每级自动+2智力
                    'dexterity' => 1      // 每级自动+1敏捷
                ];

            default:
                // 默认平衡成长
                return [
                    'vitality' => 1,
                    'strength' => 1,
                    'intelligence' => 1,
                    'dexterity' => 1
                ];
        }
    }

    /**
     * 计算升级所需经验
     *
     * @param int $level
     * @return int
     */
    public function calculateExpRequired($level)
    {
        // 新的经验计算公式：基础经验 * 等级 * (1 + 等级/10)
        // 这样低级时经验需求较少，高级时增长更快
        $baseExp = 1000;
        return floor($baseExp * $level * (1 + $level / 10));
    }

    /**
     * 获取升级奖励
     *
     * @param int $level
     * @return array
     */
    public function getLevelUpRewards($level)
    {
        $rewards = [
            'attribute_points' => 0,
            'gold' => 0,
            'silver' => 0,
            'energy' => 0,
            'base_stats' => [],
            'special_rewards' => [],
            'titles' => [],
            'skills' => []
        ];

        // 基础奖励：每级都有
        $rewards['attribute_points'] = $this->getAttributePointsPerLevel($level);
        $rewards['silver'] = $this->getSilverReward($level);
        $rewards['energy'] = 10; // 每级恢复10点体力

        // 基础属性成长（根据职业不同）
        $rewards['base_stats'] = $this->getBaseStatsGrowth($level);

        // 特殊等级奖励
        if ($level % 5 == 0) {
            // 每5级给金砖
            $rewards['gold'] = floor($level / 5) * 10;
        }

        if ($level % 10 == 0) {
            // 每10级给特殊奖励
            $rewards['special_rewards'] = $this->getSpecialRewards($level);
        }

        // 重要等级的称号奖励
        if (in_array($level, [10, 20, 30, 50, 80, 100])) {
            $rewards['titles'] = $this->getTitleRewards($level);
        }

        // 技能解锁
        $rewards['skills'] = $this->getSkillUnlocks($level);

        return $rewards;
    }

    /**
     * 根据等级获取属性点奖励
     */
    private function getAttributePointsPerLevel($level)
    {
        // 每级固定4点属性点供玩家自由分配
        // 这是在自动属性成长之外的额外分配点
        return 4;
    }

    /**
     * 获取银两奖励
     */
    private function getSilverReward($level)
    {
        return $level * 100 + rand(50, 150);
    }

    /**
     * 获取基础属性成长
     */
    private function getBaseStatsGrowth($level)
    {
        $growth = [];

        switch ($this->profession) {
            case 'warrior':
                // 武士：高生命和攻击成长
                $growth = [
                    'hp_bonus' => $level * 15,
                    'attack_bonus' => $level * 2,
                    'defense_bonus' => $level * 1
                ];
                break;

            case 'scholar':
                // 文人：高法力和法术攻击成长
                $growth = [
                    'mp_bonus' => $level * 12,
                    'magic_attack_bonus' => $level * 2,
                    'magic_defense_bonus' => $level * 1
                ];
                break;

            case 'mystic':
                // 异人：平衡成长
                $growth = [
                    'hp_bonus' => $level * 10,
                    'mp_bonus' => $level * 8,
                    'speed_bonus' => $level * 1
                ];
                break;
        }

        return $growth;
    }

    /**
     * 获取特殊奖励
     */
    private function getSpecialRewards($level)
    {
        $rewards = [];

        if ($level == 10) {
            $rewards[] = '解锁坐骑系统';
        }

        if ($level == 20) {
            $rewards[] = '解锁门派系统';
        }

        if ($level == 30) {
            $rewards[] = '解锁结婚系统';
        }

        if ($level == 50) {
            $rewards[] = '解锁转生系统';
        }

        return $rewards;
    }

    /**
     * 获取称号奖励
     */
    private function getTitleRewards($level)
    {
        $titles = [];

        switch ($level) {
            case 10:
                $titles[] = '初出茅庐';
                break;
            case 20:
                $titles[] = '小有名气';
                break;
            case 30:
                $titles[] = '声名远播';
                break;
            case 50:
                $titles[] = '一代宗师';
                break;
            case 80:
                $titles[] = '武林泰斗';
                break;
            case 100:
                $titles[] = '绝世高手';
                break;
        }

        return $titles;
    }

    /**
     * 获取技能解锁
     */
    private function getSkillUnlocks($level)
    {
        $skills = [];

        // 根据职业和等级解锁不同技能
        switch ($this->profession) {
            case 'warrior':
                if ($level == 5) $skills[] = '重击';
                if ($level == 10) $skills[] = '狂暴';
                if ($level == 15) $skills[] = '护体';
                if ($level == 20) $skills[] = '破甲';
                break;

            case 'scholar':
                if ($level == 5) $skills[] = '火球术';
                if ($level == 10) $skills[] = '冰箭术';
                if ($level == 15) $skills[] = '治疗术';
                if ($level == 20) $skills[] = '群体攻击';
                break;

            case 'mystic':
                if ($level == 5) $skills[] = '隐身术';
                if ($level == 10) $skills[] = '瞬移';
                if ($level == 15) $skills[] = '毒术';
                if ($level == 20) $skills[] = '召唤';
                break;
        }

        return $skills;
    }

    /**
     * 应用升级奖励
     *
     * @param array $rewards
     */
    private function applyLevelUpRewards($rewards)
    {
        // 应用属性点
        if ($rewards['attribute_points'] > 0) {
            $this->attribute_points = ($this->attribute_points ?? 0) + $rewards['attribute_points'];
        }

        // 应用金砖
        if ($rewards['gold'] > 0) {
            $this->gold = ($this->gold ?? 0) + $rewards['gold'];
        }

        // 应用银两
        if ($rewards['silver'] > 0) {
            $this->silver = ($this->silver ?? 0) + $rewards['silver'];
        }

        // 应用体力
        if ($rewards['energy'] > 0) {
            $this->energy = min(($this->energy ?? 0) + $rewards['energy'], 200); // 体力上限200
        }

        // 应用基础属性成长
        if (!empty($rewards['base_stats'])) {
            $this->applyBaseStatsGrowth($rewards['base_stats']);
        }

        // 记录特殊奖励、称号、技能等（这里可以扩展到单独的表）
        $this->recordSpecialRewards($rewards);
    }

    /**
     * 应用基础属性成长
     */
    private function applyBaseStatsGrowth($baseStats)
    {
        // 获取当前stats数据
        $currentStats = $this->stats;

        // 确保 $currentStats 是一个数组
        if (!is_array($currentStats)) {
            // 如果是 JSON 字符串，尝试解码
            if (is_string($currentStats)) {
                $currentStats = json_decode($currentStats, true);
            }

            // 如果解码失败或为空，初始化为空数组
            if (!is_array($currentStats)) {
                $currentStats = [];
            }
        }

        foreach ($baseStats as $stat => $bonus) {
            switch ($stat) {
                case 'hp_bonus':
                    $currentStats['hp'] = ($currentStats['hp'] ?? 0) + $bonus;
                    $currentStats['max_hp'] = ($currentStats['max_hp'] ?? 0) + $bonus;
                    break;
                case 'mp_bonus':
                    $currentStats['mp'] = ($currentStats['mp'] ?? 0) + $bonus;
                    $currentStats['max_mp'] = ($currentStats['max_mp'] ?? 0) + $bonus;
                    break;
                case 'attack_bonus':
                    $currentStats['attack'] = ($currentStats['attack'] ?? 0) + $bonus;
                    break;
                case 'defense_bonus':
                    $currentStats['defense'] = ($currentStats['defense'] ?? 0) + $bonus;
                    break;
                case 'magic_attack_bonus':
                    $currentStats['magicAttack'] = ($currentStats['magicAttack'] ?? 0) + $bonus;
                    break;
                case 'magic_defense_bonus':
                    $currentStats['magicDefense'] = ($currentStats['magicDefense'] ?? 0) + $bonus;
                    break;
                case 'speed_bonus':
                    $currentStats['speed'] = ($currentStats['speed'] ?? 0) + $bonus;
                    break;
            }
        }

        $this->stats = $currentStats;
    }

    /**
     * 记录特殊奖励
     */
    private function recordSpecialRewards($rewards)
    {
        // 这里可以扩展到单独的表来记录称号、技能等
        // 暂时记录在角色的skills字段中
        $currentSkills = $this->skills;

        // 确保 $currentSkills 是一个数组
        if (!is_array($currentSkills)) {
            // 如果是 JSON 字符串，尝试解码
            if (is_string($currentSkills)) {
                $currentSkills = json_decode($currentSkills, true);
            }

            // 如果解码失败或为空，初始化为空数组
            if (!is_array($currentSkills)) {
                $currentSkills = [];
            }
        }

        // 记录新技能
        if (!empty($rewards['skills'])) {
            foreach ($rewards['skills'] as $skill) {
                if (!in_array($skill, $currentSkills)) {
                    $currentSkills[] = $skill;
                }
            }
            $this->skills = $currentSkills;
        }

        // 记录称号（可以扩展到单独的字段或表）
        if (!empty($rewards['titles'])) {
            // 暂时不处理，可以后续扩展
        }
    }

    /**
     * 更新角色属性点分配
     *
     * @param array $attributes
     * @return bool
     */
    public function updateAttributes($attributes)
    {
        // 确保属性数据是正确的格式
        // 前端可能传递的是constitution、intelligence、strength、agility
        // 或者是vitality、intelligence、strength、dexterity
        $attributesData = [
            'vitality' => $attributes['vitality'] ?? $attributes['constitution'] ?? 0,
            'intelligence' => $attributes['intelligence'] ?? 0,
            'strength' => $attributes['strength'] ?? 0,
            'dexterity' => $attributes['dexterity'] ?? $attributes['agility'] ?? 0
        ];

        // 计算基础属性（自动成长的部分）
        $level = $this->level ?? 1;
        $baseAttributes = $this->calculateBaseAttributes($level);

        // 确保所有属性至少等于基础属性
        $attributesData['vitality'] = max($attributesData['vitality'], $baseAttributes['vitality']);
        $attributesData['intelligence'] = max($attributesData['intelligence'], $baseAttributes['intelligence']);
        $attributesData['strength'] = max($attributesData['strength'], $baseAttributes['strength']);
        $attributesData['dexterity'] = max($attributesData['dexterity'], $baseAttributes['dexterity']);

        // 计算额外分配的属性点（超出基础属性的部分）
        $extraPoints = [
            'vitality' => $attributesData['vitality'] - $baseAttributes['vitality'],
            'intelligence' => $attributesData['intelligence'] - $baseAttributes['intelligence'],
            'strength' => $attributesData['strength'] - $baseAttributes['strength'],
            'dexterity' => $attributesData['dexterity'] - $baseAttributes['dexterity']
        ];

        $totalExtraPoints = array_sum($extraPoints);

        // 检查是否有足够的属性点
        if ($totalExtraPoints > $this->attribute_points) {
            return false;
        }

        // 使用fillable方式设置JSON字段
        $this->fill(['attributes' => $attributesData]);

        // 更新剩余属性点
        $this->attribute_points = $this->attribute_points - $totalExtraPoints;

        // 重新计算战斗属性
        $this->recalculateStats();

        return $this->save();
    }

    /**
     * 计算基础属性（自动成长的部分）
     */
    private function calculateBaseAttributes($level)
    {
        $attributeGrowth = $this->getAttributeGrowthPerLevel();

        return [
            'vitality' => $attributeGrowth['vitality'] * $level,
            'intelligence' => $attributeGrowth['intelligence'] * $level,
            'strength' => $attributeGrowth['strength'] * $level,
            'dexterity' => $attributeGrowth['dexterity'] * $level
        ];
    }

    /**
     * 重置属性点分配
     *
     * @return bool
     */
    public function resetAttributes()
    {
        // 获取当前属性
        $currentAttributes = $this->attributes;

        // 确保 $currentAttributes 是一个数组
        if (!is_array($currentAttributes)) {
            // 如果是 JSON 字符串，尝试解码
            if (is_string($currentAttributes)) {
                $currentAttributes = json_decode($currentAttributes, true);
            }

            // 如果解码失败或为空，初始化为空数组
            if (!is_array($currentAttributes)) {
                $currentAttributes = [];
            }
        }

        $totalUsedPoints = array_sum($currentAttributes);

        // 重置属性为当前等级（而不是0）
        $level = $this->level ?? 1;
        parent::setAttribute('attributes', [
            'vitality' => $level,
            'intelligence' => $level,
            'strength' => $level,
            'dexterity' => $level
        ]);

        // 返还所有属性点
        $this->attribute_points = ($this->attribute_points ?? 0) + $totalUsedPoints - ($level * 4); // 减去基础属性点

        // 重新计算战斗属性
        $this->recalculateStats();

        return $this->save();
    }

    /**
     * 获取可用的总属性点（基于等级）
     */
    public function getTotalAvailableAttributePoints()
    {
        $level = $this->level ?? 1;

        // 简化规则：每级固定4点属性点 + 基础属性点（等于等级）
        // 1级: 4点（可分配）+ 4点（基础属性，每项1点）= 8点
        // 2级: 8点（可分配）+ 8点（基础属性，每项2点）= 16点
        // 3级: 12点（可分配）+ 12点（基础属性，每项3点）= 24点
        // ...以此类推
        return $level * 4 + $level * 4; // 可分配属性点 + 基础属性点
    }

    /**
     * 重新计算角色战斗属性
     *
     * @return $this
     */
    public function recalculateStats()
    {
        // 获取属性数据
        $attributes = $this->attributes;

        // 确保 $attributes 是一个数组
        if (!is_array($attributes)) {
            // 如果是 JSON 字符串，尝试解码
            if (is_string($attributes)) {
                $attributes = json_decode($attributes, true);
            }

            // 如果解码失败或为空，初始化为空数组
            if (!is_array($attributes)) {
                $attributes = [];
            }
        }

        $level = $this->level ?? 1;

        // 获取当前stats数据
        $currentStats = $this->stats;

        // 确保 $currentStats 是一个数组
        if (!is_array($currentStats)) {
            // 如果是 JSON 字符串，尝试解码
            if (is_string($currentStats)) {
                $currentStats = json_decode($currentStats, true);
            }

            // 如果解码失败或为空，初始化为空数组
            if (!is_array($currentStats)) {
                $currentStats = [];
            }
        }

        // 基础属性计算（属性点影响）
        $baseStats = [
            'hp' => 100 + (($attributes['vitality'] ?? 0) * 10),
            'mp' => 50 + (($attributes['intelligence'] ?? 0) * 5),
            'attack' => 10 + round(($attributes['strength'] ?? 0) * 0.8 + ($attributes['dexterity'] ?? 0) * 0.2),
            'defense' => 5 + round(($attributes['vitality'] ?? 0) * 0.6 + ($attributes['strength'] ?? 0) * 0.4),
            'magicAttack' => 5 + round(($attributes['intelligence'] ?? 0) * 0.9),
            'magicDefense' => 5 + round(($attributes['intelligence'] ?? 0) * 0.5 + ($attributes['vitality'] ?? 0) * 0.3),
            'speed' => 10 + round(($attributes['dexterity'] ?? 0) * 0.8)
        ];

        // 等级成长加成
        $levelBonus = $this->getLevelBonus($level);

        // 职业特色加成
        $professionBonus = $this->getProfessionBonus();

        // 合并所有加成
        $finalStats = [];
        foreach ($baseStats as $stat => $value) {
            $finalStats[$stat] = $value + ($levelBonus[$stat] ?? 0) + ($professionBonus[$stat] ?? 0);

            // 保留升级时的额外加成
            if (isset($currentStats[$stat . '_bonus'])) {
                $finalStats[$stat] += $currentStats[$stat . '_bonus'];
            }
        }

        // 设置最大值
        $finalStats['max_hp'] = $finalStats['hp'];
        $finalStats['max_mp'] = $finalStats['mp'];

        // 保留当前生命值和法力值（如果存在且不超过最大值）
        if (isset($currentStats['hp']) && $currentStats['hp'] <= $finalStats['max_hp']) {
            $finalStats['hp'] = $currentStats['hp'];
        }
        if (isset($currentStats['mp']) && $currentStats['mp'] <= $finalStats['max_mp']) {
            $finalStats['mp'] = $currentStats['mp'];
        }

        // 使用正确的方式设置JSON字段
        $this->stats = $finalStats;

        return $this;
    }

    /**
     * 获取等级加成
     */
    private function getLevelBonus($level)
    {
        return [
            'hp' => $level * 5,
            'mp' => $level * 3,
            'attack' => floor($level / 2),
            'defense' => floor($level / 3),
            'magicAttack' => floor($level / 2),
            'magicDefense' => floor($level / 3),
            'speed' => floor($level / 4)
        ];
    }

    /**
     * 获取职业特色加成
     */
    private function getProfessionBonus()
    {
        $level = $this->level ?? 1;

        switch ($this->profession) {
            case 'warrior':
                return [
                    'hp' => $level * 8,
                    'mp' => 0,
                    'attack' => $level * 1.2,
                    'defense' => $level * 1.5,
                    'magicAttack' => 0,
                    'magicDefense' => $level * 0.5,
                    'speed' => 0
                ];

            case 'scholar':
                return [
                    'hp' => $level * 2,
                    'mp' => $level * 8,
                    'attack' => 0,
                    'defense' => $level * 0.5,
                    'magicAttack' => $level * 1.5,
                    'magicDefense' => $level * 1.2,
                    'speed' => $level * 0.3
                ];

            case 'mystic':
                return [
                    'hp' => $level * 4,
                    'mp' => $level * 5,
                    'attack' => $level * 0.8,
                    'defense' => $level * 0.8,
                    'magicAttack' => $level * 0.8,
                    'magicDefense' => $level * 0.8,
                    'speed' => $level * 1.2
                ];

            default:
                return [
                    'hp' => 0, 'mp' => 0, 'attack' => 0, 'defense' => 0,
                    'magicAttack' => 0, 'magicDefense' => 0, 'speed' => 0
                ];
        }
    }



    /**
     * 获取角色当前位置
     */
    public function currentLocation()
    {
        return $this->hasOne(CharacterLocation::class)->with('location');
    }

    /**
     * 获取角色位置历史
     */
    public function locationHistory()
    {
        return $this->hasMany(CharacterLocation::class)->with('location')->orderBy('arrived_at', 'desc');
    }
}
