@extends('admin.layouts.app')

@section('title', '创建技能')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">创建新技能</div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.skills.store') }}">
            @csrf

            <div class="layui-form-item">
                <label class="layui-form-label">技能名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" required lay-verify="required" placeholder="请输入技能名称" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">技能描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入技能描述" class="layui-textarea">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">技能类型</label>
                <div class="layui-input-block">
                    <select name="type" lay-verify="required">
                        <option value="">请选择技能类型</option>
                        <option value="attack" {{ old('type') == 'attack' ? 'selected' : '' }}>攻击技能</option>
                        <option value="defense" {{ old('type') == 'defense' ? 'selected' : '' }}>防御技能</option>
                        <option value="heal" {{ old('type') == 'heal' ? 'selected' : '' }}>治疗技能</option>
                        <option value="buff" {{ old('type') == 'buff' ? 'selected' : '' }}>增益技能</option>
                        <option value="debuff" {{ old('type') == 'debuff' ? 'selected' : '' }}>减益技能</option>
                        <option value="passive" {{ old('type') == 'passive' ? 'selected' : '' }}>被动技能</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级要求</label>
                <div class="layui-input-inline">
                    <input type="number" name="level_requirement" value="{{ old('level_requirement', 1) }}" required lay-verify="required|number" placeholder="请输入等级要求" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">职业限制</label>
                <div class="layui-input-block">
                    <select name="profession_restriction">
                        <option value="">无职业限制</option>
                        <option value="warrior" {{ old('profession_restriction') == 'warrior' ? 'selected' : '' }}>武士</option>
                        <option value="scholar" {{ old('profession_restriction') == 'scholar' ? 'selected' : '' }}>文人</option>
                        <option value="mystic" {{ old('profession_restriction') == 'mystic' ? 'selected' : '' }}>异人</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">冷却时间(秒)</label>
                <div class="layui-input-inline">
                    <input type="number" name="cooldown" value="{{ old('cooldown', 0) }}" required lay-verify="required|number" placeholder="请输入冷却时间" autocomplete="off" class="layui-input" min="0" step="0.1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">法力消耗</label>
                <div class="layui-input-inline">
                    <input type="number" name="mp_cost" value="{{ old('mp_cost', 0) }}" required lay-verify="required|number" placeholder="请输入法力消耗" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">基础伤害</label>
                <div class="layui-input-inline">
                    <input type="number" name="base_damage" value="{{ old('base_damage', 0) }}" placeholder="请输入基础伤害" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">基础治疗</label>
                <div class="layui-input-inline">
                    <input type="number" name="base_healing" value="{{ old('base_healing', 0) }}" placeholder="请输入基础治疗" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">攻击加成系数</label>
                <div class="layui-input-inline">
                    <input type="number" name="attack_scaling" value="{{ old('attack_scaling', 0) }}" placeholder="请输入攻击加成系数" autocomplete="off" class="layui-input" min="0" step="0.01">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">智力加成系数</label>
                <div class="layui-input-inline">
                    <input type="number" name="intelligence_scaling" value="{{ old('intelligence_scaling', 0) }}" placeholder="请输入智力加成系数" autocomplete="off" class="layui-input" min="0" step="0.01">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">持续时间(秒)</label>
                <div class="layui-input-inline">
                    <input type="number" name="duration" value="{{ old('duration', 0) }}" placeholder="请输入持续时间" autocomplete="off" class="layui-input" min="0" step="0.1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">影响范围</label>
                <div class="layui-input-inline">
                    <select name="target_type">
                        <option value="single" {{ old('target_type') == 'single' ? 'selected' : '' }}>单体</option>
                        <option value="aoe" {{ old('target_type') == 'aoe' ? 'selected' : '' }}>范围</option>
                        <option value="self" {{ old('target_type') == 'self' ? 'selected' : '' }}>自身</option>
                        <option value="ally" {{ old('target_type') == 'ally' ? 'selected' : '' }}>友方</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">技能图标</label>
                <div class="layui-input-inline">
                    <input type="text" name="icon" value="{{ old('icon') }}" placeholder="请输入技能图标路径" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">技能状态</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="is_active" value="1" title="启用" checked>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
                    <a href="{{ route('admin.skills.index') }}" class="layui-btn layui-btn-primary">返回</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 重新渲染表单
    form.render();
});
</script>
@endsection
