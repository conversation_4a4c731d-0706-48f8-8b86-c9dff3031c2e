<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('teams', function (Blueprint $table) {
            $table->id();
            $table->string('name', 30);
            $table->text('description')->nullable();
            $table->unsignedBigInteger('leader_id');
            $table->unsignedTinyInteger('max_members')->default(5);
            $table->unsignedInteger('min_level')->default(1);
            $table->unsignedInteger('max_level')->nullable();
            $table->boolean('is_recruiting')->default(true);
            $table->timestamps();

            // 外键约束
            $table->foreign('leader_id')->references('id')->on('characters')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('teams');
    }
};
