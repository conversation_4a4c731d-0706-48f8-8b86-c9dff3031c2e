{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Bank.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Bank.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GameLayout", "bankService", "showMessage", "logger", "getCurrentCharacter", "name", "components", "data", "isLoading", "error", "activeTab", "tabs", "id", "characterInfo", "silver", "gold", "accountInfo", "gold_ingot", "depositForm", "currency", "amount", "withdrawForm", "transactions", "pagination", "transactionFilters", "type", "page", "per_page", "showResult", "resultError", "resultMessage", "created", "loadData", "watch", "newTab", "loadTransactions", "depositForm.currency", "withdrawForm.currency", "computed", "formattedTransactions", "map", "transaction", "Math", "random", "toString", "substr", "created_at", "Date", "toISOString", "parseInt", "balance", "description", "generateDescription", "methods", "character", "characterId", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "savedCharacter", "JSON", "parse", "debug", "Error", "e", "stringify", "_this$$store", "characterStatus", "$store", "state", "warn", "stateError", "accountResponse", "getAccountInfo", "account", "apiError", "min", "message", "includes", "$router", "push", "params", "response", "getTransactionHistory", "length", "Array", "isArray", "success", "deposit", "canDeposit", "showResultMessage", "withdraw", "canWithdraw", "isError", "formatDate", "dateStr", "date", "isNaN", "getTime", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "changePage", "last_page", "getMaxDepositAmount", "getMaxWithdrawAmount", "setMaxDepositAmount", "setMaxWithdrawAmount"], "sources": ["src/views/game/subpages/Bank.vue"], "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"bank-page\">\r\n      <div class=\"bank-container\">\r\n        <!-- 钱庄标题 -->\r\n        <div class=\"bank-header\">\r\n          <h1>钱庄</h1>\r\n          <div class=\"character-status\">\r\n            <div class=\"status-box\">\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">银两:</div>\r\n                <div class=\"status-value silver-value\">{{ characterInfo.silver }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">金砖:</div>\r\n                <div class=\"status-value gold-value\">{{ characterInfo.gold }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">存款银两:</div>\r\n                <div class=\"status-value silver-value\">{{ accountInfo.silver }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">存款金砖:</div>\r\n                <div class=\"status-value gold-value\">{{ accountInfo.gold_ingot }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 选项卡导航 -->\r\n        <div class=\"tabs-container\">\r\n          <div \r\n            v-for=\"tab in tabs\" \r\n            :key=\"tab.id\" \r\n            class=\"tab\" \r\n            :class=\"{ 'active': activeTab === tab.id }\"\r\n            @click=\"activeTab = tab.id\"\r\n          >\r\n            <span class=\"tab-name\">{{ tab.name }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容区域 -->\r\n        <div class=\"content-area\">\r\n          <!-- 存款 -->\r\n          <div v-if=\"activeTab === 'deposit'\" class=\"tab-content\">\r\n            <div class=\"operation-form\">\r\n              <div class=\"form-group\">\r\n                <label for=\"deposit-currency\">货币类型:</label>\r\n                <select id=\"deposit-currency\" v-model=\"depositForm.currency\" class=\"form-control\">\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"form-group\">\r\n                <label for=\"deposit-amount\">存款金额:</label>\r\n                <input \r\n                  id=\"deposit-amount\" \r\n                  type=\"number\" \r\n                  v-model=\"depositForm.amount\" \r\n                  class=\"form-control\" \r\n                  min=\"1\"\r\n                  :max=\"getMaxDepositAmount()\"\r\n                />\r\n              </div>\r\n              <div class=\"form-actions\">\r\n                <button \r\n                  @click=\"deposit\" \r\n                  class=\"action-button\" \r\n                  :disabled=\"!canDeposit()\"\r\n                >\r\n                  存款\r\n                </button>\r\n                <button @click=\"setMaxDepositAmount\" class=\"max-button\">最大</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 取款 -->\r\n          <div v-if=\"activeTab === 'withdraw'\" class=\"tab-content\">\r\n            <div class=\"operation-form\">\r\n              <div class=\"form-group\">\r\n                <label for=\"withdraw-currency\">货币类型:</label>\r\n                <select id=\"withdraw-currency\" v-model=\"withdrawForm.currency\" class=\"form-control\">\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"form-group\">\r\n                <label for=\"withdraw-amount\">取款金额:</label>\r\n                <input \r\n                  id=\"withdraw-amount\" \r\n                  type=\"number\" \r\n                  v-model=\"withdrawForm.amount\" \r\n                  class=\"form-control\" \r\n                  min=\"1\"\r\n                  :max=\"getMaxWithdrawAmount()\"\r\n                />\r\n              </div>\r\n              <div class=\"form-actions\">\r\n                <button \r\n                  @click=\"withdraw\" \r\n                  class=\"action-button\" \r\n                  :disabled=\"!canWithdraw()\"\r\n                >\r\n                  取款\r\n                </button>\r\n                <button @click=\"setMaxWithdrawAmount\" class=\"max-button\">最大</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 交易记录 -->\r\n          <div v-if=\"activeTab === 'transactions'\" class=\"tab-content\">\r\n            <div class=\"transaction-filters\">\r\n              <div class=\"filter-group\">\r\n                <label for=\"filter-currency\">货币类型:</label>\r\n                <select id=\"filter-currency\" v-model=\"transactionFilters.currency\" class=\"form-control\" @change=\"loadTransactions\">\r\n                  <option value=\"\">全部</option>\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"filter-group\">\r\n                <label for=\"filter-type\">交易类型:</label>\r\n                <select id=\"filter-type\" v-model=\"transactionFilters.type\" class=\"form-control\" @change=\"loadTransactions\">\r\n                  <option value=\"\">全部</option>\r\n                  <option value=\"deposit\">存款</option>\r\n                  <option value=\"withdraw\">取款</option>\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"transaction-list\">\r\n              <table v-if=\"formattedTransactions.length > 0\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>时间</th>\r\n                    <th>类型</th>\r\n                    <th>货币</th>\r\n                    <th>金额</th>\r\n                    <th>余额</th>\r\n                    <th>说明</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr v-for=\"transaction in formattedTransactions\" :key=\"transaction.id\">\r\n                    <td>{{ formatDate(transaction.created_at) }}</td>\r\n                    <td>{{ transaction.type === 'deposit' ? '存款' : '取款' }}</td>\r\n                    <td>{{ transaction.currency === 'silver' ? '银两' : '金砖' }}</td>\r\n                    <td>{{ transaction.amount }}</td>\r\n                    <td>{{ transaction.balance }}</td>\r\n                    <td>{{ transaction.description }}</td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n              <div v-else class=\"no-transactions\">\r\n                暂无交易记录\r\n              </div>\r\n              \r\n              <!-- 分页 -->\r\n              <div v-if=\"pagination && pagination.total > 0\" class=\"pagination\">\r\n                <button \r\n                  @click=\"changePage(pagination.current_page - 1)\" \r\n                  :disabled=\"pagination.current_page <= 1\"\r\n                  class=\"pagination-button\"\r\n                >\r\n                  上一页\r\n                </button>\r\n                <span class=\"page-info\">{{ pagination.current_page }} / {{ pagination.last_page }}</span>\r\n                <button \r\n                  @click=\"changePage(pagination.current_page + 1)\" \r\n                  :disabled=\"pagination.current_page >= pagination.last_page\"\r\n                  class=\"pagination-button\"\r\n                >\r\n                  下一页\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 返回按钮 -->\r\n        <div class=\"bottom-actions\">\r\n          <button class=\"back-button\" @click=\"$router.push('/game/main')\">返回城镇</button>\r\n        </div>\r\n\r\n        <!-- 操作结果弹窗 -->\r\n        <div v-if=\"showResult\" class=\"result-modal\">\r\n          <div class=\"result-content\" :class=\"{ 'error': resultError }\">\r\n            <h3>{{ resultError ? '操作失败' : '操作成功' }}</h3>\r\n            <p>{{ resultMessage }}</p>\r\n            <button @click=\"showResult = false\">确定</button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 加载中和错误提示 -->\r\n        <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n          <div class=\"loading-spinner\"></div>\r\n          <div>加载中...</div>\r\n        </div>\r\n        <div v-if=\"error\" class=\"error-message\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nimport bankService from '@/api/services/bankService';\r\nimport { showMessage } from '@/utils/message';\r\nimport logger from '@/utils/logger';\r\nimport { getCurrentCharacter } from '@/api/services/characterService';\r\n\r\nexport default {\r\n  name: 'Bank',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      isLoading: true,\r\n      error: null,\r\n      activeTab: 'deposit', // 默认选中存款选项卡\r\n      tabs: [\r\n        {\r\n          id: 'deposit',\r\n          name: '存款'\r\n        },\r\n        {\r\n          id: 'withdraw',\r\n          name: '取款'\r\n        },\r\n        {\r\n          id: 'transactions',\r\n          name: '交易记录'\r\n        }\r\n      ],\r\n      characterInfo: {\r\n        id: null,\r\n        name: '',\r\n        silver: 0,\r\n        gold: 0\r\n      },\r\n      accountInfo: {\r\n        silver: 0,\r\n        gold_ingot: 0\r\n      },\r\n      depositForm: {\r\n        currency: 'silver',\r\n        amount: 100\r\n      },\r\n      withdrawForm: {\r\n        currency: 'silver',\r\n        amount: 100\r\n      },\r\n      transactions: [],\r\n      pagination: null,\r\n      transactionFilters: {\r\n        currency: '',\r\n        type: '',\r\n        page: 1,\r\n        per_page: 10\r\n      },\r\n      showResult: false,\r\n      resultError: false,\r\n      resultMessage: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.loadData();\r\n  },\r\n  watch: {\r\n    activeTab(newTab) {\r\n      if (newTab === 'transactions') {\r\n        this.loadTransactions();\r\n      }\r\n    },\r\n    'depositForm.currency'() {\r\n      // 切换货币类型时，重置金额\r\n      this.depositForm.amount = 100;\r\n    },\r\n    'withdrawForm.currency'() {\r\n      // 切换货币类型时，重置金额\r\n      this.withdrawForm.amount = 100;\r\n    }\r\n  },\r\n  computed: {\r\n    /**\r\n     * 格式化交易记录，确保数据格式一致\r\n     * 处理可能的数据格式不一致问题\r\n     */\r\n    formattedTransactions() {\r\n      return this.transactions.map(transaction => {\r\n        // 确保所有必要字段都存在，使用默认值代替可能缺失的字段\r\n        return {\r\n          id: transaction.id || Math.random().toString(36).substr(2, 9), // 生成随机ID如果缺失\r\n          created_at: transaction.created_at || new Date().toISOString(),\r\n          type: transaction.type || 'deposit',\r\n          currency: transaction.currency || 'silver',\r\n          amount: parseInt(transaction.amount) || 0,\r\n          balance: parseInt(transaction.balance) || 0,\r\n          description: transaction.description || this.generateDescription(transaction)\r\n        };\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 加载初始数据\r\n     */\r\n    async loadData() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        // 获取当前角色信息\r\n        const character = getCurrentCharacter();\r\n        if (!character) {\r\n          logger.error('[Bank] 未找到角色信息，尝试从LocalStorage获取');\r\n          \r\n          // 尝试直接从LocalStorage获取\r\n          try {\r\n            const characterId = localStorage.getItem('selectedCharacterId');\r\n            const characterJson = localStorage.getItem('selectedCharacter');\r\n            \r\n            if (characterId && characterJson) {\r\n              const savedCharacter = JSON.parse(characterJson);\r\n              logger.debug('[Bank] 从LocalStorage获取到角色:', savedCharacter);\r\n              \r\n              this.characterInfo = {\r\n                id: savedCharacter.id,\r\n                name: savedCharacter.name,\r\n                silver: savedCharacter.silver || 0,\r\n                gold: savedCharacter.gold || 0\r\n              };\r\n            } else {\r\n              throw new Error('未找到角色信息，请先选择角色');\r\n            }\r\n          } catch (e) {\r\n            logger.error('[Bank] 从LocalStorage获取角色失败:', e);\r\n            throw new Error('未找到角色信息，请先选择角色');\r\n          }\r\n        } else {\r\n          // 正常获取到角色信息\r\n          this.characterInfo = {\r\n            id: character.id,\r\n            name: character.name,\r\n            silver: character.silver || 0,\r\n            gold: character.gold || 0\r\n          };\r\n          \r\n          logger.debug('[Bank] 获取到角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        // 从Vuex获取更详细的角色信息\r\n        try {\r\n          const characterStatus = this.$store?.state?.character?.characterStatus;\r\n          if (characterStatus) {\r\n            this.characterInfo.silver = characterStatus.silver || this.characterInfo.silver;\r\n            this.characterInfo.gold = characterStatus.gold || this.characterInfo.gold;\r\n            logger.debug('[Bank] 从Vuex更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n          } else {\r\n            logger.warn('[Bank] 未从Vuex获取到角色状态，使用默认值');\r\n          }\r\n        } catch (stateError) {\r\n          logger.error('[Bank] 从Vuex获取角色状态失败:', stateError);\r\n        }\r\n        \r\n        // 银行账户信息初始化为默认值，防止API调用失败时没有数据\r\n        this.accountInfo = {\r\n          silver: 0,\r\n          gold_ingot: 0\r\n        };\r\n        \r\n        try {\r\n          // 获取银行账户信息\r\n          logger.debug('[Bank] 开始获取银行账户信息, ID:', this.characterInfo.id);\r\n          const accountResponse = await bankService.getAccountInfo(this.characterInfo.id);\r\n          logger.debug('[Bank] 银行账户响应:', JSON.stringify(accountResponse));\r\n          \r\n          // 处理可能的不同响应格式\r\n          if (accountResponse) {\r\n            // 检查不同的响应数据结构\r\n            if (accountResponse.data && accountResponse.data.account) {\r\n              // 标准格式: { data: { account: {...} } }\r\n              this.accountInfo = accountResponse.data.account;\r\n              logger.debug('[Bank] 获取到账户信息(标准格式):', JSON.stringify(this.accountInfo));\r\n              \r\n              // 更新角色信息\r\n              if (accountResponse.data.character) {\r\n                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.data.character));\r\n                this.characterInfo.name = accountResponse.data.character.name || this.characterInfo.name;\r\n                this.characterInfo.silver = accountResponse.data.character.silver || this.characterInfo.silver;\r\n                this.characterInfo.gold = accountResponse.data.character.gold || this.characterInfo.gold;\r\n                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n              }\r\n            } else if (accountResponse.account) {\r\n              // 替代格式1: { account: {...} }\r\n              this.accountInfo = accountResponse.account;\r\n              logger.debug('[Bank] 获取到账户信息(替代格式1):', JSON.stringify(this.accountInfo));\r\n              \r\n              // 更新角色信息\r\n              if (accountResponse.character) {\r\n                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.character));\r\n                this.characterInfo.name = accountResponse.character.name || this.characterInfo.name;\r\n                this.characterInfo.silver = accountResponse.character.silver || this.characterInfo.silver;\r\n                this.characterInfo.gold = accountResponse.character.gold || this.characterInfo.gold;\r\n                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n              }\r\n            } else if (typeof accountResponse === 'object' && accountResponse !== null) {\r\n              // 替代格式2: 响应本身就是账户对象\r\n              // 检查是否至少有银两或金砖字段\r\n              if ('silver' in accountResponse || 'gold_ingot' in accountResponse) {\r\n                this.accountInfo = {\r\n                  silver: accountResponse.silver || 0,\r\n                  gold_ingot: accountResponse.gold_ingot || 0\r\n                };\r\n                logger.debug('[Bank] 获取到账户信息(替代格式2):', JSON.stringify(this.accountInfo));\r\n                \r\n                // 检查是否包含角色信息\r\n                if ('name' in accountResponse || 'silver' in accountResponse || 'gold' in accountResponse) {\r\n                  logger.debug('[Bank] 从API响应对象中获取角色信息');\r\n                  if ('name' in accountResponse) this.characterInfo.name = accountResponse.name;\r\n                  if ('silver' in accountResponse) this.characterInfo.silver = accountResponse.silver;\r\n                  if ('gold' in accountResponse) this.characterInfo.gold = accountResponse.gold;\r\n                  logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n                }\r\n              } else {\r\n                throw new Error('获取银行账户信息失败: 响应数据格式无效');\r\n              }\r\n            } else {\r\n              throw new Error('获取银行账户信息失败: 响应数据格式无效');\r\n            }\r\n          } else {\r\n            throw new Error('获取银行账户信息失败: 无响应数据');\r\n          }\r\n        } catch (apiError) {\r\n          // 记录API错误但不阻止页面加载，使用默认账户信息\r\n          logger.error('[Bank] API调用失败:', apiError);\r\n          logger.error('[Bank] API调用失败详情:', JSON.stringify(apiError));\r\n          \r\n          // 显示警告消息\r\n          showMessage('获取银行账户信息失败，将使用临时数据显示', 'warning');\r\n          \r\n          // 默认值已初始化，无需重复设置\r\n        } finally {\r\n          // 无论API成功与否，都初始化表单\r\n          this.depositForm.amount = Math.min(100, this.characterInfo.silver || 0);\r\n          this.withdrawForm.amount = Math.min(100, this.accountInfo.silver || 0);\r\n        }\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        this.error = error.message || '加载数据失败，请刷新重试';\r\n        this.isLoading = false;\r\n        logger.error('[Bank] 加载数据失败:', error);\r\n        \r\n        // 如果是未找到角色信息，则跳转到角色选择页面\r\n        if (error.message && error.message.includes('未找到角色信息')) {\r\n          showMessage('未找到角色信息，请先选择角色', 'error');\r\n          this.$router.push('/setup/character-select');\r\n        }\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 加载交易记录\r\n     */\r\n    async loadTransactions() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        // 如果角色信息不存在，不尝试加载交易记录\r\n        if (!this.characterInfo || !this.characterInfo.id) {\r\n          logger.warn('[Bank] 无法加载交易记录: 角色信息不存在');\r\n          this.transactions = [];\r\n          this.isLoading = false;\r\n          return;\r\n        }\r\n        \r\n        // 构建请求参数，只有当筛选值不为空字符串时才添加到请求参数中\r\n        const params = {};\r\n        if (this.transactionFilters.page) {\r\n          params.page = this.transactionFilters.page;\r\n        }\r\n        if (this.transactionFilters.per_page) {\r\n          params.per_page = this.transactionFilters.per_page;\r\n        }\r\n        // 只有当值不是空字符串时才添加筛选条件\r\n        if (this.transactionFilters.currency && this.transactionFilters.currency !== '') {\r\n          params.currency = this.transactionFilters.currency;\r\n        }\r\n        if (this.transactionFilters.type && this.transactionFilters.type !== '') {\r\n          params.type = this.transactionFilters.type;\r\n        }\r\n        \r\n        logger.debug('[Bank] 加载交易记录, 参数:', params);\r\n        \r\n        try {\r\n          const response = await bankService.getTransactionHistory(this.characterInfo.id, params);\r\n          logger.debug('[Bank] 交易记录响应:', JSON.stringify(response));\r\n          \r\n          // 处理响应结构\r\n          if (response && response.data) {\r\n            // 标准响应结构\r\n            this.transactions = response.data.transactions || [];\r\n            this.pagination = response.data.pagination || null;\r\n            logger.debug('[Bank] 解析到交易记录:', this.transactions.length);\r\n          } else if (response && Array.isArray(response)) {\r\n            // 响应直接是交易数组\r\n            this.transactions = response;\r\n            logger.debug('[Bank] 解析到交易记录(数组格式):', this.transactions.length);\r\n          } else if (response && Array.isArray(response.transactions)) {\r\n            // 响应中有transactions数组\r\n            this.transactions = response.transactions;\r\n            this.pagination = response.pagination || null;\r\n            logger.debug('[Bank] 解析到交易记录(嵌套数组):', this.transactions.length);\r\n          } else if (response && response.success && Array.isArray(response.data)) {\r\n            // success包装的数组\r\n            this.transactions = response.data;\r\n            logger.debug('[Bank] 解析到交易记录(success包装数组):', this.transactions.length);\r\n          } else {\r\n            // 无效响应，使用空数组\r\n            logger.warn('[Bank] 无效的交易记录响应格式，使用空数组:', JSON.stringify(response));\r\n            this.transactions = [];\r\n            this.pagination = null;\r\n          }\r\n          \r\n          // 检查交易记录是否有效\r\n          if (this.transactions.length > 0) {\r\n            logger.debug('[Bank] 交易记录首条样例:', JSON.stringify(this.transactions[0]));\r\n          } else {\r\n            logger.warn('[Bank] 未获取到交易记录，筛选条件:', this.transactionFilters);\r\n          }\r\n        } catch (apiError) {\r\n          // API调用失败，使用空数组\r\n          logger.error('[Bank] 交易记录API调用失败:', apiError);\r\n          this.transactions = [];\r\n          this.pagination = null;\r\n          \r\n          // 显示警告消息，但不中断加载过程\r\n          showMessage('获取交易记录失败，将显示空记录', 'warning');\r\n        }\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        this.error = error.message || '加载交易记录失败，请刷新重试';\r\n        this.isLoading = false;\r\n        logger.error('[Bank] 加载交易记录失败:', error);\r\n        \r\n        // 确保有默认值\r\n        this.transactions = this.transactions || [];\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 存款操作\r\n     */\r\n    async deposit() {\r\n      try {\r\n        if (!this.canDeposit()) {\r\n          return this.showResultMessage('无法进行存款操作', true);\r\n        }\r\n        \r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        const response = await bankService.deposit(\r\n          this.characterInfo.id,\r\n          this.depositForm.currency,\r\n          parseInt(this.depositForm.amount) || 0\r\n        );\r\n        \r\n        // 更新本地数据\r\n        if (response.account) {\r\n          this.accountInfo = response.account;\r\n          logger.debug('[Bank] 存款后更新账户信息:', JSON.stringify(this.accountInfo));\r\n        }\r\n        \r\n        if (response.character) {\r\n          // 确保数据类型正确转换\r\n          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;\r\n          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;\r\n          logger.debug('[Bank] 存款后更新角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        this.isLoading = false;\r\n        this.showResultMessage(response.message || '存款成功', false);\r\n      } catch (error) {\r\n        this.isLoading = false;\r\n        this.showResultMessage(error.message || '存款失败，请重试', true);\r\n        logger.error('[Bank] 存款操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 取款操作\r\n     */\r\n    async withdraw() {\r\n      try {\r\n        if (!this.canWithdraw()) {\r\n          return this.showResultMessage('无法进行取款操作', true);\r\n        }\r\n        \r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        const response = await bankService.withdraw(\r\n          this.characterInfo.id,\r\n          this.withdrawForm.currency,\r\n          parseInt(this.withdrawForm.amount) || 0\r\n        );\r\n        \r\n        // 更新本地数据\r\n        if (response.account) {\r\n          this.accountInfo = response.account;\r\n          logger.debug('[Bank] 取款后更新账户信息:', JSON.stringify(this.accountInfo));\r\n        }\r\n        \r\n        if (response.character) {\r\n          // 确保数据类型正确转换\r\n          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;\r\n          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;\r\n          logger.debug('[Bank] 取款后更新角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        this.isLoading = false;\r\n        this.showResultMessage(response.message || '取款成功', false);\r\n      } catch (error) {\r\n        this.isLoading = false;\r\n        this.showResultMessage(error.message || '取款失败，请重试', true);\r\n        logger.error('[Bank] 取款操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 显示操作结果消息\r\n     */\r\n    showResultMessage(message, isError = false) {\r\n      this.resultMessage = message;\r\n      this.resultError = isError;\r\n      this.showResult = true;\r\n    },\r\n    \r\n    /**\r\n     * 为缺失描述的交易记录生成默认描述\r\n     */\r\n    generateDescription(transaction) {\r\n      const type = transaction.type === 'deposit' ? '存入' : '取出';\r\n      const currency = transaction.currency === 'silver' ? '银两' : '金砖';\r\n      const amount = transaction.amount || 0;\r\n      return `${type} ${amount} ${currency}`;\r\n    },\r\n    \r\n    /**\r\n     * 格式化日期\r\n     */\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未知时间';\r\n      \r\n      try {\r\n        const date = new Date(dateStr);\r\n        if (isNaN(date.getTime())) return '日期格式错误';\r\n        \r\n        return date.toLocaleString('zh-CN', {\r\n          year: 'numeric',\r\n          month: '2-digit',\r\n          day: '2-digit',\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          second: '2-digit'\r\n        });\r\n      } catch (e) {\r\n        logger.error('[Bank] 日期格式化失败:', e);\r\n        return '日期格式错误';\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 切换分页\r\n     */\r\n    changePage(page) {\r\n      if (page < 1 || (this.pagination && page > this.pagination.last_page)) {\r\n        return;\r\n      }\r\n      \r\n      this.transactionFilters.page = page;\r\n      this.loadTransactions();\r\n    },\r\n    \r\n    /**\r\n     * 获取最大可存款金额\r\n     */\r\n    getMaxDepositAmount() {\r\n      if (this.depositForm.currency === 'silver') {\r\n        return this.characterInfo.silver;\r\n      } else {\r\n        return this.characterInfo.gold;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 获取最大可取款金额\r\n     */\r\n    getMaxWithdrawAmount() {\r\n      if (this.withdrawForm.currency === 'silver') {\r\n        return this.accountInfo.silver;\r\n      } else {\r\n        return this.accountInfo.gold_ingot;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 设置最大存款金额\r\n     */\r\n    setMaxDepositAmount() {\r\n      this.depositForm.amount = this.getMaxDepositAmount();\r\n    },\r\n    \r\n    /**\r\n     * 设置最大取款金额\r\n     */\r\n    setMaxWithdrawAmount() {\r\n      this.withdrawForm.amount = this.getMaxWithdrawAmount();\r\n    },\r\n    \r\n    /**\r\n     * 判断是否可以存款\r\n     */\r\n    canDeposit() {\r\n      const amount = parseInt(this.depositForm.amount);\r\n      if (!amount || amount <= 0) {\r\n        return false;\r\n      }\r\n      \r\n      if (this.depositForm.currency === 'silver') {\r\n        return amount <= this.characterInfo.silver;\r\n      } else {\r\n        return amount <= this.characterInfo.gold;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 判断是否可以取款\r\n     */\r\n    canWithdraw() {\r\n      const amount = parseInt(this.withdrawForm.amount);\r\n      if (!amount || amount <= 0) {\r\n        return false;\r\n      }\r\n      \r\n      if (this.withdrawForm.currency === 'silver') {\r\n        return amount <= this.accountInfo.silver;\r\n      } else {\r\n        return amount <= this.accountInfo.gold_ingot;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bank-page {\r\n  padding: 10px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.bank-container {\r\n  background-color: rgba(0, 0, 20, 0.7);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  box-shadow: 0 0 20px rgba(0, 0, 50, 0.5);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.bank-header {\r\n  margin-bottom: 15px;\r\n  padding: 12px;\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.bank-header h1 {\r\n  margin: 0 0 10px 0;\r\n  text-align: center;\r\n  color: #ffcc00;\r\n  font-size: 22px;\r\n  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);\r\n}\r\n\r\n.character-status {\r\n  margin-top: 10px;\r\n}\r\n\r\n.status-box {\r\n  background-color: rgba(153, 0, 0, 0.2);\r\n  border: 1px solid rgba(153, 0, 0, 0.5);\r\n  border-radius: 5px;\r\n  padding: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 8px;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 3px 5px;\r\n}\r\n\r\n.status-label {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-value {\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.silver-value {\r\n  color: #ffcc00;\r\n}\r\n\r\n.gold-value {\r\n  color: #ff9900;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid rgba(51, 51, 204, 0.5);\r\n}\r\n\r\n.tab {\r\n  padding: 10px 15px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  flex: 1;\r\n  border-radius: 5px 5px 0 0;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n}\r\n\r\n.tab:hover {\r\n  background-color: rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.tab.active {\r\n  background-color: rgba(51, 51, 204, 0.5);\r\n  border: 1px solid rgba(51, 51, 204, 0.8);\r\n  border-bottom: none;\r\n}\r\n\r\n.tab-name {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tab-content {\r\n  padding: 10px;\r\n}\r\n\r\n.operation-form {\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-control {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border-radius: 5px;\r\n  border: 1px solid rgba(51, 51, 204, 0.5);\r\n  background-color: rgba(0, 0, 20, 0.7);\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.action-button {\r\n  flex: 3;\r\n  padding: 12px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-button:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.action-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.max-button {\r\n  flex: 1;\r\n  padding: 12px;\r\n  background-color: #990000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.max-button:hover {\r\n  background-color: #cc0000;\r\n}\r\n\r\n.transaction-filters {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-group {\r\n  flex: 1;\r\n}\r\n\r\n.transaction-list {\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\ntable {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\nthead {\r\n  position: sticky;\r\n  top: 0;\r\n  background-color: rgba(0, 0, 51, 0.8);\r\n  z-index: 1;\r\n}\r\n\r\nth, td {\r\n  padding: 10px;\r\n  text-align: left;\r\n  border-bottom: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\nth {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\ntr:hover {\r\n  background-color: rgba(51, 51, 204, 0.1);\r\n}\r\n\r\n.no-transactions {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #aaaaaa;\r\n}\r\n\r\n.pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n  gap: 10px;\r\n}\r\n\r\n.pagination-button {\r\n  padding: 8px 15px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.pagination-button:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.pagination-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.page-info {\r\n  font-weight: bold;\r\n}\r\n\r\n.bottom-actions {\r\n  margin-top: auto;\r\n  text-align: center;\r\n}\r\n\r\n.back-button {\r\n  padding: 10px 20px;\r\n  background-color: #cc0000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #dd4444;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.back-button:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.result-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.result-content {\r\n  background-color: #000033;\r\n  border: 2px solid #3333cc;\r\n  border-radius: 6px;\r\n  padding: 20px;\r\n  width: 80%;\r\n  max-width: 400px;\r\n  text-align: center;\r\n}\r\n\r\n.result-content.error {\r\n  border-color: #cc0000;\r\n}\r\n\r\n.result-content h3 {\r\n  color: #ffcc00;\r\n  margin-top: 0;\r\n}\r\n\r\n.result-content p {\r\n  margin: 15px 0;\r\n}\r\n\r\n.result-content button {\r\n  padding: 8px 20px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.result-content button:hover {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n  color: white;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 4px solid rgba(51, 51, 204, 0.3);\r\n  border-top: 4px solid #3333cc;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.error-message {\r\n  background-color: rgba(153, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 10px;\r\n  border-radius: 5px;\r\n  margin: 10px 0;\r\n  text-align: center;\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 480px) {\r\n  .status-box {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .transaction-filters {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  th, td {\r\n    padding: 8px 5px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .action-button, .max-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;AAkNA,OAAAA,UAAA;AACA,OAAAC,WAAA;AACA,SAAAC,WAAA;AACA,OAAAC,MAAA;AACA,SAAAC,mBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAN;EAAA;EACAO,KAAA;IACA;MACAC,SAAA;MACAC,KAAA;MACAC,SAAA;MAAA;MACAC,IAAA,GACA;QACAC,EAAA;QACAP,IAAA;MACA,GACA;QACAO,EAAA;QACAP,IAAA;MACA,GACA;QACAO,EAAA;QACAP,IAAA;MACA,EACA;MACAQ,aAAA;QACAD,EAAA;QACAP,IAAA;QACAS,MAAA;QACAC,IAAA;MACA;MACAC,WAAA;QACAF,MAAA;QACAG,UAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACAC,YAAA;QACAF,QAAA;QACAC,MAAA;MACA;MACAE,YAAA;MACAC,UAAA;MACAC,kBAAA;QACAL,QAAA;QACAM,IAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,KAAA;IACAvB,UAAAwB,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA,sBAAAC,CAAA;MACA;MACA,KAAAlB,WAAA,CAAAE,MAAA;IACA;IACA,uBAAAiB,CAAA;MACA;MACA,KAAAhB,YAAA,CAAAD,MAAA;IACA;EACA;EACAkB,QAAA;IACA;AACA;AACA;AACA;IACAC,sBAAA;MACA,YAAAjB,YAAA,CAAAkB,GAAA,CAAAC,WAAA;QACA;QACA;UACA7B,EAAA,EAAA6B,WAAA,CAAA7B,EAAA,IAAA8B,IAAA,CAAAC,MAAA,GAAAC,QAAA,KAAAC,MAAA;UAAA;UACAC,UAAA,EAAAL,WAAA,CAAAK,UAAA,QAAAC,IAAA,GAAAC,WAAA;UACAvB,IAAA,EAAAgB,WAAA,CAAAhB,IAAA;UACAN,QAAA,EAAAsB,WAAA,CAAAtB,QAAA;UACAC,MAAA,EAAA6B,QAAA,CAAAR,WAAA,CAAArB,MAAA;UACA8B,OAAA,EAAAD,QAAA,CAAAR,WAAA,CAAAS,OAAA;UACAC,WAAA,EAAAV,WAAA,CAAAU,WAAA,SAAAC,mBAAA,CAAAX,WAAA;QACA;MACA;IACA;EACA;EACAY,OAAA;IACA;AACA;AACA;IACA,MAAArB,SAAA;MACA;QACA,KAAAxB,SAAA;QACA,KAAAC,KAAA;;QAEA;QACA,MAAA6C,SAAA,GAAAlD,mBAAA;QACA,KAAAkD,SAAA;UACAnD,MAAA,CAAAM,KAAA;;UAEA;UACA;YACA,MAAA8C,WAAA,GAAAC,YAAA,CAAAC,OAAA;YACA,MAAAC,aAAA,GAAAF,YAAA,CAAAC,OAAA;YAEA,IAAAF,WAAA,IAAAG,aAAA;cACA,MAAAC,cAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,aAAA;cACAvD,MAAA,CAAA2D,KAAA,+BAAAH,cAAA;cAEA,KAAA9C,aAAA;gBACAD,EAAA,EAAA+C,cAAA,CAAA/C,EAAA;gBACAP,IAAA,EAAAsD,cAAA,CAAAtD,IAAA;gBACAS,MAAA,EAAA6C,cAAA,CAAA7C,MAAA;gBACAC,IAAA,EAAA4C,cAAA,CAAA5C,IAAA;cACA;YACA;cACA,UAAAgD,KAAA;YACA;UACA,SAAAC,CAAA;YACA7D,MAAA,CAAAM,KAAA,gCAAAuD,CAAA;YACA,UAAAD,KAAA;UACA;QACA;UACA;UACA,KAAAlD,aAAA;YACAD,EAAA,EAAA0C,SAAA,CAAA1C,EAAA;YACAP,IAAA,EAAAiD,SAAA,CAAAjD,IAAA;YACAS,MAAA,EAAAwC,SAAA,CAAAxC,MAAA;YACAC,IAAA,EAAAuC,SAAA,CAAAvC,IAAA;UACA;UAEAZ,MAAA,CAAA2D,KAAA,oBAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;QACA;;QAEA;QACA;UAAA,IAAAqD,YAAA;UACA,MAAAC,eAAA,IAAAD,YAAA,QAAAE,MAAA,cAAAF,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAG,KAAA,cAAAH,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAZ,SAAA,cAAAY,YAAA,uBAAAA,YAAA,CAAAC,eAAA;UACA,IAAAA,eAAA;YACA,KAAAtD,aAAA,CAAAC,MAAA,GAAAqD,eAAA,CAAArD,MAAA,SAAAD,aAAA,CAAAC,MAAA;YACA,KAAAD,aAAA,CAAAE,IAAA,GAAAoD,eAAA,CAAApD,IAAA,SAAAF,aAAA,CAAAE,IAAA;YACAZ,MAAA,CAAA2D,KAAA,0BAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;UACA;YACAV,MAAA,CAAAmE,IAAA;UACA;QACA,SAAAC,UAAA;UACApE,MAAA,CAAAM,KAAA,0BAAA8D,UAAA;QACA;;QAEA;QACA,KAAAvD,WAAA;UACAF,MAAA;UACAG,UAAA;QACA;QAEA;UACA;UACAd,MAAA,CAAA2D,KAAA,gCAAAjD,aAAA,CAAAD,EAAA;UACA,MAAA4D,eAAA,SAAAvE,WAAA,CAAAwE,cAAA,MAAA5D,aAAA,CAAAD,EAAA;UACAT,MAAA,CAAA2D,KAAA,mBAAAF,IAAA,CAAAK,SAAA,CAAAO,eAAA;;UAEA;UACA,IAAAA,eAAA;YACA;YACA,IAAAA,eAAA,CAAAjE,IAAA,IAAAiE,eAAA,CAAAjE,IAAA,CAAAmE,OAAA;cACA;cACA,KAAA1D,WAAA,GAAAwD,eAAA,CAAAjE,IAAA,CAAAmE,OAAA;cACAvE,MAAA,CAAA2D,KAAA,0BAAAF,IAAA,CAAAK,SAAA,MAAAjD,WAAA;;cAEA;cACA,IAAAwD,eAAA,CAAAjE,IAAA,CAAA+C,SAAA;gBACAnD,MAAA,CAAA2D,KAAA,wBAAAF,IAAA,CAAAK,SAAA,CAAAO,eAAA,CAAAjE,IAAA,CAAA+C,SAAA;gBACA,KAAAzC,aAAA,CAAAR,IAAA,GAAAmE,eAAA,CAAAjE,IAAA,CAAA+C,SAAA,CAAAjD,IAAA,SAAAQ,aAAA,CAAAR,IAAA;gBACA,KAAAQ,aAAA,CAAAC,MAAA,GAAA0D,eAAA,CAAAjE,IAAA,CAAA+C,SAAA,CAAAxC,MAAA,SAAAD,aAAA,CAAAC,MAAA;gBACA,KAAAD,aAAA,CAAAE,IAAA,GAAAyD,eAAA,CAAAjE,IAAA,CAAA+C,SAAA,CAAAvC,IAAA,SAAAF,aAAA,CAAAE,IAAA;gBACAZ,MAAA,CAAA2D,KAAA,qBAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;cACA;YACA,WAAA2D,eAAA,CAAAE,OAAA;cACA;cACA,KAAA1D,WAAA,GAAAwD,eAAA,CAAAE,OAAA;cACAvE,MAAA,CAAA2D,KAAA,2BAAAF,IAAA,CAAAK,SAAA,MAAAjD,WAAA;;cAEA;cACA,IAAAwD,eAAA,CAAAlB,SAAA;gBACAnD,MAAA,CAAA2D,KAAA,wBAAAF,IAAA,CAAAK,SAAA,CAAAO,eAAA,CAAAlB,SAAA;gBACA,KAAAzC,aAAA,CAAAR,IAAA,GAAAmE,eAAA,CAAAlB,SAAA,CAAAjD,IAAA,SAAAQ,aAAA,CAAAR,IAAA;gBACA,KAAAQ,aAAA,CAAAC,MAAA,GAAA0D,eAAA,CAAAlB,SAAA,CAAAxC,MAAA,SAAAD,aAAA,CAAAC,MAAA;gBACA,KAAAD,aAAA,CAAAE,IAAA,GAAAyD,eAAA,CAAAlB,SAAA,CAAAvC,IAAA,SAAAF,aAAA,CAAAE,IAAA;gBACAZ,MAAA,CAAA2D,KAAA,qBAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;cACA;YACA,kBAAA2D,eAAA,iBAAAA,eAAA;cACA;cACA;cACA,gBAAAA,eAAA,oBAAAA,eAAA;gBACA,KAAAxD,WAAA;kBACAF,MAAA,EAAA0D,eAAA,CAAA1D,MAAA;kBACAG,UAAA,EAAAuD,eAAA,CAAAvD,UAAA;gBACA;gBACAd,MAAA,CAAA2D,KAAA,2BAAAF,IAAA,CAAAK,SAAA,MAAAjD,WAAA;;gBAEA;gBACA,cAAAwD,eAAA,gBAAAA,eAAA,cAAAA,eAAA;kBACArE,MAAA,CAAA2D,KAAA;kBACA,cAAAU,eAAA,OAAA3D,aAAA,CAAAR,IAAA,GAAAmE,eAAA,CAAAnE,IAAA;kBACA,gBAAAmE,eAAA,OAAA3D,aAAA,CAAAC,MAAA,GAAA0D,eAAA,CAAA1D,MAAA;kBACA,cAAA0D,eAAA,OAAA3D,aAAA,CAAAE,IAAA,GAAAyD,eAAA,CAAAzD,IAAA;kBACAZ,MAAA,CAAA2D,KAAA,qBAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;gBACA;cACA;gBACA,UAAAkD,KAAA;cACA;YACA;cACA,UAAAA,KAAA;YACA;UACA;YACA,UAAAA,KAAA;UACA;QACA,SAAAY,QAAA;UACA;UACAxE,MAAA,CAAAM,KAAA,oBAAAkE,QAAA;UACAxE,MAAA,CAAAM,KAAA,sBAAAmD,IAAA,CAAAK,SAAA,CAAAU,QAAA;;UAEA;UACAzE,WAAA;;UAEA;QACA;UACA;UACA,KAAAgB,WAAA,CAAAE,MAAA,GAAAsB,IAAA,CAAAkC,GAAA,WAAA/D,aAAA,CAAAC,MAAA;UACA,KAAAO,YAAA,CAAAD,MAAA,GAAAsB,IAAA,CAAAkC,GAAA,WAAA5D,WAAA,CAAAF,MAAA;QACA;QAEA,KAAAN,SAAA;MACA,SAAAC,KAAA;QACA,KAAAA,KAAA,GAAAA,KAAA,CAAAoE,OAAA;QACA,KAAArE,SAAA;QACAL,MAAA,CAAAM,KAAA,mBAAAA,KAAA;;QAEA;QACA,IAAAA,KAAA,CAAAoE,OAAA,IAAApE,KAAA,CAAAoE,OAAA,CAAAC,QAAA;UACA5E,WAAA;UACA,KAAA6E,OAAA,CAAAC,IAAA;QACA;MACA;IACA;IAEA;AACA;AACA;IACA,MAAA7C,iBAAA;MACA;QACA,KAAA3B,SAAA;QACA,KAAAC,KAAA;;QAEA;QACA,UAAAI,aAAA,UAAAA,aAAA,CAAAD,EAAA;UACAT,MAAA,CAAAmE,IAAA;UACA,KAAAhD,YAAA;UACA,KAAAd,SAAA;UACA;QACA;;QAEA;QACA,MAAAyE,MAAA;QACA,SAAAzD,kBAAA,CAAAE,IAAA;UACAuD,MAAA,CAAAvD,IAAA,QAAAF,kBAAA,CAAAE,IAAA;QACA;QACA,SAAAF,kBAAA,CAAAG,QAAA;UACAsD,MAAA,CAAAtD,QAAA,QAAAH,kBAAA,CAAAG,QAAA;QACA;QACA;QACA,SAAAH,kBAAA,CAAAL,QAAA,SAAAK,kBAAA,CAAAL,QAAA;UACA8D,MAAA,CAAA9D,QAAA,QAAAK,kBAAA,CAAAL,QAAA;QACA;QACA,SAAAK,kBAAA,CAAAC,IAAA,SAAAD,kBAAA,CAAAC,IAAA;UACAwD,MAAA,CAAAxD,IAAA,QAAAD,kBAAA,CAAAC,IAAA;QACA;QAEAtB,MAAA,CAAA2D,KAAA,uBAAAmB,MAAA;QAEA;UACA,MAAAC,QAAA,SAAAjF,WAAA,CAAAkF,qBAAA,MAAAtE,aAAA,CAAAD,EAAA,EAAAqE,MAAA;UACA9E,MAAA,CAAA2D,KAAA,mBAAAF,IAAA,CAAAK,SAAA,CAAAiB,QAAA;;UAEA;UACA,IAAAA,QAAA,IAAAA,QAAA,CAAA3E,IAAA;YACA;YACA,KAAAe,YAAA,GAAA4D,QAAA,CAAA3E,IAAA,CAAAe,YAAA;YACA,KAAAC,UAAA,GAAA2D,QAAA,CAAA3E,IAAA,CAAAgB,UAAA;YACApB,MAAA,CAAA2D,KAAA,yBAAAxC,YAAA,CAAA8D,MAAA;UACA,WAAAF,QAAA,IAAAG,KAAA,CAAAC,OAAA,CAAAJ,QAAA;YACA;YACA,KAAA5D,YAAA,GAAA4D,QAAA;YACA/E,MAAA,CAAA2D,KAAA,+BAAAxC,YAAA,CAAA8D,MAAA;UACA,WAAAF,QAAA,IAAAG,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAA5D,YAAA;YACA;YACA,KAAAA,YAAA,GAAA4D,QAAA,CAAA5D,YAAA;YACA,KAAAC,UAAA,GAAA2D,QAAA,CAAA3D,UAAA;YACApB,MAAA,CAAA2D,KAAA,+BAAAxC,YAAA,CAAA8D,MAAA;UACA,WAAAF,QAAA,IAAAA,QAAA,CAAAK,OAAA,IAAAF,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAA3E,IAAA;YACA;YACA,KAAAe,YAAA,GAAA4D,QAAA,CAAA3E,IAAA;YACAJ,MAAA,CAAA2D,KAAA,sCAAAxC,YAAA,CAAA8D,MAAA;UACA;YACA;YACAjF,MAAA,CAAAmE,IAAA,8BAAAV,IAAA,CAAAK,SAAA,CAAAiB,QAAA;YACA,KAAA5D,YAAA;YACA,KAAAC,UAAA;UACA;;UAEA;UACA,SAAAD,YAAA,CAAA8D,MAAA;YACAjF,MAAA,CAAA2D,KAAA,qBAAAF,IAAA,CAAAK,SAAA,MAAA3C,YAAA;UACA;YACAnB,MAAA,CAAAmE,IAAA,+BAAA9C,kBAAA;UACA;QACA,SAAAmD,QAAA;UACA;UACAxE,MAAA,CAAAM,KAAA,wBAAAkE,QAAA;UACA,KAAArD,YAAA;UACA,KAAAC,UAAA;;UAEA;UACArB,WAAA;QACA;QAEA,KAAAM,SAAA;MACA,SAAAC,KAAA;QACA,KAAAA,KAAA,GAAAA,KAAA,CAAAoE,OAAA;QACA,KAAArE,SAAA;QACAL,MAAA,CAAAM,KAAA,qBAAAA,KAAA;;QAEA;QACA,KAAAa,YAAA,QAAAA,YAAA;MACA;IACA;IAEA;AACA;AACA;IACA,MAAAkE,QAAA;MACA;QACA,UAAAC,UAAA;UACA,YAAAC,iBAAA;QACA;QAEA,KAAAlF,SAAA;QACA,KAAAC,KAAA;QAEA,MAAAyE,QAAA,SAAAjF,WAAA,CAAAuF,OAAA,CACA,KAAA3E,aAAA,CAAAD,EAAA,EACA,KAAAM,WAAA,CAAAC,QAAA,EACA8B,QAAA,MAAA/B,WAAA,CAAAE,MAAA,MACA;;QAEA;QACA,IAAA8D,QAAA,CAAAR,OAAA;UACA,KAAA1D,WAAA,GAAAkE,QAAA,CAAAR,OAAA;UACAvE,MAAA,CAAA2D,KAAA,sBAAAF,IAAA,CAAAK,SAAA,MAAAjD,WAAA;QACA;QAEA,IAAAkE,QAAA,CAAA5B,SAAA;UACA;UACA,KAAAzC,aAAA,CAAAC,MAAA,GAAAmC,QAAA,CAAAiC,QAAA,CAAA5B,SAAA,CAAAxC,MAAA,UAAAD,aAAA,CAAAC,MAAA;UACA,KAAAD,aAAA,CAAAE,IAAA,GAAAkC,QAAA,CAAAiC,QAAA,CAAA5B,SAAA,CAAAvC,IAAA,UAAAF,aAAA,CAAAE,IAAA;UACAZ,MAAA,CAAA2D,KAAA,sBAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;QACA;QAEA,KAAAL,SAAA;QACA,KAAAkF,iBAAA,CAAAR,QAAA,CAAAL,OAAA;MACA,SAAApE,KAAA;QACA,KAAAD,SAAA;QACA,KAAAkF,iBAAA,CAAAjF,KAAA,CAAAoE,OAAA;QACA1E,MAAA,CAAAM,KAAA,mBAAAA,KAAA;MACA;IACA;IAEA;AACA;AACA;IACA,MAAAkF,SAAA;MACA;QACA,UAAAC,WAAA;UACA,YAAAF,iBAAA;QACA;QAEA,KAAAlF,SAAA;QACA,KAAAC,KAAA;QAEA,MAAAyE,QAAA,SAAAjF,WAAA,CAAA0F,QAAA,CACA,KAAA9E,aAAA,CAAAD,EAAA,EACA,KAAAS,YAAA,CAAAF,QAAA,EACA8B,QAAA,MAAA5B,YAAA,CAAAD,MAAA,MACA;;QAEA;QACA,IAAA8D,QAAA,CAAAR,OAAA;UACA,KAAA1D,WAAA,GAAAkE,QAAA,CAAAR,OAAA;UACAvE,MAAA,CAAA2D,KAAA,sBAAAF,IAAA,CAAAK,SAAA,MAAAjD,WAAA;QACA;QAEA,IAAAkE,QAAA,CAAA5B,SAAA;UACA;UACA,KAAAzC,aAAA,CAAAC,MAAA,GAAAmC,QAAA,CAAAiC,QAAA,CAAA5B,SAAA,CAAAxC,MAAA,UAAAD,aAAA,CAAAC,MAAA;UACA,KAAAD,aAAA,CAAAE,IAAA,GAAAkC,QAAA,CAAAiC,QAAA,CAAA5B,SAAA,CAAAvC,IAAA,UAAAF,aAAA,CAAAE,IAAA;UACAZ,MAAA,CAAA2D,KAAA,sBAAAF,IAAA,CAAAK,SAAA,MAAApD,aAAA;QACA;QAEA,KAAAL,SAAA;QACA,KAAAkF,iBAAA,CAAAR,QAAA,CAAAL,OAAA;MACA,SAAApE,KAAA;QACA,KAAAD,SAAA;QACA,KAAAkF,iBAAA,CAAAjF,KAAA,CAAAoE,OAAA;QACA1E,MAAA,CAAAM,KAAA,mBAAAA,KAAA;MACA;IACA;IAEA;AACA;AACA;IACAiF,kBAAAb,OAAA,EAAAgB,OAAA;MACA,KAAA/D,aAAA,GAAA+C,OAAA;MACA,KAAAhD,WAAA,GAAAgE,OAAA;MACA,KAAAjE,UAAA;IACA;IAEA;AACA;AACA;IACAwB,oBAAAX,WAAA;MACA,MAAAhB,IAAA,GAAAgB,WAAA,CAAAhB,IAAA;MACA,MAAAN,QAAA,GAAAsB,WAAA,CAAAtB,QAAA;MACA,MAAAC,MAAA,GAAAqB,WAAA,CAAArB,MAAA;MACA,UAAAK,IAAA,IAAAL,MAAA,IAAAD,QAAA;IACA;IAEA;AACA;AACA;IACA2E,WAAAC,OAAA;MACA,KAAAA,OAAA;MAEA;QACA,MAAAC,IAAA,OAAAjD,IAAA,CAAAgD,OAAA;QACA,IAAAE,KAAA,CAAAD,IAAA,CAAAE,OAAA;QAEA,OAAAF,IAAA,CAAAG,cAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;UACAC,IAAA;UACAC,MAAA;UACAC,MAAA;QACA;MACA,SAAAzC,CAAA;QACA7D,MAAA,CAAAM,KAAA,oBAAAuD,CAAA;QACA;MACA;IACA;IAEA;AACA;AACA;IACA0C,WAAAhF,IAAA;MACA,IAAAA,IAAA,aAAAH,UAAA,IAAAG,IAAA,QAAAH,UAAA,CAAAoF,SAAA;QACA;MACA;MAEA,KAAAnF,kBAAA,CAAAE,IAAA,GAAAA,IAAA;MACA,KAAAS,gBAAA;IACA;IAEA;AACA;AACA;IACAyE,oBAAA;MACA,SAAA1F,WAAA,CAAAC,QAAA;QACA,YAAAN,aAAA,CAAAC,MAAA;MACA;QACA,YAAAD,aAAA,CAAAE,IAAA;MACA;IACA;IAEA;AACA;AACA;IACA8F,qBAAA;MACA,SAAAxF,YAAA,CAAAF,QAAA;QACA,YAAAH,WAAA,CAAAF,MAAA;MACA;QACA,YAAAE,WAAA,CAAAC,UAAA;MACA;IACA;IAEA;AACA;AACA;IACA6F,oBAAA;MACA,KAAA5F,WAAA,CAAAE,MAAA,QAAAwF,mBAAA;IACA;IAEA;AACA;AACA;IACAG,qBAAA;MACA,KAAA1F,YAAA,CAAAD,MAAA,QAAAyF,oBAAA;IACA;IAEA;AACA;AACA;IACApB,WAAA;MACA,MAAArE,MAAA,GAAA6B,QAAA,MAAA/B,WAAA,CAAAE,MAAA;MACA,KAAAA,MAAA,IAAAA,MAAA;QACA;MACA;MAEA,SAAAF,WAAA,CAAAC,QAAA;QACA,OAAAC,MAAA,SAAAP,aAAA,CAAAC,MAAA;MACA;QACA,OAAAM,MAAA,SAAAP,aAAA,CAAAE,IAAA;MACA;IACA;IAEA;AACA;AACA;IACA6E,YAAA;MACA,MAAAxE,MAAA,GAAA6B,QAAA,MAAA5B,YAAA,CAAAD,MAAA;MACA,KAAAA,MAAA,IAAAA,MAAA;QACA;MACA;MAEA,SAAAC,YAAA,CAAAF,QAAA;QACA,OAAAC,MAAA,SAAAJ,WAAA,CAAAF,MAAA;MACA;QACA,OAAAM,MAAA,SAAAJ,WAAA,CAAAC,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}