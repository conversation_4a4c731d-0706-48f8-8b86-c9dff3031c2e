{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue", "mtime": 1749718635730}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Friends.vue"], "names": [], "mappings": ";AA6LA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Friends.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"friends-page\">\n      <!-- 返回按钮 -->\n      <div class=\"header-section\">\n        <button class=\"return-btn\" @click=\"goBack\">\n          <img src=\"/static/game/UI/anniu/fhui_2.png\" alt=\"返回\" class=\"btn-image\" />\n        </button>\n        <h2 class=\"page-title\">好友系统</h2>\n      </div>\n      \n      <!-- 功能标签 -->\n      <div class=\"friends-tabs\">\n        <div \n          v-for=\"(tab, index) in friendsTabs\" \n          :key=\"index\"\n          class=\"friends-tab\"\n          :class=\"{ active: currentTab === index }\"\n          @click=\"switchTab(index)\"\n        >\n          {{ tab.name }}\n        </div>\n      </div>\n      \n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-text\">加载中...</div>\n      </div>\n      \n      <!-- 错误状态 -->\n      <div v-else-if=\"error\" class=\"error-container\">\n        <div class=\"error-text\">{{ error }}</div>\n        <div class=\"error-actions\">\n          <button v-if=\"error.includes('登录')\" class=\"retry-btn\" @click=\"goToLogin\">前往登录</button>\n          <button v-else-if=\"error.includes('角色')\" class=\"retry-btn\" @click=\"goToCharacterSelect\">选择角色</button>\n          <button v-else class=\"retry-btn\" @click=\"fetchFriendsData\">重试</button>\n        </div>\n      </div>\n      \n      <!-- 好友内容 -->\n      <div v-else class=\"friends-content\">\n        <!-- 好友列表 -->\n        <div v-if=\"currentTab === 0\" class=\"friends-list\">\n          <div v-if=\"friends.length === 0\" class=\"empty-tip\">\n            <span>暂无好友</span>\n          </div>\n          \n          <div v-else class=\"friend-items\">\n            <div \n              v-for=\"friend in friends\" \n              :key=\"friend.id\"\n              class=\"friend-item\"\n              :class=\"{ online: friend.isOnline }\"\n            >\n              <div class=\"friend-avatar\">\n                <img :src=\"friend.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"friend.name\" />\n                <div v-if=\"friend.isOnline\" class=\"online-indicator\"></div>\n              </div>\n              \n              <div class=\"friend-info\">\n                <div class=\"friend-name\">{{ friend.name }}</div>\n                <div class=\"friend-level\">等级 {{ friend.level }}</div>\n                <div class=\"friend-status\">{{ friend.isOnline ? '在线' : '离线' }}</div>\n              </div>\n              \n              <div class=\"friend-actions\">\n                <button \n                  class=\"action-btn chat\"\n                  @click=\"chatWithFriend(friend)\"\n                  :disabled=\"!friend.isOnline\"\n                >\n                  聊天\n                </button>\n                <button \n                  class=\"action-btn remove\"\n                  @click=\"removeFriend(friend)\"\n                >\n                  删除\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 好友申请 -->\n        <div v-if=\"currentTab === 1\" class=\"friend-requests\">\n          <div v-if=\"friendRequests.length === 0\" class=\"empty-tip\">\n            <span>暂无好友申请</span>\n          </div>\n          \n          <div v-else class=\"request-items\">\n            <div \n              v-for=\"request in friendRequests\" \n              :key=\"request.id\"\n              class=\"request-item\"\n            >\n              <div class=\"request-avatar\">\n                <img :src=\"request.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"request.name\" />\n              </div>\n              \n              <div class=\"request-info\">\n                <div class=\"request-name\">{{ request.name }}</div>\n                <div class=\"request-level\">等级 {{ request.level }}</div>\n                <div class=\"request-time\">{{ formatTime(request.requestTime) }}</div>\n              </div>\n              \n              <div class=\"request-actions\">\n                <button \n                  class=\"action-btn accept\"\n                  @click=\"acceptFriendRequest(request)\"\n                  :disabled=\"isActionLoading\"\n                >\n                  接受\n                </button>\n                <button \n                  class=\"action-btn reject\"\n                  @click=\"rejectFriendRequest(request)\"\n                  :disabled=\"isActionLoading\"\n                >\n                  拒绝\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 添加好友 -->\n        <div v-if=\"currentTab === 2\" class=\"add-friend\">\n          <div class=\"search-section\">\n            <div class=\"search-input-group\">\n              <input \n                v-model=\"searchQuery\"\n                type=\"text\"\n                placeholder=\"输入玩家名称搜索\"\n                class=\"search-input\"\n                @keyup.enter=\"searchPlayers\"\n              />\n              <button \n                class=\"search-btn\"\n                @click=\"searchPlayers\"\n                :disabled=\"isSearching || !searchQuery.trim()\"\n              >\n                搜索\n              </button>\n            </div>\n          </div>\n          \n          <div v-if=\"isSearching\" class=\"searching-tip\">\n            <span>搜索中...</span>\n          </div>\n          \n          <div v-else-if=\"searchResults.length === 0 && hasSearched\" class=\"empty-tip\">\n            <span>未找到相关玩家</span>\n          </div>\n          \n          <div v-else-if=\"searchResults.length > 0\" class=\"search-results\">\n            <div \n              v-for=\"player in searchResults\" \n              :key=\"player.id\"\n              class=\"search-result-item\"\n            >\n              <div class=\"result-avatar\">\n                <img :src=\"player.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"player.name\" />\n              </div>\n              \n              <div class=\"result-info\">\n                <div class=\"result-name\">{{ player.name }}</div>\n                <div class=\"result-level\">等级 {{ player.level }}</div>\n                <div class=\"result-status\">{{ player.isOnline ? '在线' : '离线' }}</div>\n              </div>\n              \n              <div class=\"result-actions\">\n                <button \n                  class=\"action-btn add\"\n                  @click=\"sendFriendRequest(player)\"\n                  :disabled=\"isActionLoading || isAlreadyFriend(player) || hasPendingRequest(player)\"\n                >\n                  {{ getAddButtonText(player) }}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport axios from 'axios'\nimport { API_BASE_URL } from '@/api/config.js'\nimport { ERROR_MESSAGES } from '@/api/constants.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      currentTab: 0,\n      friendsTabs: [\n        { name: '好友列表', type: 'friends' },\n        { name: '好友申请', type: 'requests' },\n        { name: '添加好友', type: 'add' }\n      ],\n      friends: [],\n      friendRequests: [],\n      searchQuery: '',\n      searchResults: [],\n      hasSearched: false,\n      isLoading: true,\n      isSearching: false,\n      isActionLoading: false,\n      error: null\n    }\n  },\n  computed: {\n    authToken() {\n      return this.$store.state.token || localStorage.getItem('authToken')\n    },\n    selectedCharacterId() {\n      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')\n    }\n  },\n  created() {\n    // 检查认证状态\n    if (!this.authToken) {\n      logger.warn('Friends页面: 未找到认证token')\n      this.error = '请先登录'\n      return\n    }\n\n    // 检查角色选择状态\n    if (!this.selectedCharacterId) {\n      logger.warn('Friends页面: 未选择角色')\n      this.error = '请先选择角色'\n      return\n    }\n\n    this.fetchFriendsData()\n  },\n  methods: {\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    switchTab(index) {\n      this.currentTab = index\n      logger.info('切换好友标签', this.friendsTabs[index].name)\n      \n      if (index === 1) {\n        this.fetchFriendRequests()\n      }\n    },\n    \n    async fetchFriendsData() {\n      this.isLoading = true\n      this.error = null\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friends`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.friends = response.data.friends || []\n        logger.info('获取好友列表成功', this.friends.length)\n      } catch (err) {\n        this.error = err.message || ERROR_MESSAGES.UNKNOWN_ERROR\n        this.showToast(this.error)\n        logger.error('获取好友列表失败', err)\n      } finally {\n        this.isLoading = false\n      }\n    },\n    \n    async fetchFriendRequests() {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.friendRequests = response.data.requests || []\n        logger.info('获取好友申请成功', this.friendRequests.length)\n      } catch (err) {\n        logger.error('获取好友申请失败', err)\n      }\n    },\n    \n    async searchPlayers() {\n      if (!this.searchQuery.trim()) return\n      \n      this.isSearching = true\n      this.hasSearched = false\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/players/search`, {\n          params: { query: this.searchQuery.trim() },\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.searchResults = response.data.players || []\n        this.hasSearched = true\n        logger.info('搜索玩家成功', this.searchResults.length)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('搜索玩家失败', err)\n      } finally {\n        this.isSearching = false\n      }\n    },\n    \n    async sendFriendRequest(player) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`,\n          { target_player_id: player.id },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '好友申请已发送', 'success')\n        logger.info('发送好友申请成功', player.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('发送好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async acceptFriendRequest(request) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/accept`,\n          {},\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已接受好友申请', 'success')\n        await this.fetchFriendRequests()\n        await this.fetchFriendsData()\n        logger.info('接受好友申请成功', request.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('接受好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async rejectFriendRequest(request) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/reject`,\n          {},\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已拒绝好友申请', 'success')\n        await this.fetchFriendRequests()\n        logger.info('拒绝好友申请成功', request.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('拒绝好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async removeFriend(friend) {\n      if (!confirm(`确定要删除好友\"${friend.name}\"吗？`)) {\n        return\n      }\n      \n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.delete(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friends/${friend.id}`,\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已删除好友', 'success')\n        await this.fetchFriendsData()\n        logger.info('删除好友成功', friend.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('删除好友失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    chatWithFriend(friend) {\n      this.showToast(`与${friend.name}的聊天功能开发中...`, 'info')\n      // 这里可以实现私聊功能\n    },\n    \n    isAlreadyFriend(player) {\n      return this.friends.some(friend => friend.id === player.id)\n    },\n    \n    hasPendingRequest(player) {\n      return this.friendRequests.some(request => request.id === player.id)\n    },\n    \n    getAddButtonText(player) {\n      if (this.isAlreadyFriend(player)) return '已是好友'\n      if (this.hasPendingRequest(player)) return '已申请'\n      return '添加好友'\n    },\n    \n    formatTime(timestamp) {\n      const date = new Date(timestamp)\n      const now = new Date()\n      const diff = now - date\n      \n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`\n      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`\n      return `${Math.floor(diff / 86400000)}天前`\n    },\n    \n    showToast(message, type = 'error') {\n      alert(message)\n    },\n\n    goToLogin() {\n      this.$router.push('/login')\n    },\n\n    goToCharacterSelect() {\n      this.$router.push('/setup/character-select')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.friends-page {\n  padding: 15px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2d1b69, #1a0f3d);\n  color: #fff;\n}\n\n.header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  gap: 15px;\n}\n\n.return-btn {\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  padding: 0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.btn-image {\n  width: 60px;\n  height: 40px;\n  object-fit: contain;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #fff;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.friends-tabs {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 20px;\n  border-bottom: 2px solid rgba(255, 255, 255, 0.2);\n  padding-bottom: 10px;\n}\n\n.friends-tab {\n  padding: 10px 20px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 500;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-2px);\n  }\n\n  &.active {\n    background: rgba(255, 255, 255, 0.3);\n    border-color: rgba(255, 255, 255, 0.5);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.loading-container, .error-container {\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.loading-text {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.error-text {\n  font-size: 16px;\n  color: #ff6b6b;\n  margin-bottom: 15px;\n}\n\n.error-actions {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.retry-btn {\n  padding: 10px 20px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n}\n\n.friends-content {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.empty-tip {\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  padding: 60px 20px;\n  font-size: 16px;\n}\n\n// 好友列表样式\n.friend-items, .request-items, .search-results {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.friend-item, .request-item, .search-result-item {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 12px;\n  padding: 15px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.12);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\n  }\n\n  &.online {\n    border-left: 4px solid #2ecc71;\n  }\n}\n\n.friend-avatar, .request-avatar, .result-avatar {\n  position: relative;\n  width: 60px;\n  height: 60px;\n  margin-right: 15px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 50%;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n}\n\n.online-indicator {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 16px;\n  height: 16px;\n  background: #2ecc71;\n  border: 2px solid #fff;\n  border-radius: 50%;\n}\n\n.friend-info, .request-info, .result-info {\n  flex: 1;\n  margin-right: 15px;\n}\n\n.friend-name, .request-name, .result-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 4px;\n}\n\n.friend-level, .request-level, .result-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 4px;\n}\n\n.friend-status, .result-status {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.request-time {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.friend-actions, .request-actions, .result-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.chat {\n    background: #4ecdc4;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #45b7aa;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.remove {\n    background: #e74c3c;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #c0392b;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.accept {\n    background: #2ecc71;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #27ae60;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.reject {\n    background: #95a5a6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #7f8c8d;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.add {\n    background: #3498db;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #2980b9;\n      transform: translateY(-1px);\n    }\n  }\n}\n\n// 搜索区域样式\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-input-group {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.search-input {\n  flex: 1;\n  padding: 12px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 8px;\n  color: #fff;\n  font-size: 16px;\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #4ecdc4;\n    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);\n  }\n}\n\n.search-btn {\n  padding: 12px 24px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.searching-tip {\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  padding: 40px 20px;\n  font-size: 16px;\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .friends-page {\n    padding: 10px;\n  }\n\n  .header-section {\n    margin-bottom: 15px;\n    gap: 10px;\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .btn-image {\n    width: 50px;\n    height: 35px;\n  }\n\n  .friends-tabs {\n    flex-wrap: wrap;\n    gap: 6px;\n  }\n\n  .friends-tab {\n    padding: 8px 16px;\n    font-size: 14px;\n  }\n\n  .friend-item, .request-item, .search-result-item {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .friend-avatar, .request-avatar, .result-avatar {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .friend-info, .request-info, .result-info {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .friend-actions, .request-actions, .result-actions {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n  }\n\n  .search-input, .search-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n  .friends-page {\n    padding: 8px;\n  }\n\n  .friends-content {\n    padding: 15px;\n  }\n\n  .friend-avatar, .request-avatar, .result-avatar {\n    width: 50px;\n    height: 50px;\n  }\n\n  .action-btn {\n    padding: 6px 12px;\n    font-size: 11px;\n  }\n}\n</style>\n"]}]}