<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ItemController extends Controller
{
    /**
     * 显示物品列表
     */
    public function index()
    {
        try {
            $items = DB::table('items')
                ->orderBy('type')
                ->orderBy('level_requirement')
                ->orderBy('name')
                ->paginate(15);

            return view('admin.items.index', compact('items'));
        } catch (\Exception $e) {
            // 创建一个空的分页对象而不是简单的集合
            $items = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.items.index', compact('items'));
        }
    }

    /**
     * 显示创建物品表单
     */
    public function create()
    {
        return view('admin.items.create');
    }

    /**
     * 保存新物品
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:items',
            'description' => 'nullable|string',
            'type' => 'required|string|in:weapon,armor,accessory,consumable,material,quest',
            'level_requirement' => 'nullable|integer|min:0',
            'buy_price' => 'nullable|integer|min:0',
            'sell_price' => 'nullable|integer|min:0',
            'attack_bonus' => 'nullable|integer',
            'defense_bonus' => 'nullable|integer',
            'hp_bonus' => 'nullable|integer',
            'mp_bonus' => 'nullable|integer',
            'speed_bonus' => 'nullable|integer',
            'is_tradable' => 'boolean',
            'is_stackable' => 'boolean',
            'max_stack' => 'nullable|integer|min:1',
            'rarity' => 'required|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::table('items')->insert([
                'name' => $request->name,
                'description' => $request->description,
                'type' => $request->type,
                'level_requirement' => $request->level_requirement ?? 0,
                'buy_price' => $request->buy_price ?? 0,
                'sell_price' => $request->sell_price ?? 0,
                'attack_bonus' => $request->attack_bonus ?? 0,
                'defense_bonus' => $request->defense_bonus ?? 0,
                'hp_bonus' => $request->hp_bonus ?? 0,
                'mp_bonus' => $request->mp_bonus ?? 0,
                'speed_bonus' => $request->speed_bonus ?? 0,
                'is_tradable' => $request->has('is_tradable') ? 1 : 0,
                'is_stackable' => $request->has('is_stackable') ? 1 : 0,
                'max_stack' => $request->max_stack ?? 1,
                'rarity' => $request->rarity,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return redirect()->route('admin.items.index')
                ->with('success', '物品创建成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '物品创建失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 显示物品详情
     */
    public function show($id)
    {
        try {
            $item = DB::table('items')->where('id', $id)->first();

            if (!$item) {
                return redirect()->route('admin.items.index')
                    ->with('error', '物品不存在');
            }

            return view('admin.items.show', compact('item'));
        } catch (\Exception $e) {
            return redirect()->route('admin.items.index')
                ->with('error', '无法获取物品信息: ' . $e->getMessage());
        }
    }

    /**
     * 显示编辑物品表单
     */
    public function edit($id)
    {
        try {
            $item = DB::table('items')->where('id', $id)->first();

            if (!$item) {
                return redirect()->route('admin.items.index')
                    ->with('error', '物品不存在');
            }

            return view('admin.items.edit', compact('item'));
        } catch (\Exception $e) {
            return redirect()->route('admin.items.index')
                ->with('error', '无法获取物品信息: ' . $e->getMessage());
        }
    }

    /**
     * 更新物品信息
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:items,name,' . $id,
            'description' => 'nullable|string',
            'type' => 'required|string|in:weapon,armor,accessory,consumable,material,quest',
            'level_requirement' => 'nullable|integer|min:0',
            'buy_price' => 'nullable|integer|min:0',
            'sell_price' => 'nullable|integer|min:0',
            'attack_bonus' => 'nullable|integer',
            'defense_bonus' => 'nullable|integer',
            'hp_bonus' => 'nullable|integer',
            'mp_bonus' => 'nullable|integer',
            'speed_bonus' => 'nullable|integer',
            'is_tradable' => 'boolean',
            'is_stackable' => 'boolean',
            'max_stack' => 'nullable|integer|min:1',
            'rarity' => 'required|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::table('items')
                ->where('id', $id)
                ->update([
                    'name' => $request->name,
                    'description' => $request->description,
                    'type' => $request->type,
                    'level_requirement' => $request->level_requirement ?? 0,
                    'buy_price' => $request->buy_price ?? 0,
                    'sell_price' => $request->sell_price ?? 0,
                    'attack_bonus' => $request->attack_bonus ?? 0,
                    'defense_bonus' => $request->defense_bonus ?? 0,
                    'hp_bonus' => $request->hp_bonus ?? 0,
                    'mp_bonus' => $request->mp_bonus ?? 0,
                    'speed_bonus' => $request->speed_bonus ?? 0,
                    'is_tradable' => $request->has('is_tradable') ? 1 : 0,
                    'is_stackable' => $request->has('is_stackable') ? 1 : 0,
                    'max_stack' => $request->max_stack ?? 1,
                    'rarity' => $request->rarity,
                    'updated_at' => now(),
                ]);

            return redirect()->route('admin.items.index')
                ->with('success', '物品更新成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '物品更新失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 删除物品
     */
    public function destroy($id)
    {
        try {
            // 检查是否有角色装备或持有此物品
            $equippedCount = DB::table('character_equipments')
                ->where('item_id', $id)
                ->count();

            $inventoryCount = DB::table('character_items')
                ->where('item_id', $id)
                ->count();

            if ($equippedCount > 0 || $inventoryCount > 0) {
                return redirect()->route('admin.items.index')
                    ->with('error', '无法删除物品，它正被角色使用或持有');
            }

            DB::table('items')->where('id', $id)->delete();

            return redirect()->route('admin.items.index')
                ->with('success', '物品已删除');
        } catch (\Exception $e) {
            return redirect()->route('admin.items.index')
                ->with('error', '物品删除失败: ' . $e->getMessage());
        }
    }
}
