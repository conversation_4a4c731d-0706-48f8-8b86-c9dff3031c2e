<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Region;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class RegionController extends Controller
{
    /**
     * 获取大区列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 构建查询
            $query = Region::query();

            // 获取数据并排序
            $regions = $query->ordered()->get();

            // 转换数据格式
            $regionData = $regions->map(function ($region) {
                return [
                    'id' => $region->id,
                    'name' => $region->name,
                    'code' => $region->code,
                    'description' => $region->description,
                    'type' => $region->type,
                    'typeText' => $region->type_text,
                    'levelRange' => $region->level_range,
                    'dangerLevel' => $region->danger_level,
                    'dangerLevelText' => $region->danger_level_text,
                    'isPvp' => $region->is_pvp,
                    'weatherEnabled' => $region->weather_enabled,
                    'isActive' => $region->is_active,
                    'createdAt' => $region->created_at->toISOString(),
                    'updatedAt' => $region->updated_at->toISOString(),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $regionData,
                'message' => '获取大区列表成功',
                'meta' => [
                    'total' => $regions->count(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取大区列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取指定大区详情
     */
    public function show(Request $request, $regionId): JsonResponse
    {
        try {
            $region = Region::findOrFail($regionId);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $region->id,
                    'name' => $region->name,
                    'code' => $region->code,
                    'description' => $region->description,
                    'type' => $region->type,
                    'typeText' => $region->type_text,
                    'levelRange' => $region->level_range,
                    'dangerLevel' => $region->danger_level,
                    'dangerLevelText' => $region->danger_level_text,
                    'isPvp' => $region->is_pvp,
                    'weatherEnabled' => $region->weather_enabled,
                    'isActive' => $region->is_active,
                    'createdAt' => $region->created_at->toISOString(),
                    'updatedAt' => $region->updated_at->toISOString(),
                ],
                'message' => '获取大区详情成功'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => '大区不存在'
            ], 404);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取大区详情失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 选择大区（设置用户当前大区）
     */
    public function select(Request $request, $regionId): JsonResponse
    {
        try {
            $region = Region::findOrFail($regionId);

            // 检查区服是否可以进入
            if (!$region->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => '该大区当前无法进入',
                    'data' => [
                        'isActive' => $region->is_active
                    ]
                ], 400);
            }

            // 这里可以添加用户选择大区的逻辑
            // 比如保存到用户表或会话中

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $region->id,
                    'name' => $region->name,
                    'code' => $region->code,
                    'isActive' => $region->is_active,
                ],
                'message' => '选择大区成功'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => '大区不存在'
            ], 404);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '选择大区失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取推荐大区
     */
    public function recommended(Request $request): JsonResponse
    {
        try {
            $regions = Region::where('is_active', true)
                           ->ordered()
                           ->limit(3)
                           ->get();

            $regionData = $regions->map(function ($region) {
                return [
                    'id' => $region->id,
                    'name' => $region->name,
                    'description' => $region->description,
                    'type' => $region->type,
                    'typeText' => $region->type_text,
                    'levelRange' => $region->level_range,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $regionData,
                'message' => '获取推荐大区成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取推荐大区失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
