<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use Illuminate\Support\Facades\Validator;

class ClinicController extends Controller
{
    /**
     * 获取医馆所有服务类型
     */
    public function getServiceTypes()
    {
        $types = [
            [
                'id' => 'health',
                'name' => '气血药品',
                'icon' => '/static/game/icons/services/health_potions.png',
                'description' => '恢复角色生命值的药品'
            ],
            [
                'id' => 'mana',
                'name' => '精力药品',
                'icon' => '/static/game/icons/services/mana_potions.png',
                'description' => '恢复角色精力值的药品'
            ],
            [
                'id' => 'team',
                'name' => '全员治疗',
                'icon' => '/static/game/icons/services/team_healing.png',
                'description' => '为整个队伍提供治疗服务'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $types
        ]);
    }

    /**
     * 获取气血药品列表
     */
    public function getHealthPotions(Request $request)
    {
        $potions = [
            [
                'id' => 101,
                'name' => '小型气血丹',
                'description' => '恢复20%的气血',
                'hp_recovery_percent' => 20,
                'mp_recovery_percent' => 0,
                'cost' => 30,
                'price' => 30,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/small_health_potion.png',
                'required_level' => 1,
                'type' => 'health',
                'effect_duration' => 0, // 立即生效
                'cooldown' => 0,
                'stock' => 99,
                'max_purchase' => 10,
                'effect_value' => '20%'
            ],
            [
                'id' => 102,
                'name' => '中型气血丹',
                'description' => '恢复40%的气血',
                'hp_recovery_percent' => 40,
                'mp_recovery_percent' => 0,
                'cost' => 60,
                'price' => 60,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/medium_health_potion.png',
                'required_level' => 1,
                'type' => 'health',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 99,
                'max_purchase' => 10,
                'effect_value' => '40%'
            ],
            [
                'id' => 103,
                'name' => '大型气血丹',
                'description' => '恢复70%的气血',
                'hp_recovery_percent' => 70,
                'mp_recovery_percent' => 0,
                'cost' => 120,
                'price' => 120,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/large_health_potion.png',
                'required_level' => 1,
                'type' => 'health',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 50,
                'max_purchase' => 5,
                'effect_value' => '70%'
            ],
            [
                'id' => 104,
                'name' => '超级气血丹',
                'description' => '恢复100%的气血',
                'hp_recovery_percent' => 100,
                'mp_recovery_percent' => 0,
                'cost' => 200,
                'price' => 200,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/super_health_potion.png',
                'required_level' => 1,
                'type' => 'health',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 30,
                'max_purchase' => 3,
                'effect_value' => '100%'
            ],
            [
                'id' => 105,
                'name' => '仙灵气血丹',
                'description' => '恢复100%的气血，并在10秒内额外恢复20%',
                'hp_recovery_percent' => 100,
                'mp_recovery_percent' => 0,
                'hp_regen_percent' => 20,
                'cost' => 300,
                'price' => 300,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/fairy_health_potion.png',
                'required_level' => 1,
                'type' => 'health',
                'effect_duration' => 10, // 持续10秒
                'cooldown' => 60, // 冷却时间60秒
                'stock' => 10,
                'max_purchase' => 2,
                'effect_value' => '100%+20%'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $potions
        ]);
    }

    /**
     * 获取精力药品列表
     */
    public function getManaPotions(Request $request)
    {
        $potions = [
            [
                'id' => 201,
                'name' => '小型精力丹',
                'description' => '恢复20%的精力',
                'hp_recovery_percent' => 0,
                'mp_recovery_percent' => 20,
                'cost' => 40,
                'price' => 40,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/small_mana_potion.png',
                'required_level' => 1,
                'type' => 'mana',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 99,
                'max_purchase' => 10,
                'effect_value' => '20%'
            ],
            [
                'id' => 202,
                'name' => '中型精力丹',
                'description' => '恢复40%的精力',
                'hp_recovery_percent' => 0,
                'mp_recovery_percent' => 40,
                'cost' => 80,
                'price' => 80,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/medium_mana_potion.png',
                'required_level' => 1,
                'type' => 'mana',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 99,
                'max_purchase' => 10,
                'effect_value' => '40%'
            ],
            [
                'id' => 203,
                'name' => '大型精力丹',
                'description' => '恢复70%的精力',
                'hp_recovery_percent' => 0,
                'mp_recovery_percent' => 70,
                'cost' => 150,
                'price' => 150,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/large_mana_potion.png',
                'required_level' => 1,
                'type' => 'mana',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 50,
                'max_purchase' => 5,
                'effect_value' => '70%'
            ],
            [
                'id' => 204,
                'name' => '超级精力丹',
                'description' => '恢复100%的精力',
                'hp_recovery_percent' => 0,
                'mp_recovery_percent' => 100,
                'cost' => 250,
                'price' => 250,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/super_mana_potion.png',
                'required_level' => 1,
                'type' => 'mana',
                'effect_duration' => 0,
                'cooldown' => 0,
                'stock' => 30,
                'max_purchase' => 3,
                'effect_value' => '100%'
            ],
            [
                'id' => 205,
                'name' => '仙灵精力丹',
                'description' => '恢复100%的精力，并在10秒内额外恢复20%',
                'hp_recovery_percent' => 0,
                'mp_recovery_percent' => 100,
                'mp_regen_percent' => 20,
                'cost' => 350,
                'price' => 350,
                'currency' => 'silver',
                'icon' => '/static/game/icons/potions/fairy_mana_potion.png',
                'required_level' => 1,
                'type' => 'mana',
                'effect_duration' => 10,
                'cooldown' => 60,
                'stock' => 10,
                'max_purchase' => 2,
                'effect_value' => '100%+20%'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $potions
        ]);
    }

    /**
     * 获取全员治疗服务列表
     */
    public function getTeamHealingServices(Request $request)
    {
        $services = [
            [
                'id' => 304,
                'name' => '团队完全恢复',
                'description' => '为队伍中所有成员恢复100%的气血和精力',
                'hp_recovery_percent' => 100,
                'mp_recovery_percent' => 100,
                'cost' => 500,
                'price' => 500,
                'currency' => 'silver',
                'icon' => '/static/game/icons/services/team_full_recovery.png',
                'required_level' => 1,
                'type' => 'team',
                'max_team_size' => 5,
                'effect_value' => '100%'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $services
        ]);
    }

    /**
     * 购买并使用药品
     */
    public function purchasePotion(Request $request, Character $character)
    {
        // 验证请求
        $validator = Validator::make($request->all(), [
            'potion_id' => 'required|integer|min:101|max:205',
            'quantity' => 'required|integer|min:1|max:10',
            'type' => 'required|string|in:health,mana'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first()
                ]
            ], 422);
        }

        $potionId = $request->potion_id;
        $quantity = $request->quantity;
        $type = $request->type;

        // 获取药品信息
        $potion = $this->getPotionById($potionId);

        if (!$potion) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'POTION_NOT_FOUND',
                    'message' => '药品不存在'
                ]
            ], 404);
        }

        // 检查库存
        if ($potion['stock'] < $quantity) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INSUFFICIENT_STOCK',
                    'message' => '药品库存不足'
                ]
            ], 400);
        }

        // 检查购买上限
        if ($quantity > $potion['max_purchase']) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'EXCEEDS_MAX_PURCHASE',
                    'message' => '超过最大购买数量'
                ]
            ], 400);
        }

        // 计算总价
        $totalCost = $potion['cost'] * $quantity;
        $currency = $potion['currency'];

        // 检查角色是否有足够的银两
        if ($character->silver < $totalCost) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INSUFFICIENT_SILVER',
                    'message' => '银两不足'
                ]
            ], 403);
        }

        // 获取角色当前状态
        $stats = $character->stats;
        if (!is_array($stats)) {
            $stats = json_decode($stats, true);
        }

        // 计算恢复量
        $maxHp = $stats['hp'] ?? 100;
        $maxMp = $stats['mp'] ?? 50;
        $currentHp = $stats['current_hp'] ?? $maxHp;
        $currentMp = $stats['current_mp'] ?? $maxMp;

        $hpRecoveryPerPotion = floor($maxHp * ($potion['hp_recovery_percent'] / 100));
        $mpRecoveryPerPotion = floor($maxMp * ($potion['mp_recovery_percent'] / 100));

        $totalHpRecovery = $hpRecoveryPerPotion * $quantity;
        $totalMpRecovery = $mpRecoveryPerPotion * $quantity;

        $newHp = min($maxHp, $currentHp + $totalHpRecovery);
        $newMp = min($maxMp, $currentMp + $totalMpRecovery);

        // 更新角色状态
        $stats['current_hp'] = $newHp;
        $stats['current_mp'] = $newMp;
        $character->stats = $stats;

        // 扣除费用
        $character->silver -= $totalCost;
        $character->save();

        // 返回结果
        return response()->json([
            'success' => true,
            'hp_recovered' => $newHp - $currentHp,
            'mp_recovered' => $newMp - $currentMp,
            'current_hp' => $newHp,
            'current_mp' => $newMp,
            'max_hp' => $maxHp,
            'max_mp' => $maxMp,
            'silver' => $character->silver,
            'gold' => $character->gold,
            'message' => '购买成功'
        ]);
    }

    /**
     * 使用全员治疗服务
     */
    public function useTeamService(Request $request, Character $character)
    {
        // 验证请求
        $validator = Validator::make($request->all(), [
            'service_id' => 'required|integer|min:304|max:304',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_PARAMS',
                    'message' => $validator->errors()->first()
                ]
            ], 422);
        }

        $serviceId = $request->service_id;
        $service = $this->getTeamServiceById($serviceId);

        if (!$service) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVICE_NOT_FOUND',
                    'message' => '服务不存在'
                ]
            ], 404);
        }

        // 检查角色是否有足够的银两
        $cost = $service['cost'];

        if ($character->silver < $cost) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INSUFFICIENT_SILVER',
                    'message' => '银两不足'
                ]
            ], 403);
        }

        // 获取队伍成员
        // 实际项目中应该从数据库获取队伍成员，这里简化处理
        $teamMembers = [$character]; // 至少包含当前角色

        // 检查队伍人数是否超过上限
        if (count($teamMembers) > $service['max_team_size']) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'TEAM_TOO_LARGE',
                    'message' => '队伍人数超过服务上限'
                ]
            ], 400);
        }

        // 处理每个队伍成员
        $results = [];
        foreach ($teamMembers as $member) {
            // 获取成员当前状态
            $stats = $member->stats;
            if (!is_array($stats)) {
                $stats = json_decode($stats, true);
            }

            // 计算恢复量
            $maxHp = $stats['hp'] ?? 100;
            $maxMp = $stats['mp'] ?? 50;
            $currentHp = $stats['current_hp'] ?? $maxHp;
            $currentMp = $stats['current_mp'] ?? $maxMp;

            $hpRecovery = floor($maxHp * ($service['hp_recovery_percent'] / 100));
            $mpRecovery = floor($maxMp * ($service['mp_recovery_percent'] / 100));

            $newHp = min($maxHp, $currentHp + $hpRecovery);
            $newMp = min($maxMp, $currentMp + $mpRecovery);

            // 更新成员状态
            $stats['current_hp'] = $newHp;
            $stats['current_mp'] = $newMp;

            // 如果有解除中毒功能，移除中毒状态
            if (isset($service['remove_poison']) && $service['remove_poison']) {
                if (isset($stats['status_effects'])) {
                    $stats['status_effects'] = array_filter($stats['status_effects'], function($effect) {
                        return $effect['type'] !== 'poison';
                    });
                }
            }

            // 保存成员状态
            $member->stats = $stats;
            $member->save();

            // 记录结果
            $results[] = [
                'character_id' => $member->id,
                'name' => $member->name,
                'hp_recovered' => $newHp - $currentHp,
                'mp_recovered' => $newMp - $currentMp,
                'current_hp' => $newHp,
                'current_mp' => $newMp,
                'max_hp' => $maxHp,
                'max_mp' => $maxMp
            ];
        }

        // 扣除费用（只从队长扣除）
        $character->silver -= $cost;
        $character->save();

        // 返回结果
        return response()->json([
            'success' => true,
            'data' => [
                'service' => $service,
                'team_results' => $results,
                'team_size' => count($teamMembers),
                'gold' => $character->gold,
                'silver' => $character->silver
            ],
            'message' => '完全恢复成功'
        ]);
    }

    /**
     * 根据ID获取药品信息
     */
    private function getPotionById($potionId)
    {
        // 获取所有药品
        $healthPotions = $this->getHealthPotions(new Request())->original['data'];
        $manaPotions = $this->getManaPotions(new Request())->original['data'];

        // 合并所有药品
        $allPotions = array_merge($healthPotions, $manaPotions);

        // 查找匹配的药品
        foreach ($allPotions as $potion) {
            if ($potion['id'] == $potionId) {
                return $potion;
            }
        }

        return null;
    }

    /**
     * 根据ID获取团队服务信息
     */
    private function getTeamServiceById($serviceId)
    {
        // 获取所有团队服务
        $teamServices = $this->getTeamHealingServices(new Request())->original['data'];

        // 查找匹配的服务
        foreach ($teamServices as $service) {
            if ($service['id'] == $serviceId) {
                return $service;
            }
        }

        return null;
    }
}
