/**
 * 地图状态管理模块
 */
import mapService from '../../api/services/mapService.js';
import logger from '../../utils/logger.js';

const state = {
    // 当前地图数据
    currentMap: null,
    
    // 当前位置信息
    currentLocation: {
        id: null,
        name: '',
        type: '',
        description: '',
        coordinates: { x: 0, y: 0 },
        npcs: [],
        monsters: [],
        facilities: []
    },
    
    // 可移动的位置列表
    availableLocations: [],
    
    // 世界地图数据
    worldMap: null,
    
    // 移动历史记录
    movementHistory: [],
    
    // 加载状态
    loading: {
        map: false,
        locations: false,
        moving: false
    },
    
    // 错误信息
    error: null
};

const mutations = {
    /**
     * 设置当前地图数据
     */
    SET_CURRENT_MAP(state, mapData) {
        state.currentMap = mapData;
        logger.debug('[Map Store] 设置当前地图数据:', mapData);
    },
    
    /**
     * 设置当前位置信息
     */
    SET_CURRENT_LOCATION(state, location) {
        state.currentLocation = {
            ...state.currentLocation,
            ...location
        };
        logger.debug('[Map Store] 设置当前位置:', state.currentLocation);
    },
    
    /**
     * 设置可移动位置列表
     */
    SET_AVAILABLE_LOCATIONS(state, locations) {
        state.availableLocations = locations || [];
        logger.debug('[Map Store] 设置可移动位置:', state.availableLocations);
    },
    
    /**
     * 设置世界地图数据
     */
    SET_WORLD_MAP(state, worldMap) {
        state.worldMap = worldMap;
        logger.debug('[Map Store] 设置世界地图数据:', worldMap);
    },
    
    /**
     * 添加移动历史记录
     */
    ADD_MOVEMENT_HISTORY(state, movement) {
        state.movementHistory.unshift({
            ...movement,
            timestamp: Date.now()
        });
        
        // 只保留最近20条记录
        if (state.movementHistory.length > 20) {
            state.movementHistory = state.movementHistory.slice(0, 20);
        }
        
        logger.debug('[Map Store] 添加移动历史:', movement);
    },
    
    /**
     * 设置加载状态
     */
    SET_LOADING(state, { type, loading }) {
        if (Object.prototype.hasOwnProperty.call(state.loading, type)) {
            state.loading[type] = loading;
        }
    },
    
    /**
     * 设置错误信息
     */
    SET_ERROR(state, error) {
        state.error = error;
        if (error) {
            logger.error('[Map Store] 设置错误:', error);
        }
    },
    
    /**
     * 清除错误信息
     */
    CLEAR_ERROR(state) {
        state.error = null;
    },
    
    /**
     * 重置地图状态
     */
    RESET_MAP_STATE(state) {
        state.currentMap = null;
        state.currentLocation = {
            id: null,
            name: '',
            type: '',
            description: '',
            coordinates: { x: 0, y: 0 },
            npcs: [],
            monsters: [],
            facilities: []
        };
        state.availableLocations = [];
        state.movementHistory = [];
        state.error = null;
        logger.debug('[Map Store] 重置地图状态');
    }
};

const actions = {
    /**
     * 加载当前地图数据
     */
    async loadCurrentMap({ commit, rootGetters }) {
        try {
            commit('SET_LOADING', { type: 'map', loading: true });
            commit('CLEAR_ERROR');
            
            const currentCharacter = rootGetters['character/currentCharacter'];
            if (!currentCharacter || !currentCharacter.id) {
                throw new Error('未找到当前角色信息');
            }
            
            const mapData = await mapService.getCurrentMap(currentCharacter.id);
            commit('SET_CURRENT_MAP', mapData);
            
            // 如果地图数据包含当前位置信息，也设置当前位置
            if (mapData.current_location) {
                commit('SET_CURRENT_LOCATION', mapData.current_location);
            }
            
            return mapData;
        } catch (error) {
            commit('SET_ERROR', error.message || '加载地图数据失败');
            throw error;
        } finally {
            commit('SET_LOADING', { type: 'map', loading: false });
        }
    },
    
    /**
     * 加载可移动位置列表
     */
    async loadAvailableLocations({ commit, state, rootGetters }) {
        try {
            commit('SET_LOADING', { type: 'locations', loading: true });
            commit('CLEAR_ERROR');
            
            const currentCharacter = rootGetters['character/currentCharacter'];
            if (!currentCharacter || !currentCharacter.id) {
                throw new Error('未找到当前角色信息');
            }
            
            const currentLocationId = state.currentLocation.id;
            if (!currentLocationId) {
                throw new Error('未找到当前位置信息');
            }
            
            const locations = await mapService.getAvailableLocations(currentCharacter.id, currentLocationId);
            commit('SET_AVAILABLE_LOCATIONS', locations);
            
            return locations;
        } catch (error) {
            commit('SET_ERROR', error.message || '加载可移动位置失败');
            throw error;
        } finally {
            commit('SET_LOADING', { type: 'locations', loading: false });
        }
    },
    
    /**
     * 移动到指定位置
     */
    async moveToLocation({ commit, state, rootGetters, dispatch }, targetLocationId) {
        try {
            commit('SET_LOADING', { type: 'moving', loading: true });
            commit('CLEAR_ERROR');
            
            const currentCharacter = rootGetters['character/currentCharacter'];
            if (!currentCharacter || !currentCharacter.id) {
                throw new Error('未找到当前角色信息');
            }
            
            const fromLocation = state.currentLocation.id;
            
            // 执行移动
            const moveResult = await mapService.moveToLocation(currentCharacter.id, targetLocationId);
            
            // 记录移动历史
            commit('ADD_MOVEMENT_HISTORY', {
                from: fromLocation,
                to: targetLocationId,
                cost: moveResult.cost || {},
                success: true
            });
            
            // 更新当前位置
            if (moveResult.new_location) {
                commit('SET_CURRENT_LOCATION', moveResult.new_location);
            }
            
            // 重新加载可移动位置
            await dispatch('loadAvailableLocations');
            
            // 如果角色状态发生变化，更新角色信息
            if (moveResult.character_updates) {
                commit('character/SET_CHARACTER_STATUS', moveResult.character_updates, { root: true });
            }
            
            return moveResult;
        } catch (error) {
            // 记录失败的移动历史
            commit('ADD_MOVEMENT_HISTORY', {
                from: state.currentLocation.id,
                to: targetLocationId,
                error: error.message,
                success: false
            });
            
            commit('SET_ERROR', error.message || '移动失败');
            throw error;
        } finally {
            commit('SET_LOADING', { type: 'moving', loading: false });
        }
    },
    
    /**
     * 加载位置详情
     */
    async loadLocationDetails({ commit }, locationId) {
        try {
            const locationDetails = await mapService.getLocationDetails(locationId);
            return locationDetails;
        } catch (error) {
            commit('SET_ERROR', error.message || '加载位置详情失败');
            throw error;
        }
    },
    
    /**
     * 加载世界地图
     */
    async loadWorldMap({ commit }) {
        try {
            commit('SET_LOADING', { type: 'map', loading: true });
            commit('CLEAR_ERROR');
            
            const worldMap = await mapService.getWorldMap();
            commit('SET_WORLD_MAP', worldMap);
            
            return worldMap;
        } catch (error) {
            commit('SET_ERROR', error.message || '加载世界地图失败');
            throw error;
        } finally {
            commit('SET_LOADING', { type: 'map', loading: false });
        }
    },
    
    /**
     * 获取移动消耗
     */
    async getMovementCost({ rootGetters }, { fromLocation, toLocation }) {
        try {
            const currentCharacter = rootGetters['character/currentCharacter'];
            if (!currentCharacter || !currentCharacter.id) {
                throw new Error('未找到当前角色信息');
            }
            
            const cost = await mapService.getMovementCost(currentCharacter.id, fromLocation, toLocation);
            return cost;
        } catch (error) {
            logger.error('[Map Store] 获取移动消耗失败:', error);
            throw error;
        }
    },
    
    /**
     * 初始化地图数据
     */
    async initializeMap({ dispatch }) {
        try {
            // 加载当前地图数据
            await dispatch('loadCurrentMap');
            
            // 加载可移动位置
            await dispatch('loadAvailableLocations');
            
            logger.info('[Map Store] 地图数据初始化完成');
        } catch (error) {
            logger.error('[Map Store] 地图数据初始化失败:', error);
            throw error;
        }
    }
};

const getters = {
    // 当前位置是否有NPC
    hasNpcs: state => state.currentLocation.npcs && state.currentLocation.npcs.length > 0,
    
    // 当前位置是否有怪物
    hasMonsters: state => state.currentLocation.monsters && state.currentLocation.monsters.length > 0,
    
    // 当前位置是否有设施
    hasFacilities: state => state.currentLocation.facilities && state.currentLocation.facilities.length > 0,
    
    // 是否可以移动
    canMove: state => !state.loading.moving && state.availableLocations.length > 0,
    
    // 获取指定类型的可移动位置
    getLocationsByType: state => type => {
        return state.availableLocations.filter(location => location.type === type);
    },
    
    // 最近的移动记录
    recentMovements: state => state.movementHistory.slice(0, 5)
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
};
