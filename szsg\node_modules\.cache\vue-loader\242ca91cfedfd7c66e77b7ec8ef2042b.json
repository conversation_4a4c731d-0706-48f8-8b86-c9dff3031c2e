{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue?vue&type=template&id=0012e22c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue", "mtime": 1750381212656}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}