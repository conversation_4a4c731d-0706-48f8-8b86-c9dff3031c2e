<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            // 检查并添加新字段
            if (!Schema::hasColumn('regions', 'type')) {
                $table->string('type')->default('city')->after('description');
            }

            if (!Schema::hasColumn('regions', 'level_range_min')) {
                $table->integer('level_range_min')->default(1)->after('type');
            }

            if (!Schema::hasColumn('regions', 'level_range_max')) {
                $table->integer('level_range_max')->default(100)->after('level_range_min');
            }

            if (!Schema::hasColumn('regions', 'danger_level')) {
                $table->integer('danger_level')->default(1)->after('level_range_max');
            }

            if (!Schema::hasColumn('regions', 'is_pvp')) {
                $table->boolean('is_pvp')->default(false)->after('danger_level');
            }

            if (!Schema::hasColumn('regions', 'weather_enabled')) {
                $table->boolean('weather_enabled')->default(true)->after('is_pvp');
            }

            if (!Schema::hasColumn('regions', 'background_image')) {
                $table->string('background_image')->nullable()->after('weather_enabled');
            }

            if (!Schema::hasColumn('regions', 'map_image')) {
                $table->string('map_image')->nullable()->after('background_image');
            }

            // 如果存在旧字段，重命名或删除
            if (Schema::hasColumn('regions', 'status')) {
                $table->dropColumn('status');
            }

            if (Schema::hasColumn('regions', 'player_count')) {
                $table->dropColumn('player_count');
            }

            if (Schema::hasColumn('regions', 'max_players')) {
                $table->dropColumn('max_players');
            }

            if (Schema::hasColumn('regions', 'exp_rate')) {
                $table->dropColumn('exp_rate');
            }

            if (Schema::hasColumn('regions', 'drop_rate')) {
                $table->dropColumn('drop_rate');
            }

            if (Schema::hasColumn('regions', 'is_new')) {
                $table->dropColumn('is_new');
            }

            if (Schema::hasColumn('regions', 'is_recommended')) {
                $table->dropColumn('is_recommended');
            }

            if (Schema::hasColumn('regions', 'open_time')) {
                $table->dropColumn('open_time');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            // 删除新字段
            $table->dropColumn([
                'type',
                'level_range_min',
                'level_range_max',
                'danger_level',
                'is_pvp',
                'weather_enabled',
                'background_image',
                'map_image'
            ]);

            // 恢复旧字段
            $table->string('status')->default('offline')->after('description');
            $table->integer('player_count')->default(0)->after('status');
            $table->integer('max_players')->default(1000)->after('player_count');
            $table->float('exp_rate')->default(1.0)->after('max_players');
            $table->float('drop_rate')->default(1.0)->after('exp_rate');
            $table->boolean('is_new')->default(false)->after('drop_rate');
            $table->boolean('is_recommended')->default(false)->after('is_new');
            $table->timestamp('open_time')->nullable()->after('is_recommended');
        });
    }
};
