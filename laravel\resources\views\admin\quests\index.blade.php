@extends('admin.layouts.app')

@section('title', '任务管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        任务列表
        <a href="{{ route('admin.quests.create') }}" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加任务</a>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>等级要求</th>
                    <th>经验奖励</th>
                    <th>银两奖励</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($quests as $quest)
                <tr>
                    <td>{{ $quest->id }}</td>
                    <td>{{ $quest->name }}</td>
                    <td>{{ $quest->level_requirement }}</td>
                    <td>{{ $quest->exp_reward }}</td>
                    <td>{{ $quest->silver_reward }}</td>
                    <td>
                        @if($quest->is_main_quest)
                        <span class="layui-badge layui-bg-blue">主线</span>
                        @else
                        <span class="layui-badge layui-bg-orange">支线</span>
                        @endif

                        @if($quest->is_repeatable)
                        <span class="layui-badge layui-bg-green">可重复</span>
                        @endif
                    </td>
                    <td>
                        @if($quest->is_active ?? false)
                        <span class="layui-badge layui-bg-green">启用</span>
                        @else
                        <span class="layui-badge">禁用</span>
                        @endif
                    </td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.quests.show', $quest->id) }}" class="layui-btn layui-btn-xs layui-btn-primary">查看</a>
                            <a href="{{ route('admin.quests.edit', $quest->id) }}" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteQuest({{ $quest->id }}, '{{ $quest->name }}')">删除</button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="layui-center">暂无任务数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $quests->links('admin.layouts.pagination') }}
    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@section('scripts')
<script>
function deleteQuest(id, name) {
    layer.confirm('确定要删除任务 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "{{ route('admin.quests.destroy', '') }}/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
