{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Items.vue?vue&type=style&index=0&id=d8f0c030&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Items.vue", "mtime": 1749719688754}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LmZ1bmN0aW9uLXBhZ2UgeyBmb250LXNpemU6IDIycHg7IHRleHQtYWxpZ246IGNlbnRlcjsgbWFyZ2luLXRvcDogNDBweDsgfQ=="}, {"version": 3, "sources": ["Items.vue"], "names": [], "mappings": "AASA", "file": "Items.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"function-page\">物品页面</div>\r\n  </GameLayout>\r\n</template>\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nexport default { name: 'Items', components: { GameLayout } };\r\n</script>\r\n<style scoped>.function-page { font-size: 22px; text-align: center; margin-top: 40px; }</style> "]}]}