@extends('admin.layouts.app')

@section('title', '添加用户')

@section('breadcrumb')
<a href="{{ route('admin.users.index') }}">用户列表</a>
<a><cite>添加用户</cite></a>
@endsection

@section('page-title', '添加用户')

@section('content')
<div class="layui-card">
  <div class="layui-card-body">
    <form class="layui-form" method="POST" action="{{ route('admin.users.store') }}">
      @csrf

      <div class="layui-form-item">
        <label class="layui-form-label">用户名</label>
        <div class="layui-input-block">
          <input type="text" name="username" value="{{ old('username') }}" lay-verify="required" placeholder="请输入用户名" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">邮箱</label>
        <div class="layui-input-block">
          <input type="email" name="email" value="{{ old('email') }}" lay-verify="required|email" placeholder="请输入邮箱" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">密码</label>
        <div class="layui-input-block">
          <input type="password" name="password" lay-verify="required|pwd" placeholder="请输入密码，至少6个字符" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">确认密码</label>
        <div class="layui-input-block">
          <input type="password" name="password_confirmation" lay-verify="required|pwd" placeholder="请再次输入密码" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block">
          <input type="radio" name="status" value="1" title="正常" checked>
          <input type="radio" name="status" value="0" title="禁用">
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-block">
          <button class="layui-btn" lay-submit lay-filter="formUser">立即提交</button>
          <button type="reset" class="layui-btn layui-btn-primary">重置</button>
          <a href="{{ route('admin.users.index') }}" class="layui-btn layui-btn-primary">返回</a>
        </div>
      </div>
    </form>
  </div>
</div>
@endsection

@section('scripts')
<script>
  layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    // 自定义验证规则
    form.verify({
      pwd: [
        /^.{6,}$/,
        '密码必须6位以上'
      ]
    });

    // 提交表单
    form.on('submit(formUser)', function(data){
      // 默认提交表单
      return true;
    });
  });
</script>
@endsection
