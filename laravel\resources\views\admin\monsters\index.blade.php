@extends('admin.layouts.app')

@section('title', '怪物管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        怪物列表
        <a href="{{ route('admin.monsters.create') }}" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加怪物</a>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>等级</th>
                    <th>生命值</th>
                    <th>攻击力</th>
                    <th>防御力</th>
                    <th>速度</th>
                    <th>经验奖励</th>
                    <th>银两奖励</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($monsters as $monster)
                <tr>
                    <td>{{ $monster->id }}</td>
                    <td>{{ $monster->name }}</td>
                    <td>{{ $monster->level }}</td>
                    <td>{{ $monster->hp ?? 0 }}</td>
                    <td>{{ $monster->attack ?? 0 }}</td>
                    <td>{{ $monster->defense ?? 0 }}</td>
                    <td>{{ $monster->speed ?? 0 }}</td>
                    <td>{{ $monster->exp_reward ?? 0 }}</td>
                    <td>{{ $monster->silver_reward ?? 0 }}</td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.monsters.edit', $monster->id) }}" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteMonster({{ $monster->id }}, '{{ $monster->name }}')">删除</button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="10" class="layui-center">暂无怪物数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $monsters->links('admin.layouts.pagination') }}
    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@section('scripts')
<script>
function deleteMonster(id, name) {
    layer.confirm('确定要删除怪物 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "{{ route('admin.monsters.destroy', '') }}/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
