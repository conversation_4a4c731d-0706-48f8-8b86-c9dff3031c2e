<!DOCTYPE html>
<html lang="en-GB">
    <head>
        <meta charset="utf-8" />
        <title>loglevel Demo</title>
        <meta
            name="description"
            content="A demo to show the features of loglevel."
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="stylesheet" href="styles.css" />
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,600,0,0"
        />
    </head>
    <main>
        <h1>loglevel Demo</h1>
        <form id="LogForm" class="code-container">
            <code>
                log.
                <select name="logLevel" aria-label="Log type" required>
                    <option value="trace">trace</option>
                    <option value="debug">debug</option>
                    <option value="info">info</option>
                    <option value="warn">warn</option>
                    <option value="error">error</option>
                </select>
                ("
                <input
                    name="debugMessage"
                    type="text"
                    placeholder="Log text"
                    aria-label="Log text"
                    required
                />
                ")
            </code>
            <button type="submit">Run</button>
            <details>
                <summary>More information...</summary>
                Choose your level of logging and enter some text to output it to the console using logLevel.
                <a href="https://github.com/pimterry/loglevel#logging-methods">Documentation for logging methods.</a>
            </details>
        </form>
        <form id="SetLevel" class="code-container">
            <code>
                log.setLevel("
                <select name="level" aria-label="Log type" required>
                    <option value="0">trace</option>
                    <option value="1">debug</option>
                    <option value="2">info</option>
                    <option value="3">warn</option>
                    <option value="4">error</option>
                    <option value="5">silent</option>
                </select>
                ",
                <select name="persist" aria-label="Log type" required>
                    <option value="true">true</option>
                    <option value="false">false</option>
                </select>
                )
            </code>
            <button type="submit">Run</button>
            <details>
                <summary>More information...</summary>
                Disable all logging below the given level.
                <a href="https://github.com/pimterry/loglevel#logsetlevellevel-persist">Documentation for setLevel().</a>
            </details>
        </form>
        <form id="SetDefaultLevel" class="code-container">
            <code>
                log.setDefaultLevel("
                <select name="level" aria-label="Log type" required>
                    <option value="0">trace</option>
                    <option value="1">debug</option>
                    <option value="2">info</option>
                    <option value="3">warn</option>
                    <option value="4">error</option>
                    <option value="5">silent</option>
                </select>
                ")
            </code>
            <button type="submit">Run</button>
            <details>
                <summary>More information...</summary>
                Select a level and run to set the default logging level.
                <a href="https://github.com/pimterry/loglevel#logsetdefaultlevellevel">Documentation for setDefaultLevel().</a>
            </details>
        </form>
        <div class="code-container">
            <code>
                log.resetLevel()
            </code>
            <button id="ResetLevelButton" type="button">Run</button>
            <details>
                <summary>More information...</summary>
                Reset the current logging level to default.
                <a href="https://github.com/pimterry/loglevel#logresetlevel">Documentation for resetLevel().</a>
            </details>
        </div>
        <div class="code-container">
            <code>
                log.enableAll()
            </code>
            <button id="EnableAllButton" type="button">Run</button>
            <details>
                <summary>More information...</summary>
                Enables all logs - equivalent of <code>setLevel('trace')</code>.
                <a href="https://github.com/pimterry/loglevel##logenableall-and-logdisableall">Documentation for enableAll().</a>
            </details>
        </div>
        <div class="code-container">
            <code>
                log.disableAll()
            </code>
            <button id="DisableAllButton" type="button">Run</button>
            <details>
                <summary>More information...</summary>
                Disables all logs - equivalent of <code>setLevel('silent')</code>.
                <a href="https://github.com/pimterry/loglevel##logenableall-and-logdisableall">Documentation for disableAll().</a>
            </details>
        </div>
        <h2>Log State</h2>
        <div id="LogState" class="code-container">
            <label>
                Current log level
                <input name="currentLevel" aria-label="Current log level" readonly>
            </label>
            <details>
                <summary>More information...</summary>
                Uses the <code>getLevel()</code> method to display the current log level.
                <a href="https://github.com/pimterry/loglevel##logenableall-and-logdisableall">Documentation for disableAll().</a>
            </details>
        </div>
    </main>
    <script src="../dist/loglevel.js"></script>
    <script src="script.js"></script>
</html>
