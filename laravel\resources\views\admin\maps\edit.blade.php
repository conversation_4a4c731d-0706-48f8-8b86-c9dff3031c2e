@extends('admin.layouts.app')

@section('title', '编辑地图')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <a href="{{ route('admin.maps.index') }}" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        编辑地图 - {{ $map->name }}
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">基本信息</li>
                <li>位置管理</li>
                <li>NPC管理</li>
                <li>地图预览</li>
            </ul>
            <div class="layui-tab-content">
                <!-- 基本信息 -->
                <div class="layui-tab-item layui-show">
                    <form class="layui-form" method="POST" action="{{ route('admin.maps.update', $map->id) }}">
                        @csrf
                        @method('PUT')

                        <div class="layui-form-item">
                            <label class="layui-form-label">名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" value="{{ $map->name }}" required lay-verify="required" placeholder="请输入地图名称" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">代码</label>
                            <div class="layui-input-block">
                                <input type="text" name="code" value="{{ $map->code }}" required lay-verify="required" placeholder="请输入地图代码" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">描述</label>
                            <div class="layui-input-block">
                                <textarea name="description" placeholder="请输入地图描述" class="layui-textarea">{{ $map->description }}</textarea>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">地图类型</label>
                            <div class="layui-input-block">
                                <select name="type" lay-verify="required">
                                    <option value="city" {{ $map->type == 'city' ? 'selected' : '' }}>城市</option>
                                    <option value="wilderness" {{ $map->type == 'wilderness' ? 'selected' : '' }}>野外</option>
                                    <option value="dungeon" {{ $map->type == 'dungeon' ? 'selected' : '' }}>副本</option>
                                    <option value="instance" {{ $map->type == 'instance' ? 'selected' : '' }}>实例</option>
                                    <option value="special" {{ $map->type == 'special' ? 'selected' : '' }}>特殊</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">等级范围</label>
                            <div class="layui-input-inline" style="width: 80px;">
                                <input type="number" name="level_range_min" value="{{ $map->level_range_min }}" required lay-verify="required|number" placeholder="最低" class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 80px;">
                                <input type="number" name="level_range_max" value="{{ $map->level_range_max }}" required lay-verify="required|number" placeholder="最高" class="layui-input">
                            </div>
                        </div>

                        <input type="hidden" name="danger_level" value="1">

                        <div class="layui-form-item">
                            <label class="layui-form-label">排序权重</label>
                            <div class="layui-input-inline">
                                <input type="number" name="sort_order" value="{{ $map->sort_order }}" required lay-verify="required|number" placeholder="请输入排序权重" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-text-em">数值越大排序越靠前</div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="checkbox" name="is_pvp" value="1" title="允许PVP" {{ $map->is_pvp ? 'checked' : '' }}>
                            </div>
                        </div>

                        <input type="hidden" name="weather_enabled" value="0">

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="checkbox" name="is_active" value="1" title="启用" {{ $map->is_active ? 'checked' : '' }}>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit>保存</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 位置管理 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            位置列表
                            <button class="layui-btn layui-btn-xs layui-btn-normal" id="addLocationBtn" style="float: right;">添加位置</button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>坐标</th>
                                        <th>等级要求</th>
                                        <th>安全区</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($locations as $location)
                                    <tr>
                                        <td>{{ $location->id }}</td>
                                        <td>{{ $location->name }}</td>
                                        <td>{{ $location->type }}</td>
                                        <td>({{ $location->x }}, {{ $location->y }})</td>
                                        <td>{{ $location->level_requirement }}</td>
                                        <td>
                                            @if($location->is_safe)
                                            <span class="layui-badge layui-bg-green">安全</span>
                                            @else
                                            <span class="layui-badge layui-bg-red">危险</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="layui-btn-group">
                                                <button class="layui-btn layui-btn-xs" onclick="editLocation({{ $location->id }})">编辑</button>
                                                <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteLocation({{ $location->id }}, '{{ $location->name }}')">删除</button>
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="7" class="layui-center">暂无位置数据</td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- NPC管理 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            NPC列表
                            <button class="layui-btn layui-btn-xs layui-btn-normal" id="addNpcBtn" style="float: right;">添加NPC</button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>位置</th>
                                        <th>等级</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="npcList">
                                    <tr>
                                        <td colspan="7" class="layui-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 地图预览 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-header">地图预览</div>
                        <div class="layui-card-body">
                            <div id="mapContainer" style="width: 100%; height: 600px; background-color: #f0f0f0; position: relative;">
                                <!-- 地图将在这里显示 -->
                                @if($map->map_image)
                                <img src="{{ asset('storage/' . $map->map_image) }}" alt="地图" style="max-width: 100%; max-height: 100%;">
                                @else
                                <div class="layui-center" style="padding-top: 250px;">
                                    <p>暂无地图图片</p>
                                    <button type="button" class="layui-btn" id="uploadMapBtn">
                                        <i class="layui-icon">&#xe67c;</i>上传地图图片
                                    </button>
                                </div>
                                @endif

                                <!-- 地图上的位置标记 -->
                                @foreach($locations as $location)
                                <div class="map-marker" data-id="{{ $location->id }}" style="position: absolute; left: {{ $location->x }}px; top: {{ $location->y }}px; width: 10px; height: 10px; background-color: {{ $location->is_safe ? 'green' : 'red' }}; border-radius: 50%; cursor: pointer;" title="{{ $location->name }}"></div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form', 'element', 'layer', 'jquery', 'upload'], function(){
    var form = layui.form;
    var element = layui.element;
    var layer = layui.layer;
    var $ = layui.jquery;
    var upload = layui.upload;

    // 加载NPC列表
    function loadNpcs() {
        $.ajax({
            url: '{{ route("admin.api.map.characters", $map->id) }}',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if(res.success) {
                    var html = '';
                    if(res.data.length > 0) {
                        $.each(res.data, function(i, item) {
                            if(item.is_npc) {
                                html += '<tr>';
                                html += '<td>' + item.id + '</td>';
                                html += '<td>' + item.name + '</td>';
                                html += '<td>' + item.npc_type + '</td>';
                                html += '<td>' + item.location_name + '</td>';
                                html += '<td>' + item.level + '</td>';
                                html += '<td>' + (item.status == 'active' ? '<span class="layui-badge layui-bg-green">活跃</span>' : '<span class="layui-badge">非活跃</span>') + '</td>';
                                html += '<td><a href="/admin/npcs/' + item.id + '" class="layui-btn layui-btn-xs">查看</a></td>';
                                html += '</tr>';
                            }
                        });
                    }
                    if(html === '') {
                        html = '<tr><td colspan="7" class="layui-center">暂无NPC数据</td></tr>';
                    }
                    $('#npcList').html(html);
                } else {
                    layer.msg('获取NPC列表失败：' + res.message);
                }
            },
            error: function() {
                layer.msg('获取NPC列表失败，请稍后再试');
            }
        });
    }

    // 添加位置
    $('#addLocationBtn').on('click', function() {
        layer.open({
            type: 2,
            title: '添加位置',
            area: ['800px', '600px'],
            content: '/admin/maps/{{ $map->id }}/locations/create'
        });
    });

    // 添加NPC
    $('#addNpcBtn').on('click', function() {
        layer.open({
            type: 2,
            title: '添加NPC',
            area: ['800px', '600px'],
            content: '/admin/npcs/create?region_id={{ $map->id }}'
        });
    });

    // 上传地图图片
    upload.render({
        elem: '#uploadMapBtn',
        url: '{{ route("admin.maps.upload", $map->id) }}',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res){
            if(res.code == 0){
                layer.msg('上传成功');
                setTimeout(function(){
                    window.location.reload();
                }, 1000);
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 地图标记点击事件
    $('.map-marker').on('click', function() {
        var locationId = $(this).data('id');
        editLocation(locationId);
    });

    // 初始化
    loadNpcs();
});

// 编辑位置
function editLocation(id) {
    layer.open({
        type: 2,
        title: '编辑位置',
        area: ['800px', '600px'],
        content: '/admin/locations/' + id + '/edit'
    });
}

// 删除位置
function deleteLocation(id, name) {
    layer.confirm('确定要删除位置 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        $.ajax({
            url: '/admin/locations/' + id,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(res) {
                if(res.success) {
                    layer.msg('删除成功');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    layer.msg('删除失败：' + res.message);
                }
            },
            error: function() {
                layer.msg('删除失败，请稍后再试');
            }
        });
    });
}
</script>
@endsection
