/**
 * 钱庄系统API服务
 * 提供钱庄相关的接口调用
 */
import { get, post } from '../request.js';
import logger from '../../utils/logger.js';
import { getCache, setCache, CACHE_TYPE } from './cacheService.js';
import { API_BASE_URL } from '../config.js';

// 缓存键
const CACHE_KEYS = {
    ACCOUNT_INFO: 'bank_account_info',
    TRANSACTIONS: 'bank_transactions'
};

/**
 * 钱庄服务
 */
const bankService = {
    /**
     * 获取银行账户信息
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 银行账户信息
     */
    getAccountInfo(characterId) {
        logger.debug('[BankService] 获取银行账户信息, characterId:', characterId);
        
        // 清除缓存，确保获取最新数据
        setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);
        
        const apiUrl = `/bank/account/${characterId}`;
        logger.debug('[BankService] 请求 URL:', API_BASE_URL + apiUrl);
        
        return get(apiUrl, {}, {
            loading: true,
            loadingText: '获取账户信息...'
        }).then(res => {
            logger.debug('[BankService] 银行账户信息获取成功:', res);
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, res.data, 60 * 1000); // 缓存1分钟
            return res.data;
        }).catch(error => {
            logger.error('[BankService] 获取银行账户信息失败:', error);
            logger.error('[BankService] 错误详情:', JSON.stringify(error, null, 2));
            if (error.response) {
                logger.error('[BankService] 响应状态:', error.response.status);
                logger.error('[BankService] 响应数据:', JSON.stringify(error.response.data, null, 2));
            }
            throw error;
        });
    },

    /**
     * 存款
     * @param {string} characterId - 角色ID
     * @param {string} currency - 货币类型 (silver/gold_ingot)
     * @param {number} amount - 存款金额
     * @returns {Promise<Object>} - 存款结果
     */
    deposit(characterId, currency, amount) {
        logger.debug('[BankService] 存款操作, characterId:', characterId, 'currency:', currency, 'amount:', amount);
        
        return post(`/bank/deposit/${characterId}`, {
            currency: currency,
            amount: amount
        }, {
            loading: true,
            loadingText: '正在存款...'
        }).then(res => {
            logger.debug('[BankService] 存款成功:', res);
            
            // 清除缓存，确保下次获取最新数据
            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);
            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);
            
            return {
                success: true,
                message: res.data.message || '存款成功',
                account: res.data.account,
                character: res.data.character
            };
        }).catch(error => {
            logger.error('[BankService] 存款失败:', error);
            throw {
                success: false,
                message: error.response?.data?.error?.message || '存款失败，请重试'
            };
        });
    },

    /**
     * 取款
     * @param {string} characterId - 角色ID
     * @param {string} currency - 货币类型 (silver/gold_ingot)
     * @param {number} amount - 取款金额
     * @returns {Promise<Object>} - 取款结果
     */
    withdraw(characterId, currency, amount) {
        logger.debug('[BankService] 取款操作, characterId:', characterId, 'currency:', currency, 'amount:', amount);
        
        return post(`/bank/withdraw/${characterId}`, {
            currency: currency,
            amount: amount
        }, {
            loading: true,
            loadingText: '正在取款...'
        }).then(res => {
            logger.debug('[BankService] 取款成功:', res);
            
            // 清除缓存，确保下次获取最新数据
            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);
            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);
            
            return {
                success: true,
                message: res.data.message || '取款成功',
                account: res.data.account,
                character: res.data.character
            };
        }).catch(error => {
            logger.error('[BankService] 取款失败:', error);
            throw {
                success: false,
                message: error.response?.data?.error?.message || '取款失败，请重试'
            };
        });
    },

    /**
     * 获取交易历史记录
     * @param {string} characterId - 角色ID
     * @param {Object} params - 查询参数
     * @param {number} [params.page=1] - 页码
     * @param {number} [params.per_page=10] - 每页条数
     * @param {string} [params.currency] - 货币类型筛选 (silver/gold_ingot)
     * @param {string} [params.type] - 交易类型筛选 (deposit/withdraw)
     * @returns {Promise<Object>} - 交易历史记录
     */
    getTransactionHistory(characterId, params = {}) {
        logger.debug('[BankService] 获取交易历史记录, characterId:', characterId, 'params:', params);
        
        // 清除缓存，确保获取最新数据
        setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);
        
        return get(`/bank/transactions/${characterId}`, params, {
            loading: true,
            loadingText: '获取交易记录...'
        }).then(res => {
            logger.debug('[BankService] 交易历史记录获取成功:', res);
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, res.data, 60 * 1000); // 缓存1分钟
            return res.data;
        }).catch(error => {
            logger.error('[BankService] 获取交易历史记录失败:', error);
            throw error;
        });
    }
};

export default bankService; 