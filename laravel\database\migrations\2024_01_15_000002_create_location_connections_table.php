<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('location_connections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_location_id')->constrained('locations')->onDelete('cascade');
            $table->foreignId('to_location_id')->constrained('locations')->onDelete('cascade');
            $table->integer('distance')->default(1)->comment('距离');
            $table->integer('time_cost')->default(5)->comment('时间消耗（分钟）');
            $table->integer('silver_cost')->default(0)->comment('银两消耗');
            $table->integer('level_requirement')->default(1)->comment('等级要求');
            $table->boolean('is_active')->default(true)->comment('是否可用');
            $table->timestamps();
            
            $table->unique(['from_location_id', 'to_location_id']);
            $table->index(['from_location_id']);
            $table->index(['to_location_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('location_connections');
    }
};
