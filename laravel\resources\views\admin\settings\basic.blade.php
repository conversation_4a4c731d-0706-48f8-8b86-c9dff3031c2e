@extends('admin.layouts.app')

@section('title', '系统设置')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">基本设置</div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" action="{{ route('admin.settings.basic.save') }}" method="POST">
            @csrf
            <div class="layui-form-item">
                <label class="layui-form-label">网站名称</label>
                <div class="layui-input-block">
                    <input type="text" name="site_name" value="{{ $settings['site_name'] ?? '神之西游' }}" class="layui-input" required>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">网站描述</label>
                <div class="layui-input-block">
                    <textarea name="site_description" class="layui-textarea">{{ $settings['site_description'] ?? '神之西游游戏管理系统' }}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">维护模式</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="maintenance_mode" value="1" lay-skin="switch" lay-text="开启|关闭" {{ isset($settings['maintenance_mode']) && $settings['maintenance_mode'] ? 'checked' : '' }}>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn">保存设置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 表单事件
    form.on('submit(settingsForm)', function(data){
        return true;
    });
});
</script>
@endsection
