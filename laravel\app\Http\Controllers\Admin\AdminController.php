<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AdminController extends Controller
{
    /**
     * 显示管理员列表
     */
    public function index()
    {
        $admins = Admin::all();
        return view('admin.administrators.index', compact('admins'));
    }

    /**
     * 显示创建管理员表单
     */
    public function create()
    {
        return view('admin.administrators.create');
    }

    /**
     * 存储新管理员
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:admins',
            'password' => 'required|string|min:8',
            'role' => 'required|string|in:admin,super_admin',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // 创建权限数组
        $permissions = [];
        $permissionGroups = $request->input('permissions', []);

        foreach ($permissionGroups as $group => $actions) {
            $permissions[$group] = array_keys($actions);
        }

        // 创建管理员
        Admin::create([
            'name' => $request->name,
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'permissions' => json_encode($permissions),
        ]);

        return redirect()->route('admin.administrators.index')
            ->with('success', '管理员创建成功');
    }

    /**
     * 显示管理员编辑表单
     */
    public function edit(Admin $administrator)
    {
        return view('admin.administrators.edit', compact('administrator'));
    }

    /**
     * 更新管理员信息
     */
    public function update(Request $request, Admin $administrator)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:admins,username,' . $administrator->id,
            'password' => 'nullable|string|min:8',
            'role' => 'required|string|in:admin,super_admin',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // 创建权限数组
        $permissions = [];
        $permissionGroups = $request->input('permissions', []);

        foreach ($permissionGroups as $group => $actions) {
            $permissions[$group] = array_keys($actions);
        }

        // 更新管理员
        $data = [
            'name' => $request->name,
            'username' => $request->username,
            'role' => $request->role,
            'permissions' => json_encode($permissions),
        ];

        // 如果提供了新密码，则更新密码
        if (!empty($request->password)) {
            $data['password'] = Hash::make($request->password);
        }

        $administrator->update($data);

        return redirect()->route('admin.administrators.index')
            ->with('success', '管理员信息更新成功');
    }

    /**
     * 删除管理员
     */
    public function destroy(Admin $administrator)
    {
        // 确保不能删除当前登录的管理员
        if ($administrator->id === auth('admin')->id()) {
            return redirect()->back()->with('error', '无法删除当前登录的管理员账号');
        }

        $administrator->delete();

        return redirect()->route('admin.administrators.index')
            ->with('success', '管理员已删除');
    }
}
