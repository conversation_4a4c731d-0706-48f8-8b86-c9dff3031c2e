<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CharacterController;
use App\Http\Controllers\API\ItemController;
use App\Http\Controllers\WebSocketController;
use App\Http\Controllers\API\QuestController;
use App\Http\Controllers\API\BattleController;
use App\Http\Controllers\API\SkillController;
use App\Http\Controllers\API\SocialController;
use App\Http\Controllers\API\ChatController;
use App\Http\Controllers\API\TeamController;
use App\Http\Controllers\API\RegionController;
use App\Http\Controllers\API\VipController;
use App\Http\Controllers\API\ClinicController;
use App\Http\Controllers\API\BankController;
use App\Http\Controllers\MapController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 认证路由
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);

    // 需要认证的路由
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
    });
});

// WebSocket路由
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/websocket/token', [WebSocketController::class, 'getToken']);
});

// 大区API（公开访问）
Route::prefix('regions')->group(function () {
    Route::get('/', [RegionController::class, 'index']);
    Route::get('/recommended', [RegionController::class, 'recommended']);
    Route::get('/{region}', [RegionController::class, 'show']);
    Route::post('/{region}/select', [RegionController::class, 'select']);
});

// 需要认证的API路由
Route::middleware('auth:sanctum')->group(function () {
    // 角色API
    Route::prefix('characters')->group(function () {
        Route::get('/', [CharacterController::class, 'index']);
        Route::post('/', [CharacterController::class, 'store']);
        Route::get('/{character}', [CharacterController::class, 'show']);

        // 角色状态
        Route::get('/{character}/status', [CharacterController::class, 'getStatus']);
        Route::put('/{character}/attributes', [CharacterController::class, 'updateAttributes']);
        Route::post('/{character}/attributes/reset', [CharacterController::class, 'resetAttributes']);
        Route::get('/{character}/experience', [CharacterController::class, 'getExperience']);
        Route::post('/{character}/experience', [CharacterController::class, 'addExperience']);

        // 角色物品/背包
        Route::get('/{character}/bag', [ItemController::class, 'getBag']);
        Route::post('/{character}/bag/items/{item}/use', [ItemController::class, 'useItem']);
        Route::post('/{character}/bag/items/{item}/equip', [ItemController::class, 'equipItem']);
        Route::post('/{character}/bag/items/{item}/unequip', [ItemController::class, 'unequipItem']);
        Route::delete('/{character}/bag/items/{item}', [ItemController::class, 'discardItem']);

        // 角色技能
        Route::get('/{character}/skills', [SkillController::class, 'getSkills']);
        Route::post('/{character}/skills/{skill}/level-up', [SkillController::class, 'levelUpSkill']);
        Route::post('/{character}/skills/{skill}/set-shortcut', [SkillController::class, 'setSkillShortcut']);

        // 角色装备
        Route::get('/{character}/equipment', [ItemController::class, 'getEquipment']);
        Route::post('/{character}/equipment/{item}/equip', [ItemController::class, 'equipItem']);
        Route::post('/{character}/equipment/{item}/unequip', [ItemController::class, 'unequipItem']);

        // 地图相关路由
        Route::get('/{character}/map', [MapController::class, 'getCurrentMap']);
        Route::get('/{character}/map/locations', [MapController::class, 'getAvailableLocations']);
        Route::post('/{character}/map/move', [MapController::class, 'moveToLocation']);
        Route::get('/{character}/map/movement-cost', [MapController::class, 'getMovementCost']);
        Route::get('/{character}/map/locations/{locationId}/entities', [MapController::class, 'getLocationEntities']);

        // 角色任务
        Route::get('/{character}/quests', [QuestController::class, 'getCharacterQuests']);
        Route::post('/{character}/quests/{quest}/accept', [QuestController::class, 'acceptQuest']);
        Route::post('/{character}/quests/{quest}/complete', [QuestController::class, 'completeQuest']);
        Route::post('/{character}/quests/{quest}/abandon', [QuestController::class, 'abandonQuest']);
        Route::post('/{character}/quests/{quest}/update-progress', [QuestController::class, 'updateQuestProgress']);

        // 角色战斗
        Route::post('/{character}/battles/pve/{enemy}', [BattleController::class, 'startPveBattle']);
        Route::post('/{character}/battles/pvp/{opponent}', [BattleController::class, 'startPvpBattle']);
        Route::post('/{character}/battles/{battle}/action', [BattleController::class, 'executeBattleAction']);
        Route::get('/{character}/battles/{battle}', [BattleController::class, 'getBattleStatus']);
        Route::post('/{character}/battles/{battle}/flee', [BattleController::class, 'fleeBattle']);

        // 社交功能
        Route::get('/{character}/friends', [SocialController::class, 'getFriends']);
        Route::post('/{character}/friends/{target}', [SocialController::class, 'addFriend']);
        Route::delete('/{character}/friends/{target}', [SocialController::class, 'removeFriend']);

        // 组队功能
        Route::post('/{character}/team/create', [TeamController::class, 'createTeam']);
        Route::get('/{character}/team/current', [TeamController::class, 'getCurrentTeam']);
        Route::post('/{character}/team/join', [TeamController::class, 'joinTeam']);
        Route::post('/{character}/team/leave', [TeamController::class, 'leaveTeam']);
        Route::post('/{character}/team/kick', [TeamController::class, 'kickMember']);
        Route::post('/{character}/team/disband', [TeamController::class, 'disbandTeam']);
        Route::post('/{character}/team/invite', [TeamController::class, 'inviteToTeam']);
        Route::get('/{character}/team/invites', [TeamController::class, 'getTeamInvites']);
        Route::post('/{character}/team/invite-response', [TeamController::class, 'respondToInvite']);
    });

    // 队伍API
    Route::prefix('teams')->group(function () {
        Route::get('/', [TeamController::class, 'getTeamList']);
        Route::get('/{team}', [TeamController::class, 'getTeamDetail']);
    });

    // 聊天API
    Route::prefix('chat')->group(function () {
        Route::get('/channels', [ChatController::class, 'getChannels']);
        Route::get('/channels/{channel}/messages', [ChatController::class, 'getMessages']);
        Route::post('/channels/{channel}/messages', [ChatController::class, 'sendMessage']);
        Route::post('/private/{target}', [ChatController::class, 'sendPrivateMessage']);
    });

    // 任务API
    Route::prefix('quests')->group(function () {
        Route::get('/', [QuestController::class, 'getAvailableQuests']);
        Route::get('/{quest}', [QuestController::class, 'getQuestDetails']);
    });

    // 商店API
    Route::prefix('shop')->group(function () {
        Route::get('/items', [ItemController::class, 'getShopItems']);
        Route::post('/purchase/{item}', [ItemController::class, 'purchaseItem']);
        Route::post('/sell/{characterItem}', [ItemController::class, 'sellItem']);
    });

    // 战斗相关的API路由
    Route::prefix('battle')->group(function () {
        Route::get('/enemies', [BattleController::class, 'getEnemies']);
        Route::post('/start', [BattleController::class, 'startBattle']);
        Route::post('/{battleId}/action', [BattleController::class, 'performAction']);
        Route::get('/{battleId}/status', [BattleController::class, 'getBattleState']);
        Route::get('/{battleId}/log', [BattleController::class, 'getBattleLog']);
        Route::post('/end', [BattleController::class, 'endBattle']);
        Route::post('/flee', [BattleController::class, 'flee']);
    });

    // 市场相关的API路由
    Route::prefix('market')->group(function () {
        Route::get('/items', [ItemController::class, 'getMarketItems']);
        Route::post('/purchase/{item}', [ItemController::class, 'purchaseItem']);
    });

    // VIP相关的API路由
    Route::prefix('vip')->group(function () {
        Route::get('/info', [VipController::class, 'getInfo']);
        Route::get('/levels', [VipController::class, 'getLevels']);
        Route::post('/claim-reward', [VipController::class, 'claimReward']);
        Route::post('/claim-history-reward', [VipController::class, 'claimHistoryReward']);
        Route::post('/daily-reward', [VipController::class, 'claimDailyReward']);
        Route::post('/add-exp', [VipController::class, 'addExp']);
    });

    // 医馆相关的API路由
    Route::prefix('clinic')->group(function () {
        Route::get('/service-types', [ClinicController::class, 'getServiceTypes']);
        Route::get('/health-potions', [ClinicController::class, 'getHealthPotions']);
        Route::get('/mana-potions', [ClinicController::class, 'getManaPotions']);
        Route::get('/team-services', [ClinicController::class, 'getTeamHealingServices']);
        Route::post('/purchase-potion/{character}', [ClinicController::class, 'purchasePotion']);
        Route::post('/team-healing/{character}', [ClinicController::class, 'useTeamService']);
    });

    // 钱庄相关的API路由
    Route::prefix('bank')->group(function () {
        Route::get('/account/{character}', [BankController::class, 'getAccountInfo']);
        Route::post('/deposit/{character}', [BankController::class, 'deposit']);
        Route::post('/withdraw/{character}', [BankController::class, 'withdraw']);
        Route::get('/transactions/{character}', [BankController::class, 'getTransactionHistory']);
    });

    // 地图相关的API路由
    Route::prefix('map')->group(function () {
        Route::get('/world', [MapController::class, 'getWorldMap']);
        Route::get('/locations/{location}', [MapController::class, 'getLocationDetails']);
    });

    // 测试API
    Route::get('/test/character/{character}', function($characterId) {
        try {
            $character = \App\Models\Character::find($characterId);
            if (!$character) {
                return response()->json(['error' => 'Character not found']);
            }

            return response()->json([
                'character' => $character->toArray(),
                'table_columns' => \Schema::getColumnListing('characters')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'table_exists' => \Schema::hasTable('characters'),
                'columns' => \Schema::hasTable('characters') ? \Schema::getColumnListing('characters') : []
            ]);
        }
    });
});
