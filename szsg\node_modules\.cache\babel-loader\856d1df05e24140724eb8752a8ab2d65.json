{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue", "mtime": 1749699239704}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GameLayout", "mapState", "mapActions", "regionService", "logger", "name", "components", "data", "<PERSON><PERSON><PERSON><PERSON>", "currentRegion", "isBackPressed", "isCreatePressed", "isEnterPressed", "computed", "characters", "$store", "getters", "id", "created", "debug", "getCurrentRegion", "showToast", "$router", "push", "loadCharacterList", "methods", "loadCharacters", "length", "info", "error", "message", "selectCharacterLocal", "character", "confirmSelection", "success", "selectCharacter", "setTimeout", "createNewCharacter", "goBack", "handleBackMouseDown", "handleBackMouseUp", "handleCreateMouseDown", "handleCreateMouseUp", "handleEnterMouseDown", "handleEnterMouseUp", "getBackButtonImage", "getCreateButtonImage", "getEnterButtonImage", "getClassName", "profession", "classMap", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "toast", "document", "createElement", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/setup/CharacterSelect.vue"], "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"character-select-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <p class=\"page-subtitle\">请选择您要使用的角色，或创建新角色</p>\n        <div v-if=\"currentRegion\" class=\"current-region\">\n          当前大区: <span class=\"region-name\">{{ currentRegion.name }}</span>\n        </div>\n\n        <!-- 调试信息 -->\n        <div v-if=\"$route.query.debug\" class=\"debug-info\">\n          <h4>调试信息:</h4>\n          <p>加载状态: {{ isLoading }}</p>\n          <p>错误信息: {{ error || '无' }}</p>\n          <p>角色数量: {{ characters.length }}</p>\n          <p>当前大区ID: {{ currentRegion?.id }}</p>\n          <details>\n            <summary>角色数据</summary>\n            <pre>{{ JSON.stringify(characters, null, 2) }}</pre>\n          </details>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-spinner\"></div>\n        <p class=\"loading-text\">正在加载角色列表...</p>\n      </div>\n\n      <!-- 错误提示 -->\n      <div v-if=\"error && !isLoading\" class=\"error-container\">\n        <div class=\"error-message\">\n          <i class=\"error-icon\">⚠️</i>\n          <p>{{ error }}</p>\n          <button @click=\"loadCharacterList\" class=\"retry-btn\">重试</button>\n        </div>\n      </div>\n\n      <!-- 角色列表 -->\n      <div v-if=\"!isLoading && !error\" class=\"characters-container\">\n        <!-- 空状态提示 -->\n        <div v-if=\"characters.length === 0\" class=\"empty-state\">\n          <div class=\"empty-icon\">👤</div>\n          <h3>还没有角色</h3>\n          <p>在当前大区 \"{{ currentRegion?.name }}\" 中还没有创建角色</p>\n        </div>\n\n        <!-- 角色卡片网格 -->\n        <div v-else class=\"characters-grid\">\n          <!-- 现有角色 -->\n          <div \n            v-for=\"character in characters\" \n            :key=\"character.id\"\n            class=\"character-card\"\n            :class=\"{ 'selected': selectedCharacter?.id === character.id }\"\n            @click=\"selectCharacterLocal(character)\"\n          >\n            <div class=\"character-avatar\">\n              <img :src=\"character.avatar || '/static/game/avatars/default.png'\" :alt=\"character.name\" />\n              <div class=\"character-level\">Lv.{{ character.level }}</div>\n            </div>\n            <div class=\"character-info\">\n              <h3 class=\"character-name\">{{ character.name }}</h3>\n              <p class=\"character-class\">{{ getClassName(character.profession || character.class) }}</p>\n              <div class=\"character-stats\">\n                <span class=\"stat\">经验: {{ character.experience || character.exp || 0 }}</span>\n                <span class=\"stat\">创建时间: {{ formatDate(character.created_at || character.createdAt) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 创建新角色按钮 -->\n          <div \n            v-if=\"characters.length < 3\"\n            class=\"character-card create-new\"\n            @click=\"createNewCharacter\"\n          >\n            <div class=\"create-icon\">\n              <i class=\"plus-icon\">+</i>\n            </div>\n            <div class=\"create-text\">\n              <h3>创建新角色</h3>\n              <p>还可以创建 {{ 3 - characters.length }} 个角色</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <!-- 返回大区选择图片按钮 -->\n          <div\n            @click=\"goBack\"\n            @mousedown=\"handleBackMouseDown\"\n            @mouseup=\"handleBackMouseUp\"\n            @mouseleave=\"handleBackMouseUp\"\n            class=\"return-btn\"\n            :class=\"{ 'pressed': isBackPressed }\"\n          >\n            <img\n              :src=\"getBackButtonImage()\"\n              alt=\"返回大区选择\"\n              class=\"return-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n\n          <!-- 如果没有角色，显示创建角色图片按钮 -->\n          <div\n            v-if=\"characters.length === 0\"\n            @click=\"createNewCharacter\"\n            @mousedown=\"handleCreateMouseDown\"\n            @mouseup=\"handleCreateMouseUp\"\n            @mouseleave=\"handleCreateMouseUp\"\n            class=\"create-character-btn\"\n            :class=\"{ 'pressed': isCreatePressed }\"\n          >\n            <img\n              :src=\"getCreateButtonImage()\"\n              alt=\"创建第一个角色\"\n              class=\"create-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n\n          <!-- 如果有角色，显示进入游戏图片按钮 -->\n          <div\n            v-else\n            @click=\"confirmSelection\"\n            @mousedown=\"handleEnterMouseDown\"\n            @mouseup=\"handleEnterMouseUp\"\n            @mouseleave=\"handleEnterMouseUp\"\n            class=\"enter-game-btn\"\n            :class=\"{ 'disabled': !selectedCharacter, 'pressed': isEnterPressed }\"\n          >\n            <img\n              :src=\"getEnterButtonImage()\"\n              alt=\"进入游戏\"\n              class=\"enter-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapState, mapActions } from 'vuex'\nimport { regionService } from '@/api'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'CharacterSelect',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      selectedCharacter: null,\n      currentRegion: null,\n      // 按钮状态\n      isBackPressed: false,\n      isCreatePressed: false,\n      isEnterPressed: false\n    }\n  },\n  computed: {\n    ...mapState('character', ['isLoading', 'error']),\n\n    // 获取当前大区的角色列表\n    characters() {\n      if (!this.currentRegion) return [];\n      return this.$store.getters['character/charactersByRegion'](this.currentRegion.id);\n    }\n  },\n  async created() {\n    logger.debug('[CharacterSelect] 页面初始化')\n\n    // 获取当前选择的大区\n    this.currentRegion = regionService.getCurrentRegion()\n    logger.debug('[CharacterSelect] 当前大区:', this.currentRegion)\n\n    if (!this.currentRegion) {\n      this.showToast('请先选择大区')\n      this.$router.push('/setup/region-select')\n      return\n    }\n\n    await this.loadCharacterList()\n  },\n  methods: {\n    ...mapActions('character', ['loadCharacters', 'selectCharacter']),\n\n    async loadCharacterList() {\n      if (!this.currentRegion) return\n\n      try {\n        logger.debug('[CharacterSelect] 开始加载角色列表，大区ID:', this.currentRegion.id)\n        await this.loadCharacters(this.currentRegion.id)\n        logger.debug('[CharacterSelect] 角色列表加载完成，角色数量:', this.characters.length)\n\n        // 如果没有角色，显示提示\n        if (this.characters.length === 0) {\n          logger.info('[CharacterSelect] 当前大区没有角色')\n        }\n      } catch (error) {\n        logger.error('[CharacterSelect] 加载角色列表失败:', error)\n        this.showToast('加载角色列表失败: ' + (error.message || '未知错误'))\n      }\n    },\n\n    selectCharacterLocal(character) {\n      this.selectedCharacter = character\n      logger.debug('[CharacterSelect] 选择角色:', character.name)\n    },\n\n    async confirmSelection() {\n      if (!this.selectedCharacter) {\n        this.showToast('请先选择一个角色')\n        return\n      }\n\n      try {\n        const success = await this.selectCharacter(this.selectedCharacter)\n        if (success) {\n          this.showToast('角色选择成功，正在进入游戏...')\n          // 跳转到游戏主界面\n          setTimeout(() => {\n            this.$router.push('/game/main')\n          }, 1000)\n        } else {\n          this.showToast('选择角色失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[CharacterSelect] 确认选择失败:', error)\n        this.showToast('选择角色失败，请重试')\n      }\n    },\n\n    createNewCharacter() {\n      this.$router.push('/setup/create-character')\n    },\n\n    goBack() {\n      this.$router.push('/setup/region-select')\n    },\n\n    // 按钮状态处理方法\n    handleBackMouseDown() {\n      this.isBackPressed = true\n    },\n\n    handleBackMouseUp() {\n      this.isBackPressed = false\n    },\n\n    handleCreateMouseDown() {\n      this.isCreatePressed = true\n    },\n\n    handleCreateMouseUp() {\n      this.isCreatePressed = false\n    },\n\n    handleEnterMouseDown() {\n      if (!this.selectedCharacter) return\n      this.isEnterPressed = true\n    },\n\n    handleEnterMouseUp() {\n      this.isEnterPressed = false\n    },\n\n    // 按钮图片获取方法\n    getBackButtonImage() {\n      return this.isBackPressed\n        ? '/static/game/UI/anniu/fhui_.png'\n        : '/static/game/UI/anniu/fhui_2.png'\n    },\n\n    getCreateButtonImage() {\n      return this.isCreatePressed\n        ? '/static/game/UI/anniu/cj_1.png'\n        : '/static/game/UI/anniu/cj.png'\n    },\n\n    getEnterButtonImage() {\n      if (!this.selectedCharacter) {\n        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片\n      }\n      return this.isEnterPressed\n        ? '/static/game/UI/anniu/jr_4.png'\n        : '/static/game/UI/anniu/jr_3.png'\n    },\n\n    getClassName(profession) {\n      const classMap = {\n        'warrior': '武士',\n        'scholar': '文人',\n        'mystic': '异人'\n      }\n      return classMap[profession] || profession\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '未知'\n      const date = new Date(dateString)\n      return date.toLocaleDateString('zh-CN')\n    },\n\n    showToast(message) {\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.character-select-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.page-title {\n  font-size: 32px;\n  color: #d4af37;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.page-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  margin: 0 0 10px 0;\n}\n\n.current-region {\n  font-size: 14px;\n  color: #999;\n}\n\n.region-name {\n  color: #d4af37;\n  font-weight: bold;\n}\n\n.debug-info {\n  margin-top: 20px;\n  padding: 15px;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid #444;\n  border-radius: 5px;\n  font-size: 12px;\n  text-align: left;\n}\n\n.debug-info h4 {\n  color: #d4af37;\n  margin: 0 0 10px 0;\n}\n\n.debug-info p {\n  margin: 5px 0;\n  color: #ccc;\n}\n\n.debug-info pre {\n  background: rgba(0, 0, 0, 0.5);\n  padding: 10px;\n  border-radius: 3px;\n  overflow-x: auto;\n  font-size: 11px;\n  color: #fff;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #333;\n  border-top: 4px solid #d4af37;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  color: #ccc;\n  font-size: 16px;\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.error-message {\n  text-align: center;\n  color: #ff6b6b;\n  background: rgba(255, 107, 107, 0.1);\n  padding: 30px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.retry-btn {\n  margin-top: 15px;\n  padding: 8px 16px;\n  background: #d4af37;\n  color: #000;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.retry-btn:hover {\n  background: #b8941f;\n}\n\n.characters-container {\n  flex: 1;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #ccc;\n}\n\n.empty-icon {\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.5;\n}\n\n.empty-state h3 {\n  color: #d4af37;\n  margin: 0 0 10px 0;\n  font-size: 24px;\n}\n\n.empty-state p {\n  margin: 0 0 30px 0;\n  font-size: 16px;\n}\n\n.characters-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  margin-bottom: 30px;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.character-card {\n  background: linear-gradient(135deg, rgba(30, 30, 80, 0.9), rgba(50, 50, 120, 0.8));\n  border: 2px solid #4a5568;\n  border-radius: 8px;\n  padding: 15px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  min-height: 100px;\n  position: relative;\n  overflow: hidden;\n}\n\n.character-card:hover {\n  border-color: #d4af37;\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(255, 215, 0, 0.1));\n  transform: translateY(-2px);\n}\n\n.character-card.selected {\n  border-color: #d4af37;\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(255, 215, 0, 0.2));\n  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);\n  transform: translateY(-2px);\n}\n\n.character-card.selected::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);\n  pointer-events: none;\n}\n\n.character-card.create-new {\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  border-style: dashed;\n  border-color: #666;\n  background: linear-gradient(135deg, rgba(60, 60, 60, 0.3), rgba(80, 80, 80, 0.2));\n}\n\n.character-card.create-new:hover {\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(255, 215, 0, 0.05));\n  border-color: #d4af37;\n}\n\n.character-avatar {\n  position: relative;\n  margin-right: 20px;\n  flex-shrink: 0;\n}\n\n.character-avatar img {\n  width: 70px;\n  height: 70px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 3px solid #4a5568;\n}\n\n.character-level {\n  position: absolute;\n  bottom: -5px;\n  right: -5px;\n  background: linear-gradient(135deg, #d4af37, #ffd700);\n  color: #000;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n  border: 1px solid #b8941f;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.character-info {\n  text-align: center;\n}\n\n.character-name {\n  font-size: 18px;\n  color: #d4af37;\n  margin: 0 0 5px 0;\n}\n\n.character-class {\n  font-size: 14px;\n  color: #ccc;\n  margin: 0 0 10px 0;\n}\n\n.character-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.stat {\n  font-size: 12px;\n  color: #999;\n}\n\n.create-icon {\n  margin-bottom: 15px;\n}\n\n.plus-icon {\n  font-size: 48px;\n  color: #666;\n  font-style: normal;\n}\n\n.create-text h3 {\n  color: #d4af37;\n  margin: 0 0 8px 0;\n  font-size: 18px;\n}\n\n.create-text p {\n  color: #999;\n  margin: 0;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 30px;\n  padding: 20px 0;\n  flex-wrap: nowrap; /* 确保按钮不换行 */\n}\n\n\n\n/* 图片按钮通用样式 */\n.create-character-btn,\n.return-btn,\n.enter-game-btn {\n  cursor: pointer;\n  display: inline-block;\n  transition: all 0.2s ease;\n  flex-shrink: 0; /* 防止按钮被压缩 */\n}\n\n.enter-game-btn.disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n/* 创建角色图片按钮样式 */\n.create-character-btn.pressed .create-btn-img {\n  transform: scale(0.95);\n}\n\n.create-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 返回按钮图片样式 */\n.return-btn.pressed .return-btn-img {\n  transform: scale(0.95);\n}\n\n.return-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 进入游戏按钮图片样式 */\n.enter-game-btn.pressed .enter-btn-img {\n  transform: scale(0.95);\n}\n\n.enter-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .character-select-container {\n    padding: 15px;\n  }\n\n  .page-title {\n    font-size: 24px;\n  }\n\n  .characters-grid {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n\n  .action-buttons {\n    flex-direction: row;\n    flex-wrap: nowrap; /* 保持按钮在同一行 */\n    justify-content: center;\n    gap: 15px;\n    padding: 15px 0;\n  }\n\n  .create-character-btn,\n  .return-btn,\n  .enter-game-btn {\n    display: flex;\n    justify-content: center;\n    flex-shrink: 0; /* 防止按钮被压缩 */\n  }\n\n  .create-btn-img,\n  .return-btn-img,\n  .enter-btn-img {\n    max-width: 120px; /* 在移动设备上稍微缩小按钮 */\n  }\n\n  .empty-state {\n    padding: 40px 15px;\n  }\n\n  .empty-state h3 {\n    font-size: 20px;\n  }\n\n  .empty-state p {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": ";AAqJA,OAAAA,UAAA;AACA,SAAAC,QAAA,EAAAC,UAAA;AACA,SAAAC,aAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN;EACA;EACAO,KAAA;IACA;MACAC,iBAAA;MACAC,aAAA;MACA;MACAC,aAAA;MACAC,eAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAZ,QAAA;IAEA;IACAa,WAAA;MACA,UAAAL,aAAA;MACA,YAAAM,MAAA,CAAAC,OAAA,sCAAAP,aAAA,CAAAQ,EAAA;IACA;EACA;EACA,MAAAC,QAAA;IACAd,MAAA,CAAAe,KAAA;;IAEA;IACA,KAAAV,aAAA,GAAAN,aAAA,CAAAiB,gBAAA;IACAhB,MAAA,CAAAe,KAAA,iCAAAV,aAAA;IAEA,UAAAA,aAAA;MACA,KAAAY,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;MACA;IACA;IAEA,WAAAC,iBAAA;EACA;EACAC,OAAA;IACA,GAAAvB,UAAA;IAEA,MAAAsB,kBAAA;MACA,UAAAf,aAAA;MAEA;QACAL,MAAA,CAAAe,KAAA,0CAAAV,aAAA,CAAAQ,EAAA;QACA,WAAAS,cAAA,MAAAjB,aAAA,CAAAQ,EAAA;QACAb,MAAA,CAAAe,KAAA,0CAAAL,UAAA,CAAAa,MAAA;;QAEA;QACA,SAAAb,UAAA,CAAAa,MAAA;UACAvB,MAAA,CAAAwB,IAAA;QACA;MACA,SAAAC,KAAA;QACAzB,MAAA,CAAAyB,KAAA,gCAAAA,KAAA;QACA,KAAAR,SAAA,iBAAAQ,KAAA,CAAAC,OAAA;MACA;IACA;IAEAC,qBAAAC,SAAA;MACA,KAAAxB,iBAAA,GAAAwB,SAAA;MACA5B,MAAA,CAAAe,KAAA,4BAAAa,SAAA,CAAA3B,IAAA;IACA;IAEA,MAAA4B,iBAAA;MACA,UAAAzB,iBAAA;QACA,KAAAa,SAAA;QACA;MACA;MAEA;QACA,MAAAa,OAAA,cAAAC,eAAA,MAAA3B,iBAAA;QACA,IAAA0B,OAAA;UACA,KAAAb,SAAA;UACA;UACAe,UAAA;YACA,KAAAd,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAF,SAAA;QACA;MACA,SAAAQ,KAAA;QACAzB,MAAA,CAAAyB,KAAA,8BAAAA,KAAA;QACA,KAAAR,SAAA;MACA;IACA;IAEAgB,mBAAA;MACA,KAAAf,OAAA,CAAAC,IAAA;IACA;IAEAe,OAAA;MACA,KAAAhB,OAAA,CAAAC,IAAA;IACA;IAEA;IACAgB,oBAAA;MACA,KAAA7B,aAAA;IACA;IAEA8B,kBAAA;MACA,KAAA9B,aAAA;IACA;IAEA+B,sBAAA;MACA,KAAA9B,eAAA;IACA;IAEA+B,oBAAA;MACA,KAAA/B,eAAA;IACA;IAEAgC,qBAAA;MACA,UAAAnC,iBAAA;MACA,KAAAI,cAAA;IACA;IAEAgC,mBAAA;MACA,KAAAhC,cAAA;IACA;IAEA;IACAiC,mBAAA;MACA,YAAAnC,aAAA,GACA,oCACA;IACA;IAEAoC,qBAAA;MACA,YAAAnC,eAAA,GACA,mCACA;IACA;IAEAoC,oBAAA;MACA,UAAAvC,iBAAA;QACA;MACA;MACA,YAAAI,cAAA,GACA,mCACA;IACA;IAEAoC,aAAAC,UAAA;MACA,MAAAC,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,UAAA,KAAAA,UAAA;IACA;IAEAE,WAAAC,UAAA;MACA,KAAAA,UAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,UAAA;MACA,OAAAC,IAAA,CAAAE,kBAAA;IACA;IAEAlC,UAAAS,OAAA;MACA,MAAA0B,KAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,KAAA,CAAAG,WAAA,GAAA7B,OAAA;MACA0B,KAAA,CAAAI,KAAA,CAAAC,OAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACAJ,QAAA,CAAAK,IAAA,CAAAC,WAAA,CAAAP,KAAA;MACApB,UAAA;QACAqB,QAAA,CAAAK,IAAA,CAAAE,WAAA,CAAAR,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}