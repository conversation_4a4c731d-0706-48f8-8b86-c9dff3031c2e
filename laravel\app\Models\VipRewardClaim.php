<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VipRewardClaim extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'character_id',
        'vip_level',
        'claimed_at',
        'reward_description',
    ];

    /**
     * 需要进行类型转换的属性
     *
     * @var array
     */
    protected $casts = [
        'claimed_at' => 'datetime',
    ];

    /**
     * 获取关联的角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class);
    }
}
