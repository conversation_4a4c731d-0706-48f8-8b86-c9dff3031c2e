<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 检查characters表是否存在
        if (Schema::hasTable('characters')) {
            Schema::table('characters', function (Blueprint $table) {
                $table->integer('attribute_points')->default(4)->after('experience');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('characters') && Schema::hasColumn('characters', 'attribute_points')) {
            Schema::table('characters', function (Blueprint $table) {
                $table->dropColumn('attribute_points');
            });
        }
    }
};
