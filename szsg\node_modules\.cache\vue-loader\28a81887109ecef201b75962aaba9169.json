{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=template&id=f28434cc&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750348047473}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}