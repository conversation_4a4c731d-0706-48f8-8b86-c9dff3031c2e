{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=template&id=f28434cc&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750347661761}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}