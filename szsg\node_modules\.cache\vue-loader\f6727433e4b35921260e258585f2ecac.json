{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\RequestTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\RequestTest.vue", "mtime": 1749872688134}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RequestTest.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RequestTest.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"request-test\">\n    <h2>请求测试工具</h2>\n    <p>用于测试和调试HTTP 431错误</p>\n\n    <div class=\"test-section\">\n      <h3>存储状态</h3>\n      <div class=\"storage-info\">\n        <p>总存储大小: {{ formatBytes(storageSize) }}</p>\n        <p>存储项目数: {{ storageCount }}</p>\n        <p>最大项目: {{ largestItem.key }} ({{ formatBytes(largestItem.size) }})</p>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>请求测试</h3>\n      <div class=\"test-buttons\">\n        <button @click=\"testRegionRequest\" :disabled=\"isLoading\">\n          {{ isLoading ? '测试中...' : '测试大区API' }}\n        </button>\n        <button @click=\"emergencyClean\" :disabled=\"isLoading\">\n          紧急清理存储\n        </button>\n        <button @click=\"clearCookies\" :disabled=\"isLoading\">\n          清理Cookies\n        </button>\n        <button @click=\"testDirectRequest\" :disabled=\"isLoading\">\n          直接请求测试\n        </button>\n        <button @click=\"fix431AndTest\" :disabled=\"isLoading\">\n          修复431错误并测试\n        </button>\n        <button @click=\"refreshInfo\">\n          刷新信息\n        </button>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试结果</h3>\n      <div class=\"result-area\">\n        <pre>{{ testResult }}</pre>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>请求头信息</h3>\n      <div class=\"headers-info\">\n        <p>预估请求头大小: {{ estimatedHeaderSize }} bytes</p>\n        <div class=\"headers-list\">\n          <div v-for=\"(value, key) in estimatedHeaders\" :key=\"key\" class=\"header-item\">\n            <span class=\"header-key\">{{ key }}:</span>\n            <span class=\"header-value\">{{ value.substring(0, 100) }}{{ value.length > 100 ? '...' : '' }}</span>\n            <span class=\"header-size\">({{ key.length + value.length }} bytes)</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getRegions } from '@/api/services/regionService.js'\nimport { emergencyCleanupStorage, clearAllCookies } from '@/utils/storage.js'\nimport { fix431Error, check431Risk, minimalFetch } from '@/utils/fix431Error.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'RequestTest',\n  data() {\n    return {\n      isLoading: false,\n      testResult: '点击\"测试大区API\"开始测试',\n      storageSize: 0,\n      storageCount: 0,\n      largestItem: { key: '', size: 0 },\n      estimatedHeaders: {},\n      estimatedHeaderSize: 0\n    }\n  },\n  mounted() {\n    this.refreshInfo()\n  },\n  methods: {\n    async testRegionRequest() {\n      this.isLoading = true\n      this.testResult = '正在测试大区API请求...'\n      \n      try {\n        const startTime = Date.now()\n        const result = await getRegions()\n        const endTime = Date.now()\n        \n        this.testResult = `✅ 请求成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(result, null, 2)}`\n      } catch (error) {\n        this.testResult = `❌ 请求失败！\n错误代码: ${error.code}\n错误信息: ${error.message}\n需要清理: ${error.needCleanup ? '是' : '否'}\n详细信息: ${JSON.stringify(error, null, 2)}`\n        \n        logger.error('[RequestTest] 测试失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async emergencyClean() {\n      this.isLoading = true\n      this.testResult = '正在执行紧急清理...'\n\n      try {\n        const success = emergencyCleanupStorage()\n        if (success) {\n          this.testResult = '✅ 紧急清理完成！'\n        } else {\n          this.testResult = '❌ 紧急清理失败！'\n        }\n      } catch (error) {\n        this.testResult = `❌ 紧急清理出错: ${error.message}`\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async clearCookies() {\n      this.isLoading = true\n      this.testResult = '正在清理Cookies...'\n\n      try {\n        const success = clearAllCookies()\n        if (success) {\n          this.testResult = '✅ Cookies清理完成！'\n        } else {\n          this.testResult = '❌ Cookies清理失败！'\n        }\n      } catch (error) {\n        this.testResult = `❌ 清理Cookies出错: ${error.message}`\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async testDirectRequest() {\n      this.isLoading = true\n      this.testResult = '正在执行直接请求测试...'\n\n      try {\n        const startTime = Date.now()\n\n        // 使用最小化的fetch请求\n        const data = await minimalFetch('/api/regions')\n\n        const endTime = Date.now()\n\n        this.testResult = `✅ 直接请求成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(data, null, 2)}`\n      } catch (error) {\n        this.testResult = `❌ 直接请求出错: ${error.message}`\n        logger.error('[RequestTest] 直接请求失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async fix431AndTest() {\n      this.isLoading = true\n      this.testResult = '正在修复431错误并测试...'\n\n      try {\n        // 1. 检查风险\n        const riskCheck = check431Risk()\n        this.testResult += `\\n\\n风险检查结果:\\n${JSON.stringify(riskCheck, null, 2)}`\n\n        // 2. 执行修复\n        if (riskCheck.hasRisk) {\n          this.testResult += '\\n\\n检测到风险，正在修复...'\n          const fixResult = fix431Error()\n          this.testResult += `\\n修复结果: ${fixResult ? '成功' : '失败'}`\n        }\n\n        // 3. 测试请求\n        this.testResult += '\\n\\n正在测试大区API...'\n        const startTime = Date.now()\n\n        try {\n          const result = await getRegions()\n          const endTime = Date.now()\n\n          this.testResult += `\\n\\n✅ 大区API测试成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(result, null, 2)}`\n        } catch (apiError) {\n          this.testResult += `\\n\\n❌ 大区API测试失败: ${apiError.message}`\n\n          // 尝试最小化请求\n          try {\n            this.testResult += '\\n\\n尝试最小化请求...'\n            const minimalResult = await minimalFetch('/api/regions')\n            this.testResult += `\\n\\n✅ 最小化请求成功！\n结果: ${JSON.stringify(minimalResult, null, 2)}`\n          } catch (minimalError) {\n            this.testResult += `\\n\\n❌ 最小化请求也失败: ${minimalError.message}`\n          }\n        }\n\n      } catch (error) {\n        this.testResult += `\\n\\n❌ 修复过程出错: ${error.message}`\n        logger.error('[RequestTest] 431修复失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    refreshInfo() {\n      this.calculateStorageInfo()\n      this.calculateHeaderInfo()\n    },\n\n    calculateStorageInfo() {\n      let totalSize = 0\n      let count = 0\n      let largest = { key: '', size: 0 }\n\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        const value = localStorage.getItem(key) || ''\n        const size = key.length + value.length\n        \n        totalSize += size\n        count++\n        \n        if (size > largest.size) {\n          largest = { key, size }\n        }\n      }\n\n      this.storageSize = totalSize\n      this.storageCount = count\n      this.largestItem = largest\n    },\n\n    calculateHeaderInfo() {\n      // 模拟请求头\n      const headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'User-Agent': navigator.userAgent,\n        'Referer': window.location.href,\n        'Accept-Language': navigator.language,\n        'Accept-Encoding': 'gzip, deflate, br',\n        'Connection': 'keep-alive',\n        'Cache-Control': 'no-cache'\n      }\n\n      // 添加可能的认证头\n      try {\n        const token = this.$store?.state?.auth?.token\n        if (token && typeof token === 'string') {\n          headers['Authorization'] = `Bearer ${token}`\n        }\n      } catch (e) {\n        // 忽略错误\n      }\n\n      this.estimatedHeaders = headers\n      this.estimatedHeaderSize = Object.keys(headers).reduce((size, key) => {\n        return size + key.length + (headers[key] || '').length\n      }, 0)\n    },\n\n    formatBytes(bytes) {\n      if (bytes === 0) return '0 Bytes'\n      const k = 1024\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.request-test {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background: #f9f9f9;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.storage-info p {\n  margin: 5px 0;\n  font-family: monospace;\n}\n\n.test-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.test-buttons button {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  background: #007bff;\n  color: white;\n  cursor: pointer;\n}\n\n.test-buttons button:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.test-buttons button:hover:not(:disabled) {\n  background: #0056b3;\n}\n\n.result-area {\n  background: #000;\n  color: #0f0;\n  padding: 15px;\n  border-radius: 4px;\n  font-family: monospace;\n  font-size: 12px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.headers-info p {\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.headers-list {\n  max-height: 200px;\n  overflow-y: auto;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  padding: 10px;\n  background: white;\n}\n\n.header-item {\n  display: flex;\n  margin-bottom: 5px;\n  font-family: monospace;\n  font-size: 12px;\n}\n\n.header-key {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 150px;\n}\n\n.header-value {\n  flex: 1;\n  margin: 0 10px;\n  color: #333;\n}\n\n.header-size {\n  color: #666;\n  font-size: 10px;\n}\n</style>\n"]}]}