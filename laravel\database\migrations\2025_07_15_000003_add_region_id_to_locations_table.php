<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRegionIdToLocationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('locations', function (Blueprint $table) {
            // 检查表中是否已经存在region_id字段
            if (!Schema::hasColumn('locations', 'region_id')) {
                $table->unsignedBigInteger('region_id')->nullable()->after('id')->comment('所属区域ID');
                $table->foreign('region_id')->references('id')->on('regions')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('locations', function (Blueprint $table) {
            // 删除外键约束
            if (Schema::hasColumn('locations', 'region_id')) {
                $table->dropForeign(['region_id']);
                $table->dropColumn('region_id');
            }
        });
    }
}
