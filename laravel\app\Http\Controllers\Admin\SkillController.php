<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Skill;
use Illuminate\Support\Facades\Validator;

class SkillController extends Controller
{
    /**
     * 显示技能列表
     */
    public function index()
    {
        try {
            $skills = Skill::orderBy('required_level')
                ->orderBy('name')
                ->paginate(15);

            return view('admin.skills.index', compact('skills'));
        } catch (\Exception $e) {
            // 创建一个空的分页对象而不是简单的集合
            $skills = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.skills.index', compact('skills'));
        }
    }

    /**
     * 显示创建技能表单
     */
    public function create()
    {
        return view('admin.skills.create');
    }

    /**
     * 保存新技能
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:skills',
            'description' => 'required|string',
            'type' => 'required|string|in:active,passive,ultimate',
            'target_type' => 'required|string|in:self,single,aoe,buff,debuff',
            'mp_cost' => 'nullable|integer|min:0',
            'cooldown' => 'nullable|integer|min:0',
            'base_power' => 'nullable|integer|min:0',
            'element' => 'nullable|string',
            'required_level' => 'required|integer|min:1',
            'icon' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // 处理职业限制
            $professionRestrictions = [];
            if ($request->has('profession_restrictions')) {
                $professionRestrictions = array_keys(array_filter($request->profession_restrictions ?? []));
            }

            // 处理技能效果
            $effects = [];
            if ($request->has('effects') && is_array($request->effects)) {
                foreach ($request->effects as $effect) {
                    if (!empty($effect['type']) && isset($effect['value'])) {
                        $effects[] = $effect;
                    }
                }
            }

            Skill::create([
                'name' => $request->name,
                'description' => $request->description,
                'type' => $request->type,
                'target_type' => $request->target_type,
                'mp_cost' => $request->mp_cost ?? 0,
                'cooldown' => $request->cooldown ?? 0,
                'base_power' => $request->base_power ?? 0,
                'element' => $request->element,
                'effects' => !empty($effects) ? $effects : null,
                'profession_restrictions' => !empty($professionRestrictions) ? $professionRestrictions : null,
                'required_level' => $request->required_level,
                'icon' => $request->icon,
            ]);

            return redirect()->route('admin.skills.index')
                ->with('success', '技能创建成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '技能创建失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 显示技能详情
     */
    public function show($id)
    {
        try {
            $skill = Skill::findOrFail($id);
            return view('admin.skills.show', compact('skill'));
        } catch (\Exception $e) {
            return redirect()->route('admin.skills.index')
                ->with('error', '无法获取技能信息: ' . $e->getMessage());
        }
    }

    /**
     * 显示编辑技能表单
     */
    public function edit($id)
    {
        try {
            $skill = Skill::findOrFail($id);
            return view('admin.skills.edit', compact('skill'));
        } catch (\Exception $e) {
            return redirect()->route('admin.skills.index')
                ->with('error', '无法获取技能信息: ' . $e->getMessage());
        }
    }

    /**
     * 更新技能信息
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:skills,name,' . $id,
            'description' => 'required|string',
            'type' => 'required|string|in:active,passive,ultimate',
            'target_type' => 'required|string|in:self,single,aoe,buff,debuff',
            'mp_cost' => 'nullable|integer|min:0',
            'cooldown' => 'nullable|integer|min:0',
            'base_power' => 'nullable|integer|min:0',
            'element' => 'nullable|string',
            'required_level' => 'required|integer|min:1',
            'icon' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $skill = Skill::findOrFail($id);

            // 处理职业限制
            $professionRestrictions = [];
            if ($request->has('profession_restrictions')) {
                $professionRestrictions = array_keys(array_filter($request->profession_restrictions ?? []));
            }

            // 处理技能效果
            $effects = [];
            if ($request->has('effects') && is_array($request->effects)) {
                foreach ($request->effects as $effect) {
                    if (!empty($effect['type']) && isset($effect['value'])) {
                        $effects[] = $effect;
                    }
                }
            }

            $skill->update([
                'name' => $request->name,
                'description' => $request->description,
                'type' => $request->type,
                'target_type' => $request->target_type,
                'mp_cost' => $request->mp_cost ?? 0,
                'cooldown' => $request->cooldown ?? 0,
                'base_power' => $request->base_power ?? 0,
                'element' => $request->element,
                'effects' => !empty($effects) ? $effects : null,
                'profession_restrictions' => !empty($professionRestrictions) ? $professionRestrictions : null,
                'required_level' => $request->required_level,
                'icon' => $request->icon,
            ]);

            return redirect()->route('admin.skills.index')
                ->with('success', '技能更新成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '技能更新失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 删除技能
     */
    public function destroy($id)
    {
        try {
            $skill = Skill::findOrFail($id);

            // 检查是否有角色学习了此技能
            if ($skill->characters()->count() > 0) {
                return redirect()->route('admin.skills.index')
                    ->with('error', '无法删除技能，它正被角色使用');
            }

            $skill->delete();

            return redirect()->route('admin.skills.index')
                ->with('success', '技能已删除');
        } catch (\Exception $e) {
            return redirect()->route('admin.skills.index')
                ->with('error', '技能删除失败: ' . $e->getMessage());
        }
    }
}
