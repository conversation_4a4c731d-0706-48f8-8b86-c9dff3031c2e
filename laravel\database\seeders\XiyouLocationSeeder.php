<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;
use App\Models\LocationConnection;

class XiyouLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 禁用外键检查
        \DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // 清空现有数据
        LocationConnection::truncate();
        Location::truncate();

        // 重新启用外键检查
        \DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 创建西游记主题位置数据
        $locations = [
            [
                'name' => '东胜神洲',
                'type' => 'town',
                'description' => '四大部洲之一，繁华的仙界城镇，各路神仙聚集之地',
                'x' => 0,
                'y' => 0,
                'level_requirement' => 1,
                'is_safe' => true,
                'facilities' => ['bank', 'market', 'inn', 'clinic'],
                'npcs' => [
                    [
                        'id' => 'city_god',
                        'name' => '城隍爷',
                        'type' => 'npc',
                        'level' => 50,
                        'services' => ['信息', '任务']
                    ],
                    [
                        'id' => 'immortal_merchant',
                        'name' => '仙界商人',
                        'type' => 'npc',
                        'level' => 30,
                        'services' => ['交易', '法宝']
                    ]
                ],
                'monsters' => []
            ],
            [
                'name' => '花果山',
                'type' => 'mountain',
                'description' => '美猴王的故乡，山清水秀，仙桃满树，是修炼的好地方',
                'x' => 1,
                'y' => 1,
                'level_requirement' => 5,
                'is_safe' => false,
                'facilities' => ['temple'],
                'npcs' => [
                    [
                        'id' => 'monkey_elder',
                        'name' => '猴族长老',
                        'type' => 'npc',
                        'level' => 40,
                        'services' => ['指引', '武艺']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'spirit_monkey',
                        'name' => '灵猴',
                        'type' => 'monster',
                        'level' => 15,
                        'description' => '花果山的灵猴，机灵活泼，略通人性'
                    ],
                    [
                        'id' => 'mountain_demon',
                        'name' => '山魈',
                        'type' => 'monster',
                        'level' => 20,
                        'description' => '山中精怪，力大无穷'
                    ]
                ]
            ],
            [
                'name' => '水帘洞',
                'type' => 'cave',
                'description' => '花果山水帘洞，美猴王的洞府，洞中别有洞天',
                'x' => 2,
                'y' => 1,
                'level_requirement' => 10,
                'is_safe' => true,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'cave_guardian',
                        'name' => '洞府守护',
                        'type' => 'npc',
                        'level' => 35,
                        'services' => ['修炼', '秘籍']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'water_spirit',
                        'name' => '水灵',
                        'type' => 'monster',
                        'level' => 12,
                        'description' => '水帘洞中的水系精灵'
                    ]
                ]
            ],
            [
                'name' => '西牛贺洲',
                'type' => 'town',
                'description' => '四大部洲之一，佛法昌盛之地，通往西天的必经之路',
                'x' => -2,
                'y' => 0,
                'level_requirement' => 15,
                'is_safe' => true,
                'facilities' => ['temple', 'market'],
                'npcs' => [
                    [
                        'id' => 'buddhist_monk',
                        'name' => '得道高僧',
                        'type' => 'npc',
                        'level' => 60,
                        'services' => ['佛法', '指引']
                    ]
                ],
                'monsters' => []
            ],
            [
                'name' => '灵山',
                'type' => 'mountain',
                'description' => '佛祖所在的圣地，佛光普照，是西天取经的终点',
                'x' => -3,
                'y' => 1,
                'level_requirement' => 50,
                'is_safe' => true,
                'facilities' => ['temple'],
                'npcs' => [
                    [
                        'id' => 'arhat',
                        'name' => '罗汉',
                        'type' => 'npc',
                        'level' => 80,
                        'services' => ['佛法', '真经']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'guardian_vajra',
                        'name' => '护法金刚',
                        'type' => 'monster',
                        'level' => 50,
                        'description' => '守护灵山的金刚力士'
                    ]
                ]
            ],
            [
                'name' => '南瞻部洲',
                'type' => 'town',
                'description' => '四大部洲之一，人间界所在，凡人聚居之地',
                'x' => 0,
                'y' => -2,
                'level_requirement' => 1,
                'is_safe' => false,
                'facilities' => ['market', 'inn'],
                'npcs' => [
                    [
                        'id' => 'mortal_official',
                        'name' => '凡间官员',
                        'type' => 'npc',
                        'level' => 20,
                        'services' => ['信息', '任务']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'bandit',
                        'name' => '山贼',
                        'type' => 'monster',
                        'level' => 8,
                        'description' => '人间的盗匪，武艺平平'
                    ]
                ]
            ],
            [
                'name' => '北俱芦洲',
                'type' => 'field',
                'description' => '四大部洲之一，妖魔聚集之地，充满危险',
                'x' => 0,
                'y' => 2,
                'level_requirement' => 25,
                'is_safe' => false,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'demon_hunter',
                        'name' => '降妖师',
                        'type' => 'npc',
                        'level' => 45,
                        'services' => ['降妖', '法器']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'demon_soldier',
                        'name' => '妖兵',
                        'type' => 'monster',
                        'level' => 25,
                        'description' => '妖魔大军的士兵'
                    ],
                    [
                        'id' => 'demon_general',
                        'name' => '妖将',
                        'type' => 'monster',
                        'level' => 35,
                        'description' => '妖魔大军的将领，实力强劲'
                    ]
                ]
            ],
            [
                'name' => '东海龙宫',
                'type' => 'underwater',
                'description' => '东海龙王的水下宫殿，珍宝无数，戒备森严',
                'x' => 3,
                'y' => 0,
                'level_requirement' => 30,
                'is_safe' => false,
                'facilities' => ['treasure_vault'],
                'npcs' => [
                    [
                        'id' => 'dragon_king',
                        'name' => '东海龙王',
                        'type' => 'npc',
                        'level' => 70,
                        'services' => ['法宝', '神兵']
                    ],
                    [
                        'id' => 'shrimp_soldier',
                        'name' => '虾兵',
                        'type' => 'npc',
                        'level' => 25,
                        'services' => ['守卫', '信息']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'crab_general',
                        'name' => '蟹将',
                        'type' => 'monster',
                        'level' => 30,
                        'description' => '龙宫的蟹将，身披重甲'
                    ]
                ]
            ]
        ];

        // 创建位置
        $createdLocations = [];
        foreach ($locations as $locationData) {
            $location = Location::create($locationData);
            $createdLocations[$locationData['name']] = $location;
        }

        // 创建位置连接（西游记主题）
        $connections = [
            // 东胜神洲的连接
            ['东胜神洲', '花果山', 1, 5, 0, 1],
            ['东胜神洲', '南瞻部洲', 2, 10, 0, 1],
            ['东胜神洲', '东海龙宫', 3, 20, 0, 20],

            // 花果山的连接
            ['花果山', '东胜神洲', 1, 5, 0, 1],
            ['花果山', '水帘洞', 1, 3, 0, 5],

            // 水帘洞的连接
            ['水帘洞', '花果山', 1, 3, 0, 5],

            // 西牛贺洲的连接
            ['西牛贺洲', '灵山', 2, 15, 0, 30],
            ['西牛贺洲', '南瞻部洲', 2, 12, 0, 10],

            // 灵山的连接
            ['灵山', '西牛贺洲', 2, 15, 0, 30],

            // 南瞻部洲的连接
            ['南瞻部洲', '东胜神洲', 2, 10, 0, 1],
            ['南瞻部洲', '西牛贺洲', 2, 12, 0, 10],
            ['南瞻部洲', '北俱芦洲', 3, 18, 0, 15],

            // 北俱芦洲的连接
            ['北俱芦洲', '南瞻部洲', 3, 18, 0, 15],

            // 东海龙宫的连接
            ['东海龙宫', '东胜神洲', 3, 20, 0, 20],
        ];

        foreach ($connections as $connection) {
            [$fromName, $toName, $distance, $timeCost, $silverCost, $levelReq] = $connection;

            LocationConnection::create([
                'from_location_id' => $createdLocations[$fromName]->id,
                'to_location_id' => $createdLocations[$toName]->id,
                'distance' => $distance,
                'time_cost' => $timeCost,
                'silver_cost' => $silverCost,
                'level_requirement' => $levelReq,
                'is_active' => true,
            ]);
        }
    }
}
