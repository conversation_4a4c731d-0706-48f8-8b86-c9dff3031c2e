<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class MonsterController extends Controller
{
    /**
     * 显示怪物列表
     */
    public function index()
    {
        try {
            $monsters = DB::table('monsters')
                ->orderBy('level')
                ->orderBy('name')
                ->paginate(15);

            return view('admin.monsters.index', compact('monsters'));
        } catch (\Exception $e) {
            // 创建一个空的分页对象而不是简单的集合
            $monsters = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.monsters.index', compact('monsters'));
        }
    }

    /**
     * 显示创建怪物表单
     */
    public function create()
    {
        // 获取位置列表，供选择怪物出现地点
        try {
            $locations = DB::table('locations')
                ->join('regions', 'locations.region_id', '=', 'regions.id')
                ->select('locations.id', 'locations.name', 'regions.name as region_name')
                ->orderBy('regions.name')
                ->orderBy('locations.name')
                ->get();

            // 获取掉落物品列表
            $items = DB::table('items')
                ->orderBy('name')
                ->get();

            return view('admin.monsters.create', compact('locations', 'items'));
        } catch (\Exception $e) {
            $locations = collect([]);
            $items = collect([]);
            return view('admin.monsters.create', compact('locations', 'items'));
        }
    }

    /**
     * 保存新怪物
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'level' => 'required|integer|min:1',
            'hp' => 'required|integer|min:1',
            'mp' => 'required|integer|min:0',
            'attack' => 'required|integer|min:1',
            'defense' => 'required|integer|min:0',
            'speed' => 'required|integer|min:1',
            'exp_reward' => 'required|integer|min:1',
            'silver_reward' => 'required|integer|min:0',
            'location_ids' => 'nullable|array',
            'drop_items' => 'nullable|array',
            'drop_items.*.item_id' => 'required|integer',
            'drop_items.*.drop_rate' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // 创建怪物
            $monsterId = DB::table('monsters')->insertGetId([
                'name' => $request->name,
                'description' => $request->description,
                'level' => $request->level,
                'hp' => $request->hp,
                'mp' => $request->mp,
                'attack' => $request->attack,
                'defense' => $request->defense,
                'speed' => $request->speed,
                'exp_reward' => $request->exp_reward,
                'silver_reward' => $request->silver_reward,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // 保存怪物出现位置
            if ($request->has('location_ids') && is_array($request->location_ids)) {
                foreach ($request->location_ids as $locationId) {
                    DB::table('monster_locations')->insert([
                        'monster_id' => $monsterId,
                        'location_id' => $locationId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            // 保存怪物掉落物品
            if ($request->has('drop_items') && is_array($request->drop_items)) {
                foreach ($request->drop_items as $drop) {
                    if (isset($drop['item_id']) && isset($drop['drop_rate'])) {
                        DB::table('monster_drops')->insert([
                            'monster_id' => $monsterId,
                            'item_id' => $drop['item_id'],
                            'drop_rate' => $drop['drop_rate'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('admin.monsters.edit', $monsterId)
                ->with('success', '怪物创建成功');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', '怪物创建失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 显示编辑怪物表单
     */
    public function edit($id)
    {
        try {
            $monster = DB::table('monsters')->where('id', $id)->first();

            if (!$monster) {
                return redirect()->route('admin.monsters.index')
                    ->with('error', '怪物不存在');
            }

            // 获取所有位置
            $locations = DB::table('locations')
                ->join('regions', 'locations.region_id', '=', 'regions.id')
                ->select('locations.id', 'locations.name', 'regions.name as region_name')
                ->orderBy('regions.name')
                ->orderBy('locations.name')
                ->get();

            // 获取怪物当前的位置
            $monsterLocations = DB::table('monster_locations')
                ->where('monster_id', $id)
                ->pluck('location_id')
                ->toArray();

            // 获取所有物品
            $items = DB::table('items')
                ->orderBy('name')
                ->get();

            // 获取怪物当前的掉落物品
            $monsterDrops = DB::table('monster_drops')
                ->where('monster_id', $id)
                ->get()
                ->keyBy('item_id')
                ->toArray();

            return view('admin.monsters.edit', compact(
                'monster',
                'locations',
                'monsterLocations',
                'items',
                'monsterDrops'
            ));
        } catch (\Exception $e) {
            return redirect()->route('admin.monsters.index')
                ->with('error', '无法获取怪物信息: ' . $e->getMessage());
        }
    }

    /**
     * 更新怪物信息
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'level' => 'required|integer|min:1',
            'hp' => 'required|integer|min:1',
            'mp' => 'required|integer|min:0',
            'attack' => 'required|integer|min:1',
            'defense' => 'required|integer|min:0',
            'speed' => 'required|integer|min:1',
            'exp_reward' => 'required|integer|min:1',
            'silver_reward' => 'required|integer|min:0',
            'location_ids' => 'nullable|array',
            'drop_items' => 'nullable|array',
            'drop_items.*.item_id' => 'required|integer',
            'drop_items.*.drop_rate' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // 更新怪物基本信息
            DB::table('monsters')
                ->where('id', $id)
                ->update([
                    'name' => $request->name,
                    'description' => $request->description,
                    'level' => $request->level,
                    'hp' => $request->hp,
                    'mp' => $request->mp,
                    'attack' => $request->attack,
                    'defense' => $request->defense,
                    'speed' => $request->speed,
                    'exp_reward' => $request->exp_reward,
                    'silver_reward' => $request->silver_reward,
                    'updated_at' => now(),
                ]);

            // 更新怪物出现位置
            DB::table('monster_locations')
                ->where('monster_id', $id)
                ->delete();

            if ($request->has('location_ids') && is_array($request->location_ids)) {
                foreach ($request->location_ids as $locationId) {
                    DB::table('monster_locations')->insert([
                        'monster_id' => $id,
                        'location_id' => $locationId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            // 更新怪物掉落物品
            DB::table('monster_drops')
                ->where('monster_id', $id)
                ->delete();

            if ($request->has('drop_items') && is_array($request->drop_items)) {
                foreach ($request->drop_items as $drop) {
                    if (isset($drop['item_id']) && isset($drop['drop_rate'])) {
                        DB::table('monster_drops')->insert([
                            'monster_id' => $id,
                            'item_id' => $drop['item_id'],
                            'drop_rate' => $drop['drop_rate'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('admin.monsters.edit', $id)
                ->with('success', '怪物更新成功');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', '怪物更新失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 删除怪物
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            // 删除怪物关联数据
            DB::table('monster_locations')->where('monster_id', $id)->delete();
            DB::table('monster_drops')->where('monster_id', $id)->delete();

            // 删除怪物
            DB::table('monsters')->where('id', $id)->delete();

            DB::commit();

            return redirect()->route('admin.monsters.index')
                ->with('success', '怪物已删除');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->route('admin.monsters.index')
                ->with('error', '怪物删除失败: ' . $e->getMessage());
        }
    }
}
