/**
 * 技能系统API服务
 * 提供技能相关的接口调用
 */
import { get, post } from '../request.js';
import cacheService from './cacheService.js';
import logger from '../../utils/logger.js';

// 缓存键
const CACHE_KEYS = {
    SKILL_LIST: 'skill_list',
    SKILL_DETAILS: 'skill_details',
    SKILL_TREE: 'skill_tree'
};

/**
 * 技能服务
 */
const skillService = {
    /**
     * 获取角色技能列表
     * @param {string} characterId - 角色ID
     * @param {Object} params - 查询参数
     * @param {string} params.type - 技能类型筛选 (attack/defense/support)
     * @param {boolean} params.learned - 是否只显示已学技能
     * @returns {Promise<Object>} - 技能列表
     */
    getSkills(characterId, params = {}) {
        logger.debug('[SkillService] 获取技能列表, characterId:', characterId, 'params:', params);
        
        return get(`/characters/${characterId}/skills`, params, {
            loading: true,
            loadingText: '加载技能列表...'
        }).then(res => {
            logger.debug('[SkillService] 技能列表响应:', res);
            // 缓存结果
            const cacheKey = `${CACHE_KEYS.SKILL_LIST}_${characterId}_${JSON.stringify(params)}`;
            cacheService.set(cacheKey, res.data, 300); // 缓存5分钟
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 获取技能列表失败:', error);
            throw error;
        });
    },

    /**
     * 获取战斗可用技能
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 战斗技能列表
     */
    getBattleSkills(characterId) {
        logger.debug('[SkillService] 获取战斗技能, characterId:', characterId);
        
        return get(`/characters/${characterId}/skills/battle`, {}, {
            loading: true,
            loadingText: '加载战斗技能...'
        }).then(res => {
            logger.debug('[SkillService] 战斗技能响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 获取战斗技能失败:', error);
            throw error;
        });
    },

    /**
     * 获取技能详情
     * @param {string} skillId - 技能ID
     * @returns {Promise<Object>} - 技能详情
     */
    getSkillDetails(skillId) {
        logger.debug('[SkillService] 获取技能详情, skillId:', skillId);
        
        return get(`/skills/${skillId}`, {}, {
            loading: true,
            loadingText: '加载技能详情...'
        }).then(res => {
            logger.debug('[SkillService] 技能详情响应:', res);
            // 缓存结果
            cacheService.set(`${CACHE_KEYS.SKILL_DETAILS}_${skillId}`, res.data, 600); // 缓存10分钟
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 获取技能详情失败:', error);
            throw error;
        });
    },

    /**
     * 学习技能
     * @param {string} characterId - 角色ID
     * @param {string} skillId - 技能ID
     * @returns {Promise<Object>} - 学习结果
     */
    learnSkill(characterId, skillId) {
        logger.debug('[SkillService] 学习技能, characterId:', characterId, 'skillId:', skillId);
        
        return post(`/characters/${characterId}/skills/${skillId}/learn`, {}, {
            loading: true,
            loadingText: '学习技能中...'
        }).then(res => {
            logger.debug('[SkillService] 学习技能响应:', res);
            // 清除技能列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 学习技能失败:', error);
            throw error;
        });
    },

    /**
     * 升级技能
     * @param {string} characterId - 角色ID
     * @param {string} skillId - 技能ID
     * @returns {Promise<Object>} - 升级结果
     */
    upgradeSkill(characterId, skillId) {
        logger.debug('[SkillService] 升级技能, characterId:', characterId, 'skillId:', skillId);
        
        return post(`/characters/${characterId}/skills/${skillId}/upgrade`, {}, {
            loading: true,
            loadingText: '升级技能中...'
        }).then(res => {
            logger.debug('[SkillService] 升级技能响应:', res);
            // 清除技能列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 升级技能失败:', error);
            throw error;
        });
    },

    /**
     * 使用技能
     * @param {string} characterId - 角色ID
     * @param {string} skillId - 技能ID
     * @param {Object} params - 使用参数
     * @param {string} params.targetId - 目标ID (可选)
     * @param {number} params.targetX - 目标X坐标 (可选)
     * @param {number} params.targetY - 目标Y坐标 (可选)
     * @returns {Promise<Object>} - 使用结果
     */
    useSkill(characterId, skillId, params = {}) {
        logger.debug('[SkillService] 使用技能, characterId:', characterId, 'skillId:', skillId, 'params:', params);
        
        return post(`/characters/${characterId}/skills/${skillId}/use`, params, {
            loading: true,
            loadingText: '使用技能中...'
        }).then(res => {
            logger.debug('[SkillService] 使用技能响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 使用技能失败:', error);
            throw error;
        });
    },

    /**
     * 重置技能点
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 重置结果
     */
    resetSkills(characterId) {
        logger.debug('[SkillService] 重置技能点, characterId:', characterId);
        
        return post(`/characters/${characterId}/skills/reset`, {}, {
            loading: true,
            loadingText: '重置技能点中...'
        }).then(res => {
            logger.debug('[SkillService] 重置技能点响应:', res);
            // 清除技能列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 重置技能点失败:', error);
            throw error;
        });
    },

    /**
     * 获取技能树
     * @param {string} characterId - 角色ID
     * @param {string} profession - 职业
     * @returns {Promise<Object>} - 技能树数据
     */
    getSkillTree(characterId, profession) {
        logger.debug('[SkillService] 获取技能树, characterId:', characterId, 'profession:', profession);
        
        return get(`/characters/${characterId}/skills/tree`, { profession }, {
            loading: true,
            loadingText: '加载技能树...'
        }).then(res => {
            logger.debug('[SkillService] 技能树响应:', res);
            // 缓存结果
            cacheService.set(`${CACHE_KEYS.SKILL_TREE}_${characterId}_${profession}`, res.data, 600); // 缓存10分钟
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 获取技能树失败:', error);
            throw error;
        });
    },

    /**
     * 获取技能冷却状态
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 技能冷却状态
     */
    getSkillCooldowns(characterId) {
        logger.debug('[SkillService] 获取技能冷却状态, characterId:', characterId);
        
        return get(`/characters/${characterId}/skills/cooldowns`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[SkillService] 技能冷却状态响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 获取技能冷却状态失败:', error);
            throw error;
        });
    },

    /**
     * 设置技能快捷键
     * @param {string} characterId - 角色ID
     * @param {string} skillId - 技能ID
     * @param {string} hotkey - 快捷键
     * @returns {Promise<Object>} - 设置结果
     */
    setSkillHotkey(characterId, skillId, hotkey) {
        logger.debug('[SkillService] 设置技能快捷键, characterId:', characterId, 'skillId:', skillId, 'hotkey:', hotkey);
        
        return post(`/characters/${characterId}/skills/${skillId}/hotkey`, {
            hotkey
        }, {
            loading: true,
            loadingText: '设置快捷键中...'
        }).then(res => {
            logger.debug('[SkillService] 设置技能快捷键响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 设置技能快捷键失败:', error);
            throw error;
        });
    },

    /**
     * 获取技能效果预览
     * @param {string} skillId - 技能ID
     * @param {number} level - 技能等级
     * @returns {Promise<Object>} - 技能效果预览
     */
    getSkillPreview(skillId, level) {
        logger.debug('[SkillService] 获取技能效果预览, skillId:', skillId, 'level:', level);
        
        return get(`/skills/${skillId}/preview`, { level }, {
            loading: false
        }).then(res => {
            logger.debug('[SkillService] 技能效果预览响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[SkillService] 获取技能效果预览失败:', error);
            throw error;
        });
    },

    /**
     * 清除技能相关的缓存
     * @param {string} characterId - 角色ID
     */
    clearCache(characterId) {
        if (characterId) {
            cacheService.removeByPrefix(`${CACHE_KEYS.SKILL_LIST}_${characterId}`);
            cacheService.removeByPrefix(`${CACHE_KEYS.SKILL_TREE}_${characterId}`);
        } else {
            // 清除所有技能缓存
            cacheService.removeByPrefix(CACHE_KEYS.SKILL_LIST);
            cacheService.removeByPrefix(CACHE_KEYS.SKILL_DETAILS);
            cacheService.removeByPrefix(CACHE_KEYS.SKILL_TREE);
        }
    }
};

export default skillService;
