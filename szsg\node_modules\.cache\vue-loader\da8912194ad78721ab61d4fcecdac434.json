{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\game\\BattleAnimation.vue?vue&type=style&index=0&id=7f84ddff&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\game\\BattleAnimation.vue", "mtime": 1750337441932}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BattleAnimation.vue"], "names": [], "mappings": ";AA4PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "BattleAnimation.vue", "sourceRoot": "src/components/game", "sourcesContent": ["<template>\n  <div class=\"battle-animations\">\n    <!-- 默认战斗场景 -->\n    <div class=\"battle-scene\">\n      <div class=\"battle-ground\">\n        <div class=\"ground-line\"></div>\n        <div class=\"battle-text\" v-if=\"!hasActiveAnimations\">\n          <p>战斗进行中...</p>\n          <p class=\"battle-tip\">点击攻击按钮开始战斗</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 动画效果 -->\n    <div\n      v-for=\"animation in animations\"\n      :key=\"animation.id\"\n      :class=\"['animation-element', animation.className]\"\n      :style=\"animation.style\"\n      @animationend=\"removeAnimation(animation.id)\"\n    >\n      {{ animation.text }}\n    </div>\n\n    <!-- Lottie 动画 (用于复杂特效) -->\n    <lottie-player\n      v-if=\"showLottieEffect\"\n      :src=\"lottieAnimationUrl\"\n      background=\"transparent\"\n      speed=\"1\"\n      style=\"width: 300px; height: 300px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);\"\n      autoplay\n      @complete=\"onLottieComplete\"\n    ></lottie-player>\n\n    <!-- 粒子效果容器 -->\n    <div id=\"particles-container\" v-show=\"showParticles\" class=\"particles-container\"></div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'BattleAnimation',\n  \n  data() {\n    return {\n      animations: [],\n      showLottieEffect: false,\n      lottieAnimationUrl: '',\n      showParticles: false,\n      animationIdCounter: 0\n    }\n  },\n\n  computed: {\n    hasActiveAnimations() {\n      return this.animations.length > 0 || this.showLottieEffect || this.showParticles\n    }\n  },\n  \n  methods: {\n    // 播放攻击动画\n    playAttackAnimation(type = 'bounce') {\n      const animation = {\n        id: this.generateAnimationId(),\n        text: '攻击！',\n        className: 'attack-text bounce-in',\n        style: {\n          animation: 'bounceIn 1s ease-out'\n        },\n        duration: 1000\n      };\n      this.animations.push(animation);\n\n      // 自动移除动画\n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放伤害数字动画\n    playDamageAnimation(damage, isCritical = false) {\n      const animation = {\n        id: this.generateAnimationId(),\n        text: `-${damage}`,\n        className: isCritical ? 'critical-damage fade-up' : 'normal-damage fade-up',\n        style: {\n          animation: isCritical ? 'criticalDamage 1.5s ease-out' : 'normalDamage 1.5s ease-out'\n        },\n        duration: 1500\n      };\n      this.animations.push(animation);\n\n      // 自动移除动画\n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放治疗动画\n    playHealAnimation(healing) {\n      const animation = {\n        id: this.generateAnimationId(),\n        type: 'fadeInUp',\n        text: `+${healing}`,\n        className: 'heal-text',\n        duration: 1500\n      };\n      this.animations.push(animation);\n      \n      // 自动移除动画\n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放技能特效 (使用Lottie)\n    playSkillEffect(skillType) {\n      const lottieUrls = {\n        fire: 'https://assets2.lottiefiles.com/packages/lf20_XZ3pkn.json',\n        ice: 'https://assets9.lottiefiles.com/packages/lf20_dmw9cg8h.json',\n        lightning: 'https://assets4.lottiefiles.com/packages/lf20_tl52xr3o.json',\n        heal: 'https://assets1.lottiefiles.com/packages/lf20_qp1spzqv.json'\n      };\n      \n      this.lottieAnimationUrl = lottieUrls[skillType] || lottieUrls.fire;\n      this.showLottieEffect = true;\n    },\n\n    // 播放粒子效果\n    playParticleEffect(type = 'explosion') {\n      this.showParticles = true;\n      \n      // 根据类型配置不同的粒子效果\n      const configs = {\n        explosion: {\n          particles: {\n            number: { value: 100 },\n            color: { value: [\"#ff0000\", \"#ff8800\", \"#ffff00\"] },\n            shape: { type: \"circle\" },\n            size: { value: 4, random: true },\n            move: {\n              enable: true,\n              speed: 8,\n              direction: \"none\",\n              out_mode: \"out\"\n            }\n          }\n        },\n        magic: {\n          particles: {\n            number: { value: 50 },\n            color: { value: [\"#0066ff\", \"#8800ff\", \"#ff00ff\"] },\n            shape: { type: \"star\" },\n            size: { value: 3, random: true },\n            move: {\n              enable: true,\n              speed: 4,\n              direction: \"top\",\n              out_mode: \"out\"\n            }\n          }\n        }\n      };\n\n      if (window.particlesJS) {\n        window.particlesJS('particles-container', configs[type] || configs.explosion);\n        \n        // 3秒后隐藏粒子效果\n        setTimeout(() => {\n          this.showParticles = false;\n        }, 3000);\n      }\n    },\n\n    // 播放Miss动画\n    playMissAnimation() {\n      const animation = {\n        id: this.generateAnimationId(),\n        type: 'fadeOutUp',\n        text: 'MISS',\n        className: 'miss-text',\n        duration: 1000\n      };\n      this.animations.push(animation);\n      \n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放状态效果动画\n    playStatusAnimation(statusType, text) {\n      const animationTypes = {\n        buff: 'pulse',\n        debuff: 'shake',\n        poison: 'wobble',\n        stun: 'flash'\n      };\n      \n      const animation = {\n        id: this.generateAnimationId(),\n        type: animationTypes[statusType] || 'pulse',\n        text: text,\n        className: `status-${statusType}`,\n        duration: 2000\n      };\n      this.animations.push(animation);\n      \n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 生成动画ID\n    generateAnimationId() {\n      return ++this.animationIdCounter;\n    },\n\n    // 移除动画\n    removeAnimation(id) {\n      this.animations = this.animations.filter(anim => anim.id !== id);\n    },\n\n    // Lottie动画完成回调\n    onLottieComplete() {\n      this.showLottieEffect = false;\n    },\n\n    // 清除所有动画\n    clearAllAnimations() {\n      this.animations = [];\n      this.showLottieEffect = false;\n      this.showParticles = false;\n    }\n  },\n\n  mounted() {\n    // 检查动画库是否加载\n    if (window.particlesJS) {\n      // Particles.js 已加载\n    }\n  },\n\n  beforeDestroy() {\n    // 清理动画\n    this.clearAllAnimations();\n  }\n}\n</script>\n\n<style scoped>\n.battle-animations {\n  position: relative;\n  width: 100%;\n  height: 300px;\n  overflow: hidden;\n  background: linear-gradient(180deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);\n  border: 2px solid #666;\n  border-radius: 8px;\n}\n\n/* 战斗场景 */\n.battle-scene {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.battle-ground {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.ground-line {\n  position: absolute;\n  bottom: 20px;\n  left: 10%;\n  right: 10%;\n  height: 2px;\n  background: linear-gradient(90deg, transparent 0%, #8B4513 50%, transparent 100%);\n}\n\n.battle-text {\n  text-align: center;\n  color: #e2e8f0;\n}\n\n.battle-text p {\n  margin: 5px 0;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n}\n\n.battle-tip {\n  font-size: 14px !important;\n  color: #a0aec0 !important;\n  font-weight: normal !important;\n}\n\n.animation-element {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 24px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  z-index: 10;\n  pointer-events: none;\n}\n\n/* 攻击文字样式 */\n.attack-text {\n  color: #fff;\n  font-size: 20px;\n}\n\n/* 伤害数字样式 */\n.normal-damage {\n  color: #ff4444;\n  font-size: 28px;\n}\n\n.critical-damage {\n  color: #ff0000;\n  font-size: 32px;\n  text-shadow: 0 0 10px #ff0000;\n}\n\n/* 治疗数字样式 */\n.heal-text {\n  color: #44ff44;\n  font-size: 24px;\n}\n\n/* Miss文字样式 */\n.miss-text {\n  color: #888;\n  font-size: 20px;\n  font-style: italic;\n}\n\n/* 状态效果样式 */\n.status-buff {\n  color: #00ff00;\n  font-size: 18px;\n}\n\n.status-debuff {\n  color: #ff8800;\n  font-size: 18px;\n}\n\n.status-poison {\n  color: #8800ff;\n  font-size: 18px;\n}\n\n.status-stun {\n  color: #ffff00;\n  font-size: 18px;\n}\n\n/* 粒子效果容器 */\n.particles-container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 5;\n}\n\n/* Animate.css 自定义动画时长 */\n.animate__animated {\n  animation-duration: 1s;\n}\n\n.animate__bounceInDown {\n  animation-duration: 1.5s;\n}\n\n.animate__fadeInUp {\n  animation-duration: 1.2s;\n}\n\n.animate__fadeOutUp {\n  animation-duration: 1s;\n}\n\n.animate__pulse {\n  animation-duration: 2s;\n}\n\n.animate__shake {\n  animation-duration: 1s;\n}\n\n.animate__wobble {\n  animation-duration: 1s;\n}\n\n.animate__flash {\n  animation-duration: 1s;\n}\n</style>\n"]}]}