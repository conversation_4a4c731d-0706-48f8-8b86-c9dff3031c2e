<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('regions', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('大区名称');
            $table->string('code', 50)->unique()->comment('大区代码');
            $table->text('description')->nullable()->comment('大区描述');
            $table->enum('status', ['online', 'busy', 'maintenance', 'offline'])
                  ->default('online')->comment('大区状态');
            $table->integer('player_count')->default(0)->comment('当前在线人数');
            $table->integer('max_players')->default(2000)->comment('最大承载人数');
            $table->string('icon')->nullable()->comment('大区图标');
            $table->json('features')->nullable()->comment('大区特色功能');
            $table->decimal('exp_rate', 3, 2)->default(1.00)->comment('经验倍率');
            $table->decimal('drop_rate', 3, 2)->default(1.00)->comment('掉落倍率');
            $table->boolean('is_new')->default(false)->comment('是否为新区');
            $table->boolean('is_recommended')->default(false)->comment('是否推荐');
            $table->integer('sort_order')->default(0)->comment('排序权重');
            $table->timestamp('open_time')->nullable()->comment('开区时间');
            $table->timestamps();
            
            // 索引
            $table->index(['status', 'sort_order']);
            $table->index('is_recommended');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('regions');
    }
};
