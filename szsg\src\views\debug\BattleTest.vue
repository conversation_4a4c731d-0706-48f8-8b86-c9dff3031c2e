<template>
  <div class="battle-test">
    <h1>战斗系统测试</h1>
    
    <div class="test-section">
      <h2>测试怪物列表</h2>
      <div class="monster-list">
        <div 
          v-for="monster in testMonsters" 
          :key="monster.id"
          class="monster-card"
          @click="startTestBattle(monster)"
        >
          <img :src="monster.avatar" :alt="monster.name" class="monster-avatar" />
          <div class="monster-info">
            <h3>{{ monster.name }}</h3>
            <p>等级: {{ monster.level }}</p>
            <p>类型: {{ monster.type }}</p>
            <p>生命: {{ monster.max_health }}</p>
            <p>攻击: {{ monster.attack }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>动画测试</h2>
      <div class="animation-test">
        <BattleAnimation ref="animationTest" />
        <div class="animation-controls">
          <button @click="testAttackAnimation">测试攻击动画</button>
          <button @click="testDamageAnimation">测试伤害动画</button>
          <button @click="testCriticalAnimation">测试暴击动画</button>
          <button @click="testHealAnimation">测试治疗动画</button>
          <button @click="testSkillAnimation">测试技能特效</button>
          <button @click="testParticleAnimation">测试粒子效果</button>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>API测试</h2>
      <div class="api-test">
        <button @click="testBattleAPI" :disabled="isTestingAPI">
          {{ isTestingAPI ? '测试中...' : '测试战斗API' }}
        </button>
        <div v-if="apiTestResult" class="api-result">
          <h4>API测试结果:</h4>
          <pre>{{ JSON.stringify(apiTestResult, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BattleAnimation from '@/components/game/BattleAnimation.vue'
import battleService from '@/api/services/battleService.js'
import logger from '@/utils/logger.js'

export default {
  name: 'BattleTest',
  components: {
    BattleAnimation
  },
  
  data() {
    return {
      testMonsters: [
        {
          id: 1,
          name: '灵猴',
          level: 5,
          type: 'beast',
          max_health: 80,
          attack: 15,
          avatar: '/static/game/UI/tx/monster/monkey.png'
        },
        {
          id: 2,
          name: '山魈',
          level: 8,
          type: 'demon',
          max_health: 120,
          attack: 22,
          avatar: '/static/game/UI/tx/monster/demon.png'
        },
        {
          id: 3,
          name: '水灵',
          level: 6,
          type: 'elemental',
          max_health: 90,
          attack: 18,
          avatar: '/static/game/UI/tx/monster/water_spirit.png'
        }
      ],
      isTestingAPI: false,
      apiTestResult: null
    }
  },
  
  methods: {
    // 开始测试战斗
    startTestBattle(monster) {
      // 获取当前用户的角色ID
      const currentCharacter = this.$store.getters['character/currentCharacter'];
      let testCharacterId = 1; // 默认值

      if (currentCharacter && currentCharacter.id) {
        testCharacterId = currentCharacter.id;
      } else {
        // 尝试从localStorage获取
        try {
          const storedCharacter = localStorage.getItem('selectedCharacter');
          if (storedCharacter) {
            const character = JSON.parse(storedCharacter);
            testCharacterId = character.id || 1;
          }
        } catch (error) {
          // 无法获取角色信息，使用默认ID
        }
      }

      // 显示将要使用的参数
      this.$toast(`开始战斗测试 - 角色ID: ${testCharacterId}, 怪物ID: ${monster.id}`)

      this.$router.push({
        path: '/game/battle',
        query: {
          characterId: testCharacterId,
          monsterId: monster.id,
          locationId: 'test_location'
        }
      });
    },

    // 测试攻击动画
    testAttackAnimation() {
      if (this.$refs.animationTest) {
        this.$refs.animationTest.playAttackAnimation();
      }
    },

    // 测试伤害动画
    testDamageAnimation() {
      if (this.$refs.animationTest) {
        this.$refs.animationTest.playDamageAnimation(25, false);
      }
    },

    // 测试暴击动画
    testCriticalAnimation() {
      if (this.$refs.animationTest) {
        this.$refs.animationTest.playDamageAnimation(45, true);
      }
    },

    // 测试治疗动画
    testHealAnimation() {
      if (this.$refs.animationTest) {
        this.$refs.animationTest.playHealAnimation(30);
      }
    },

    // 测试技能特效
    testSkillAnimation() {
      if (this.$refs.animationTest) {
        this.$refs.animationTest.playSkillEffect('fire');
      }
    },

    // 测试粒子效果
    testParticleAnimation() {
      if (this.$refs.animationTest) {
        this.$refs.animationTest.playParticleEffect('explosion');
      }
    },

    // 测试战斗API
    async testBattleAPI() {
      this.isTestingAPI = true;
      this.apiTestResult = null;

      try {
        logger.info('[BattleTest] 开始测试战斗API...');

        // 首先测试简单的API调用
        const testData = {
          character_id: 1,
          monster_id: 1,
          location_id: 'test_location'
        };

        logger.info('[BattleTest] 测试数据:', testData);

        // 测试开始战斗API
        const result = await battleService.startBattle(1, 1, 'test_location');
        this.apiTestResult = {
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        };
        logger.info('[BattleTest] API测试成功:', result);
      } catch (error) {
        this.apiTestResult = {
          success: false,
          error: error.message,
          details: {
            name: error.name,
            message: error.message,
            response: error.response?.data,
            status: error.response?.status,
            config: error.config
          },
          timestamp: new Date().toISOString()
        };
        logger.error('[BattleTest] API测试失败:', error);
      } finally {
        this.isTestingAPI = false;
      }
    }
  },

  mounted() {
    logger.info('[BattleTest] 战斗测试页面已加载');
    
    // 检查动画库是否加载
    if (window.particlesJS) {
      logger.info('[BattleTest] Particles.js 已加载');
    } else {
      logger.warn('[BattleTest] Particles.js 未加载');
    }
  }
}
</script>

<style scoped>
.battle-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.battle-test h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h2 {
  color: #555;
  margin-bottom: 15px;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

/* 怪物列表样式 */
.monster-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.monster-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.monster-card:hover {
  border-color: #007bff;
  background: #f8f9fa;
  transform: translateY(-2px);
}

.monster-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #ccc;
}

.monster-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.monster-info p {
  margin: 2px 0;
  color: #666;
  font-size: 14px;
}

/* 动画测试样式 */
.animation-test {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.animation-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.animation-controls button {
  padding: 10px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.animation-controls button:hover {
  background: #0056b3;
}

/* API测试样式 */
.api-test button {
  padding: 12px 24px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease;
}

.api-test button:hover:not(:disabled) {
  background: #218838;
}

.api-test button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.api-result {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
}

.api-result h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.api-result pre {
  background: #e9ecef;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  font-size: 12px;
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .battle-test {
    padding: 10px;
  }
  
  .monster-list {
    grid-template-columns: 1fr;
  }
  
  .monster-card {
    flex-direction: column;
    text-align: center;
  }
  
  .animation-controls {
    flex-direction: column;
  }
}
</style>
