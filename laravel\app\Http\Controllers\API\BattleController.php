<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use App\Models\Monster;

class BattleController extends Controller
{
    /**
     * 获取敌人列表
     */
    public function getEnemies(Request $request)
    {
        // 模拟敌人数据
        $enemies = [
            [
                'id' => 1,
                'name' => '野狼',
                'level' => 1,
                'hp' => 80,
                'max_hp' => 80,
                'attack' => 15,
                'defense' => 5,
                'exp_reward' => 25,
                'gold_reward' => 10,
                'avatar' => '/static/game/enemies/wolf.png',
                'description' => '森林中常见的野生动物，具有一定的攻击性。'
            ],
            [
                'id' => 2,
                'name' => '哥布林',
                'level' => 2,
                'hp' => 120,
                'max_hp' => 120,
                'attack' => 20,
                'defense' => 8,
                'exp_reward' => 40,
                'gold_reward' => 15,
                'avatar' => '/static/game/enemies/goblin.png',
                'description' => '狡猾的小怪物，喜欢偷袭过路的冒险者。'
            ],
            [
                'id' => 3,
                'name' => '骷髅战士',
                'level' => 3,
                'hp' => 150,
                'max_hp' => 150,
                'attack' => 25,
                'defense' => 12,
                'exp_reward' => 60,
                'gold_reward' => 20,
                'avatar' => '/static/game/enemies/skeleton.png',
                'description' => '被黑暗魔法复活的骷髅，拥有不死的特性。'
            ],
            [
                'id' => 4,
                'name' => '森林巨熊',
                'level' => 5,
                'hp' => 300,
                'max_hp' => 300,
                'attack' => 40,
                'defense' => 20,
                'exp_reward' => 150,
                'gold_reward' => 50,
                'avatar' => '/static/game/enemies/bear.png',
                'description' => '森林深处的强大野兽，拥有惊人的力量。'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'enemies' => $enemies
            ]
        ]);
    }

    /**
     * 开始PVE战斗
     */
    public function startPveBattle(Request $request, Character $character, $enemyId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟战斗初始化
        $enemy = [
            'id' => $enemyId,
            'name' => '野狼',
            'level' => 1,
            'hp' => 80,
            'max_hp' => 80,
            'attack' => 15,
            'defense' => 5
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'battle_id' => rand(1000, 9999),
                'enemy' => $enemy,
                'character' => [
                    'hp' => 100,
                    'max_hp' => 100,
                    'mp' => 50,
                    'max_mp' => 50
                ]
            ]
        ]);
    }

    /**
     * 开始PVP战斗
     */
    public function startPvpBattle(Request $request, Character $character, $opponentId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟PVP战斗初始化
        return response()->json([
            'success' => true,
            'message' => 'PVP战斗功能暂未开放'
        ]);
    }

    /**
     * 执行战斗行动
     */
    public function executeBattleAction(Request $request, Character $character, $battleId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        $action = $request->input('action');

        // 模拟战斗行动结果
        $result = [
            'success' => true,
            'logs' => [
                ['message' => "你使用了{$action}", 'type' => 'player'],
                ['message' => '对敌人造成了25点伤害', 'type' => 'damage'],
                ['message' => '敌人反击，对你造成了10点伤害', 'type' => 'enemy']
            ],
            'character' => [
                'hp' => 90,
                'mp' => 45
            ],
            'enemy' => [
                'hp' => 55
            ],
            'isPlayerTurn' => false,
            'battleEnd' => false
        ];

        return response()->json($result);
    }

    /**
     * 获取战斗状态
     */
    public function getBattleStatus(Request $request, Character $character, $battleId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟战斗状态
        return response()->json([
            'success' => true,
            'data' => [
                'battle_id' => $battleId,
                'status' => 'active',
                'character' => [
                    'hp' => 90,
                    'max_hp' => 100,
                    'mp' => 45,
                    'max_mp' => 50
                ],
                'enemy' => [
                    'hp' => 55,
                    'max_hp' => 80
                ],
                'isPlayerTurn' => true
            ]
        ]);
    }

    /**
     * 逃跑
     */
    public function fleeBattle(Request $request, Character $character, $battleId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟逃跑结果
        return response()->json([
            'success' => true,
            'message' => '成功逃脱了战斗'
        ]);
    }

    /**
     * 开始战斗 (前端调用的API)
     */
    public function startBattle(Request $request)
    {
        try {
            $request->validate([
                'character_id' => 'required|integer|exists:characters,id',
                'monster_id' => 'required|integer',
                'location_id' => 'nullable|string|max:50'
            ]);

            $characterId = $request->input('character_id');
            $monsterId = $request->input('monster_id');
            $locationId = $request->input('location_id');

            // 验证角色权限
            $character = Character::find($characterId);
            if (!$character || $character->user_id !== $request->user()->id) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问该角色'
                ], 403);
            }

            // 从数据库获取怪物数据
            $monster = Monster::find($monsterId);

            if (!$monster) {
                return response()->json([
                    'success' => false,
                    'message' => '怪物不存在'
                ], 404);
            }
            $battleId = rand(1000, 9999);

            // 从角色数据中获取属性和状态
            $attributes = $character->attributes ? json_decode($character->attributes, true) : [];
            $stats = $character->stats ? json_decode($character->stats, true) : [];

            // 计算角色战斗属性
            $characterHp = $stats['hp'] ?? 100;
            $characterMaxHp = $stats['max_hp'] ?? 100;
            $characterMp = $stats['mp'] ?? 50;
            $characterMaxMp = $stats['max_mp'] ?? 50;
            $characterAttack = $stats['attack'] ?? (($attributes['strength'] ?? 10) * 2 + 10);
            $characterDefense = $stats['defense'] ?? (($attributes['constitution'] ?? 10) + 5);
            $characterSpeed = $stats['speed'] ?? (($attributes['agility'] ?? 10) + 5);

            // 构建真实战斗数据
            $battleData = [
                'id' => $battleId,
                'status' => 'ongoing',
                'rounds' => 0,
                'character' => [
                    'id' => $character->id,
                    'name' => $character->name,
                    'level' => $character->level,
                    'avatar' => $character->avatar,
                    'hp' => $characterHp,
                    'max_hp' => $characterMaxHp,
                    'mp' => $characterMp,
                    'max_mp' => $characterMaxMp,
                    'attack' => $characterAttack,
                    'defense' => $characterDefense,
                    'speed' => $characterSpeed
                ],
                'monster' => [
                    'id' => $monster->id,
                    'name' => $monster->name,
                    'level' => $monster->level,
                    'avatar' => $monster->avatar,
                    'hp' => $monster->current_health,
                    'max_hp' => $monster->max_health,
                    'mp' => $monster->current_mana,
                    'max_mp' => $monster->max_mana,
                    'attack' => $monster->attack,
                    'defense' => $monster->defense,
                    'speed' => $monster->speed,
                    'type' => $monster->type,
                    'element' => $monster->element
                ],
                'rewards' => [
                    'exp_gained' => 0,
                    'gold_gained' => 0,
                    'items_gained' => []
                ],
                'duration' => 0,
                'can_act' => true
            ];

            return response()->json([
                'success' => true,
                'message' => '战斗开始！',
                'data' => [
                    'battle_id' => $battleId,
                    'battle' => $battleData
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 执行战斗行动 (前端调用的API)
     */
    public function performAction(Request $request, $battleId)
    {
        try {
            $request->validate([
                'character_id' => 'required|integer|exists:characters,id',
                'action' => 'required|string|in:attack,flee'
            ]);

            $characterId = $request->input('character_id');
            $action = $request->input('action');

            // 验证角色权限
            $character = Character::find($characterId);
            if (!$character || $character->user_id !== $request->user()->id) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问该角色'
                ], 403);
            }

            $result = [];

            if ($action === 'attack') {
                // 模拟角色攻击
                $characterDamage = rand(15, 25);
                $isCritical = rand(1, 100) <= 20; // 20%暴击率
                if ($isCritical) {
                    $characterDamage = intval($characterDamage * 1.5);
                }

                $result['character_action'] = [
                    'type' => 'character_attack',
                    'damage' => $characterDamage,
                    'is_critical' => $isCritical,
                    'target_hp' => max(0, 80 - $characterDamage) // 模拟怪物剩余血量
                ];

                // 模拟怪物反击
                $monsterDamage = rand(8, 15);
                $monsterCritical = rand(1, 100) <= 10; // 10%暴击率
                if ($monsterCritical) {
                    $monsterDamage = intval($monsterDamage * 1.5);
                }

                $result['monster_action'] = [
                    'type' => 'monster_attack',
                    'damage' => $monsterDamage,
                    'is_critical' => $monsterCritical,
                    'target_hp' => max(0, 100 - $monsterDamage) // 模拟角色剩余血量
                ];

                // 检查战斗是否结束
                if ($result['character_action']['target_hp'] <= 0) {
                    $result['character_action']['battle_end'] = [
                        'result' => 'victory',
                        'message' => '战斗胜利！',
                        'rewards' => [
                            'exp' => 25,
                            'gold' => 10,
                            'items' => []
                        ]
                    ];
                } elseif ($result['monster_action']['target_hp'] <= 0) {
                    $result['monster_action']['battle_end'] = [
                        'result' => 'defeat',
                        'message' => '战斗失败！'
                    ];
                }

            } elseif ($action === 'flee') {
                $fleeSuccess = rand(1, 100) <= 70; // 70%逃跑成功率
                $result['flee_result'] = [
                    'success' => $fleeSuccess,
                    'message' => $fleeSuccess ? '成功逃脱！' : '逃跑失败！'
                ];
            }

            // 模拟更新后的战斗数据
            $battleData = [
                'id' => $battleId,
                'status' => 'ongoing',
                'rounds' => 1,
                'character' => [
                    'id' => $character->id,
                    'name' => $character->name,
                    'level' => $character->level,
                    'avatar' => $character->avatar,
                    'hp' => isset($result['monster_action']) ? $result['monster_action']['target_hp'] : 100,
                    'max_hp' => 100,
                    'mp' => 50,
                    'max_mp' => 50,
                    'attack' => 20,
                    'defense' => 10,
                    'speed' => 15
                ],
                'monster' => [
                    'id' => 1,
                    'name' => '灵猴',
                    'level' => 5,
                    'avatar' => '/static/game/UI/tx/monster/monkey.png',
                    'hp' => isset($result['character_action']) ? $result['character_action']['target_hp'] : 80,
                    'max_hp' => 80,
                    'mp' => 30,
                    'max_mp' => 30,
                    'attack' => 15,
                    'defense' => 8,
                    'speed' => 10,
                    'type' => 'beast',
                    'element' => 'none'
                ],
                'rewards' => [
                    'exp_gained' => 0,
                    'gold_gained' => 0,
                    'items_gained' => []
                ],
                'duration' => 30,
                'can_act' => true
            ];

            return response()->json([
                'success' => true,
                'message' => '动作执行成功',
                'data' => [
                    'battle' => $battleData,
                    'action_results' => $result
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取战斗状态 (前端调用的API)
     */
    public function getBattleState(Request $request, $battleId)
    {
        try {
            // 获取当前用户
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => '用户未认证'
                ], 401);
            }

            // 获取用户的当前角色
            $character = $user->characters()->first();
            if (!$character) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到角色'
                ], 404);
            }

            // 模拟战斗状态数据
            $battleData = [
                'id' => intval($battleId),
                'status' => 'ongoing',
                'rounds' => 1,
                'character' => [
                    'id' => $character->id,
                    'name' => $character->name,
                    'level' => $character->level,
                    'avatar' => $character->avatar ?: '/static/game/UI/tx/default_avatar.png',
                    'hp' => 90,
                    'max_hp' => 100,
                    'mp' => 45,
                    'max_mp' => 50,
                    'attack' => 20,
                    'defense' => 10,
                    'speed' => 15
                ],
                'monster' => [
                    'id' => 1,
                    'name' => '灵猴',
                    'level' => 5,
                    'avatar' => '/static/game/UI/tx/monster/monkey.png',
                    'hp' => 55,
                    'max_hp' => 80,
                    'mp' => 30,
                    'max_mp' => 30,
                    'attack' => 15,
                    'defense' => 8,
                    'speed' => 10,
                    'type' => 'beast',
                    'element' => 'none'
                ],
                'rewards' => [
                    'exp_gained' => 0,
                    'gold_gained' => 0,
                    'items_gained' => []
                ],
                'duration' => 45,
                'can_act' => true
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'battle' => $battleData
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('获取战斗状态失败', [
                'battle_id' => $battleId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取战斗状态失败'
            ], 500);
        }
    }

    /**
     * 获取战斗日志
     */
    public function getBattleLog(Request $request, $battleId)
    {
        try {
            // 获取当前用户
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => '用户未认证'
                ], 401);
            }

            // 获取用户的当前角色
            $character = $user->characters()->first();
            if (!$character) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到角色'
                ], 404);
            }

            // 模拟战斗日志 - 使用实际的战斗ID
            $battleLog = [
                [
                    'round' => 0,
                    'action' => 'battle_start',
                    'data' => [
                        'character' => [
                            'name' => $character->name,
                            'level' => $character->level,
                            'hp' => 100,
                            'mp' => 50
                        ],
                        'monster' => [
                            'name' => '灵猴',
                            'level' => 5,
                            'hp' => 80,
                            'mp' => 30
                        ]
                    ],
                    'timestamp' => now()->toISOString()
                ],
                [
                    'round' => 1,
                    'action' => 'character_attack',
                    'data' => [
                        'damage' => 25,
                        'is_critical' => false,
                        'monster_hp_before' => 80,
                        'monster_hp_after' => 55
                    ],
                    'timestamp' => now()->subSeconds(30)->toISOString()
                ],
                [
                    'round' => 1,
                    'action' => 'monster_attack',
                    'data' => [
                        'damage' => 10,
                        'is_critical' => false,
                        'character_hp_before' => 100,
                        'character_hp_after' => 90
                    ],
                    'timestamp' => now()->subSeconds(25)->toISOString()
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'battle_log' => $battleLog,
                    'rounds' => 1,
                    'status' => 'ongoing'
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('获取战斗日志失败', [
                'battle_id' => $battleId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取战斗日志失败'
            ], 500);
        }
    }

    /**
     * 结束战斗 (前端调用的API)
     */
    public function endBattle(Request $request)
    {
        $characterId = $request->input('character_id');

        // 验证角色权限
        $character = Character::find($characterId);
        if (!$character || $character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟结束战斗
        return response()->json([
            'success' => true,
            'message' => '战斗已结束'
        ]);
    }

    /**
     * 逃跑 (前端调用的API)
     */
    public function flee(Request $request)
    {
        $characterId = $request->input('character_id');

        // 验证角色权限
        $character = Character::find($characterId);
        if (!$character || $character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟逃跑结果
        return response()->json([
            'success' => true,
            'message' => '成功逃脱了战斗'
        ]);
    }
}
