<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use App\Models\BankAccount;
use App\Models\BankTransaction;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class BankController extends Controller
{
    /**
     * 获取银行账户信息
     *
     * @param Request $request
     * @param string $characterId 角色ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAccountInfo(Request $request, $characterId)
    {
        try {
            Log::info("获取银行账户信息 - 开始处理请求，角色ID: {$characterId}");

            // 获取角色信息
            try {
                $character = Character::findOrFail($characterId);
                Log::info("找到角色: {$character->name} (ID: {$characterId})");
            } catch (\Exception $e) {
                Log::error("获取角色信息失败: {$e->getMessage()}");
                return response()->json([
                    'success' => false,
                    'error' => [
                        'message' => '角色不存在',
                        'details' => 'Character not found'
                    ]
                ], 404);
            }

            // 获取或创建银行账户
            try {
                $bankAccount = BankAccount::firstOrCreate(
                    ['character_id' => $characterId],
                    ['silver' => 0, 'gold_ingot' => 0]
                );

                Log::info("银行账户信息: 银两 {$bankAccount->silver}, 金砖 {$bankAccount->gold_ingot}");
            } catch (\Exception $e) {
                Log::error("银行账户创建/查询失败: {$e->getMessage()}");
                Log::error("异常详情: " . $e->getTraceAsString());

                // 尝试使用默认值
                try {
                    // 检查表是否存在
                    if (!\Schema::hasTable('bank_accounts')) {
                        Log::error("bank_accounts表不存在，尝试运行迁移");
                        \Artisan::call('migrate', ['--path' => 'database/migrations/2025_06_15_000001_create_bank_accounts_table.php']);
                        Log::info("银行账户表迁移完成");
                    }

                    $bankAccount = new BankAccount();
                    $bankAccount->character_id = $characterId;
                    $bankAccount->silver = 0;
                    $bankAccount->gold_ingot = 0;
                    $bankAccount->save();

                    Log::info("创建了默认银行账户");
                } catch (\Exception $innerException) {
                    Log::error("创建默认银行账户失败: {$innerException->getMessage()}");
                    return response()->json([
                        'success' => false,
                        'error' => [
                            'message' => '银行账户创建失败',
                            'details' => $innerException->getMessage()
                        ]
                    ], 500);
                }
            }

            // 构建符合前端期望格式的响应 - 将数据直接放在data中而不是嵌套在data.data中
            $responseData = [
                'success' => true,
                'data' => [
                    'account' => [
                        'silver' => (int)$bankAccount->silver,
                        'gold_ingot' => (int)$bankAccount->gold_ingot,
                    ],
                    'character' => [
                        'name' => $character->name,
                        'silver' => (int)$character->silver,
                        'gold' => (int)$character->gold,
                    ]
                ]
            ];

            Log::info("返回银行账户信息: " . json_encode($responseData));
            return response()->json($responseData);

        } catch (\Exception $e) {
            Log::error("获取银行账户信息失败: {$e->getMessage()}");
            Log::error("异常堆栈: {$e->getTraceAsString()}");

            return response()->json([
                'success' => false,
                'error' => [
                    'message' => '获取银行账户信息失败',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * 存款
     *
     * @param Request $request
     * @param string $characterId 角色ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function deposit(Request $request, $characterId)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'currency' => 'required|in:silver,gold_ingot',
                'amount' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'message' => '参数验证失败',
                        'details' => $validator->errors()
                    ]
                ], 422);
            }

            // 获取角色信息
            $character = Character::findOrFail($characterId);

            // 获取或创建银行账户
            $bankAccount = BankAccount::firstOrCreate(
                ['character_id' => $characterId],
                ['silver' => 0, 'gold_ingot' => 0]
            );

            // 处理货币类型
            $currency = $request->input('currency');
            $amount = $request->input('amount');

            // 如果是金砖，需要转换参数名
            $characterCurrency = $currency === 'gold_ingot' ? 'gold' : $currency;

            // 执行存款操作
            $result = $bankAccount->deposit($currency, $amount);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'message' => $result['message']
                    ]
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'account' => [
                        'silver' => $bankAccount->silver,
                        'gold_ingot' => $bankAccount->gold_ingot,
                    ],
                    'character' => [
                        'silver' => $character->silver,
                        'gold' => $character->gold,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('存款操作失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'message' => '存款操作失败',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * 取款
     *
     * @param Request $request
     * @param string $characterId 角色ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function withdraw(Request $request, $characterId)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'currency' => 'required|in:silver,gold_ingot',
                'amount' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'message' => '参数验证失败',
                        'details' => $validator->errors()
                    ]
                ], 422);
            }

            // 获取角色信息
            $character = Character::findOrFail($characterId);

            // 获取银行账户
            $bankAccount = BankAccount::where('character_id', $characterId)->first();

            if (!$bankAccount) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'message' => '银行账户不存在'
                    ]
                ], 400);
            }

            // 处理货币类型
            $currency = $request->input('currency');
            $amount = $request->input('amount');

            // 执行取款操作
            $result = $bankAccount->withdraw($currency, $amount);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'message' => $result['message']
                    ]
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'account' => [
                        'silver' => $bankAccount->silver,
                        'gold_ingot' => $bankAccount->gold_ingot,
                    ],
                    'character' => [
                        'silver' => $character->silver,
                        'gold' => $character->gold,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('取款操作失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'message' => '取款操作失败',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * 获取交易历史记录
     *
     * @param Request $request
     * @param string $characterId 角色ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactionHistory(Request $request, $characterId)
    {
        try {
            Log::info("获取银行交易历史记录 - 开始处理请求，角色ID: {$characterId}");

            // 获取角色信息
            try {
                $character = Character::findOrFail($characterId);
                Log::info("找到角色: {$character->name} (ID: {$characterId})");
            } catch (\Exception $e) {
                Log::error("获取角色信息失败: {$e->getMessage()}");
                // 返回空数据而不是错误，使前端能正常处理
                return response()->json([
                    'success' => true,
                    'data' => [
                        'transactions' => [],
                        'pagination' => [
                            'current_page' => 1,
                            'per_page' => 10,
                            'total' => 0,
                            'last_page' => 1
                        ]
                    ]
                ]);
            }

            // 分页参数
            $page = (int)$request->input('page', 1);
            $perPage = (int)$request->input('per_page', 10);

            // 筛选参数
            $filters = [];
            if ($request->has('currency') && $request->input('currency') !== '') {
                $filters['currency'] = $request->input('currency');
                Log::info("应用筛选: 货币类型 = " . $request->input('currency'));
            }
            if ($request->has('type') && $request->input('type') !== '') {
                $filters['type'] = $request->input('type');
                Log::info("应用筛选: 交易类型 = " . $request->input('type'));
            }

            Log::info("请求参数: " . json_encode($request->all()) . ", 应用筛选条件: " . json_encode($filters));

            try {
                // 调试信息：检查数据库中是否存在交易记录
                $totalCount = \App\Models\BankTransaction::where('character_id', $characterId)->count();
                Log::info("角色 {$characterId} 在数据库中的交易记录总数: {$totalCount}");

                // 获取交易记录
                $query = \App\Models\BankTransaction::where('character_id', $characterId);

                // 应用筛选
                foreach ($filters as $key => $value) {
                    $query->where($key, $value);
                }

                // 排序
                $query->orderBy('created_at', 'desc');

                // 获取所有记录（用于调试）
                $allRecords = $query->get()->toArray();
                Log::info("查询到的记录数(不分页): " . count($allRecords));
                if (count($allRecords) > 0) {
                    Log::info("第一条记录样本: " . json_encode($allRecords[0]));
                }

                // 分页
                $transactions = $query->paginate($perPage, ['*'], 'page', $page);

                Log::info("获取到交易记录: " . $transactions->count() . ", 总数: " . $transactions->total());

                // 处理数据，确保日期格式一致性
                $formattedTransactions = [];
                foreach ($transactions->items() as $transaction) {
                    $item = $transaction->toArray();
                    // 确保日期格式为标准ISO格式
                    $item['created_at'] = date('Y-m-d H:i:s', strtotime($transaction->created_at));
                    $item['updated_at'] = date('Y-m-d H:i:s', strtotime($transaction->updated_at));

                    // 确保所有必要字段都存在
                    $item['id'] = $transaction->id;
                    $item['character_id'] = $transaction->character_id;
                    $item['type'] = $transaction->type;
                    $item['currency'] = $transaction->currency;
                    $item['amount'] = (int)$transaction->amount;
                    $item['balance'] = (int)$transaction->balance;
                    $item['description'] = $transaction->description ?:
                        ($transaction->type == 'deposit' ? '存入' : '取出') .
                        ' ' . $transaction->amount . ' ' .
                        ($transaction->currency == 'silver' ? '银两' : '金砖');

                    $formattedTransactions[] = $item;
                }

                // 构建返回数据
                $responseData = [
                    'success' => true,
                    'data' => [
                        'transactions' => $formattedTransactions,
                        'pagination' => [
                            'current_page' => $transactions->currentPage(),
                            'per_page' => $transactions->perPage(),
                            'total' => $transactions->total(),
                            'last_page' => $transactions->lastPage(),
                        ]
                    ]
                ];

                Log::info("返回交易记录数据: " . json_encode(array_map(function($item) {
                    return [
                        'id' => $item['id'],
                        'type' => $item['type'],
                        'amount' => $item['amount'],
                        'balance' => $item['balance']
                    ];
                }, $formattedTransactions)));

                return response()->json($responseData);
            } catch (\Exception $e) {
                Log::error("获取交易记录查询失败: {$e->getMessage()}");
                Log::error("异常详情: " . $e->getTraceAsString());

                // 返回空数据而不是错误
                return response()->json([
                    'success' => true,
                    'data' => [
                        'transactions' => [],
                        'pagination' => [
                            'current_page' => (int)$page,
                            'per_page' => (int)$perPage,
                            'total' => 0,
                            'last_page' => 1
                        ]
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error("获取银行交易记录失败: {$e->getMessage()}");
            Log::error("异常堆栈: {$e->getTraceAsString()}");

            // 返回空数据而不是错误状态码
            return response()->json([
                'success' => true,  // 即使出错也返回success=true让前端正常展示
                'data' => [
                    'transactions' => [],
                    'pagination' => [
                        'current_page' => (int)($request->input('page', 1)),
                        'per_page' => (int)($request->input('per_page', 10)),
                        'total' => 0,
                        'last_page' => 1
                    ]
                ],
                'error' => [
                    'message' => '获取交易记录出现问题，将展示空数据',
                    'details' => $e->getMessage()
                ]
            ]);
        }
    }
}
