<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CharacterLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'character_id',
        'location_id',
        'arrived_at',
    ];

    protected $casts = [
        'arrived_at' => 'datetime',
    ];

    /**
     * 角色
     */
    public function character(): BelongsTo
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 位置
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * 获取角色当前位置
     * @param int $characterId 角色ID
     * @return CharacterLocation|null
     */
    public static function getCurrentLocation($characterId)
    {
        return static::where('character_id', $characterId)->first();
    }

    /**
     * 设置角色位置
     * @param int $characterId 角色ID
     * @param int $locationId 位置ID
     * @return CharacterLocation
     */
    public static function setLocation($characterId, $locationId)
    {
        return static::updateOrCreate(
            ['character_id' => $characterId],
            [
                'location_id' => $locationId,
                'arrived_at' => now()
            ]
        );
    }

    /**
     * 获取在指定位置的所有角色
     * @param int $locationId 位置ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getCharactersAtLocation($locationId)
    {
        return static::where('location_id', $locationId)
            ->with('character')
            ->get();
    }
}
