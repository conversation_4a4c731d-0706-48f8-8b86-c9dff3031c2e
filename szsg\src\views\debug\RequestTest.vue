<template>
  <div class="request-test">
    <h2>请求测试工具</h2>
    <p>用于测试和调试HTTP 431错误</p>

    <div class="test-section">
      <h3>存储状态</h3>
      <div class="storage-info">
        <p>总存储大小: {{ formatBytes(storageSize) }}</p>
        <p>存储项目数: {{ storageCount }}</p>
        <p>最大项目: {{ largestItem.key }} ({{ formatBytes(largestItem.size) }})</p>
      </div>
    </div>

    <div class="test-section">
      <h3>请求测试</h3>
      <div class="test-buttons">
        <button @click="testRegionRequest" :disabled="isLoading">
          {{ isLoading ? '测试中...' : '测试大区API' }}
        </button>
        <button @click="emergencyClean" :disabled="isLoading">
          紧急清理存储
        </button>
        <button @click="clearCookies" :disabled="isLoading">
          清理Cookies
        </button>
        <button @click="testDirectRequest" :disabled="isLoading">
          直接请求测试
        </button>
        <button @click="fix431AndTest" :disabled="isLoading">
          修复431错误并测试
        </button>
        <button @click="refreshInfo">
          刷新信息
        </button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试结果</h3>
      <div class="result-area">
        <pre>{{ testResult }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>请求头信息</h3>
      <div class="headers-info">
        <p>预估请求头大小: {{ estimatedHeaderSize }} bytes</p>
        <div class="headers-list">
          <div v-for="(value, key) in estimatedHeaders" :key="key" class="header-item">
            <span class="header-key">{{ key }}:</span>
            <span class="header-value">{{ value.substring(0, 100) }}{{ value.length > 100 ? '...' : '' }}</span>
            <span class="header-size">({{ key.length + value.length }} bytes)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRegions } from '@/api/services/regionService.js'
import { emergencyCleanupStorage, clearAllCookies } from '@/utils/storage.js'
import { fix431Error, check431Risk, minimalFetch } from '@/utils/fix431Error.js'
import logger from '@/utils/logger'

export default {
  name: 'RequestTest',
  data() {
    return {
      isLoading: false,
      testResult: '点击"测试大区API"开始测试',
      storageSize: 0,
      storageCount: 0,
      largestItem: { key: '', size: 0 },
      estimatedHeaders: {},
      estimatedHeaderSize: 0
    }
  },
  mounted() {
    this.refreshInfo()
  },
  methods: {
    async testRegionRequest() {
      this.isLoading = true
      this.testResult = '正在测试大区API请求...'
      
      try {
        const startTime = Date.now()
        const result = await getRegions()
        const endTime = Date.now()
        
        this.testResult = `✅ 请求成功！
时间: ${endTime - startTime}ms
结果: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `❌ 请求失败！
错误代码: ${error.code}
错误信息: ${error.message}
需要清理: ${error.needCleanup ? '是' : '否'}
详细信息: ${JSON.stringify(error, null, 2)}`
        
        logger.error('[RequestTest] 测试失败:', error)
      } finally {
        this.isLoading = false
        this.refreshInfo()
      }
    },

    async emergencyClean() {
      this.isLoading = true
      this.testResult = '正在执行紧急清理...'

      try {
        const success = emergencyCleanupStorage()
        if (success) {
          this.testResult = '✅ 紧急清理完成！'
        } else {
          this.testResult = '❌ 紧急清理失败！'
        }
      } catch (error) {
        this.testResult = `❌ 紧急清理出错: ${error.message}`
      } finally {
        this.isLoading = false
        this.refreshInfo()
      }
    },

    async clearCookies() {
      this.isLoading = true
      this.testResult = '正在清理Cookies...'

      try {
        const success = clearAllCookies()
        if (success) {
          this.testResult = '✅ Cookies清理完成！'
        } else {
          this.testResult = '❌ Cookies清理失败！'
        }
      } catch (error) {
        this.testResult = `❌ 清理Cookies出错: ${error.message}`
      } finally {
        this.isLoading = false
        this.refreshInfo()
      }
    },

    async testDirectRequest() {
      this.isLoading = true
      this.testResult = '正在执行直接请求测试...'

      try {
        const startTime = Date.now()

        // 使用最小化的fetch请求
        const data = await minimalFetch('/api/regions')

        const endTime = Date.now()

        this.testResult = `✅ 直接请求成功！
时间: ${endTime - startTime}ms
结果: ${JSON.stringify(data, null, 2)}`
      } catch (error) {
        this.testResult = `❌ 直接请求出错: ${error.message}`
        logger.error('[RequestTest] 直接请求失败:', error)
      } finally {
        this.isLoading = false
        this.refreshInfo()
      }
    },

    async fix431AndTest() {
      this.isLoading = true
      this.testResult = '正在修复431错误并测试...'

      try {
        // 1. 检查风险
        const riskCheck = check431Risk()
        this.testResult += `\n\n风险检查结果:\n${JSON.stringify(riskCheck, null, 2)}`

        // 2. 执行修复
        if (riskCheck.hasRisk) {
          this.testResult += '\n\n检测到风险，正在修复...'
          const fixResult = fix431Error()
          this.testResult += `\n修复结果: ${fixResult ? '成功' : '失败'}`
        }

        // 3. 测试请求
        this.testResult += '\n\n正在测试大区API...'
        const startTime = Date.now()

        try {
          const result = await getRegions()
          const endTime = Date.now()

          this.testResult += `\n\n✅ 大区API测试成功！
时间: ${endTime - startTime}ms
结果: ${JSON.stringify(result, null, 2)}`
        } catch (apiError) {
          this.testResult += `\n\n❌ 大区API测试失败: ${apiError.message}`

          // 尝试最小化请求
          try {
            this.testResult += '\n\n尝试最小化请求...'
            const minimalResult = await minimalFetch('/api/regions')
            this.testResult += `\n\n✅ 最小化请求成功！
结果: ${JSON.stringify(minimalResult, null, 2)}`
          } catch (minimalError) {
            this.testResult += `\n\n❌ 最小化请求也失败: ${minimalError.message}`
          }
        }

      } catch (error) {
        this.testResult += `\n\n❌ 修复过程出错: ${error.message}`
        logger.error('[RequestTest] 431修复失败:', error)
      } finally {
        this.isLoading = false
        this.refreshInfo()
      }
    },

    refreshInfo() {
      this.calculateStorageInfo()
      this.calculateHeaderInfo()
    },

    calculateStorageInfo() {
      let totalSize = 0
      let count = 0
      let largest = { key: '', size: 0 }

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        const value = localStorage.getItem(key) || ''
        const size = key.length + value.length
        
        totalSize += size
        count++
        
        if (size > largest.size) {
          largest = { key, size }
        }
      }

      this.storageSize = totalSize
      this.storageCount = count
      this.largestItem = largest
    },

    calculateHeaderInfo() {
      // 模拟请求头
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': navigator.userAgent,
        'Referer': window.location.href,
        'Accept-Language': navigator.language,
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache'
      }

      // 添加可能的认证头
      try {
        const token = this.$store?.state?.auth?.token
        if (token && typeof token === 'string') {
          headers['Authorization'] = `Bearer ${token}`
        }
      } catch (e) {
        // 忽略错误
      }

      this.estimatedHeaders = headers
      this.estimatedHeaderSize = Object.keys(headers).reduce((size, key) => {
        return size + key.length + (headers[key] || '').length
      }, 0)
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.request-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

.storage-info p {
  margin: 5px 0;
  font-family: monospace;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-buttons button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.test-buttons button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.test-buttons button:hover:not(:disabled) {
  background: #0056b3;
}

.result-area {
  background: #000;
  color: #0f0;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.headers-info p {
  font-weight: bold;
  margin-bottom: 10px;
}

.headers-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
  background: white;
}

.header-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.header-key {
  font-weight: bold;
  color: #007bff;
  min-width: 150px;
}

.header-value {
  flex: 1;
  margin: 0 10px;
  color: #333;
}

.header-size {
  color: #666;
  font-size: 10px;
}
</style>
