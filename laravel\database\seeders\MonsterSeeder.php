<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MonsterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 首先检查是否有位置数据，如果没有则创建一个默认位置
        $defaultLocationId = DB::table('locations')->where('name', '花果山')->value('id');
        if (!$defaultLocationId) {
            $defaultLocationId = DB::table('locations')->insertGetId([
                'name' => '花果山',
                'description' => '美猴王的故乡',
                'x' => 0,
                'y' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        $monsters = [
            [
                'name' => '灵猴',
                'title' => '花果山守护者',
                'description' => '花果山上的灵猴，身手敏捷，善于攀爬跳跃。',
                'level' => 5,
                'type' => 'beast',
                'element' => 'none',
                'size' => 'small',
                'location_id' => $defaultLocationId,
                'x' => 0,
                'y' => 0,
                'threat_level' => 1,
                'avatar' => '/static/game/UI/tx/monster/monkey.png',
                'max_health' => 80,
                'current_health' => 80,
                'max_mana' => 30,
                'current_mana' => 30,
                'attack' => 15,
                'defense' => 8,
                'speed' => 18,
                'exp_reward' => 25,
                'silver_reward' => 8,
                'drop_items' => json_encode([
                    [
                        'item_id' => 'monkey_fur',
                        'name' => '猴毛',
                        'quantity' => 1,
                        'chance' => 30
                    ],
                    [
                        'item_id' => 'small_health_potion',
                        'name' => '小型生命药水',
                        'quantity' => 1,
                        'chance' => 15
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '山魈',
                'title' => '山林恶鬼',
                'description' => '栖息在深山老林中的恶鬼，力大无穷，性情凶暴。',
                'level' => 8,
                'type' => 'demon',
                'element' => 'dark',
                'size' => 'medium',
                'location_id' => $defaultLocationId,
                'x' => 100,
                'y' => 100,
                'threat_level' => 2,
                'avatar' => '/static/game/UI/tx/monster/demon.png',
                'max_health' => 120,
                'current_health' => 120,
                'max_mana' => 40,
                'current_mana' => 40,
                'attack' => 22,
                'defense' => 15,
                'speed' => 12,
                'exp_reward' => 40,
                'silver_reward' => 15,
                'drop_items' => json_encode([
                    [
                        'item_id' => 'demon_claw',
                        'name' => '魔爪',
                        'quantity' => 1,
                        'chance' => 25
                    ],
                    [
                        'item_id' => 'dark_essence',
                        'name' => '暗黑精华',
                        'quantity' => 1,
                        'chance' => 10
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '水灵',
                'title' => '水帘洞精灵',
                'description' => '水帘洞中的水系精灵，能够操控水流进行攻击。',
                'level' => 6,
                'type' => 'elemental',
                'element' => 'water',
                'size' => 'small',
                'location_id' => $defaultLocationId,
                'x' => 200,
                'y' => 200,
                'threat_level' => 1,
                'avatar' => '/static/game/UI/tx/monster/water_spirit.png',
                'max_health' => 90,
                'current_health' => 90,
                'max_mana' => 60,
                'current_mana' => 60,
                'attack' => 18,
                'defense' => 10,
                'speed' => 15,
                'exp_reward' => 30,
                'silver_reward' => 12,
                'drop_items' => json_encode([
                    [
                        'item_id' => 'water_crystal',
                        'name' => '水晶石',
                        'quantity' => 1,
                        'chance' => 20
                    ],
                    [
                        'item_id' => 'mp_potion',
                        'name' => '魔法药水',
                        'quantity' => 1,
                        'chance' => 18
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        DB::table('monsters')->insert($monsters);
    }
}
