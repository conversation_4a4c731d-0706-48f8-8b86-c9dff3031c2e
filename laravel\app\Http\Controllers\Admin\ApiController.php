<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use App\Models\Region;
use App\Models\Location;

class ApiController extends Controller
{
    /**
     * 获取地图中的角色列表
     */
    public function getMapCharacters($mapId)
    {
        try {
            $map = Region::findOrFail($mapId);

            // 获取该地图下所有角色
            $characters = Character::where('region_id', $mapId)
                ->with(['location:id,name', 'user:id,name'])
                ->select('id', 'name', 'level', 'profession', 'location_id', 'user_id', 'status', 'created_at')
                ->orderBy('level', 'desc')
                ->limit(100)
                ->get()
                ->map(function ($character) {
                    return [
                        'id' => $character->id,
                        'name' => $character->name,
                        'level' => $character->level,
                        'profession' => $character->profession,
                        'location_name' => $character->location ? $character->location->name : '未知',
                        'user_name' => $character->user ? $character->user->name : '未知',
                        'status' => $character->status,
                        'created_at' => $character->created_at->format('Y-m-d H:i:s')
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $characters
            ]);
        } catch (\Exception $e) {
            \Log::error('获取地图角色列表失败: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => '获取角色列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取地图统计数据
     */
    public function getMapStats($mapId)
    {
        try {
            $map = Region::findOrFail($mapId);

            // 获取过去7天的在线人数趋势
            $playerTrend = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                // 这里应该从日志或统计表中获取真实数据
                // 这里用随机数模拟
                $playerTrend[] = [
                    'date' => $date,
                    'count' => $i == 0 ? $map->player_count : rand(100, 300)
                ];
            }

            // 获取位置类型分布
            $locationTypes = Location::where('region_id', $mapId)
                ->selectRaw('type, count(*) as count')
                ->groupBy('type')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->type => $item->count];
                })
                ->toArray();

            return response()->json([
                'success' => true,
                'data' => [
                    'player_trend' => $playerTrend,
                    'location_types' => $locationTypes
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取地图统计数据失败: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取地图的位置数据
     */
    public function getLocations(Request $request)
    {
        try {
            $regionId = $request->input('region_id');
            $locations = \App\Models\Location::where('region_id', $regionId)
                ->where('is_active', true)
                ->orderBy('name')
                ->get(['id', 'name', 'type', 'is_safe']);

            return response()->json([
                'success' => true,
                'data' => $locations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
