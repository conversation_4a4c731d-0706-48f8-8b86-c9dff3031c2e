<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;
use App\Models\LocationConnection;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建基础位置
        $locations = [
            [
                'name' => '城镇中心',
                'type' => 'town',
                'description' => '繁华的城镇中心，这里有各种商店和设施',
                'x' => 0,
                'y' => 0,
                'level_requirement' => 1,
                'is_safe' => true,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'town_guard',
                        'name' => '城镇守卫',
                        'type' => 'npc',
                        'level' => 20,
                        'services' => ['信息', '任务']
                    ],
                    [
                        'id' => 'merchant',
                        'name' => '商人',
                        'type' => 'npc',
                        'level' => 15,
                        'services' => ['交易', '物品']
                    ]
                ],
                'monsters' => []
            ],
            [
                'name' => '森林入口',
                'type' => 'forest',
                'description' => '古老森林的入口，空气中弥漫着神秘的气息',
                'x' => -1,
                'y' => 1,
                'level_requirement' => 3,
                'is_safe' => false,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'forest_ranger',
                        'name' => '森林守护者',
                        'type' => 'npc',
                        'level' => 25,
                        'services' => ['指引', '任务']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'forest_wolf',
                        'name' => '森林狼',
                        'type' => 'monster',
                        'level' => 8,
                        'description' => '栖息在森林中的野狼，具有一定的攻击性'
                    ],
                    [
                        'id' => 'tree_spirit',
                        'name' => '树精',
                        'type' => 'monster',
                        'level' => 12,
                        'description' => '森林中的精灵，拥有自然魔法'
                    ]
                ]
            ],
            [
                'name' => '森林深处',
                'type' => 'forest',
                'description' => '森林的深处，危险与机遇并存',
                'x' => -2,
                'y' => 2,
                'level_requirement' => 10,
                'is_safe' => false,
                'facilities' => [],
                'npcs' => [],
                'monsters' => [
                    [
                        'id' => 'forest_bear',
                        'name' => '森林熊',
                        'type' => 'monster',
                        'level' => 15,
                        'description' => '强大的森林霸主，拥有惊人的力量'
                    ],
                    [
                        'id' => 'ancient_ent',
                        'name' => '远古树人',
                        'type' => 'monster',
                        'level' => 20,
                        'description' => '古老的树人，守护着森林的秘密'
                    ]
                ]
            ],
            [
                'name' => '山脚',
                'type' => 'mountain',
                'description' => '高山的脚下，可以看到通往山顶的崎岖小径',
                'x' => 1,
                'y' => 1,
                'level_requirement' => 5,
                'is_safe' => false,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'mountain_guide',
                        'name' => '登山向导',
                        'type' => 'npc',
                        'level' => 30,
                        'services' => ['指引', '装备']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'mountain_goat',
                        'name' => '山羊',
                        'type' => 'monster',
                        'level' => 5,
                        'description' => '生活在山区的野生山羊'
                    ]
                ]
            ],
            [
                'name' => '山顶',
                'type' => 'mountain',
                'description' => '高山之巅，可以俯瞰整个大陆的壮丽景色',
                'x' => 2,
                'y' => 2,
                'level_requirement' => 15,
                'is_safe' => true,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'hermit',
                        'name' => '隐士',
                        'type' => 'npc',
                        'level' => 50,
                        'services' => ['修炼', '秘籍']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'mountain_eagle',
                        'name' => '山鹰',
                        'type' => 'monster',
                        'level' => 18,
                        'description' => '翱翔在山顶的巨鹰，拥有敏锐的视力'
                    ]
                ]
            ],
            [
                'name' => '地下城入口',
                'type' => 'dungeon',
                'description' => '通往地下迷宫的入口，散发着阴森的气息',
                'x' => 0,
                'y' => -1,
                'level_requirement' => 8,
                'is_safe' => false,
                'facilities' => [],
                'npcs' => [
                    [
                        'id' => 'dungeon_keeper',
                        'name' => '地下城守护者',
                        'type' => 'npc',
                        'level' => 35,
                        'services' => ['警告', '信息']
                    ]
                ],
                'monsters' => [
                    [
                        'id' => 'skeleton_warrior',
                        'name' => '骷髅战士',
                        'type' => 'monster',
                        'level' => 12,
                        'description' => '不死的骷髅战士，手持锈蚀的武器'
                    ]
                ]
            ],
            [
                'name' => '地下城深层',
                'type' => 'dungeon',
                'description' => '地下城的深层，充满了未知的危险',
                'x' => 0,
                'y' => -2,
                'level_requirement' => 20,
                'is_safe' => false,
                'facilities' => [],
                'npcs' => [],
                'monsters' => [
                    [
                        'id' => 'shadow_demon',
                        'name' => '暗影恶魔',
                        'type' => 'monster',
                        'level' => 25,
                        'description' => '来自暗影位面的恶魔，拥有强大的暗黑魔法'
                    ],
                    [
                        'id' => 'dungeon_boss',
                        'name' => '地下城领主',
                        'type' => 'monster',
                        'level' => 30,
                        'description' => '地下城的统治者，拥有恐怖的力量'
                    ]
                ]
            ]
        ];

        // 创建位置
        $createdLocations = [];
        foreach ($locations as $locationData) {
            $location = Location::create($locationData);
            $createdLocations[$locationData['name']] = $location;
        }

        // 创建位置连接（已完全移除体力消耗功能）
        $connections = [
            // 城镇中心的连接
            ['城镇中心', '森林入口', 1, 5, 0, 1],
            ['城镇中心', '山脚', 1, 5, 0, 1],
            ['城镇中心', '地下城入口', 1, 3, 0, 1],

            // 森林区域的连接
            ['森林入口', '城镇中心', 1, 5, 0, 1],
            ['森林入口', '森林深处', 1, 8, 0, 5],

            // 森林深处的连接
            ['森林深处', '森林入口', 1, 8, 0, 5],

            // 山脉区域的连接
            ['山脚', '城镇中心', 1, 5, 0, 1],
            ['山脚', '山顶', 2, 15, 0, 10],

            // 山顶的连接
            ['山顶', '山脚', 2, 15, 0, 10],

            // 地下城区域的连接
            ['地下城入口', '城镇中心', 1, 3, 0, 1],
            ['地下城入口', '地下城深层', 1, 10, 0, 15],

            // 地下城深层的连接
            ['地下城深层', '地下城入口', 1, 10, 0, 15],
        ];

        foreach ($connections as $connection) {
            [$fromName, $toName, $distance, $timeCost, $silverCost, $levelReq] = $connection;

            LocationConnection::create([
                'from_location_id' => $createdLocations[$fromName]->id,
                'to_location_id' => $createdLocations[$toName]->id,
                'distance' => $distance,
                'time_cost' => $timeCost,
                'silver_cost' => $silverCost,
                'level_requirement' => $levelReq,
                'is_active' => true,
            ]);
        }
    }
}
