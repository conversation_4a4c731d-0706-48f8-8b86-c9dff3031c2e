/**
 * 专门用于修复HTTP 431错误的工具函数
 */
import logger from './logger.js';

/**
 * 立即清理所有可能导致431错误的数据
 */
export function fix431Error() {
  logger.info('[Fix431] 开始修复HTTP 431错误...');

  try {
    // 1. 清理所有cookies
    clearAllCookies();

    // 2. 清理localStorage
    clearLargeLocalStorageItems();

    // 3. 清理sessionStorage
    try {
      sessionStorage.clear();
    } catch (e) {
      logger.warn('[Fix431] 清理sessionStorage失败:', e);
    }

    // 4. 清理可能的缓存
    clearBrowserCache();

    logger.info('[Fix431] HTTP 431错误修复完成');
    return true;
  } catch (error) {
    logger.error('[Fix431] 修复失败:', error);
    return false;
  }
}

/**
 * 清理所有cookies
 */
function clearAllCookies() {
  try {
    logger.debug('[Fix431] 清理cookies...');

    // 获取所有cookies
    const cookies = document.cookie.split(';');

    // 清理每个cookie，尝试多种路径和域名
    cookies.forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      if (name) {
        // 多种清理方式确保彻底删除
        const expireDate = 'Thu, 01 Jan 1970 00:00:00 GMT';
        const paths = ['/', '/api', '/api/'];
        const domains = ['', 'localhost', '.localhost', '127.0.0.1', '.127.0.0.1'];

        paths.forEach(path => {
          domains.forEach(domain => {
            const domainStr = domain ? `;domain=${domain}` : '';
            document.cookie = `${name}=;expires=${expireDate};path=${path}${domainStr}`;
          });
        });
      }
    });

    logger.debug('[Fix431] Cookies清理完成');
  } catch (e) {
    logger.error('[Fix431] 清理cookies失败:', e);
  }
}

/**
 * 清理大型localStorage项目
 */
function clearLargeLocalStorageItems() {
  try {
    logger.debug('[Fix431] 清理localStorage...');

    const keysToRemove = [];

    // 检查所有localStorage项目
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key) || '';
        const size = key.length + value.length;

        // 清理大于5KB的项目，或者特定的缓存项目
        if (size > 5120 ||
            key.startsWith('SZXY_CACHE_') ||
            key.startsWith('cache_') ||
            key.startsWith('vuex') ||
            key.includes('token') ||
            key.includes('session')) {
          keysToRemove.push(key);
        }
      }
    }

    // 删除标记的项目
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      logger.debug(`[Fix431] 删除localStorage项目: ${key}`);
    });

    // 重新创建最小化的游戏状态
    const minimalState = {
      auth: {
        isAuthenticated: false
      },
      game: {
        settings: {
          language: 'zh-CN'
        }
      }
    };

    localStorage.setItem('szxy-game-state', JSON.stringify(minimalState));

    logger.debug('[Fix431] localStorage清理完成');
  } catch (e) {
    logger.error('[Fix431] 清理localStorage失败:', e);
  }
}

/**
 * 尝试清理浏览器缓存
 */
function clearBrowserCache() {
  try {
    logger.debug('[Fix431] 尝试清理浏览器缓存...');

    // 如果支持，清理缓存存储
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      }).catch(e => {
        logger.warn('[Fix431] 清理缓存存储失败:', e);
      });
    }

    // 清理可能的IndexedDB
    if ('indexedDB' in window) {
      try {
        // 这里可以添加清理IndexedDB的逻辑
        logger.debug('[Fix431] IndexedDB清理跳过（需要具体实现）');
      } catch (e) {
        logger.warn('[Fix431] 清理IndexedDB失败:', e);
      }
    }

    logger.debug('[Fix431] 浏览器缓存清理完成');
  } catch (e) {
    logger.error('[Fix431] 清理浏览器缓存失败:', e);
  }
}

/**
 * 检查是否可能出现431错误
 */
export function check431Risk() {
  try {
    let totalSize = 0;
    
    // 检查localStorage大小
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key) || '';
        totalSize += key.length + value.length;
      }
    }
    
    // 检查cookies大小
    const cookieSize = document.cookie.length;
    
    // 估算请求头大小
    const estimatedHeaderSize = cookieSize + 2048; // 基础头部大小
    
    logger.debug(`[Fix431] 风险检查 - localStorage: ${totalSize} bytes, cookies: ${cookieSize} bytes, 估算请求头: ${estimatedHeaderSize} bytes`);
    
    // 如果估算的请求头大小超过8KB，认为有风险
    return {
      hasRisk: estimatedHeaderSize > 8192,
      localStorageSize: totalSize,
      cookieSize: cookieSize,
      estimatedHeaderSize: estimatedHeaderSize
    };
  } catch (e) {
    logger.error('[Fix431] 风险检查失败:', e);
    return {
      hasRisk: true,
      error: e.message
    };
  }
}

/**
 * 创建最小化的fetch请求
 */
export async function minimalFetch(url, options = {}) {
  try {
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'Accept': 'application/json'
      },
      credentials: 'omit',
      cache: 'no-cache',
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    logger.error('[Fix431] 最小化请求失败:', error);
    throw error;
  }
}
