@extends('admin.layouts.app')

@section('title', '系统首页')

@section('breadcrumb')
<a><cite>系统首页</cite></a>
@endsection

@section('page-title', '系统概览')

@section('content')
<!-- 系统概览 -->
<div class="layui-row layui-col-space15">
  <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
    <div class="layui-card">
      <div class="layui-card-header">用户总数</div>
      <div class="layui-card-body">
        <p class="layui-text-center" style="font-size: 30px; text-align: center;">
          <i class="layui-icon layui-icon-user" style="color: #1E9FFF;"></i>
          {{ $stats['users'] ?? 0 }}
        </p>
      </div>
    </div>
  </div>
  <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
    <div class="layui-card">
      <div class="layui-card-header">角色总数</div>
      <div class="layui-card-body">
        <p class="layui-text-center" style="font-size: 30px; text-align: center;">
          <i class="layui-icon layui-icon-username" style="color: #01AAED;"></i>
          {{ $stats['characters'] ?? 0 }}
        </p>
      </div>
    </div>
  </div>
  <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
    <div class="layui-card">
      <div class="layui-card-header">战斗总数</div>
      <div class="layui-card-body">
        <p class="layui-text-center" style="font-size: 30px; text-align: center;">
          <i class="layui-icon layui-icon-fire" style="color: #FF5722;"></i>
          {{ $stats['battles'] ?? 0 }}
        </p>
      </div>
    </div>
  </div>
  <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
    <div class="layui-card">
      <div class="layui-card-header">活跃用户</div>
      <div class="layui-card-body">
        <p class="layui-text-center" style="font-size: 30px; text-align: center;">
          <i class="layui-icon layui-icon-star" style="color: #FFB800;"></i>
          {{ $stats['active_users'] ?? 0 }}
        </p>
      </div>
    </div>
  </div>
</div>

<!-- 图表数据 -->
<div class="layui-row layui-col-space15">
  <div class="layui-col-md6">
    <div class="layui-card">
      <div class="layui-card-header">最近7天新注册用户</div>
      <div class="layui-card-body">
        <div id="reg-chart" style="width: 100%; height: 300px;"></div>
      </div>
    </div>
  </div>
  <div class="layui-col-md6">
    <div class="layui-card">
      <div class="layui-card-header">最近7天战斗数据</div>
      <div class="layui-card-body">
        <div id="battle-chart" style="width: 100%; height: 300px;"></div>
      </div>
    </div>
  </div>
</div>

<!-- 最近用户列表 -->
<div class="layui-card">
  <div class="layui-card-header">最近注册用户</div>
  <div class="layui-card-body">
    <table class="layui-table">
      <colgroup>
        <col width="80">
        <col width="200">
        <col width="180">
        <col width="120">
      </colgroup>
      <thead>
        <tr>
          <th>ID</th>
          <th>用户名</th>
          <th>注册时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        @foreach($recentUsers ?? [] as $user)
        <tr>
          <td>{{ $user->id }}</td>
          <td>{{ $user->name }}</td>
          <td>{{ $user->created_at }}</td>
          <td>
            <a href="{{ route('admin.users.edit', $user->id) }}" class="layui-btn layui-btn-xs">编辑</a>
          </td>
        </tr>
        @endforeach

        @if(empty($recentUsers) || count($recentUsers) === 0)
        <tr>
          <td colspan="4" style="text-align: center;">暂无数据</td>
        </tr>
        @endif
      </tbody>
    </table>
  </div>
</div>
@endsection

@section('js')
<!-- 引入ECharts -->
<script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.0/echarts.min.js"></script>
<script>
  layui.use(['layer', 'element'], function(){
    var $ = layui.jquery;
    var layer = layui.layer;
    var element = layui.element;

    // 获取统计数据
    $.get("{{ route('admin.stats') }}", function(res) {
      // 初始化图表
      initCharts(res);
    });

    // 初始化ECharts图表
    function initCharts(data) {
      var regChart = echarts.init(document.getElementById('reg-chart'));
      var battleChart = echarts.init(document.getElementById('battle-chart'));

      // 模拟数据(如果API未返回数据)
      if (!data || !data.registrations) {
        data = {
          registrations: [
            {date: '2023-07-10', count: 5},
            {date: '2023-07-11', count: 7},
            {date: '2023-07-12', count: 3},
            {date: '2023-07-13', count: 9},
            {date: '2023-07-14', count: 12},
            {date: '2023-07-15', count: 8},
            {date: '2023-07-16', count: 15}
          ],
          battles: [
            {date: '2023-07-10', count: 25},
            {date: '2023-07-11', count: 37},
            {date: '2023-07-12', count: 43},
            {date: '2023-07-13', count: 29},
            {date: '2023-07-14', count: 52},
            {date: '2023-07-15', count: 48},
            {date: '2023-07-16', count: 65}
          ]
        };
      }

      // 处理注册数据
      var regDates = data.registrations.map(function(item) {
        return item.date;
      });
      var regCounts = data.registrations.map(function(item) {
        return item.count;
      });

      // 处理战斗数据
      var battleDates = data.battles.map(function(item) {
        return item.date;
      });
      var battleCounts = data.battles.map(function(item) {
        return item.count;
      });

      // 注册用户图表配置
      var regOption = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: regDates
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: regCounts,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#1E9FFF'
          },
          lineStyle: {
            color: '#1E9FFF',
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(30, 159, 255, 0.5)'
              }, {
                offset: 1, color: 'rgba(30, 159, 255, 0.1)'
              }]
            }
          }
        }]
      };

      // 战斗数据图表配置
      var battleOption = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: battleDates
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: battleCounts,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#FF5722'
          },
          lineStyle: {
            color: '#FF5722',
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(255, 87, 34, 0.5)'
              }, {
                offset: 1, color: 'rgba(255, 87, 34, 0.1)'
              }]
            }
          }
        }]
      };

      // 设置图表配置项
      regChart.setOption(regOption);
      battleChart.setOption(battleOption);

      // 窗口大小变化时，重新调整图表大小
      window.addEventListener('resize', function() {
        regChart.resize();
        battleChart.resize();
      });
    }
  });
</script>
@endsection
