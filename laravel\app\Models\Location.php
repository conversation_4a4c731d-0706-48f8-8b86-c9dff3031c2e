<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Location extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'region_id',
        'name',
        'description',
        'type',
        'x',
        'y',
        'level_requirement',
        'is_safe',
        'is_active',
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'x' => 'integer',
        'y' => 'integer',
        'level_requirement' => 'integer',
        'is_safe' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * 获取所属地图
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 获取在此位置的角色
     */
    public function characters(): HasMany
    {
        return $this->hasMany(Character::class, 'location_id');
    }

    /**
     * 获取此位置的怪物
     */
    public function monsters(): HasMany
    {
        return $this->hasMany(Monster::class, 'spawn_location_id');
    }

    /**
     * 获取此位置的NPC
     */
    public function npcs(): HasMany
    {
        return $this->hasMany(Npc::class, 'location_id');
    }

    /**
     * 获取从此位置出发的连接
     */
    public function connectionsFrom(): HasMany
    {
        return $this->hasMany(LocationConnection::class, 'from_location_id');
    }

    /**
     * 获取到达此位置的连接
     */
    public function connectionsTo(): HasMany
    {
        return $this->hasMany(LocationConnection::class, 'to_location_id');
    }

    /**
     * 获取可以从此位置到达的位置
     */
    public function connectedLocations(): BelongsToMany
    {
        return $this->belongsToMany(
            Location::class,
            'location_connections',
            'from_location_id',
            'to_location_id'
        )->withPivot([
            'distance',
            'time_cost',
            'silver_cost',
            'level_requirement',
            'is_active'
        ])->wherePivot('is_active', true);
    }

    /**
     * 获取可移动的位置列表
     * @param int $characterLevel 角色等级
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAvailableLocations($characterLevel = 1)
    {
        return $this->connectedLocations()
            ->wherePivot('level_requirement', '<=', $characterLevel)
            ->get();
    }

    /**
     * 检查是否可以移动到指定位置
     * @param int $targetLocationId 目标位置ID
     * @param int $characterLevel 角色等级
     * @return bool
     */
    public function canMoveTo($targetLocationId, $characterLevel = 1)
    {
        return $this->connectionsFrom()
            ->where('to_location_id', $targetLocationId)
            ->where('level_requirement', '<=', $characterLevel)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * 获取移动到指定位置的消耗
     * @param int $targetLocationId 目标位置ID
     * @return LocationConnection|null
     */
    public function getMovementCost($targetLocationId)
    {
        return $this->connectionsFrom()
            ->where('to_location_id', $targetLocationId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * 根据类型获取位置
     * @param string $type 位置类型
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function byType($type)
    {
        return static::where('type', $type);
    }

    /**
     * 根据等级要求获取位置
     * @param int $level 角色等级
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function byLevelRequirement($level)
    {
        return static::where('level_requirement', '<=', $level);
    }

    /**
     * 获取安全区域
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function safeAreas()
    {
        return static::where('is_safe', true);
    }

    /**
     * 获取危险区域
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function dangerousAreas()
    {
        return static::where('is_safe', false);
    }

    /**
     * 计算到另一个位置的直线距离
     * @param Location $location 目标位置
     * @return float
     */
    public function distanceTo(Location $location)
    {
        $dx = $this->x - $location->x;
        $dy = $this->y - $location->y;
        return sqrt($dx * $dx + $dy * $dy);
    }

    /**
     * 获取附近的位置
     * @param int $radius 半径
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function nearbyLocations($radius = 2)
    {
        return static::where('id', '!=', $this->id)
            ->where('x', '>=', $this->x - $radius)
            ->where('x', '<=', $this->x + $radius)
            ->where('y', '>=', $this->y - $radius)
            ->where('y', '<=', $this->y + $radius)
            ->get();
    }
}
