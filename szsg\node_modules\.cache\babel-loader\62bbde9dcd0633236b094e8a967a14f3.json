{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Friends.vue", "mtime": 1749718635730}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3Iuc29tZS5qcyI7CmltcG9ydCBHYW1lTGF5b3V0IGZyb20gJ0AvbGF5b3V0cy9HYW1lTGF5b3V0LnZ1ZSc7CmltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7CmltcG9ydCB7IEFQSV9CQVNFX1VSTCB9IGZyb20gJ0AvYXBpL2NvbmZpZy5qcyc7CmltcG9ydCB7IEVSUk9SX01FU1NBR0VTIH0gZnJvbSAnQC9hcGkvY29uc3RhbnRzLmpzJzsKaW1wb3J0IGxvZ2dlciBmcm9tICdAL3V0aWxzL2xvZ2dlcic7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBHYW1lTGF5b3V0CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFRhYjogMCwKICAgICAgZnJpZW5kc1RhYnM6IFt7CiAgICAgICAgbmFtZTogJ+WlveWPi+WIl+ihqCcsCiAgICAgICAgdHlwZTogJ2ZyaWVuZHMnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5aW95Y+L55Sz6K+3JywKICAgICAgICB0eXBlOiAncmVxdWVzdHMnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5re75Yqg5aW95Y+LJywKICAgICAgICB0eXBlOiAnYWRkJwogICAgICB9XSwKICAgICAgZnJpZW5kczogW10sCiAgICAgIGZyaWVuZFJlcXVlc3RzOiBbXSwKICAgICAgc2VhcmNoUXVlcnk6ICcnLAogICAgICBzZWFyY2hSZXN1bHRzOiBbXSwKICAgICAgaGFzU2VhcmNoZWQ6IGZhbHNlLAogICAgICBpc0xvYWRpbmc6IHRydWUsCiAgICAgIGlzU2VhcmNoaW5nOiBmYWxzZSwKICAgICAgaXNBY3Rpb25Mb2FkaW5nOiBmYWxzZSwKICAgICAgZXJyb3I6IG51bGwKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgYXV0aFRva2VuKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudG9rZW4gfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhUb2tlbicpOwogICAgfSwKICAgIHNlbGVjdGVkQ2hhcmFjdGVySWQoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5nZXR0ZXJzWydjaGFyYWN0ZXIvY2hhcmFjdGVySWQnXSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnc2VsZWN0ZWRDaGFyYWN0ZXJJZCcpOwogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIC8vIOajgOafpeiupOivgeeKtuaAgQogICAgaWYgKCF0aGlzLmF1dGhUb2tlbikgewogICAgICBsb2dnZXIud2FybignRnJpZW5kc+mhtemdojog5pyq5om+5Yiw6K6k6K+BdG9rZW4nKTsKICAgICAgdGhpcy5lcnJvciA9ICfor7flhYjnmbvlvZUnOwogICAgICByZXR1cm47CiAgICB9CgogICAgLy8g5qOA5p+l6KeS6Imy6YCJ5oup54q25oCBCiAgICBpZiAoIXRoaXMuc2VsZWN0ZWRDaGFyYWN0ZXJJZCkgewogICAgICBsb2dnZXIud2FybignRnJpZW5kc+mhtemdojog5pyq6YCJ5oup6KeS6ImyJyk7CiAgICAgIHRoaXMuZXJyb3IgPSAn6K+35YWI6YCJ5oup6KeS6ImyJzsKICAgICAgcmV0dXJuOwogICAgfQogICAgdGhpcy5mZXRjaEZyaWVuZHNEYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9LAogICAgc3dpdGNoVGFiKGluZGV4KSB7CiAgICAgIHRoaXMuY3VycmVudFRhYiA9IGluZGV4OwogICAgICBsb2dnZXIuaW5mbygn5YiH5o2i5aW95Y+L5qCH562+JywgdGhpcy5mcmllbmRzVGFic1tpbmRleF0ubmFtZSk7CiAgICAgIGlmIChpbmRleCA9PT0gMSkgewogICAgICAgIHRoaXMuZmV0Y2hGcmllbmRSZXF1ZXN0cygpOwogICAgICB9CiAgICB9LAogICAgYXN5bmMgZmV0Y2hGcmllbmRzRGF0YSgpIHsKICAgICAgdGhpcy5pc0xvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLmVycm9yID0gbnVsbDsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtBUElfQkFTRV9VUkx9L2NoYXJhY3RlcnMvJHt0aGlzLnNlbGVjdGVkQ2hhcmFjdGVySWR9L2ZyaWVuZHNgLCB7CiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuYXV0aFRva2VufWAsCiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmZyaWVuZHMgPSByZXNwb25zZS5kYXRhLmZyaWVuZHMgfHwgW107CiAgICAgICAgbG9nZ2VyLmluZm8oJ+iOt+WPluWlveWPi+WIl+ihqOaIkOWKnycsIHRoaXMuZnJpZW5kcy5sZW5ndGgpOwogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICB0aGlzLmVycm9yID0gZXJyLm1lc3NhZ2UgfHwgRVJST1JfTUVTU0FHRVMuVU5LTk9XTl9FUlJPUjsKICAgICAgICB0aGlzLnNob3dUb2FzdCh0aGlzLmVycm9yKTsKICAgICAgICBsb2dnZXIuZXJyb3IoJ+iOt+WPluWlveWPi+WIl+ihqOWksei0pScsIGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc0xvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGZldGNoRnJpZW5kUmVxdWVzdHMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7QVBJX0JBU0VfVVJMfS9jaGFyYWN0ZXJzLyR7dGhpcy5zZWxlY3RlZENoYXJhY3RlcklkfS9mcmllbmQtcmVxdWVzdHNgLCB7CiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuYXV0aFRva2VufWAsCiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmZyaWVuZFJlcXVlc3RzID0gcmVzcG9uc2UuZGF0YS5yZXF1ZXN0cyB8fCBbXTsKICAgICAgICBsb2dnZXIuaW5mbygn6I635Y+W5aW95Y+L55Sz6K+35oiQ5YqfJywgdGhpcy5mcmllbmRSZXF1ZXN0cy5sZW5ndGgpOwogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBsb2dnZXIuZXJyb3IoJ+iOt+WPluWlveWPi+eUs+ivt+Wksei0pScsIGVycik7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBzZWFyY2hQbGF5ZXJzKCkgewogICAgICBpZiAoIXRoaXMuc2VhcmNoUXVlcnkudHJpbSgpKSByZXR1cm47CiAgICAgIHRoaXMuaXNTZWFyY2hpbmcgPSB0cnVlOwogICAgICB0aGlzLmhhc1NlYXJjaGVkID0gZmFsc2U7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7QVBJX0JBU0VfVVJMfS9wbGF5ZXJzL3NlYXJjaGAsIHsKICAgICAgICAgIHBhcmFtczogewogICAgICAgICAgICBxdWVyeTogdGhpcy5zZWFyY2hRdWVyeS50cmltKCkKICAgICAgICAgIH0sCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuYXV0aFRva2VufWAsCiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLnNlYXJjaFJlc3VsdHMgPSByZXNwb25zZS5kYXRhLnBsYXllcnMgfHwgW107CiAgICAgICAgdGhpcy5oYXNTZWFyY2hlZCA9IHRydWU7CiAgICAgICAgbG9nZ2VyLmluZm8oJ+aQnOe0oueOqeWutuaIkOWKnycsIHRoaXMuc2VhcmNoUmVzdWx0cy5sZW5ndGgpOwogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICB2YXIgX2VyciRyZXNwb25zZTsKICAgICAgICB0aGlzLnNob3dUb2FzdCgoKF9lcnIkcmVzcG9uc2UgPSBlcnIucmVzcG9uc2UpID09PSBudWxsIHx8IF9lcnIkcmVzcG9uc2UgPT09IHZvaWQgMCB8fCAoX2VyciRyZXNwb25zZSA9IF9lcnIkcmVzcG9uc2UuZGF0YSkgPT09IG51bGwgfHwgX2VyciRyZXNwb25zZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2VyciRyZXNwb25zZS5tZXNzYWdlKSB8fCBFUlJPUl9NRVNTQUdFUy5VTktOT1dOX0VSUk9SKTsKICAgICAgICBsb2dnZXIuZXJyb3IoJ+aQnOe0oueOqeWutuWksei0pScsIGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc1NlYXJjaGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgYXN5bmMgc2VuZEZyaWVuZFJlcXVlc3QocGxheWVyKSB7CiAgICAgIGlmICh0aGlzLmlzQWN0aW9uTG9hZGluZykgcmV0dXJuOwogICAgICB0aGlzLmlzQWN0aW9uTG9hZGluZyA9IHRydWU7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KGAke0FQSV9CQVNFX1VSTH0vY2hhcmFjdGVycy8ke3RoaXMuc2VsZWN0ZWRDaGFyYWN0ZXJJZH0vZnJpZW5kLXJlcXVlc3RzYCwgewogICAgICAgICAgdGFyZ2V0X3BsYXllcl9pZDogcGxheWVyLmlkCiAgICAgICAgfSwgewogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0aGlzLmF1dGhUb2tlbn1gLAogICAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nCiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5zaG93VG9hc3QocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICflpb3lj4vnlLPor7flt7Llj5HpgIEnLCAnc3VjY2VzcycpOwogICAgICAgIGxvZ2dlci5pbmZvKCflj5HpgIHlpb3lj4vnlLPor7fmiJDlip8nLCBwbGF5ZXIubmFtZSk7CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIHZhciBfZXJyJHJlc3BvbnNlMjsKICAgICAgICB0aGlzLnNob3dUb2FzdCgoKF9lcnIkcmVzcG9uc2UyID0gZXJyLnJlc3BvbnNlKSA9PT0gbnVsbCB8fCBfZXJyJHJlc3BvbnNlMiA9PT0gdm9pZCAwIHx8IChfZXJyJHJlc3BvbnNlMiA9IF9lcnIkcmVzcG9uc2UyLmRhdGEpID09PSBudWxsIHx8IF9lcnIkcmVzcG9uc2UyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZXJyJHJlc3BvbnNlMi5tZXNzYWdlKSB8fCBFUlJPUl9NRVNTQUdFUy5VTktOT1dOX0VSUk9SKTsKICAgICAgICBsb2dnZXIuZXJyb3IoJ+WPkemAgeWlveWPi+eUs+ivt+Wksei0pScsIGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc0FjdGlvbkxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGFjY2VwdEZyaWVuZFJlcXVlc3QocmVxdWVzdCkgewogICAgICBpZiAodGhpcy5pc0FjdGlvbkxvYWRpbmcpIHJldHVybjsKICAgICAgdGhpcy5pc0FjdGlvbkxvYWRpbmcgPSB0cnVlOwogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtBUElfQkFTRV9VUkx9L2NoYXJhY3RlcnMvJHt0aGlzLnNlbGVjdGVkQ2hhcmFjdGVySWR9L2ZyaWVuZC1yZXF1ZXN0cy8ke3JlcXVlc3QuaWR9L2FjY2VwdGAsIHt9LCB7CiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuYXV0aFRva2VufWAsCiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLnNob3dUb2FzdChyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+W3suaOpeWPl+WlveWPi+eUs+ivtycsICdzdWNjZXNzJyk7CiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaEZyaWVuZFJlcXVlc3RzKCk7CiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaEZyaWVuZHNEYXRhKCk7CiAgICAgICAgbG9nZ2VyLmluZm8oJ+aOpeWPl+WlveWPi+eUs+ivt+aIkOWKnycsIHJlcXVlc3QubmFtZSk7CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIHZhciBfZXJyJHJlc3BvbnNlMzsKICAgICAgICB0aGlzLnNob3dUb2FzdCgoKF9lcnIkcmVzcG9uc2UzID0gZXJyLnJlc3BvbnNlKSA9PT0gbnVsbCB8fCBfZXJyJHJlc3BvbnNlMyA9PT0gdm9pZCAwIHx8IChfZXJyJHJlc3BvbnNlMyA9IF9lcnIkcmVzcG9uc2UzLmRhdGEpID09PSBudWxsIHx8IF9lcnIkcmVzcG9uc2UzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZXJyJHJlc3BvbnNlMy5tZXNzYWdlKSB8fCBFUlJPUl9NRVNTQUdFUy5VTktOT1dOX0VSUk9SKTsKICAgICAgICBsb2dnZXIuZXJyb3IoJ+aOpeWPl+WlveWPi+eUs+ivt+Wksei0pScsIGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc0FjdGlvbkxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIHJlamVjdEZyaWVuZFJlcXVlc3QocmVxdWVzdCkgewogICAgICBpZiAodGhpcy5pc0FjdGlvbkxvYWRpbmcpIHJldHVybjsKICAgICAgdGhpcy5pc0FjdGlvbkxvYWRpbmcgPSB0cnVlOwogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtBUElfQkFTRV9VUkx9L2NoYXJhY3RlcnMvJHt0aGlzLnNlbGVjdGVkQ2hhcmFjdGVySWR9L2ZyaWVuZC1yZXF1ZXN0cy8ke3JlcXVlc3QuaWR9L3JlamVjdGAsIHt9LCB7CiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuYXV0aFRva2VufWAsCiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLnNob3dUb2FzdChyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+W3suaLkue7neWlveWPi+eUs+ivtycsICdzdWNjZXNzJyk7CiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaEZyaWVuZFJlcXVlc3RzKCk7CiAgICAgICAgbG9nZ2VyLmluZm8oJ+aLkue7neWlveWPi+eUs+ivt+aIkOWKnycsIHJlcXVlc3QubmFtZSk7CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIHZhciBfZXJyJHJlc3BvbnNlNDsKICAgICAgICB0aGlzLnNob3dUb2FzdCgoKF9lcnIkcmVzcG9uc2U0ID0gZXJyLnJlc3BvbnNlKSA9PT0gbnVsbCB8fCBfZXJyJHJlc3BvbnNlNCA9PT0gdm9pZCAwIHx8IChfZXJyJHJlc3BvbnNlNCA9IF9lcnIkcmVzcG9uc2U0LmRhdGEpID09PSBudWxsIHx8IF9lcnIkcmVzcG9uc2U0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZXJyJHJlc3BvbnNlNC5tZXNzYWdlKSB8fCBFUlJPUl9NRVNTQUdFUy5VTktOT1dOX0VSUk9SKTsKICAgICAgICBsb2dnZXIuZXJyb3IoJ+aLkue7neWlveWPi+eUs+ivt+Wksei0pScsIGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc0FjdGlvbkxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIHJlbW92ZUZyaWVuZChmcmllbmQpIHsKICAgICAgaWYgKCFjb25maXJtKGDnoa7lrpropoHliKDpmaTlpb3lj4siJHtmcmllbmQubmFtZX0i5ZCX77yfYCkpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5pc0FjdGlvbkxvYWRpbmcgPSB0cnVlOwogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZGVsZXRlKGAke0FQSV9CQVNFX1VSTH0vY2hhcmFjdGVycy8ke3RoaXMuc2VsZWN0ZWRDaGFyYWN0ZXJJZH0vZnJpZW5kcy8ke2ZyaWVuZC5pZH1gLCB7CiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuYXV0aFRva2VufWAsCiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLnNob3dUb2FzdChyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+W3suWIoOmZpOWlveWPiycsICdzdWNjZXNzJyk7CiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaEZyaWVuZHNEYXRhKCk7CiAgICAgICAgbG9nZ2VyLmluZm8oJ+WIoOmZpOWlveWPi+aIkOWKnycsIGZyaWVuZC5uYW1lKTsKICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgdmFyIF9lcnIkcmVzcG9uc2U1OwogICAgICAgIHRoaXMuc2hvd1RvYXN0KCgoX2VyciRyZXNwb25zZTUgPSBlcnIucmVzcG9uc2UpID09PSBudWxsIHx8IF9lcnIkcmVzcG9uc2U1ID09PSB2b2lkIDAgfHwgKF9lcnIkcmVzcG9uc2U1ID0gX2VyciRyZXNwb25zZTUuZGF0YSkgPT09IG51bGwgfHwgX2VyciRyZXNwb25zZTUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9lcnIkcmVzcG9uc2U1Lm1lc3NhZ2UpIHx8IEVSUk9SX01FU1NBR0VTLlVOS05PV05fRVJST1IpOwogICAgICAgIGxvZ2dlci5lcnJvcign5Yig6Zmk5aW95Y+L5aSx6LSlJywgZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzQWN0aW9uTG9hZGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgY2hhdFdpdGhGcmllbmQoZnJpZW5kKSB7CiAgICAgIHRoaXMuc2hvd1RvYXN0KGDkuI4ke2ZyaWVuZC5uYW1lfeeahOiBiuWkqeWKn+iDveW8gOWPkeS4rS4uLmAsICdpbmZvJyk7CiAgICAgIC8vIOi/memHjOWPr+S7peWunueOsOengeiBiuWKn+iDvQogICAgfSwKICAgIGlzQWxyZWFkeUZyaWVuZChwbGF5ZXIpIHsKICAgICAgcmV0dXJuIHRoaXMuZnJpZW5kcy5zb21lKGZyaWVuZCA9PiBmcmllbmQuaWQgPT09IHBsYXllci5pZCk7CiAgICB9LAogICAgaGFzUGVuZGluZ1JlcXVlc3QocGxheWVyKSB7CiAgICAgIHJldHVybiB0aGlzLmZyaWVuZFJlcXVlc3RzLnNvbWUocmVxdWVzdCA9PiByZXF1ZXN0LmlkID09PSBwbGF5ZXIuaWQpOwogICAgfSwKICAgIGdldEFkZEJ1dHRvblRleHQocGxheWVyKSB7CiAgICAgIGlmICh0aGlzLmlzQWxyZWFkeUZyaWVuZChwbGF5ZXIpKSByZXR1cm4gJ+W3suaYr+WlveWPiyc7CiAgICAgIGlmICh0aGlzLmhhc1BlbmRpbmdSZXF1ZXN0KHBsYXllcikpIHJldHVybiAn5bey55Sz6K+3JzsKICAgICAgcmV0dXJuICfmt7vliqDlpb3lj4snOwogICAgfSwKICAgIGZvcm1hdFRpbWUodGltZXN0YW1wKSB7CiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lc3RhbXApOwogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBkaWZmID0gbm93IC0gZGF0ZTsKICAgICAgaWYgKGRpZmYgPCA2MDAwMCkgcmV0dXJuICfliJrliJonOwogICAgICBpZiAoZGlmZiA8IDM2MDAwMDApIHJldHVybiBgJHtNYXRoLmZsb29yKGRpZmYgLyA2MDAwMCl95YiG6ZKf5YmNYDsKICAgICAgaWYgKGRpZmYgPCA4NjQwMDAwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZiAvIDM2MDAwMDApfeWwj+aXtuWJjWA7CiAgICAgIHJldHVybiBgJHtNYXRoLmZsb29yKGRpZmYgLyA4NjQwMDAwMCl95aSp5YmNYDsKICAgIH0sCiAgICBzaG93VG9hc3QobWVzc2FnZSwgdHlwZSA9ICdlcnJvcicpIHsKICAgICAgYWxlcnQobWVzc2FnZSk7CiAgICB9LAogICAgZ29Ub0xvZ2luKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2xvZ2luJyk7CiAgICB9LAogICAgZ29Ub0NoYXJhY3RlclNlbGVjdCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9zZXR1cC9jaGFyYWN0ZXItc2VsZWN0Jyk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["GameLayout", "axios", "API_BASE_URL", "ERROR_MESSAGES", "logger", "components", "data", "currentTab", "friendsTabs", "name", "type", "friends", "friendRequests", "searchQuery", "searchResults", "hasSearched", "isLoading", "isSearching", "isActionLoading", "error", "computed", "authToken", "$store", "state", "token", "localStorage", "getItem", "selectedCharacterId", "getters", "created", "warn", "fetchFriendsData", "methods", "goBack", "$router", "go", "switchTab", "index", "info", "fetchFriendRequests", "response", "get", "headers", "length", "err", "message", "UNKNOWN_ERROR", "showToast", "requests", "searchPlayers", "trim", "params", "query", "players", "_err$response", "sendFriendRequest", "player", "post", "target_player_id", "id", "_err$response2", "acceptFriendRequest", "request", "_err$response3", "rejectFriendRequest", "_err$response4", "removeFriend", "friend", "confirm", "delete", "_err$response5", "chatWithFriend", "isAlreadyFriend", "some", "hasPendingRequest", "getAddButtonText", "formatTime", "timestamp", "date", "Date", "now", "diff", "Math", "floor", "alert", "goToLogin", "push", "goToCharacterSelect"], "sources": ["src/views/game/subpages/Friends.vue"], "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"friends-page\">\n      <!-- 返回按钮 -->\n      <div class=\"header-section\">\n        <button class=\"return-btn\" @click=\"goBack\">\n          <img src=\"/static/game/UI/anniu/fhui_2.png\" alt=\"返回\" class=\"btn-image\" />\n        </button>\n        <h2 class=\"page-title\">好友系统</h2>\n      </div>\n      \n      <!-- 功能标签 -->\n      <div class=\"friends-tabs\">\n        <div \n          v-for=\"(tab, index) in friendsTabs\" \n          :key=\"index\"\n          class=\"friends-tab\"\n          :class=\"{ active: currentTab === index }\"\n          @click=\"switchTab(index)\"\n        >\n          {{ tab.name }}\n        </div>\n      </div>\n      \n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-text\">加载中...</div>\n      </div>\n      \n      <!-- 错误状态 -->\n      <div v-else-if=\"error\" class=\"error-container\">\n        <div class=\"error-text\">{{ error }}</div>\n        <div class=\"error-actions\">\n          <button v-if=\"error.includes('登录')\" class=\"retry-btn\" @click=\"goToLogin\">前往登录</button>\n          <button v-else-if=\"error.includes('角色')\" class=\"retry-btn\" @click=\"goToCharacterSelect\">选择角色</button>\n          <button v-else class=\"retry-btn\" @click=\"fetchFriendsData\">重试</button>\n        </div>\n      </div>\n      \n      <!-- 好友内容 -->\n      <div v-else class=\"friends-content\">\n        <!-- 好友列表 -->\n        <div v-if=\"currentTab === 0\" class=\"friends-list\">\n          <div v-if=\"friends.length === 0\" class=\"empty-tip\">\n            <span>暂无好友</span>\n          </div>\n          \n          <div v-else class=\"friend-items\">\n            <div \n              v-for=\"friend in friends\" \n              :key=\"friend.id\"\n              class=\"friend-item\"\n              :class=\"{ online: friend.isOnline }\"\n            >\n              <div class=\"friend-avatar\">\n                <img :src=\"friend.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"friend.name\" />\n                <div v-if=\"friend.isOnline\" class=\"online-indicator\"></div>\n              </div>\n              \n              <div class=\"friend-info\">\n                <div class=\"friend-name\">{{ friend.name }}</div>\n                <div class=\"friend-level\">等级 {{ friend.level }}</div>\n                <div class=\"friend-status\">{{ friend.isOnline ? '在线' : '离线' }}</div>\n              </div>\n              \n              <div class=\"friend-actions\">\n                <button \n                  class=\"action-btn chat\"\n                  @click=\"chatWithFriend(friend)\"\n                  :disabled=\"!friend.isOnline\"\n                >\n                  聊天\n                </button>\n                <button \n                  class=\"action-btn remove\"\n                  @click=\"removeFriend(friend)\"\n                >\n                  删除\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 好友申请 -->\n        <div v-if=\"currentTab === 1\" class=\"friend-requests\">\n          <div v-if=\"friendRequests.length === 0\" class=\"empty-tip\">\n            <span>暂无好友申请</span>\n          </div>\n          \n          <div v-else class=\"request-items\">\n            <div \n              v-for=\"request in friendRequests\" \n              :key=\"request.id\"\n              class=\"request-item\"\n            >\n              <div class=\"request-avatar\">\n                <img :src=\"request.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"request.name\" />\n              </div>\n              \n              <div class=\"request-info\">\n                <div class=\"request-name\">{{ request.name }}</div>\n                <div class=\"request-level\">等级 {{ request.level }}</div>\n                <div class=\"request-time\">{{ formatTime(request.requestTime) }}</div>\n              </div>\n              \n              <div class=\"request-actions\">\n                <button \n                  class=\"action-btn accept\"\n                  @click=\"acceptFriendRequest(request)\"\n                  :disabled=\"isActionLoading\"\n                >\n                  接受\n                </button>\n                <button \n                  class=\"action-btn reject\"\n                  @click=\"rejectFriendRequest(request)\"\n                  :disabled=\"isActionLoading\"\n                >\n                  拒绝\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 添加好友 -->\n        <div v-if=\"currentTab === 2\" class=\"add-friend\">\n          <div class=\"search-section\">\n            <div class=\"search-input-group\">\n              <input \n                v-model=\"searchQuery\"\n                type=\"text\"\n                placeholder=\"输入玩家名称搜索\"\n                class=\"search-input\"\n                @keyup.enter=\"searchPlayers\"\n              />\n              <button \n                class=\"search-btn\"\n                @click=\"searchPlayers\"\n                :disabled=\"isSearching || !searchQuery.trim()\"\n              >\n                搜索\n              </button>\n            </div>\n          </div>\n          \n          <div v-if=\"isSearching\" class=\"searching-tip\">\n            <span>搜索中...</span>\n          </div>\n          \n          <div v-else-if=\"searchResults.length === 0 && hasSearched\" class=\"empty-tip\">\n            <span>未找到相关玩家</span>\n          </div>\n          \n          <div v-else-if=\"searchResults.length > 0\" class=\"search-results\">\n            <div \n              v-for=\"player in searchResults\" \n              :key=\"player.id\"\n              class=\"search-result-item\"\n            >\n              <div class=\"result-avatar\">\n                <img :src=\"player.avatar || '/static/game/UI/tx/male/tx1.png'\" :alt=\"player.name\" />\n              </div>\n              \n              <div class=\"result-info\">\n                <div class=\"result-name\">{{ player.name }}</div>\n                <div class=\"result-level\">等级 {{ player.level }}</div>\n                <div class=\"result-status\">{{ player.isOnline ? '在线' : '离线' }}</div>\n              </div>\n              \n              <div class=\"result-actions\">\n                <button \n                  class=\"action-btn add\"\n                  @click=\"sendFriendRequest(player)\"\n                  :disabled=\"isActionLoading || isAlreadyFriend(player) || hasPendingRequest(player)\"\n                >\n                  {{ getAddButtonText(player) }}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport axios from 'axios'\nimport { API_BASE_URL } from '@/api/config.js'\nimport { ERROR_MESSAGES } from '@/api/constants.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      currentTab: 0,\n      friendsTabs: [\n        { name: '好友列表', type: 'friends' },\n        { name: '好友申请', type: 'requests' },\n        { name: '添加好友', type: 'add' }\n      ],\n      friends: [],\n      friendRequests: [],\n      searchQuery: '',\n      searchResults: [],\n      hasSearched: false,\n      isLoading: true,\n      isSearching: false,\n      isActionLoading: false,\n      error: null\n    }\n  },\n  computed: {\n    authToken() {\n      return this.$store.state.token || localStorage.getItem('authToken')\n    },\n    selectedCharacterId() {\n      return this.$store.getters['character/characterId'] || localStorage.getItem('selectedCharacterId')\n    }\n  },\n  created() {\n    // 检查认证状态\n    if (!this.authToken) {\n      logger.warn('Friends页面: 未找到认证token')\n      this.error = '请先登录'\n      return\n    }\n\n    // 检查角色选择状态\n    if (!this.selectedCharacterId) {\n      logger.warn('Friends页面: 未选择角色')\n      this.error = '请先选择角色'\n      return\n    }\n\n    this.fetchFriendsData()\n  },\n  methods: {\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    switchTab(index) {\n      this.currentTab = index\n      logger.info('切换好友标签', this.friendsTabs[index].name)\n      \n      if (index === 1) {\n        this.fetchFriendRequests()\n      }\n    },\n    \n    async fetchFriendsData() {\n      this.isLoading = true\n      this.error = null\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friends`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.friends = response.data.friends || []\n        logger.info('获取好友列表成功', this.friends.length)\n      } catch (err) {\n        this.error = err.message || ERROR_MESSAGES.UNKNOWN_ERROR\n        this.showToast(this.error)\n        logger.error('获取好友列表失败', err)\n      } finally {\n        this.isLoading = false\n      }\n    },\n    \n    async fetchFriendRequests() {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`, {\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.friendRequests = response.data.requests || []\n        logger.info('获取好友申请成功', this.friendRequests.length)\n      } catch (err) {\n        logger.error('获取好友申请失败', err)\n      }\n    },\n    \n    async searchPlayers() {\n      if (!this.searchQuery.trim()) return\n      \n      this.isSearching = true\n      this.hasSearched = false\n      \n      try {\n        const response = await axios.get(`${API_BASE_URL}/players/search`, {\n          params: { query: this.searchQuery.trim() },\n          headers: {\n            'Authorization': `Bearer ${this.authToken}`,\n            'Accept': 'application/json'\n          }\n        })\n        \n        this.searchResults = response.data.players || []\n        this.hasSearched = true\n        logger.info('搜索玩家成功', this.searchResults.length)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('搜索玩家失败', err)\n      } finally {\n        this.isSearching = false\n      }\n    },\n    \n    async sendFriendRequest(player) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests`,\n          { target_player_id: player.id },\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '好友申请已发送', 'success')\n        logger.info('发送好友申请成功', player.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('发送好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async acceptFriendRequest(request) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/accept`,\n          {},\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已接受好友申请', 'success')\n        await this.fetchFriendRequests()\n        await this.fetchFriendsData()\n        logger.info('接受好友申请成功', request.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('接受好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async rejectFriendRequest(request) {\n      if (this.isActionLoading) return\n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.post(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friend-requests/${request.id}/reject`,\n          {},\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已拒绝好友申请', 'success')\n        await this.fetchFriendRequests()\n        logger.info('拒绝好友申请成功', request.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('拒绝好友申请失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    async removeFriend(friend) {\n      if (!confirm(`确定要删除好友\"${friend.name}\"吗？`)) {\n        return\n      }\n      \n      this.isActionLoading = true\n      \n      try {\n        const response = await axios.delete(\n          `${API_BASE_URL}/characters/${this.selectedCharacterId}/friends/${friend.id}`,\n          {\n            headers: {\n              'Authorization': `Bearer ${this.authToken}`,\n              'Accept': 'application/json'\n            }\n          }\n        )\n        \n        this.showToast(response.data.message || '已删除好友', 'success')\n        await this.fetchFriendsData()\n        logger.info('删除好友成功', friend.name)\n      } catch (err) {\n        this.showToast(err.response?.data?.message || ERROR_MESSAGES.UNKNOWN_ERROR)\n        logger.error('删除好友失败', err)\n      } finally {\n        this.isActionLoading = false\n      }\n    },\n    \n    chatWithFriend(friend) {\n      this.showToast(`与${friend.name}的聊天功能开发中...`, 'info')\n      // 这里可以实现私聊功能\n    },\n    \n    isAlreadyFriend(player) {\n      return this.friends.some(friend => friend.id === player.id)\n    },\n    \n    hasPendingRequest(player) {\n      return this.friendRequests.some(request => request.id === player.id)\n    },\n    \n    getAddButtonText(player) {\n      if (this.isAlreadyFriend(player)) return '已是好友'\n      if (this.hasPendingRequest(player)) return '已申请'\n      return '添加好友'\n    },\n    \n    formatTime(timestamp) {\n      const date = new Date(timestamp)\n      const now = new Date()\n      const diff = now - date\n      \n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`\n      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`\n      return `${Math.floor(diff / 86400000)}天前`\n    },\n    \n    showToast(message, type = 'error') {\n      alert(message)\n    },\n\n    goToLogin() {\n      this.$router.push('/login')\n    },\n\n    goToCharacterSelect() {\n      this.$router.push('/setup/character-select')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.friends-page {\n  padding: 15px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2d1b69, #1a0f3d);\n  color: #fff;\n}\n\n.header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  gap: 15px;\n}\n\n.return-btn {\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  padding: 0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.btn-image {\n  width: 60px;\n  height: 40px;\n  object-fit: contain;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #fff;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.friends-tabs {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 20px;\n  border-bottom: 2px solid rgba(255, 255, 255, 0.2);\n  padding-bottom: 10px;\n}\n\n.friends-tab {\n  padding: 10px 20px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 500;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-2px);\n  }\n\n  &.active {\n    background: rgba(255, 255, 255, 0.3);\n    border-color: rgba(255, 255, 255, 0.5);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.loading-container, .error-container {\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.loading-text {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.error-text {\n  font-size: 16px;\n  color: #ff6b6b;\n  margin-bottom: 15px;\n}\n\n.error-actions {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.retry-btn {\n  padding: 10px 20px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n}\n\n.friends-content {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.empty-tip {\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  padding: 60px 20px;\n  font-size: 16px;\n}\n\n// 好友列表样式\n.friend-items, .request-items, .search-results {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.friend-item, .request-item, .search-result-item {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 12px;\n  padding: 15px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.12);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\n  }\n\n  &.online {\n    border-left: 4px solid #2ecc71;\n  }\n}\n\n.friend-avatar, .request-avatar, .result-avatar {\n  position: relative;\n  width: 60px;\n  height: 60px;\n  margin-right: 15px;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 50%;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n}\n\n.online-indicator {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 16px;\n  height: 16px;\n  background: #2ecc71;\n  border: 2px solid #fff;\n  border-radius: 50%;\n}\n\n.friend-info, .request-info, .result-info {\n  flex: 1;\n  margin-right: 15px;\n}\n\n.friend-name, .request-name, .result-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 4px;\n}\n\n.friend-level, .request-level, .result-level {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 4px;\n}\n\n.friend-status, .result-status {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.request-time {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.friend-actions, .request-actions, .result-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.chat {\n    background: #4ecdc4;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #45b7aa;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.remove {\n    background: #e74c3c;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #c0392b;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.accept {\n    background: #2ecc71;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #27ae60;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.reject {\n    background: #95a5a6;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #7f8c8d;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.add {\n    background: #3498db;\n    color: white;\n\n    &:hover:not(:disabled) {\n      background: #2980b9;\n      transform: translateY(-1px);\n    }\n  }\n}\n\n// 搜索区域样式\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-input-group {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.search-input {\n  flex: 1;\n  padding: 12px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 8px;\n  color: #fff;\n  font-size: 16px;\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #4ecdc4;\n    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);\n  }\n}\n\n.search-btn {\n  padding: 12px 24px;\n  background: #4ecdc4;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: #45b7aa;\n    transform: translateY(-2px);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.searching-tip {\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  padding: 40px 20px;\n  font-size: 16px;\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .friends-page {\n    padding: 10px;\n  }\n\n  .header-section {\n    margin-bottom: 15px;\n    gap: 10px;\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .btn-image {\n    width: 50px;\n    height: 35px;\n  }\n\n  .friends-tabs {\n    flex-wrap: wrap;\n    gap: 6px;\n  }\n\n  .friends-tab {\n    padding: 8px 16px;\n    font-size: 14px;\n  }\n\n  .friend-item, .request-item, .search-result-item {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .friend-avatar, .request-avatar, .result-avatar {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .friend-info, .request-info, .result-info {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .friend-actions, .request-actions, .result-actions {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n  }\n\n  .search-input, .search-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n  .friends-page {\n    padding: 8px;\n  }\n\n  .friends-content {\n    padding: 15px;\n  }\n\n  .friend-avatar, .request-avatar, .result-avatar {\n    width: 50px;\n    height: 50px;\n  }\n\n  .action-btn {\n    padding: 6px 12px;\n    font-size: 11px;\n  }\n}\n</style>\n"], "mappings": ";;;AA6LA,OAAAA,UAAA;AACA,OAAAC,KAAA;AACA,SAAAC,YAAA;AACA,SAAAC,cAAA;AACA,OAAAC,MAAA;AAEA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,UAAA;MACAC,WAAA,GACA;QAAAC,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,IAAA;MAAA,EACA;MACAC,OAAA;MACAC,cAAA;MACAC,WAAA;MACAC,aAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,eAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,KAAA,IAAAC,YAAA,CAAAC,OAAA;IACA;IACAC,oBAAA;MACA,YAAAL,MAAA,CAAAM,OAAA,6BAAAH,YAAA,CAAAC,OAAA;IACA;EACA;EACAG,QAAA;IACA;IACA,UAAAR,SAAA;MACAjB,MAAA,CAAA0B,IAAA;MACA,KAAAX,KAAA;MACA;IACA;;IAEA;IACA,UAAAQ,mBAAA;MACAvB,MAAA,CAAA0B,IAAA;MACA,KAAAX,KAAA;MACA;IACA;IAEA,KAAAY,gBAAA;EACA;EACAC,OAAA;IACAC,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEAC,UAAAC,KAAA;MACA,KAAA9B,UAAA,GAAA8B,KAAA;MACAjC,MAAA,CAAAkC,IAAA,gBAAA9B,WAAA,CAAA6B,KAAA,EAAA5B,IAAA;MAEA,IAAA4B,KAAA;QACA,KAAAE,mBAAA;MACA;IACA;IAEA,MAAAR,iBAAA;MACA,KAAAf,SAAA;MACA,KAAAG,KAAA;MAEA;QACA,MAAAqB,QAAA,SAAAvC,KAAA,CAAAwC,GAAA,IAAAvC,YAAA,oBAAAyB,mBAAA;UACAe,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA;QAEA,KAAAV,OAAA,GAAA6B,QAAA,CAAAlC,IAAA,CAAAK,OAAA;QACAP,MAAA,CAAAkC,IAAA,kBAAA3B,OAAA,CAAAgC,MAAA;MACA,SAAAC,GAAA;QACA,KAAAzB,KAAA,GAAAyB,GAAA,CAAAC,OAAA,IAAA1C,cAAA,CAAA2C,aAAA;QACA,KAAAC,SAAA,MAAA5B,KAAA;QACAf,MAAA,CAAAe,KAAA,aAAAyB,GAAA;MACA;QACA,KAAA5B,SAAA;MACA;IACA;IAEA,MAAAuB,oBAAA;MACA;QACA,MAAAC,QAAA,SAAAvC,KAAA,CAAAwC,GAAA,IAAAvC,YAAA,oBAAAyB,mBAAA;UACAe,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA;QAEA,KAAAT,cAAA,GAAA4B,QAAA,CAAAlC,IAAA,CAAA0C,QAAA;QACA5C,MAAA,CAAAkC,IAAA,kBAAA1B,cAAA,CAAA+B,MAAA;MACA,SAAAC,GAAA;QACAxC,MAAA,CAAAe,KAAA,aAAAyB,GAAA;MACA;IACA;IAEA,MAAAK,cAAA;MACA,UAAApC,WAAA,CAAAqC,IAAA;MAEA,KAAAjC,WAAA;MACA,KAAAF,WAAA;MAEA;QACA,MAAAyB,QAAA,SAAAvC,KAAA,CAAAwC,GAAA,IAAAvC,YAAA;UACAiD,MAAA;YAAAC,KAAA,OAAAvC,WAAA,CAAAqC,IAAA;UAAA;UACAR,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA;QAEA,KAAAP,aAAA,GAAA0B,QAAA,CAAAlC,IAAA,CAAA+C,OAAA;QACA,KAAAtC,WAAA;QACAX,MAAA,CAAAkC,IAAA,gBAAAxB,aAAA,CAAA6B,MAAA;MACA,SAAAC,GAAA;QAAA,IAAAU,aAAA;QACA,KAAAP,SAAA,GAAAO,aAAA,GAAAV,GAAA,CAAAJ,QAAA,cAAAc,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAAhD,IAAA,cAAAgD,aAAA,uBAAAA,aAAA,CAAAT,OAAA,KAAA1C,cAAA,CAAA2C,aAAA;QACA1C,MAAA,CAAAe,KAAA,WAAAyB,GAAA;MACA;QACA,KAAA3B,WAAA;MACA;IACA;IAEA,MAAAsC,kBAAAC,MAAA;MACA,SAAAtC,eAAA;MACA,KAAAA,eAAA;MAEA;QACA,MAAAsB,QAAA,SAAAvC,KAAA,CAAAwD,IAAA,CACA,GAAAvD,YAAA,oBAAAyB,mBAAA,oBACA;UAAA+B,gBAAA,EAAAF,MAAA,CAAAG;QAAA,GACA;UACAjB,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA,CACA;QAEA,KAAA0B,SAAA,CAAAP,QAAA,CAAAlC,IAAA,CAAAuC,OAAA;QACAzC,MAAA,CAAAkC,IAAA,aAAAkB,MAAA,CAAA/C,IAAA;MACA,SAAAmC,GAAA;QAAA,IAAAgB,cAAA;QACA,KAAAb,SAAA,GAAAa,cAAA,GAAAhB,GAAA,CAAAJ,QAAA,cAAAoB,cAAA,gBAAAA,cAAA,GAAAA,cAAA,CAAAtD,IAAA,cAAAsD,cAAA,uBAAAA,cAAA,CAAAf,OAAA,KAAA1C,cAAA,CAAA2C,aAAA;QACA1C,MAAA,CAAAe,KAAA,aAAAyB,GAAA;MACA;QACA,KAAA1B,eAAA;MACA;IACA;IAEA,MAAA2C,oBAAAC,OAAA;MACA,SAAA5C,eAAA;MACA,KAAAA,eAAA;MAEA;QACA,MAAAsB,QAAA,SAAAvC,KAAA,CAAAwD,IAAA,CACA,GAAAvD,YAAA,oBAAAyB,mBAAA,oBAAAmC,OAAA,CAAAH,EAAA,WACA,IACA;UACAjB,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA,CACA;QAEA,KAAA0B,SAAA,CAAAP,QAAA,CAAAlC,IAAA,CAAAuC,OAAA;QACA,WAAAN,mBAAA;QACA,WAAAR,gBAAA;QACA3B,MAAA,CAAAkC,IAAA,aAAAwB,OAAA,CAAArD,IAAA;MACA,SAAAmC,GAAA;QAAA,IAAAmB,cAAA;QACA,KAAAhB,SAAA,GAAAgB,cAAA,GAAAnB,GAAA,CAAAJ,QAAA,cAAAuB,cAAA,gBAAAA,cAAA,GAAAA,cAAA,CAAAzD,IAAA,cAAAyD,cAAA,uBAAAA,cAAA,CAAAlB,OAAA,KAAA1C,cAAA,CAAA2C,aAAA;QACA1C,MAAA,CAAAe,KAAA,aAAAyB,GAAA;MACA;QACA,KAAA1B,eAAA;MACA;IACA;IAEA,MAAA8C,oBAAAF,OAAA;MACA,SAAA5C,eAAA;MACA,KAAAA,eAAA;MAEA;QACA,MAAAsB,QAAA,SAAAvC,KAAA,CAAAwD,IAAA,CACA,GAAAvD,YAAA,oBAAAyB,mBAAA,oBAAAmC,OAAA,CAAAH,EAAA,WACA,IACA;UACAjB,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA,CACA;QAEA,KAAA0B,SAAA,CAAAP,QAAA,CAAAlC,IAAA,CAAAuC,OAAA;QACA,WAAAN,mBAAA;QACAnC,MAAA,CAAAkC,IAAA,aAAAwB,OAAA,CAAArD,IAAA;MACA,SAAAmC,GAAA;QAAA,IAAAqB,cAAA;QACA,KAAAlB,SAAA,GAAAkB,cAAA,GAAArB,GAAA,CAAAJ,QAAA,cAAAyB,cAAA,gBAAAA,cAAA,GAAAA,cAAA,CAAA3D,IAAA,cAAA2D,cAAA,uBAAAA,cAAA,CAAApB,OAAA,KAAA1C,cAAA,CAAA2C,aAAA;QACA1C,MAAA,CAAAe,KAAA,aAAAyB,GAAA;MACA;QACA,KAAA1B,eAAA;MACA;IACA;IAEA,MAAAgD,aAAAC,MAAA;MACA,KAAAC,OAAA,YAAAD,MAAA,CAAA1D,IAAA;QACA;MACA;MAEA,KAAAS,eAAA;MAEA;QACA,MAAAsB,QAAA,SAAAvC,KAAA,CAAAoE,MAAA,CACA,GAAAnE,YAAA,oBAAAyB,mBAAA,YAAAwC,MAAA,CAAAR,EAAA,IACA;UACAjB,OAAA;YACA,gCAAArB,SAAA;YACA;UACA;QACA,CACA;QAEA,KAAA0B,SAAA,CAAAP,QAAA,CAAAlC,IAAA,CAAAuC,OAAA;QACA,WAAAd,gBAAA;QACA3B,MAAA,CAAAkC,IAAA,WAAA6B,MAAA,CAAA1D,IAAA;MACA,SAAAmC,GAAA;QAAA,IAAA0B,cAAA;QACA,KAAAvB,SAAA,GAAAuB,cAAA,GAAA1B,GAAA,CAAAJ,QAAA,cAAA8B,cAAA,gBAAAA,cAAA,GAAAA,cAAA,CAAAhE,IAAA,cAAAgE,cAAA,uBAAAA,cAAA,CAAAzB,OAAA,KAAA1C,cAAA,CAAA2C,aAAA;QACA1C,MAAA,CAAAe,KAAA,WAAAyB,GAAA;MACA;QACA,KAAA1B,eAAA;MACA;IACA;IAEAqD,eAAAJ,MAAA;MACA,KAAApB,SAAA,KAAAoB,MAAA,CAAA1D,IAAA;MACA;IACA;IAEA+D,gBAAAhB,MAAA;MACA,YAAA7C,OAAA,CAAA8D,IAAA,CAAAN,MAAA,IAAAA,MAAA,CAAAR,EAAA,KAAAH,MAAA,CAAAG,EAAA;IACA;IAEAe,kBAAAlB,MAAA;MACA,YAAA5C,cAAA,CAAA6D,IAAA,CAAAX,OAAA,IAAAA,OAAA,CAAAH,EAAA,KAAAH,MAAA,CAAAG,EAAA;IACA;IAEAgB,iBAAAnB,MAAA;MACA,SAAAgB,eAAA,CAAAhB,MAAA;MACA,SAAAkB,iBAAA,CAAAlB,MAAA;MACA;IACA;IAEAoB,WAAAC,SAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,SAAA;MACA,MAAAG,GAAA,OAAAD,IAAA;MACA,MAAAE,IAAA,GAAAD,GAAA,GAAAF,IAAA;MAEA,IAAAG,IAAA;MACA,IAAAA,IAAA,sBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,IAAAA,IAAA,uBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;IACA;IAEAlC,UAAAF,OAAA,EAAAnC,IAAA;MACA0E,KAAA,CAAAvC,OAAA;IACA;IAEAwC,UAAA;MACA,KAAAnD,OAAA,CAAAoD,IAAA;IACA;IAEAC,oBAAA;MACA,KAAArD,OAAA,CAAAoD,IAAA;IACA;EACA;AACA", "ignoreList": []}]}