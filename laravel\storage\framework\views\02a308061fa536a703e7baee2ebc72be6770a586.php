<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title><?php echo e(config('admin.name', '神之西游管理系统')); ?> - 管理员登录</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <!-- LayUI CSS -->
  <link rel="stylesheet" href="https://www.layuicdn.com/layui-v2.5.6/css/layui.css" media="all">
  <style>
    body {
      background-color: #f2f2f2;
      overflow: hidden;
    }
    .login-wrap {
      width: 400px;
      margin: 120px auto 0;
    }
    .login-header {
      text-align: center;
      margin-bottom: 20px;
    }
    .login-header h2 {
      color: #333;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
    }
    .layui-form {
      background: #fff;
      padding: 30px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      border-radius: 4px;
    }
    .layui-form-item {
      margin-bottom: 25px;
    }
    .layui-input {
      height: 45px;
      font-size: 16px;
    }
    .layui-btn {
      width: 100%;
      height: 45px;
      font-size: 16px;
    }
    .login-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .login-error {
      color: #FF5722;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <div class="login-wrap">
    <div class="login-header">
      <h2><?php echo e(config('admin.name', '神之西游管理系统')); ?></h2>
    </div>

    <form class="layui-form" method="POST" action="<?php echo e(route('admin.login.post')); ?>">
      <?php echo csrf_field(); ?>

      <?php if($errors->any()): ?>
        <div class="login-error">
          <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <p><?php echo e($error); ?></p>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
      <?php endif; ?>

      <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 0;">
          <input type="text" name="username" lay-verify="required" placeholder="用户名" autocomplete="off" class="layui-input" value="<?php echo e(old('username')); ?>">
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 0;">
          <input type="password" name="password" lay-verify="required" placeholder="密码" autocomplete="off" class="layui-input">
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 0;">
          <input type="checkbox" name="remember" lay-skin="primary" title="记住我">
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 0;">
          <button class="layui-btn layui-btn-normal" lay-submit lay-filter="formLogin">登 录</button>
        </div>
      </div>
    </form>
  </div>

  <div class="login-bg"></div>

  <!-- LayUI JS -->
  <script src="https://www.layuicdn.com/layui-v2.5.6/layui.js"></script>
  <script>
    layui.use(['form', 'element', 'layer'], function(){
      var form = layui.form;
      var layer = layui.layer;
      var $ = layui.jquery;

      // 登录表单提交
      form.on('submit(formLogin)', function(data){
        return true; // 允许表单提交
      });

      // 粒子背景效果
      if (typeof particlesJS !== 'undefined') {
        particlesJS.load('login-bg', {
          "particles": {
            "number": {"value": 80, "density": {"enable": true, "value_area": 800}},
            "color": {"value": "#6b6b6b"},
            "shape": {"type": "circle"},
            "opacity": {"value": 0.5, "random": false},
            "size": {"value": 3, "random": true},
            "line_linked": {"enable": true, "distance": 150, "color": "#c8c8c8", "opacity": 0.4, "width": 1},
            "move": {"enable": true, "speed": 1, "direction": "none", "random": false, "straight": false, "out_mode": "out", "bounce": false}
          },
          "interactivity": {
            "detect_on": "canvas",
            "events": {
              "onhover": {"enable": false},
              "onclick": {"enable": false},
              "resize": true
            }
          },
          "retina_detect": true
        });
      }
    });
  </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\szxy\laravel\resources\views/admin/auth/login.blade.php ENDPATH**/ ?>