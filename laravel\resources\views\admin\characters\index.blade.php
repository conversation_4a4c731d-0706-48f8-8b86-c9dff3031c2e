@extends('admin.layouts.app')

@section('title', '角色管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        角色列表
        <a href="{{ route('admin.characters.create') }}" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加角色</a>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>角色名</th>
                    <th>所属用户</th>
                    <th>等级</th>
                    <th>HP</th>
                    <th>攻击</th>
                    <th>防御</th>
                    <th>银两</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($characters as $character)
                <tr>
                    <td>{{ $character->id }}</td>
                    <td>{{ $character->name }}</td>
                    <td>{{ $character->user_username ?? '无' }}</td>
                    <td>{{ $character->level }}</td>
                    <td>{{ $character->hp ?? 0 }}</td>
                    <td>{{ $character->attack ?? 0 }}</td>
                    <td>{{ $character->defense ?? 0 }}</td>
                    <td>{{ $character->silver ?? 0 }}</td>
                    <td>
                        @if(isset($character->status) && $character->status == 1)
                        <span class="layui-badge layui-bg-green">正常</span>
                        @else
                        <span class="layui-badge">禁用</span>
                        @endif
                    </td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.characters.show', $character->id) }}" class="layui-btn layui-btn-xs layui-btn-primary">查看</a>
                            <a href="{{ route('admin.characters.edit', $character->id) }}" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteCharacter({{ $character->id }}, '{{ $character->name }}')">删除</button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="10" class="layui-center">暂无角色数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $characters->links('admin.layouts.pagination') }}
    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@section('scripts')
<script>
function deleteCharacter(id, name) {
    layer.confirm('确定要删除角色 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "{{ route('admin.characters.destroy', '') }}/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
