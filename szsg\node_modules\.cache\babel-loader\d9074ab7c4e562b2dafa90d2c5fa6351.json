{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=template&id=f28434cc&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750348047473}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "minimized", "compact", "compactMode", "on", "click", "toggleMinimize", "_v", "connected", "isConnected", "attrs", "title", "$event", "stopPropagation", "clearMessages", "apply", "arguments", "_e", "toggleCompactMode", "_s", "_l", "channels", "channel", "index", "key", "active", "currentChannelIndex", "getChannelIcon", "id", "switchChannel", "name", "unread", "ref", "filteredMessages", "length", "msg", "type", "isSelf", "handleSenderClick", "sender", "timestamp", "formatTime", "content", "directives", "rawName", "value", "newMessage", "expression", "placeholder", "getInputPlaceholder", "disabled", "maxlength", "domProps", "keyup", "indexOf", "_k", "keyCode", "sendMessage", "focus", "onInputFocus", "blur", "onInputBlur", "input", "target", "composing", "trim", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/components/GameChat.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"chat-container\",\n      class: { minimized: _vm.minimized, compact: _vm.compactMode },\n    },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"chat-header\", on: { click: _vm.toggleMinimize } },\n        [\n          _c(\"div\", { staticClass: \"chat-title-section\" }, [\n            _c(\"span\", { staticClass: \"chat-title\" }, [_vm._v(\"聊天\")]),\n            _c(\"div\", {\n              staticClass: \"chat-status\",\n              class: { connected: _vm.isConnected },\n              attrs: { title: _vm.isConnected ? \"已连接\" : \"未连接\" },\n            }),\n          ]),\n          _c(\"div\", { staticClass: \"chat-controls\" }, [\n            !_vm.minimized\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"control-btn\",\n                    attrs: { title: \"清空消息\" },\n                    on: {\n                      click: function ($event) {\n                        $event.stopPropagation()\n                        return _vm.clearMessages.apply(null, arguments)\n                      },\n                    },\n                  },\n                  [_c(\"span\", { staticClass: \"icon\" }, [_vm._v(\"🗑\")])]\n                )\n              : _vm._e(),\n            !_vm.minimized\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"control-btn\",\n                    attrs: { title: \"紧凑模式\" },\n                    on: {\n                      click: function ($event) {\n                        $event.stopPropagation()\n                        return _vm.toggleCompactMode.apply(null, arguments)\n                      },\n                    },\n                  },\n                  [\n                    _c(\"span\", { staticClass: \"icon\" }, [\n                      _vm._v(_vm._s(_vm.compactMode ? \"📖\" : \"📄\")),\n                    ]),\n                  ]\n                )\n              : _vm._e(),\n            _c(\n              \"button\",\n              {\n                staticClass: \"control-btn minimize-btn\",\n                attrs: { title: _vm.minimized ? \"展开聊天\" : \"收起聊天\" },\n              },\n              [\n                _c(\"span\", { staticClass: \"icon\" }, [\n                  _vm._v(_vm._s(_vm.minimized ? \"⬆\" : \"⬇\")),\n                ]),\n              ]\n            ),\n          ]),\n        ]\n      ),\n      !_vm.minimized\n        ? _c(\"div\", { staticClass: \"chat-body\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"chat-channels\" },\n              _vm._l(_vm.channels, function (channel, index) {\n                return _c(\n                  \"button\",\n                  {\n                    key: index,\n                    staticClass: \"channel-tab\",\n                    class: { active: _vm.currentChannelIndex === index },\n                    attrs: { \"data-icon\": _vm.getChannelIcon(channel.id) },\n                    on: {\n                      click: function ($event) {\n                        return _vm.switchChannel(index)\n                      },\n                    },\n                  },\n                  [\n                    _c(\"span\", { staticClass: \"channel-icon\" }, [\n                      _vm._v(_vm._s(_vm.getChannelIcon(channel.id))),\n                    ]),\n                    _c(\"span\", { staticClass: \"channel-name\" }, [\n                      _vm._v(_vm._s(channel.name)),\n                    ]),\n                    channel.unread > 0\n                      ? _c(\"span\", { staticClass: \"channel-badge\" }, [\n                          _vm._v(\n                            _vm._s(channel.unread > 99 ? \"99+\" : channel.unread)\n                          ),\n                        ])\n                      : _vm._e(),\n                  ]\n                )\n              }),\n              0\n            ),\n            _c(\n              \"div\",\n              { ref: \"chatMessagesContainer\", staticClass: \"chat-messages\" },\n              [\n                _vm.filteredMessages.length > 0\n                  ? _vm._l(_vm.filteredMessages, function (msg, index) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: index,\n                          staticClass: \"chat-message\",\n                          class: {\n                            \"system-message\": msg.type === \"system\",\n                            \"npc-message\": msg.type === \"npc\",\n                            \"player-message\": msg.type === \"player\",\n                            \"self-message\": msg.isSelf,\n                            compact: _vm.compactMode,\n                          },\n                        },\n                        [\n                          msg.type !== \"system\"\n                            ? _c(\"div\", { staticClass: \"message-header\" }, [\n                                _c(\n                                  \"span\",\n                                  {\n                                    staticClass: \"message-sender\",\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.handleSenderClick(msg)\n                                      },\n                                    },\n                                  },\n                                  [_vm._v(_vm._s(msg.sender))]\n                                ),\n                                msg.timestamp && !_vm.compactMode\n                                  ? _c(\n                                      \"span\",\n                                      { staticClass: \"message-time\" },\n                                      [\n                                        _vm._v(\n                                          _vm._s(_vm.formatTime(msg.timestamp))\n                                        ),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                              ])\n                            : _vm._e(),\n                          _c(\"div\", { staticClass: \"message-content\" }, [\n                            _vm._v(_vm._s(msg.content)),\n                          ]),\n                          msg.timestamp && _vm.compactMode\n                            ? _c(\n                                \"div\",\n                                { staticClass: \"message-time-compact\" },\n                                [_vm._v(_vm._s(_vm.formatTime(msg.timestamp)))]\n                              )\n                            : _vm._e(),\n                        ]\n                      )\n                    })\n                  : _c(\"div\", { staticClass: \"empty-messages\" }, [\n                      _c(\"div\", { staticClass: \"empty-icon\" }, [_vm._v(\"💬\")]),\n                      _c(\"div\", { staticClass: \"empty-text\" }, [\n                        _vm._v(\"暂无消息\"),\n                      ]),\n                      _c(\"div\", { staticClass: \"empty-hint\" }, [\n                        _vm._v(\"在下方输入框发送消息开始聊天\"),\n                      ]),\n                    ]),\n              ],\n              2\n            ),\n            _c(\"div\", { staticClass: \"chat-input-container\" }, [\n              _c(\"div\", { staticClass: \"input-wrapper\" }, [\n                _c(\"input\", {\n                  directives: [\n                    {\n                      name: \"model\",\n                      rawName: \"v-model\",\n                      value: _vm.newMessage,\n                      expression: \"newMessage\",\n                    },\n                  ],\n                  staticClass: \"chat-input\",\n                  attrs: {\n                    placeholder: _vm.getInputPlaceholder(),\n                    disabled: !_vm.isConnected,\n                    maxlength: \"200\",\n                  },\n                  domProps: { value: _vm.newMessage },\n                  on: {\n                    keyup: function ($event) {\n                      if (\n                        !$event.type.indexOf(\"key\") &&\n                        _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                      )\n                        return null\n                      return _vm.sendMessage.apply(null, arguments)\n                    },\n                    focus: _vm.onInputFocus,\n                    blur: _vm.onInputBlur,\n                    input: function ($event) {\n                      if ($event.target.composing) return\n                      _vm.newMessage = $event.target.value\n                    },\n                  },\n                }),\n                _vm.newMessage.length > 150\n                  ? _c(\"div\", { staticClass: \"input-counter\" }, [\n                      _vm._v(_vm._s(_vm.newMessage.length) + \"/200\"),\n                    ])\n                  : _vm._e(),\n              ]),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"send-btn\",\n                  attrs: {\n                    disabled: !_vm.isConnected || !_vm.newMessage.trim(),\n                    title: !_vm.isConnected ? \"未连接到聊天服务器\" : \"发送消息\",\n                  },\n                  on: { click: _vm.sendMessage },\n                },\n                [\n                  _c(\"span\", { staticClass: \"send-icon\" }, [_vm._v(\"📤\")]),\n                  _c(\"span\", { staticClass: \"send-text\" }, [_vm._v(\"发送\")]),\n                ]\n              ),\n            ]),\n          ])\n        : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MAAEC,SAAS,EAAEL,GAAG,CAACK,SAAS;MAAEC,OAAO,EAAEN,GAAG,CAACO;IAAY;EAC9D,CAAC,EACD,CACEN,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,aAAa;IAAEK,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACU;IAAe;EAAE,CAAC,EACjE,CACET,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACzDV,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEQ,SAAS,EAAEZ,GAAG,CAACa;IAAY,CAAC;IACrCC,KAAK,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACa,WAAW,GAAG,KAAK,GAAG;IAAM;EAClD,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1C,CAACH,GAAG,CAACK,SAAS,GACVJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,aAAa;IAC1BW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBP,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;QACvBA,MAAM,CAACC,eAAe,CAAC,CAAC;QACxB,OAAOjB,GAAG,CAACkB,aAAa,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACjD;IACF;EACF,CAAC,EACD,CAACnB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACtD,CAAC,GACDX,GAAG,CAACqB,EAAE,CAAC,CAAC,EACZ,CAACrB,GAAG,CAACK,SAAS,GACVJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,aAAa;IAC1BW,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBP,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;QACvBA,MAAM,CAACC,eAAe,CAAC,CAAC;QACxB,OAAOjB,GAAG,CAACsB,iBAAiB,CAACH,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACrD;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAClCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACO,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAC9C,CAAC,CAEN,CAAC,GACDP,GAAG,CAACqB,EAAE,CAAC,CAAC,EACZpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,0BAA0B;IACvCW,KAAK,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACK,SAAS,GAAG,MAAM,GAAG;IAAO;EAClD,CAAC,EACD,CACEJ,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAClCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACK,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAC1C,CAAC,CAEN,CAAC,CACF,CAAC,CAEN,CAAC,EACD,CAACL,GAAG,CAACK,SAAS,GACVJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChCH,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACyB,QAAQ,EAAE,UAAUC,OAAO,EAAEC,KAAK,EAAE;IAC7C,OAAO1B,EAAE,CACP,QAAQ,EACR;MACE2B,GAAG,EAAED,KAAK;MACVxB,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE;QAAEyB,MAAM,EAAE7B,GAAG,CAAC8B,mBAAmB,KAAKH;MAAM,CAAC;MACpDb,KAAK,EAAE;QAAE,WAAW,EAAEd,GAAG,CAAC+B,cAAc,CAACL,OAAO,CAACM,EAAE;MAAE,CAAC;MACtDxB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;UACvB,OAAOhB,GAAG,CAACiC,aAAa,CAACN,KAAK,CAAC;QACjC;MACF;IACF,CAAC,EACD,CACE1B,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAAC+B,cAAc,CAACL,OAAO,CAACM,EAAE,CAAC,CAAC,CAAC,CAC/C,CAAC,EACF/B,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACG,OAAO,CAACQ,IAAI,CAAC,CAAC,CAC7B,CAAC,EACFR,OAAO,CAACS,MAAM,GAAG,CAAC,GACdlC,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CACJX,GAAG,CAACuB,EAAE,CAACG,OAAO,CAACS,MAAM,GAAG,EAAE,GAAG,KAAK,GAAGT,OAAO,CAACS,MAAM,CACrD,CAAC,CACF,CAAC,GACFnC,GAAG,CAACqB,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEmC,GAAG,EAAE,uBAAuB;IAAEjC,WAAW,EAAE;EAAgB,CAAC,EAC9D,CACEH,GAAG,CAACqC,gBAAgB,CAACC,MAAM,GAAG,CAAC,GAC3BtC,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACqC,gBAAgB,EAAE,UAAUE,GAAG,EAAEZ,KAAK,EAAE;IACjD,OAAO1B,EAAE,CACP,KAAK,EACL;MACE2B,GAAG,EAAED,KAAK;MACVxB,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QACL,gBAAgB,EAAEmC,GAAG,CAACC,IAAI,KAAK,QAAQ;QACvC,aAAa,EAAED,GAAG,CAACC,IAAI,KAAK,KAAK;QACjC,gBAAgB,EAAED,GAAG,CAACC,IAAI,KAAK,QAAQ;QACvC,cAAc,EAAED,GAAG,CAACE,MAAM;QAC1BnC,OAAO,EAAEN,GAAG,CAACO;MACf;IACF,CAAC,EACD,CACEgC,GAAG,CAACC,IAAI,KAAK,QAAQ,GACjBvC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,MAAM,EACN;MACEE,WAAW,EAAE,gBAAgB;MAC7BK,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUO,MAAM,EAAE;UACvB,OAAOhB,GAAG,CAAC0C,iBAAiB,CAACH,GAAG,CAAC;QACnC;MACF;IACF,CAAC,EACD,CAACvC,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACgB,GAAG,CAACI,MAAM,CAAC,CAAC,CAC7B,CAAC,EACDJ,GAAG,CAACK,SAAS,IAAI,CAAC5C,GAAG,CAACO,WAAW,GAC7BN,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEH,GAAG,CAACW,EAAE,CACJX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAAC6C,UAAU,CAACN,GAAG,CAACK,SAAS,CAAC,CACtC,CAAC,CAEL,CAAC,GACD5C,GAAG,CAACqB,EAAE,CAAC,CAAC,CACb,CAAC,GACFrB,GAAG,CAACqB,EAAE,CAAC,CAAC,EACZpB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACgB,GAAG,CAACO,OAAO,CAAC,CAAC,CAC5B,CAAC,EACFP,GAAG,CAACK,SAAS,IAAI5C,GAAG,CAACO,WAAW,GAC5BN,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CAACH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAAC6C,UAAU,CAACN,GAAG,CAACK,SAAS,CAAC,CAAC,CAAC,CAChD,CAAC,GACD5C,GAAG,CAACqB,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CAAC,CAAC,GACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxDV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACW,EAAE,CAAC,gBAAgB,CAAC,CACzB,CAAC,CACH,CAAC,CACP,EACD,CACF,CAAC,EACDV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,OAAO,EAAE;IACV8C,UAAU,EAAE,CACV;MACEb,IAAI,EAAE,OAAO;MACbc,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAEjD,GAAG,CAACkD,UAAU;MACrBC,UAAU,EAAE;IACd,CAAC,CACF;IACDhD,WAAW,EAAE,YAAY;IACzBW,KAAK,EAAE;MACLsC,WAAW,EAAEpD,GAAG,CAACqD,mBAAmB,CAAC,CAAC;MACtCC,QAAQ,EAAE,CAACtD,GAAG,CAACa,WAAW;MAC1B0C,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE;MAAEP,KAAK,EAAEjD,GAAG,CAACkD;IAAW,CAAC;IACnC1C,EAAE,EAAE;MACFiD,KAAK,EAAE,SAAAA,CAAUzC,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACwB,IAAI,CAACkB,OAAO,CAAC,KAAK,CAAC,IAC3B1D,GAAG,CAAC2D,EAAE,CAAC3C,MAAM,CAAC4C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE5C,MAAM,CAACY,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,OAAO5B,GAAG,CAAC6D,WAAW,CAAC1C,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C,CAAC;MACD0C,KAAK,EAAE9D,GAAG,CAAC+D,YAAY;MACvBC,IAAI,EAAEhE,GAAG,CAACiE,WAAW;MACrBC,KAAK,EAAE,SAAAA,CAAUlD,MAAM,EAAE;QACvB,IAAIA,MAAM,CAACmD,MAAM,CAACC,SAAS,EAAE;QAC7BpE,GAAG,CAACkD,UAAU,GAAGlC,MAAM,CAACmD,MAAM,CAAClB,KAAK;MACtC;IACF;EACF,CAAC,CAAC,EACFjD,GAAG,CAACkD,UAAU,CAACZ,MAAM,GAAG,GAAG,GACvBrC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACkD,UAAU,CAACZ,MAAM,CAAC,GAAG,MAAM,CAAC,CAC/C,CAAC,GACFtC,GAAG,CAACqB,EAAE,CAAC,CAAC,CACb,CAAC,EACFpB,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,UAAU;IACvBW,KAAK,EAAE;MACLwC,QAAQ,EAAE,CAACtD,GAAG,CAACa,WAAW,IAAI,CAACb,GAAG,CAACkD,UAAU,CAACmB,IAAI,CAAC,CAAC;MACpDtD,KAAK,EAAE,CAACf,GAAG,CAACa,WAAW,GAAG,WAAW,GAAG;IAC1C,CAAC;IACDL,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAAC6D;IAAY;EAC/B,CAAC,EACD,CACE5D,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxDV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAE5D,CAAC,CACF,CAAC,CACH,CAAC,GACFX,GAAG,CAACqB,EAAE,CAAC,CAAC,CAEhB,CAAC;AACH,CAAC;AACD,IAAIiD,eAAe,GAAG,EAAE;AACxBvE,MAAM,CAACwE,aAAa,GAAG,IAAI;AAE3B,SAASxE,MAAM,EAAEuE,eAAe", "ignoreList": []}]}