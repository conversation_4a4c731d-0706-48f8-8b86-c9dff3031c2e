{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue", "mtime": 1749699239704}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CharacterSelect.vue"], "names": [], "mappings": ";AAqJA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CharacterSelect.vue", "sourceRoot": "src/views/setup", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"character-select-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <p class=\"page-subtitle\">请选择您要使用的角色，或创建新角色</p>\n        <div v-if=\"currentRegion\" class=\"current-region\">\n          当前大区: <span class=\"region-name\">{{ currentRegion.name }}</span>\n        </div>\n\n        <!-- 调试信息 -->\n        <div v-if=\"$route.query.debug\" class=\"debug-info\">\n          <h4>调试信息:</h4>\n          <p>加载状态: {{ isLoading }}</p>\n          <p>错误信息: {{ error || '无' }}</p>\n          <p>角色数量: {{ characters.length }}</p>\n          <p>当前大区ID: {{ currentRegion?.id }}</p>\n          <details>\n            <summary>角色数据</summary>\n            <pre>{{ JSON.stringify(characters, null, 2) }}</pre>\n          </details>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-spinner\"></div>\n        <p class=\"loading-text\">正在加载角色列表...</p>\n      </div>\n\n      <!-- 错误提示 -->\n      <div v-if=\"error && !isLoading\" class=\"error-container\">\n        <div class=\"error-message\">\n          <i class=\"error-icon\">⚠️</i>\n          <p>{{ error }}</p>\n          <button @click=\"loadCharacterList\" class=\"retry-btn\">重试</button>\n        </div>\n      </div>\n\n      <!-- 角色列表 -->\n      <div v-if=\"!isLoading && !error\" class=\"characters-container\">\n        <!-- 空状态提示 -->\n        <div v-if=\"characters.length === 0\" class=\"empty-state\">\n          <div class=\"empty-icon\">👤</div>\n          <h3>还没有角色</h3>\n          <p>在当前大区 \"{{ currentRegion?.name }}\" 中还没有创建角色</p>\n        </div>\n\n        <!-- 角色卡片网格 -->\n        <div v-else class=\"characters-grid\">\n          <!-- 现有角色 -->\n          <div \n            v-for=\"character in characters\" \n            :key=\"character.id\"\n            class=\"character-card\"\n            :class=\"{ 'selected': selectedCharacter?.id === character.id }\"\n            @click=\"selectCharacterLocal(character)\"\n          >\n            <div class=\"character-avatar\">\n              <img :src=\"character.avatar || '/static/game/avatars/default.png'\" :alt=\"character.name\" />\n              <div class=\"character-level\">Lv.{{ character.level }}</div>\n            </div>\n            <div class=\"character-info\">\n              <h3 class=\"character-name\">{{ character.name }}</h3>\n              <p class=\"character-class\">{{ getClassName(character.profession || character.class) }}</p>\n              <div class=\"character-stats\">\n                <span class=\"stat\">经验: {{ character.experience || character.exp || 0 }}</span>\n                <span class=\"stat\">创建时间: {{ formatDate(character.created_at || character.createdAt) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 创建新角色按钮 -->\n          <div \n            v-if=\"characters.length < 3\"\n            class=\"character-card create-new\"\n            @click=\"createNewCharacter\"\n          >\n            <div class=\"create-icon\">\n              <i class=\"plus-icon\">+</i>\n            </div>\n            <div class=\"create-text\">\n              <h3>创建新角色</h3>\n              <p>还可以创建 {{ 3 - characters.length }} 个角色</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <!-- 返回大区选择图片按钮 -->\n          <div\n            @click=\"goBack\"\n            @mousedown=\"handleBackMouseDown\"\n            @mouseup=\"handleBackMouseUp\"\n            @mouseleave=\"handleBackMouseUp\"\n            class=\"return-btn\"\n            :class=\"{ 'pressed': isBackPressed }\"\n          >\n            <img\n              :src=\"getBackButtonImage()\"\n              alt=\"返回大区选择\"\n              class=\"return-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n\n          <!-- 如果没有角色，显示创建角色图片按钮 -->\n          <div\n            v-if=\"characters.length === 0\"\n            @click=\"createNewCharacter\"\n            @mousedown=\"handleCreateMouseDown\"\n            @mouseup=\"handleCreateMouseUp\"\n            @mouseleave=\"handleCreateMouseUp\"\n            class=\"create-character-btn\"\n            :class=\"{ 'pressed': isCreatePressed }\"\n          >\n            <img\n              :src=\"getCreateButtonImage()\"\n              alt=\"创建第一个角色\"\n              class=\"create-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n\n          <!-- 如果有角色，显示进入游戏图片按钮 -->\n          <div\n            v-else\n            @click=\"confirmSelection\"\n            @mousedown=\"handleEnterMouseDown\"\n            @mouseup=\"handleEnterMouseUp\"\n            @mouseleave=\"handleEnterMouseUp\"\n            class=\"enter-game-btn\"\n            :class=\"{ 'disabled': !selectedCharacter, 'pressed': isEnterPressed }\"\n          >\n            <img\n              :src=\"getEnterButtonImage()\"\n              alt=\"进入游戏\"\n              class=\"enter-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapState, mapActions } from 'vuex'\nimport { regionService } from '@/api'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'CharacterSelect',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      selectedCharacter: null,\n      currentRegion: null,\n      // 按钮状态\n      isBackPressed: false,\n      isCreatePressed: false,\n      isEnterPressed: false\n    }\n  },\n  computed: {\n    ...mapState('character', ['isLoading', 'error']),\n\n    // 获取当前大区的角色列表\n    characters() {\n      if (!this.currentRegion) return [];\n      return this.$store.getters['character/charactersByRegion'](this.currentRegion.id);\n    }\n  },\n  async created() {\n    logger.debug('[CharacterSelect] 页面初始化')\n\n    // 获取当前选择的大区\n    this.currentRegion = regionService.getCurrentRegion()\n    logger.debug('[CharacterSelect] 当前大区:', this.currentRegion)\n\n    if (!this.currentRegion) {\n      this.showToast('请先选择大区')\n      this.$router.push('/setup/region-select')\n      return\n    }\n\n    await this.loadCharacterList()\n  },\n  methods: {\n    ...mapActions('character', ['loadCharacters', 'selectCharacter']),\n\n    async loadCharacterList() {\n      if (!this.currentRegion) return\n\n      try {\n        logger.debug('[CharacterSelect] 开始加载角色列表，大区ID:', this.currentRegion.id)\n        await this.loadCharacters(this.currentRegion.id)\n        logger.debug('[CharacterSelect] 角色列表加载完成，角色数量:', this.characters.length)\n\n        // 如果没有角色，显示提示\n        if (this.characters.length === 0) {\n          logger.info('[CharacterSelect] 当前大区没有角色')\n        }\n      } catch (error) {\n        logger.error('[CharacterSelect] 加载角色列表失败:', error)\n        this.showToast('加载角色列表失败: ' + (error.message || '未知错误'))\n      }\n    },\n\n    selectCharacterLocal(character) {\n      this.selectedCharacter = character\n      logger.debug('[CharacterSelect] 选择角色:', character.name)\n    },\n\n    async confirmSelection() {\n      if (!this.selectedCharacter) {\n        this.showToast('请先选择一个角色')\n        return\n      }\n\n      try {\n        const success = await this.selectCharacter(this.selectedCharacter)\n        if (success) {\n          this.showToast('角色选择成功，正在进入游戏...')\n          // 跳转到游戏主界面\n          setTimeout(() => {\n            this.$router.push('/game/main')\n          }, 1000)\n        } else {\n          this.showToast('选择角色失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[CharacterSelect] 确认选择失败:', error)\n        this.showToast('选择角色失败，请重试')\n      }\n    },\n\n    createNewCharacter() {\n      this.$router.push('/setup/create-character')\n    },\n\n    goBack() {\n      this.$router.push('/setup/region-select')\n    },\n\n    // 按钮状态处理方法\n    handleBackMouseDown() {\n      this.isBackPressed = true\n    },\n\n    handleBackMouseUp() {\n      this.isBackPressed = false\n    },\n\n    handleCreateMouseDown() {\n      this.isCreatePressed = true\n    },\n\n    handleCreateMouseUp() {\n      this.isCreatePressed = false\n    },\n\n    handleEnterMouseDown() {\n      if (!this.selectedCharacter) return\n      this.isEnterPressed = true\n    },\n\n    handleEnterMouseUp() {\n      this.isEnterPressed = false\n    },\n\n    // 按钮图片获取方法\n    getBackButtonImage() {\n      return this.isBackPressed\n        ? '/static/game/UI/anniu/fhui_.png'\n        : '/static/game/UI/anniu/fhui_2.png'\n    },\n\n    getCreateButtonImage() {\n      return this.isCreatePressed\n        ? '/static/game/UI/anniu/cj_1.png'\n        : '/static/game/UI/anniu/cj.png'\n    },\n\n    getEnterButtonImage() {\n      if (!this.selectedCharacter) {\n        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片\n      }\n      return this.isEnterPressed\n        ? '/static/game/UI/anniu/jr_4.png'\n        : '/static/game/UI/anniu/jr_3.png'\n    },\n\n    getClassName(profession) {\n      const classMap = {\n        'warrior': '武士',\n        'scholar': '文人',\n        'mystic': '异人'\n      }\n      return classMap[profession] || profession\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '未知'\n      const date = new Date(dateString)\n      return date.toLocaleDateString('zh-CN')\n    },\n\n    showToast(message) {\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.character-select-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.page-title {\n  font-size: 32px;\n  color: #d4af37;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.page-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  margin: 0 0 10px 0;\n}\n\n.current-region {\n  font-size: 14px;\n  color: #999;\n}\n\n.region-name {\n  color: #d4af37;\n  font-weight: bold;\n}\n\n.debug-info {\n  margin-top: 20px;\n  padding: 15px;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid #444;\n  border-radius: 5px;\n  font-size: 12px;\n  text-align: left;\n}\n\n.debug-info h4 {\n  color: #d4af37;\n  margin: 0 0 10px 0;\n}\n\n.debug-info p {\n  margin: 5px 0;\n  color: #ccc;\n}\n\n.debug-info pre {\n  background: rgba(0, 0, 0, 0.5);\n  padding: 10px;\n  border-radius: 3px;\n  overflow-x: auto;\n  font-size: 11px;\n  color: #fff;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #333;\n  border-top: 4px solid #d4af37;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  color: #ccc;\n  font-size: 16px;\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.error-message {\n  text-align: center;\n  color: #ff6b6b;\n  background: rgba(255, 107, 107, 0.1);\n  padding: 30px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.retry-btn {\n  margin-top: 15px;\n  padding: 8px 16px;\n  background: #d4af37;\n  color: #000;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.retry-btn:hover {\n  background: #b8941f;\n}\n\n.characters-container {\n  flex: 1;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #ccc;\n}\n\n.empty-icon {\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.5;\n}\n\n.empty-state h3 {\n  color: #d4af37;\n  margin: 0 0 10px 0;\n  font-size: 24px;\n}\n\n.empty-state p {\n  margin: 0 0 30px 0;\n  font-size: 16px;\n}\n\n.characters-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  margin-bottom: 30px;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.character-card {\n  background: linear-gradient(135deg, rgba(30, 30, 80, 0.9), rgba(50, 50, 120, 0.8));\n  border: 2px solid #4a5568;\n  border-radius: 8px;\n  padding: 15px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  min-height: 100px;\n  position: relative;\n  overflow: hidden;\n}\n\n.character-card:hover {\n  border-color: #d4af37;\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(255, 215, 0, 0.1));\n  transform: translateY(-2px);\n}\n\n.character-card.selected {\n  border-color: #d4af37;\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(255, 215, 0, 0.2));\n  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);\n  transform: translateY(-2px);\n}\n\n.character-card.selected::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);\n  pointer-events: none;\n}\n\n.character-card.create-new {\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  border-style: dashed;\n  border-color: #666;\n  background: linear-gradient(135deg, rgba(60, 60, 60, 0.3), rgba(80, 80, 80, 0.2));\n}\n\n.character-card.create-new:hover {\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(255, 215, 0, 0.05));\n  border-color: #d4af37;\n}\n\n.character-avatar {\n  position: relative;\n  margin-right: 20px;\n  flex-shrink: 0;\n}\n\n.character-avatar img {\n  width: 70px;\n  height: 70px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 3px solid #4a5568;\n}\n\n.character-level {\n  position: absolute;\n  bottom: -5px;\n  right: -5px;\n  background: linear-gradient(135deg, #d4af37, #ffd700);\n  color: #000;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n  border: 1px solid #b8941f;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.character-info {\n  text-align: center;\n}\n\n.character-name {\n  font-size: 18px;\n  color: #d4af37;\n  margin: 0 0 5px 0;\n}\n\n.character-class {\n  font-size: 14px;\n  color: #ccc;\n  margin: 0 0 10px 0;\n}\n\n.character-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.stat {\n  font-size: 12px;\n  color: #999;\n}\n\n.create-icon {\n  margin-bottom: 15px;\n}\n\n.plus-icon {\n  font-size: 48px;\n  color: #666;\n  font-style: normal;\n}\n\n.create-text h3 {\n  color: #d4af37;\n  margin: 0 0 8px 0;\n  font-size: 18px;\n}\n\n.create-text p {\n  color: #999;\n  margin: 0;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 30px;\n  padding: 20px 0;\n  flex-wrap: nowrap; /* 确保按钮不换行 */\n}\n\n\n\n/* 图片按钮通用样式 */\n.create-character-btn,\n.return-btn,\n.enter-game-btn {\n  cursor: pointer;\n  display: inline-block;\n  transition: all 0.2s ease;\n  flex-shrink: 0; /* 防止按钮被压缩 */\n}\n\n.enter-game-btn.disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n/* 创建角色图片按钮样式 */\n.create-character-btn.pressed .create-btn-img {\n  transform: scale(0.95);\n}\n\n.create-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 返回按钮图片样式 */\n.return-btn.pressed .return-btn-img {\n  transform: scale(0.95);\n}\n\n.return-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 进入游戏按钮图片样式 */\n.enter-game-btn.pressed .enter-btn-img {\n  transform: scale(0.95);\n}\n\n.enter-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .character-select-container {\n    padding: 15px;\n  }\n\n  .page-title {\n    font-size: 24px;\n  }\n\n  .characters-grid {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n\n  .action-buttons {\n    flex-direction: row;\n    flex-wrap: nowrap; /* 保持按钮在同一行 */\n    justify-content: center;\n    gap: 15px;\n    padding: 15px 0;\n  }\n\n  .create-character-btn,\n  .return-btn,\n  .enter-game-btn {\n    display: flex;\n    justify-content: center;\n    flex-shrink: 0; /* 防止按钮被压缩 */\n  }\n\n  .create-btn-img,\n  .return-btn-img,\n  .enter-btn-img {\n    max-width: 120px; /* 在移动设备上稍微缩小按钮 */\n  }\n\n  .empty-state {\n    padding: 40px 15px;\n  }\n\n  .empty-state h3 {\n    font-size: 20px;\n  }\n\n  .empty-state p {\n    font-size: 14px;\n  }\n}\n</style>\n"]}]}