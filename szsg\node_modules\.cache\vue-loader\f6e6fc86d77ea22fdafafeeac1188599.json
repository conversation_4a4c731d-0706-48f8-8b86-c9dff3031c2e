{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Battle.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Battle.vue", "mtime": 1749890584410}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Battle.vue"], "names": [], "mappings": ";AAsHA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Battle.vue", "sourceRoot": "src/views/game", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"battle-container\">\n      <!-- 怪物信息区域 -->\n      <div class=\"monster-section\">\n        <div class=\"monster-info\">\n          <img :src=\"monster.avatar || '/static/game/ui/default_monster.png'\" class=\"monster-avatar\" />\n          <div class=\"monster-details\">\n            <h3>{{ monster.name }} Lv.{{ monster.level }}</h3>\n            <div class=\"hp-bar\">\n              <div class=\"bar-fill hp-fill\" :style=\"{ width: monsterHpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ monster.hp }}/{{ monster.max_hp }}</span>\n            </div>\n            <div class=\"mp-bar\">\n              <div class=\"bar-fill mp-fill\" :style=\"{ width: monsterMpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ monster.mp }}/{{ monster.max_mp }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 战斗动画区域 -->\n      <div class=\"battle-animation-area\" ref=\"animationArea\">\n        <BattleAnimation ref=\"battleAnimation\" />\n      </div>\n\n      <!-- 角色信息区域 -->\n      <div class=\"character-section\">\n        <div class=\"character-info\">\n          <img :src=\"character.avatar || '/static/game/ui/default_avatar.png'\" class=\"character-avatar\" />\n          <div class=\"character-details\">\n            <h3>{{ character.name }} Lv.{{ character.level }}</h3>\n            <div class=\"hp-bar\">\n              <div class=\"bar-fill hp-fill\" :style=\"{ width: characterHpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ character.hp }}/{{ character.max_hp }}</span>\n            </div>\n            <div class=\"mp-bar\">\n              <div class=\"bar-fill mp-fill\" :style=\"{ width: characterMpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ character.mp }}/{{ character.max_mp }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 操作按钮区域 -->\n      <div class=\"action-buttons\">\n        <button \n          @click=\"attack\" \n          :disabled=\"!canAct\" \n          class=\"action-btn attack-btn\"\n        >\n          攻击\n        </button>\n        <button \n          @click=\"openItems\" \n          :disabled=\"!canAct\" \n          class=\"action-btn item-btn\"\n        >\n          物品\n        </button>\n        <button \n          @click=\"flee\" \n          :disabled=\"!canAct\" \n          class=\"action-btn flee-btn\"\n        >\n          逃跑\n        </button>\n        <button \n          @click=\"returnToMain\" \n          v-if=\"battleFinished\"\n          class=\"action-btn return-btn\"\n        >\n          返回\n        </button>\n      </div>\n\n      <!-- 战斗日志 -->\n      <div class=\"battle-log\" v-if=\"showLog\">\n        <div class=\"log-header\">\n          <h4>战斗记录</h4>\n          <button @click=\"toggleLog\" class=\"close-btn\">×</button>\n        </div>\n        <div class=\"log-content\">\n          <div \n            v-for=\"(log, index) in battleLogs\" \n            :key=\"index\"\n            class=\"log-entry\"\n          >\n            <span class=\"log-time\">第{{ log.round }}回合</span>\n            <span class=\"log-message\">{{ formatLogMessage(log) }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 战斗结果弹窗 -->\n      <div v-if=\"showResult\" class=\"battle-result-modal\">\n        <div class=\"result-content\">\n          <h3 :class=\"resultClass\">{{ resultTitle }}</h3>\n          <div v-if=\"battleResult === 'victory'\" class=\"rewards\">\n            <p>获得经验: {{ rewards.exp_gained }}</p>\n            <p>获得金币: {{ rewards.gold_gained }}</p>\n            <div v-if=\"rewards.items_gained && rewards.items_gained.length > 0\">\n              <p>获得物品:</p>\n              <ul>\n                <li v-for=\"item in rewards.items_gained\" :key=\"item.item_id\">\n                  {{ item.name }} × {{ item.quantity }}\n                </li>\n              </ul>\n            </div>\n          </div>\n          <button @click=\"closeResult\" class=\"confirm-btn\">确定</button>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport BattleAnimation from '@/components/game/BattleAnimation.vue'\nimport battleService from '@/api/services/battleService.js'\nimport logger from '@/utils/logger.js'\n\nexport default {\n  name: 'Battle',\n  components: {\n    GameLayout,\n    BattleAnimation\n  },\n  \n  data() {\n    return {\n      battleId: null,\n      character: {\n        id: null,\n        name: '',\n        level: 1,\n        avatar: '',\n        hp: 100,\n        max_hp: 100,\n        mp: 50,\n        max_mp: 50\n      },\n      monster: {\n        id: null,\n        name: '',\n        level: 1,\n        avatar: '',\n        hp: 100,\n        max_hp: 100,\n        mp: 50,\n        max_mp: 50\n      },\n      battleStatus: 'ongoing', // ongoing, victory, defeat, fled\n      canAct: true,\n      isProcessing: false,\n      battleLogs: [],\n      showLog: false,\n      showResult: false,\n      battleResult: null,\n      rewards: {},\n      animationManager: null\n    }\n  },\n\n  computed: {\n    characterHpPercent() {\n      return this.character.max_hp > 0 ? (this.character.hp / this.character.max_hp) * 100 : 0\n    },\n    \n    characterMpPercent() {\n      return this.character.max_mp > 0 ? (this.character.mp / this.character.max_mp) * 100 : 0\n    },\n    \n    monsterHpPercent() {\n      return this.monster.max_hp > 0 ? (this.monster.hp / this.monster.max_hp) * 100 : 0\n    },\n    \n    monsterMpPercent() {\n      return this.monster.max_mp > 0 ? (this.monster.mp / this.monster.max_mp) * 100 : 0\n    },\n    \n    battleFinished() {\n      return ['victory', 'defeat', 'fled'].includes(this.battleStatus)\n    },\n    \n    resultTitle() {\n      switch (this.battleResult) {\n        case 'victory': return '战斗胜利！'\n        case 'defeat': return '战斗失败！'\n        case 'fled': return '成功逃脱！'\n        default: return ''\n      }\n    },\n    \n    resultClass() {\n      switch (this.battleResult) {\n        case 'victory': return 'victory-text'\n        case 'defeat': return 'defeat-text'\n        case 'fled': return 'flee-text'\n        default: return ''\n      }\n    }\n  },\n\n  async mounted() {\n    try {\n      // 从路由参数获取战斗信息\n      const { characterId, monsterId, locationId } = this.$route.query\n      logger.debug('[Battle] 路由参数:', { characterId, monsterId, locationId })\n\n      if (!characterId || !monsterId) {\n        logger.error('[Battle] 缺少战斗参数')\n        this.showToast('缺少战斗参数')\n        this.$router.push('/game/main')\n        return\n      }\n\n      // 验证用户认证状态\n      const isAuthenticated = this.$store.state.auth.isAuthenticated\n      logger.debug('[Battle] 用户认证状态:', isAuthenticated)\n\n      if (!isAuthenticated) {\n        logger.error('[Battle] 用户未认证')\n        this.showToast('请先登录')\n        this.$router.push('/login')\n        return\n      }\n\n      // 开始战斗\n      await this.initBattle(parseInt(characterId), parseInt(monsterId), locationId)\n\n    } catch (error) {\n      logger.error('[Battle] 初始化战斗失败:', error)\n      this.showToast('初始化战斗失败')\n      this.$router.push('/game/main')\n    }\n  },\n\n  methods: {\n    // 初始化战斗\n    async initBattle(characterId, monsterId, locationId) {\n      try {\n        logger.debug('[Battle] 开始初始化战斗:', { characterId, monsterId, locationId })\n\n        // 验证参数\n        if (!characterId || !monsterId) {\n          throw new Error('缺少必要的战斗参数')\n        }\n\n        const result = await battleService.startBattle(characterId, monsterId, locationId)\n        logger.debug('[Battle] 战斗API响应:', result)\n\n        // 检查API响应结构 - battleService返回的是res.data，所以直接检查battle_id\n        if (result && result.battle_id && result.battle) {\n          this.battleId = result.battle_id\n          this.updateBattleData(result.battle)\n          this.showToast('战斗开始！')\n        } else {\n          // 显示更详细的错误信息\n          const errorMessage = result?.message || result?.error || '开始战斗失败'\n          logger.error('[Battle] 战斗失败详情:', result)\n          throw new Error(errorMessage)\n        }\n      } catch (error) {\n        logger.error('[Battle] 开始战斗失败:', error)\n        // 如果是网络错误，显示更友好的错误信息\n        if (error.response) {\n          logger.error('[Battle] HTTP错误:', error.response.status, error.response.data)\n          throw new Error(`服务器错误 (${error.response.status}): ${error.response.data?.message || '未知错误'}`)\n        } else if (error.request) {\n          logger.error('[Battle] 网络错误:', error.request)\n          throw new Error('网络连接失败，请检查网络连接')\n        } else {\n          throw error\n        }\n      }\n    },\n\n    // 更新战斗数据\n    updateBattleData(battleData) {\n      this.character = { ...battleData.character }\n      this.monster = { ...battleData.monster }\n      this.battleStatus = battleData.status\n      this.canAct = battleData.can_act\n      this.rewards = battleData.rewards || {}\n    },\n\n    // 攻击\n    async attack() {\n      if (!this.canAct || this.isProcessing) return\n      \n      try {\n        this.isProcessing = true\n        this.canAct = false\n        \n        const result = await battleService.performAction(this.battleId, this.character.id, 'attack')\n        \n        if (result.success) {\n          // 播放攻击动画\n          if (this.$refs.battleAnimation) {\n            this.$refs.battleAnimation.playAttackAnimation()\n            \n            // 处理角色攻击结果\n            if (result.data.action_results.character_action) {\n              const charAction = result.data.action_results.character_action\n              this.$refs.battleAnimation.playDamageAnimation(charAction.damage, charAction.is_critical)\n            }\n            \n            // 处理怪物反击\n            if (result.data.action_results.monster_action) {\n              const monsterAction = result.data.action_results.monster_action\n              setTimeout(() => {\n                this.$refs.battleAnimation.playDamageAnimation(monsterAction.damage, monsterAction.is_critical)\n              }, 1500)\n            }\n          }\n          \n          // 更新战斗数据\n          this.updateBattleData(result.data.battle)\n          \n          // 检查战斗是否结束\n          if (result.data.action_results.character_action?.battle_end) {\n            this.handleBattleEnd(result.data.action_results.character_action.battle_end)\n          }\n          \n        } else {\n          this.showToast(result.message || '攻击失败')\n        }\n      } catch (error) {\n        logger.error('[Battle] 攻击失败:', error)\n        this.showToast('攻击失败')\n      } finally {\n        this.isProcessing = false\n        if (this.battleStatus === 'ongoing') {\n          this.canAct = true\n        }\n      }\n    },\n\n    // 逃跑\n    async flee() {\n      if (!this.canAct || this.isProcessing) return\n      \n      try {\n        this.isProcessing = true\n        this.canAct = false\n        \n        const result = await battleService.performAction(this.battleId, this.character.id, 'flee')\n        \n        if (result.success) {\n          if (result.data.action_results.flee_result?.success) {\n            this.battleResult = 'fled'\n            this.battleStatus = 'fled'\n            this.showResult = true\n          } else {\n            this.showToast('逃跑失败！')\n            this.canAct = true\n          }\n        } else {\n          this.showToast(result.message || '逃跑失败')\n          this.canAct = true\n        }\n      } catch (error) {\n        logger.error('[Battle] 逃跑失败:', error)\n        this.showToast('逃跑失败')\n        this.canAct = true\n      } finally {\n        this.isProcessing = false\n      }\n    },\n\n    // 处理战斗结束\n    handleBattleEnd(endResult) {\n      this.battleResult = endResult.result\n      this.battleStatus = endResult.result\n      this.canAct = false\n      \n      if (endResult.rewards) {\n        this.rewards = endResult.rewards\n      }\n      \n      // 延迟显示结果，让动画播放完\n      setTimeout(() => {\n        this.showResult = true\n      }, 2000)\n    },\n\n    // 打开物品界面\n    openItems() {\n      // TODO: 实现物品使用界面\n      this.showToast('物品功能开发中...')\n    },\n\n    // 切换日志显示\n    toggleLog() {\n      this.showLog = !this.showLog\n      if (this.showLog) {\n        this.loadBattleLog()\n      }\n    },\n\n    // 加载战斗日志\n    async loadBattleLog() {\n      try {\n        const result = await battleService.getBattleLog(this.battleId)\n        if (result.success) {\n          this.battleLogs = result.data.battle_log || []\n        }\n      } catch (error) {\n        logger.error('[Battle] 加载战斗日志失败:', error)\n      }\n    },\n\n    // 格式化日志消息\n    formatLogMessage(log) {\n      switch (log.action) {\n        case 'battle_start':\n          return '战斗开始！'\n        case 'character_attack': {\n          const damage = log.data.damage || 0\n          const critical = log.data.is_critical ? '(暴击)' : ''\n          return `你对${this.monster.name}造成了${damage}点伤害${critical}`\n        }\n        case 'monster_attack': {\n          const monsterDamage = log.data.damage || 0\n          const monsterCritical = log.data.is_critical ? '(暴击)' : ''\n          return `${this.monster.name}对你造成了${monsterDamage}点伤害${monsterCritical}`\n        }\n        case 'battle_end':\n          return `战斗结束，结果：${log.data.result}`\n        default:\n          return log.action\n      }\n    },\n\n    // 关闭结果弹窗\n    closeResult() {\n      this.showResult = false\n      this.returnToMain()\n    },\n\n    // 返回主界面\n    returnToMain() {\n      this.$router.push('/game/main')\n    },\n\n    // 显示提示\n    showToast(message) {\n      // 使用全局提示组件\n      if (this.$toast) {\n        this.$toast(message)\n      } else {\n        alert(message)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.battle-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);\n  color: #fff;\n  font-family: 'Microsoft YaHei', sans-serif;\n}\n\n/* 怪物信息区域 */\n.monster-section {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(139, 69, 19, 0.3);\n  border-bottom: 2px solid #8B4513;\n}\n\n.monster-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 15px;\n  border-radius: 10px;\n  border: 2px solid #666;\n}\n\n.monster-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid #ff6b6b;\n  object-fit: cover;\n}\n\n.monster-details h3 {\n  margin: 0 0 10px 0;\n  color: #ff6b6b;\n  font-size: 18px;\n}\n\n/* 战斗动画区域 */\n.battle-animation-area {\n  flex: 2;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* 角色信息区域 */\n.character-section {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(0, 100, 0, 0.2);\n  border-top: 2px solid #228B22;\n}\n\n.character-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 15px;\n  border-radius: 10px;\n  border: 2px solid #666;\n}\n\n.character-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid #87ceeb;\n  object-fit: cover;\n}\n\n.character-details h3 {\n  margin: 0 0 10px 0;\n  color: #87ceeb;\n  font-size: 18px;\n}\n\n/* 血条和蓝条样式 */\n.hp-bar, .mp-bar {\n  position: relative;\n  width: 200px;\n  height: 20px;\n  background: #333;\n  border: 1px solid #666;\n  border-radius: 10px;\n  margin: 5px 0;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  transition: width 0.5s ease;\n  border-radius: 9px;\n}\n\n.hp-fill {\n  background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%);\n}\n\n.mp-fill {\n  background: linear-gradient(90deg, #4444ff 0%, #6666ff 100%);\n}\n\n.bar-text {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  border-top: 2px solid #666;\n}\n\n.action-btn {\n  padding: 12px 24px;\n  font-size: 16px;\n  font-weight: bold;\n  border: 2px solid;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  min-width: 80px;\n}\n\n.attack-btn {\n  border-color: #ff4444;\n  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);\n}\n\n.attack-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ff6666 0%, #ff4444 100%);\n  transform: translateY(-2px);\n}\n\n.item-btn {\n  border-color: #44ff44;\n  background: linear-gradient(135deg, #44ff44 0%, #33cc33 100%);\n}\n\n.item-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #66ff66 0%, #44ff44 100%);\n  transform: translateY(-2px);\n}\n\n.flee-btn {\n  border-color: #ffff44;\n  background: linear-gradient(135deg, #ffff44 0%, #cccc33 100%);\n  color: #333;\n}\n\n.flee-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ffff66 0%, #ffff44 100%);\n  transform: translateY(-2px);\n}\n\n.return-btn {\n  border-color: #888;\n  background: linear-gradient(135deg, #888 0%, #666 100%);\n}\n\n.return-btn:hover {\n  background: linear-gradient(135deg, #aaa 0%, #888 100%);\n  transform: translateY(-2px);\n}\n\n.action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 战斗日志 */\n.battle-log {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 400px;\n  max-height: 300px;\n  background: rgba(0, 0, 0, 0.9);\n  border: 2px solid #666;\n  border-radius: 10px;\n  z-index: 1000;\n}\n\n.log-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border-bottom: 1px solid #666;\n}\n\n.log-header h4 {\n  margin: 0;\n  color: #fff;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 20px;\n  cursor: pointer;\n  padding: 0;\n  width: 25px;\n  height: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.log-content {\n  max-height: 200px;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.log-entry {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.log-time {\n  color: #888;\n  min-width: 60px;\n}\n\n.log-message {\n  color: #fff;\n}\n\n/* 战斗结果弹窗 */\n.battle-result-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 2000;\n}\n\n.result-content {\n  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);\n  border: 3px solid #666;\n  border-radius: 15px;\n  padding: 30px;\n  text-align: center;\n  min-width: 300px;\n}\n\n.result-content h3 {\n  margin: 0 0 20px 0;\n  font-size: 24px;\n}\n\n.victory-text {\n  color: #44ff44;\n  text-shadow: 0 0 10px #44ff44;\n}\n\n.defeat-text {\n  color: #ff4444;\n  text-shadow: 0 0 10px #ff4444;\n}\n\n.flee-text {\n  color: #ffff44;\n  text-shadow: 0 0 10px #ffff44;\n}\n\n.rewards {\n  margin: 20px 0;\n  text-align: left;\n}\n\n.rewards p {\n  margin: 8px 0;\n  color: #fff;\n}\n\n.rewards ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rewards li {\n  color: #87ceeb;\n  margin: 5px 0;\n}\n\n.confirm-btn {\n  padding: 10px 30px;\n  font-size: 16px;\n  font-weight: bold;\n  background: linear-gradient(135deg, #4444ff 0%, #3333cc 100%);\n  color: #fff;\n  border: 2px solid #4444ff;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.confirm-btn:hover {\n  background: linear-gradient(135deg, #6666ff 0%, #4444ff 100%);\n  transform: translateY(-2px);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .monster-info, .character-info {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .monster-avatar, .character-avatar {\n    width: 60px;\n    height: 60px;\n  }\n\n  .hp-bar, .mp-bar {\n    width: 150px;\n  }\n\n  .action-buttons {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .action-btn {\n    padding: 10px 20px;\n    font-size: 14px;\n    min-width: 70px;\n  }\n\n  .battle-log {\n    width: 90%;\n    max-width: 350px;\n  }\n}\n</style>\n"]}]}