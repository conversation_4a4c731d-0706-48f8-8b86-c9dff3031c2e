<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MonsterSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $monsters = [
            [
                'name' => '灵猴',
                'title' => '花果山守护者',
                'description' => '花果山上的灵猴，身手敏捷，善于攀爬跳跃。',
                'level' => 5,
                'type' => 'beast',
                'element' => 'none',
                'size' => 'small',
                'threat_level' => 1,
                'avatar' => '/static/game/UI/tx/monster/monkey.png',
                'max_health' => 80,
                'max_mp' => 30,
                'attack' => 15,
                'defense' => 8,
                'speed' => 18,
                'constitution' => 12,
                'intelligence' => 10,
                'strength' => 14,
                'agility' => 20,
                'exp_reward' => 25,
                'gold_reward' => 8,
                'item_drops' => json_encode([
                    [
                        'item_id' => 'monkey_fur',
                        'name' => '猴毛',
                        'quantity' => 1,
                        'chance' => 30
                    ],
                    [
                        'item_id' => 'small_health_potion',
                        'name' => '小型生命药水',
                        'quantity' => 1,
                        'chance' => 15
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '山魈',
                'title' => '山林恶鬼',
                'description' => '栖息在深山老林中的恶鬼，力大无穷，性情凶暴。',
                'level' => 8,
                'type' => 'demon',
                'element' => 'dark',
                'size' => 'medium',
                'threat_level' => 2,
                'avatar' => '/static/game/UI/tx/monster/demon.png',
                'max_health' => 120,
                'max_mp' => 40,
                'attack' => 22,
                'defense' => 15,
                'speed' => 12,
                'constitution' => 18,
                'intelligence' => 8,
                'strength' => 25,
                'agility' => 10,
                'exp_reward' => 40,
                'gold_reward' => 15,
                'item_drops' => json_encode([
                    [
                        'item_id' => 'demon_claw',
                        'name' => '魔爪',
                        'quantity' => 1,
                        'chance' => 25
                    ],
                    [
                        'item_id' => 'dark_essence',
                        'name' => '暗黑精华',
                        'quantity' => 1,
                        'chance' => 10
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '水灵',
                'title' => '水帘洞精灵',
                'description' => '水帘洞中的水系精灵，能够操控水流进行攻击。',
                'level' => 6,
                'type' => 'elemental',
                'element' => 'water',
                'size' => 'small',
                'threat_level' => 1,
                'avatar' => '/static/game/UI/tx/monster/water_spirit.png',
                'max_health' => 90,
                'max_mp' => 60,
                'attack' => 18,
                'defense' => 10,
                'speed' => 15,
                'constitution' => 10,
                'intelligence' => 20,
                'strength' => 8,
                'agility' => 16,
                'exp_reward' => 30,
                'gold_reward' => 12,
                'item_drops' => json_encode([
                    [
                        'item_id' => 'water_crystal',
                        'name' => '水晶石',
                        'quantity' => 1,
                        'chance' => 20
                    ],
                    [
                        'item_id' => 'mp_potion',
                        'name' => '魔法药水',
                        'quantity' => 1,
                        'chance' => 18
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '护法金刚',
                'title' => '灵山守护者',
                'description' => '灵山的护法金刚，实力强大，是佛门的忠实守护者。',
                'level' => 15,
                'type' => 'immortal',
                'element' => 'light',
                'size' => 'large',
                'threat_level' => 4,
                'avatar' => '/static/game/UI/tx/monster/guardian.png',
                'max_health' => 250,
                'max_mp' => 100,
                'attack' => 35,
                'defense' => 30,
                'speed' => 10,
                'constitution' => 30,
                'intelligence' => 25,
                'strength' => 40,
                'agility' => 8,
                'exp_reward' => 100,
                'gold_reward' => 50,
                'item_drops' => json_encode([
                    [
                        'item_id' => 'golden_relic',
                        'name' => '金刚舍利',
                        'quantity' => 1,
                        'chance' => 5
                    ],
                    [
                        'item_id' => 'blessed_armor',
                        'name' => '护法铠甲',
                        'quantity' => 1,
                        'chance' => 8
                    ],
                    [
                        'item_id' => 'large_health_potion',
                        'name' => '大型生命药水',
                        'quantity' => 1,
                        'chance' => 25
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '山贼',
                'title' => '落草为寇',
                'description' => '占山为王的山贼，经常拦路抢劫过往行人。',
                'level' => 4,
                'type' => 'other',
                'element' => 'none',
                'size' => 'medium',
                'threat_level' => 1,
                'avatar' => '/static/game/UI/tx/monster/bandit.png',
                'max_health' => 70,
                'max_mp' => 20,
                'attack' => 12,
                'defense' => 6,
                'speed' => 14,
                'constitution' => 10,
                'intelligence' => 6,
                'strength' => 16,
                'agility' => 12,
                'exp_reward' => 20,
                'gold_reward' => 10,
                'item_drops' => json_encode([
                    [
                        'item_id' => 'rusty_sword',
                        'name' => '生锈的剑',
                        'quantity' => 1,
                        'chance' => 15
                    ],
                    [
                        'item_id' => 'bandit_gold',
                        'name' => '山贼金币',
                        'quantity' => 5,
                        'chance' => 40
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '蟹将',
                'title' => '龙宫守卫',
                'description' => '东海龙宫的蟹将，身披坚硬甲壳，挥舞巨钳守护龙宫。',
                'level' => 10,
                'type' => 'beast',
                'element' => 'water',
                'size' => 'medium',
                'threat_level' => 3,
                'avatar' => '/static/game/UI/tx/monster/crab_general.png',
                'max_health' => 150,
                'max_mp' => 50,
                'attack' => 25,
                'defense' => 20,
                'speed' => 8,
                'constitution' => 20,
                'intelligence' => 12,
                'strength' => 28,
                'agility' => 6,
                'exp_reward' => 60,
                'gold_reward' => 25,
                'item_drops' => json_encode([
                    [
                        'item_id' => 'crab_shell',
                        'name' => '蟹壳',
                        'quantity' => 1,
                        'chance' => 30
                    ],
                    [
                        'item_id' => 'dragon_scale',
                        'name' => '龙鳞',
                        'quantity' => 1,
                        'chance' => 8
                    ],
                    [
                        'item_id' => 'pearl',
                        'name' => '珍珠',
                        'quantity' => 1,
                        'chance' => 12
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        DB::table('monsters')->insert($monsters);
    }
}
