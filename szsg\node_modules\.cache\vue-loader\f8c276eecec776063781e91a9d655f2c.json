{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue", "mtime": 1749890251457}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BattleTest.vue"], "names": [], "mappings": ";AAwDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BattleTest.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"battle-test\">\n    <h1>战斗系统测试</h1>\n    \n    <div class=\"test-section\">\n      <h2>测试怪物列表</h2>\n      <div class=\"monster-list\">\n        <div \n          v-for=\"monster in testMonsters\" \n          :key=\"monster.id\"\n          class=\"monster-card\"\n          @click=\"startTestBattle(monster)\"\n        >\n          <img :src=\"monster.avatar\" :alt=\"monster.name\" class=\"monster-avatar\" />\n          <div class=\"monster-info\">\n            <h3>{{ monster.name }}</h3>\n            <p>等级: {{ monster.level }}</p>\n            <p>类型: {{ monster.type }}</p>\n            <p>生命: {{ monster.max_health }}</p>\n            <p>攻击: {{ monster.attack }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h2>动画测试</h2>\n      <div class=\"animation-test\">\n        <BattleAnimation ref=\"animationTest\" />\n        <div class=\"animation-controls\">\n          <button @click=\"testAttackAnimation\">测试攻击动画</button>\n          <button @click=\"testDamageAnimation\">测试伤害动画</button>\n          <button @click=\"testCriticalAnimation\">测试暴击动画</button>\n          <button @click=\"testHealAnimation\">测试治疗动画</button>\n          <button @click=\"testSkillAnimation\">测试技能特效</button>\n          <button @click=\"testParticleAnimation\">测试粒子效果</button>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h2>API测试</h2>\n      <div class=\"api-test\">\n        <button @click=\"testBattleAPI\" :disabled=\"isTestingAPI\">\n          {{ isTestingAPI ? '测试中...' : '测试战斗API' }}\n        </button>\n        <div v-if=\"apiTestResult\" class=\"api-result\">\n          <h4>API测试结果:</h4>\n          <pre>{{ JSON.stringify(apiTestResult, null, 2) }}</pre>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport BattleAnimation from '@/components/game/BattleAnimation.vue'\nimport battleService from '@/api/services/battleService.js'\nimport logger from '@/utils/logger.js'\n\nexport default {\n  name: 'BattleTest',\n  components: {\n    BattleAnimation\n  },\n  \n  data() {\n    return {\n      testMonsters: [\n        {\n          id: 1,\n          name: '灵猴',\n          level: 5,\n          type: 'beast',\n          max_health: 80,\n          attack: 15,\n          avatar: '/static/game/UI/tx/monster/monkey.png'\n        },\n        {\n          id: 2,\n          name: '山魈',\n          level: 8,\n          type: 'demon',\n          max_health: 120,\n          attack: 22,\n          avatar: '/static/game/UI/tx/monster/demon.png'\n        },\n        {\n          id: 3,\n          name: '水灵',\n          level: 6,\n          type: 'elemental',\n          max_health: 90,\n          attack: 18,\n          avatar: '/static/game/UI/tx/monster/water_spirit.png'\n        }\n      ],\n      isTestingAPI: false,\n      apiTestResult: null\n    }\n  },\n  \n  methods: {\n    // 开始测试战斗\n    startTestBattle(monster) {\n      // 获取当前用户的角色ID\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      let testCharacterId = 1; // 默认值\n\n      if (currentCharacter && currentCharacter.id) {\n        testCharacterId = currentCharacter.id;\n      } else {\n        // 尝试从localStorage获取\n        try {\n          const storedCharacter = localStorage.getItem('selectedCharacter');\n          if (storedCharacter) {\n            const character = JSON.parse(storedCharacter);\n            testCharacterId = character.id || 1;\n          }\n        } catch (error) {\n          // 无法获取角色信息，使用默认ID\n        }\n      }\n\n      // 显示将要使用的参数\n      this.$toast(`开始战斗测试 - 角色ID: ${testCharacterId}, 怪物ID: ${monster.id}`)\n\n      this.$router.push({\n        path: '/game/battle',\n        query: {\n          characterId: testCharacterId,\n          monsterId: monster.id,\n          locationId: 'test_location'\n        }\n      });\n    },\n\n    // 测试攻击动画\n    testAttackAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playAttackAnimation();\n      }\n    },\n\n    // 测试伤害动画\n    testDamageAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playDamageAnimation(25, false);\n      }\n    },\n\n    // 测试暴击动画\n    testCriticalAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playDamageAnimation(45, true);\n      }\n    },\n\n    // 测试治疗动画\n    testHealAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playHealAnimation(30);\n      }\n    },\n\n    // 测试技能特效\n    testSkillAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playSkillEffect('fire');\n      }\n    },\n\n    // 测试粒子效果\n    testParticleAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playParticleEffect('explosion');\n      }\n    },\n\n    // 测试战斗API\n    async testBattleAPI() {\n      this.isTestingAPI = true;\n      this.apiTestResult = null;\n\n      try {\n        logger.info('[BattleTest] 开始测试战斗API...');\n\n        // 首先测试简单的API调用\n        const testData = {\n          character_id: 1,\n          monster_id: 1,\n          location_id: 'test_location'\n        };\n\n        logger.info('[BattleTest] 测试数据:', testData);\n\n        // 测试开始战斗API\n        const result = await battleService.startBattle(1, 1, 'test_location');\n        this.apiTestResult = {\n          success: true,\n          data: result,\n          timestamp: new Date().toISOString()\n        };\n        logger.info('[BattleTest] API测试成功:', result);\n      } catch (error) {\n        this.apiTestResult = {\n          success: false,\n          error: error.message,\n          details: {\n            name: error.name,\n            message: error.message,\n            response: error.response?.data,\n            status: error.response?.status,\n            config: error.config\n          },\n          timestamp: new Date().toISOString()\n        };\n        logger.error('[BattleTest] API测试失败:', error);\n      } finally {\n        this.isTestingAPI = false;\n      }\n    }\n  },\n\n  mounted() {\n    logger.info('[BattleTest] 战斗测试页面已加载');\n    \n    // 检查动画库是否加载\n    if (window.particlesJS) {\n      logger.info('[BattleTest] Particles.js 已加载');\n    } else {\n      logger.warn('[BattleTest] Particles.js 未加载');\n    }\n  }\n}\n</script>\n\n<style scoped>\n.battle-test {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.battle-test h1 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 30px;\n}\n\n.test-section {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.test-section h2 {\n  color: #555;\n  margin-bottom: 15px;\n  border-bottom: 2px solid #eee;\n  padding-bottom: 10px;\n}\n\n/* 怪物列表样式 */\n.monster-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 15px;\n}\n\n.monster-card {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.monster-card:hover {\n  border-color: #007bff;\n  background: #f8f9fa;\n  transform: translateY(-2px);\n}\n\n.monster-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid #ccc;\n}\n\n.monster-info h3 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.monster-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n/* 动画测试样式 */\n.animation-test {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.animation-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.animation-controls button {\n  padding: 10px 15px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.animation-controls button:hover {\n  background: #0056b3;\n}\n\n/* API测试样式 */\n.api-test button {\n  padding: 12px 24px;\n  background: #28a745;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: background 0.3s ease;\n}\n\n.api-test button:hover:not(:disabled) {\n  background: #218838;\n}\n\n.api-test button:disabled {\n  background: #6c757d;\n  cursor: not-allowed;\n}\n\n.api-result {\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 5px;\n}\n\n.api-result h4 {\n  margin: 0 0 10px 0;\n  color: #495057;\n}\n\n.api-result pre {\n  background: #e9ecef;\n  padding: 10px;\n  border-radius: 3px;\n  overflow-x: auto;\n  font-size: 12px;\n  color: #495057;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .battle-test {\n    padding: 10px;\n  }\n  \n  .monster-list {\n    grid-template-columns: 1fr;\n  }\n  \n  .monster-card {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .animation-controls {\n    flex-direction: column;\n  }\n}\n</style>\n"]}]}