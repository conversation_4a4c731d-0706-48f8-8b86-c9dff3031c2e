<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('characters', function (Blueprint $table) {
            // 只添加确实缺失的字段：gold, silver, energy
            // attribute_points 已经存在，不需要添加
            $table->integer('gold')->default(0)->after('stats');
            $table->integer('silver')->default(0)->after('gold');
            $table->integer('energy')->default(100)->after('silver');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('characters', function (Blueprint $table) {
            $table->dropColumn(['gold', 'silver', 'energy']);
        });
    }
};
