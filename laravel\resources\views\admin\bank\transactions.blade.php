@extends('admin.layouts.app')

@section('title', '钱庄交易记录')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">钱庄交易记录</div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-btn-group">
                    <a href="{{ route('admin.bank') }}" class="layui-btn layui-btn-primary">返回账户列表</a>
                </div>
            </div>
        </div>

        <div class="layui-form layui-form-pane">
            <form action="{{ route('admin.bank.transactions') }}" method="GET">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">交易类型</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">全部</option>
                                <option value="deposit" {{ request('type') == 'deposit' ? 'selected' : '' }}>存款</option>
                                <option value="withdraw" {{ request('type') == 'withdraw' ? 'selected' : '' }}>取款</option>
                                <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>转账</option>
                                <option value="interest" {{ request('type') == 'interest' ? 'selected' : '' }}>利息</option>
                                <option value="exchange" {{ request('type') == 'exchange' ? 'selected' : '' }}>兑换</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">货币类型</label>
                        <div class="layui-input-inline">
                            <select name="currency">
                                <option value="">全部</option>
                                <option value="silver" {{ request('currency') == 'silver' ? 'selected' : '' }}>银两</option>
                                <option value="gold_ingot" {{ request('currency') == 'gold_ingot' ? 'selected' : '' }}>金砖</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">角色</label>
                        <div class="layui-input-inline">
                            <select name="character_id">
                                <option value="">全部</option>
                                @foreach($characters as $character)
                                <option value="{{ $character->id }}" {{ request('character_id') == $character->id ? 'selected' : '' }}>
                                    {{ $character->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">开始日期</label>
                        <div class="layui-input-inline">
                            <input type="date" name="date_from" value="{{ request('date_from') }}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">结束日期</label>
                        <div class="layui-input-inline">
                            <input type="date" name="date_to" value="{{ request('date_to') }}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit>筛选</button>
                        <a href="{{ route('admin.bank.transactions') }}" class="layui-btn layui-btn-primary">重置</a>
                    </div>
                </div>
            </form>
        </div>

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>角色</th>
                    <th>类型</th>
                    <th>货币</th>
                    <th>金额</th>
                    <th>余额</th>
                    <th>备注</th>
                    <th>时间</th>
                </tr>
            </thead>
            <tbody>
                @forelse($transactions as $transaction)
                <tr>
                    <td>{{ $transaction->id }}</td>
                    <td>{{ $transaction->character_name }}</td>
                    <td>
                        @php
                        $typeMap = [
                            'deposit' => '存款',
                            'withdraw' => '取款',
                            'transfer' => '转账',
                            'interest' => '利息',
                            'exchange' => '兑换',
                        ];
                        @endphp
                        {{ $typeMap[$transaction->type] ?? $transaction->type }}
                    </td>
                    <td>{{ $transaction->currency == 'gold_ingot' ? '金砖' : '银两' }}</td>
                    <td>
                        @if($transaction->amount > 0)
                            <span style="color: green;">+{{ number_format($transaction->amount) }}</span>
                        @else
                            <span style="color: red;">{{ number_format($transaction->amount) }}</span>
                        @endif
                    </td>
                    <td>{{ number_format($transaction->balance) }}</td>
                    <td>{{ $transaction->note }}</td>
                    <td>{{ $transaction->created_at }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="layui-center">暂无交易记录</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $transactions->links('admin.layouts.pagination') }}
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form', 'laydate'], function(){
    var form = layui.form;
    var laydate = layui.laydate;

    // 重新渲染表单
    form.render();
});
</script>
@endsection
