{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue?vue&type=style&index=0&id=9f58988a&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue", "mtime": 1749712660456}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GameLayout.vue"], "names": [], "mappings": ";AAs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file": "GameLayout.vue", "sourceRoot": "src/layouts", "sourcesContent": ["<template>\r\n  <div class=\"game-layout-container\">\r\n    <!-- 动态背景效果 -->\r\n    <div class=\"background-effects\">\r\n      <div class=\"floating-particles\">\r\n        <div v-for=\"i in 20\" :key=\"i\" class=\"particle\" :style=\"getParticleStyle(i)\"></div>\r\n      </div>\r\n      <div class=\"background-gradient\"></div>\r\n    </div>\r\n\r\n    <!-- 顶部装饰边框 -->\r\n    <div class=\"top-border-frame\" :style=\"{ backgroundImage: `url(${topBorderImage})` }\">\r\n      <div class=\"top-border-overlay\">\r\n        <div class=\"border-content\">\r\n          <!-- 可选的页面标识文字 -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 左右固定边框 -->\r\n    <div class=\"left-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n    <div class=\"right-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n\r\n    <!-- 页面实际内容插入点 -->\r\n    <div class=\"main-content-area\">\r\n      <div class=\"content-wrapper\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部装饰边框 -->\r\n    <div class=\"bottom-border-frame\">\r\n      <div class=\"footer-decoration\">\r\n        <div class=\"footer-pattern\"></div>\r\n        <div class=\"footer-text\">\r\n          <span class=\"version-info\">Version 1.0.0</span>\r\n          <span class=\"copyright\">© 2025 神之西游</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载遮罩 -->\r\n    <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n      <div class=\"loading-content\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <div class=\"loading-text\">{{ loadingText || '加载中...' }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logger from '@/utils/logger';\r\n\r\nexport default {\r\n  name: 'GameLayout',\r\n  props: {\r\n    // 页面类型：'setup', 'game', 'main', 'battle' 等\r\n    pageType: {\r\n      type: String,\r\n      default: 'main'\r\n    },\r\n    // 自定义页面标题\r\n    customTitle: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 是否显示装饰元素\r\n    showDecorations: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isLoading: false,\r\n      loadingText: ''\r\n    };\r\n  },\r\n  computed: {\r\n    // 从store获取全局加载状态\r\n    globalLoading() {\r\n      return this.$store.state.isLoading;\r\n    },\r\n\r\n    // 根据页面类型或路由获取顶部边框图片\r\n    topBorderImage() {\r\n      // 如果有自定义标题，优先使用pageType\r\n      if (this.pageType && this.pageType !== 'main') {\r\n        return this.getImageByPageType(this.pageType);\r\n      }\r\n\r\n      // 根据当前路由自动判断\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '/static/game/UI/bj/xuanzefenqu.png';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '/static/game/UI/bj/xuanzejuese.png';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '/static/game/UI/bj/chuangjian.png';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '/static/game/UI/bj/youxi.png';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '/static/game/UI/bj/zhandou.png';\r\n      }\r\n\r\n      // 默认主页面\r\n      return '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 页面标题\r\n    pageTitle() {\r\n      if (this.customTitle) {\r\n        return this.customTitle;\r\n      }\r\n\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '选择大区';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '选择角色';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '创建角色';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '游戏世界';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '战斗场景';\r\n      }\r\n\r\n      return '神之西游';\r\n    }\r\n  },\r\n  watch: {\r\n    globalLoading(newVal) {\r\n      this.isLoading = newVal;\r\n    }\r\n  },\r\n  created() {\r\n    logger.debug('[GameLayout] 初始化');\r\n\r\n    // 获取当前页面路径\r\n    const currentPath = this.$route.path;\r\n    logger.debug('[GameLayout] 当前路径:', currentPath);\r\n\r\n    // 设置页面列表（这些页面只需要验证登录，不需要验证角色选择）\r\n    const setupPages = [\r\n      '/setup/region-select',\r\n      '/setup/character-select',\r\n      '/setup/create-character'\r\n    ];\r\n\r\n    // 检查认证状态（路由守卫已经处理了基本的登录检查，这里只做额外检查）\r\n    try {\r\n      const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');\r\n      const storeAuth = this.$store.state.auth?.isAuthenticated;\r\n      const isAuthenticated = storeAuth || !!authToken;\r\n\r\n      // 如果没有认证信息，让路由守卫处理重定向\r\n      if (!isAuthenticated) {\r\n        logger.debug('[GameLayout] 未登录，由路由守卫处理');\r\n        return;\r\n      }\r\n\r\n      logger.debug('[GameLayout] 已登录，继续检查角色状态');\r\n\r\n      // 只有非设置页面才需要检查角色选择状态\r\n      const isSetupPage = setupPages.some(page => currentPath.includes(page));\r\n      if (!isSetupPage) {\r\n        const selectedCharacter = localStorage.getItem('selectedCharacter');\r\n        if (!selectedCharacter) {\r\n          logger.debug('[GameLayout] 未选择角色，重定向到区域选择页');\r\n          this.showToast('请先选择角色');\r\n          // 使用nextTick避免在created中立即重定向\r\n          this.$nextTick(() => {\r\n            this.$router.replace('/setup/region-select');\r\n          });\r\n          return;\r\n        }\r\n      }\r\n\r\n      logger.debug('[GameLayout] 认证检查通过，路径:', currentPath);\r\n    } catch (error) {\r\n      logger.error('[GameLayout] 认证检查失败:', error);\r\n    }\r\n  },\r\n  methods: {\r\n    // 根据页面类型获取图片\r\n    getImageByPageType(pageType) {\r\n      const imageMap = {\r\n        'setup': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'region-select': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'character-select': '/static/game/UI/bj/xuanzejuese.png',\r\n        'create-character': '/static/game/UI/bj/chuangjian.png',\r\n        'game': '/static/game/UI/bj/youxi.png',\r\n        'battle': '/static/game/UI/bj/zhandou.png',\r\n        'main': '/static/game/UI/bj/szxy_1.png'\r\n      };\r\n      return imageMap[pageType] || '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 获取粒子样式\r\n    getParticleStyle() {\r\n      const delay = Math.random() * 20;\r\n      const duration = 15 + Math.random() * 10;\r\n      const size = 2 + Math.random() * 4;\r\n      const left = Math.random() * 100;\r\n\r\n      return {\r\n        left: `${left}%`,\r\n        animationDelay: `${delay}s`,\r\n        animationDuration: `${duration}s`,\r\n        width: `${size}px`,\r\n        height: `${size}px`\r\n      };\r\n    },\r\n\r\n    // 显示提示框\r\n    showToast(message, duration = 2000) {\r\n      logger.debug('[GameLayout] 显示提示:', message);\r\n      const toast = document.createElement('div');\r\n      toast.innerHTML = message;\r\n      toast.style.cssText = `\r\n        position: fixed;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        background: linear-gradient(135deg, rgba(212, 175, 55, 0.9), rgba(184, 148, 31, 0.9));\r\n        color: #000;\r\n        padding: 15px 25px;\r\n        border-radius: 8px;\r\n        border: 2px solid #d4af37;\r\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.3);\r\n        z-index: 10000;\r\n        font-weight: bold;\r\n        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);\r\n        animation: toastSlideIn 0.3s ease-out;\r\n      `;\r\n\r\n      // 添加动画样式\r\n      const style = document.createElement('style');\r\n      style.textContent = `\r\n        @keyframes toastSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translate(-50%, -50%) scale(0.8);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translate(-50%, -50%) scale(1);\r\n          }\r\n        }\r\n      `;\r\n      document.head.appendChild(style);\r\n\r\n      document.body.appendChild(toast);\r\n      setTimeout(() => {\r\n        toast.style.animation = 'toastSlideIn 0.3s ease-out reverse';\r\n        setTimeout(() => {\r\n          if (document.body.contains(toast)) {\r\n            document.body.removeChild(toast);\r\n          }\r\n          if (document.head.contains(style)) {\r\n            document.head.removeChild(style);\r\n          }\r\n        }, 300);\r\n      }, duration);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.game-layout-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background: #000;\r\n}\r\n\r\n.left-border-frame {\r\n  position: fixed;\r\n  left: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 20px;\r\n  z-index: 100;\r\n  pointer-events: none;\r\n}\r\n\r\n.right-border-frame {\r\n  position: fixed;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 20px;\r\n  z-index: 100;\r\n  pointer-events: none;\r\n}\r\n\r\n.main-content-area {\r\n  position: relative;\r\n  width: calc(100% - 40px);\r\n  margin: 0 auto;\r\n  height: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n/* 动态背景效果 */\r\n.background-effects {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: -1;\r\n}\r\n\r\n.background-gradient {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background:\r\n    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),\r\n    linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.02) 50%, transparent 70%);\r\n  animation: gradientShift 20s ease-in-out infinite;\r\n}\r\n\r\n@keyframes gradientShift {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n/* 浮动粒子效果 */\r\n.floating-particles {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  background: #d4af37;\r\n  border-radius: 50%;\r\n  opacity: 0.6;\r\n  animation: float-up linear infinite;\r\n}\r\n\r\n@keyframes float-up {\r\n  0% {\r\n    transform: translateY(100vh) rotate(0deg);\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 0.6;\r\n  }\r\n  90% {\r\n    opacity: 0.6;\r\n  }\r\n  100% {\r\n    transform: translateY(-100px) rotate(360deg);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 顶部边框装饰 */\r\n.top-border-frame {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 60px;\r\n  z-index: 1500;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.top-border-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.border-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n@keyframes sparkle {\r\n  0%, 100% { opacity: 0.7; transform: scale(1); }\r\n  50% { opacity: 1; transform: scale(1.1); }\r\n}\r\n\r\n/* 左右边框样式 */\r\n.left-border-frame,\r\n.right-border-frame {\r\n  position: fixed;\r\n  top: 60px;\r\n  bottom: 40px;\r\n  z-index: 1400;\r\n  pointer-events: none;\r\n}\r\n\r\n.left-border-frame {\r\n  left: 0;\r\n  width: 30px;\r\n  background: url('/static/game/UI/bk/gnl_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.right-border-frame {\r\n  right: 0;\r\n  width: 40px;\r\n  background: url('/static/game/UI/bk/gnl1_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.border-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: repeating-linear-gradient(\r\n    0deg,\r\n    transparent,\r\n    transparent 20px,\r\n    rgba(212, 175, 55, 0.1) 21px,\r\n    rgba(212, 175, 55, 0.1) 22px\r\n  );\r\n}\r\n\r\n.border-glow {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 2px;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, transparent, #d4af37, transparent);\r\n  opacity: 0.6;\r\n  animation: borderPulse 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderPulse {\r\n  0%, 100% { opacity: 0.3; }\r\n  50% { opacity: 0.8; }\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 50px 15px 35px 15px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .game-title {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 60px 25px 45px 20px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    height: 50px;\r\n  }\r\n\r\n  .left-border-frame,\r\n  .right-border-frame {\r\n    top: 50px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n</style> "]}]}