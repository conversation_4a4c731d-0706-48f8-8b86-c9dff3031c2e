/**
 * 加载初始数据
 */
async loadData() {
  try {
    this.isLoading = true;
    this.error = null;
    
    // 获取当前角色信息
    const character = getCurrentCharacter();
    if (!character) {
      throw new Error('未找到角色信息，请先选择角色');
    }
    
    this.characterInfo = {
      id: character.id,
      name: character.name,
      silver: character.silver || 0,
      gold: character.gold || 0
    };
    
    logger.debug('[Bank] 角色信息:', JSON.stringify(this.characterInfo));
    
    // 从Vuex获取更详细的角色信息
    const characterStatus = this.$store.state.character.characterStatus;
    if (characterStatus) {
      this.characterInfo.silver = characterStatus.silver || this.characterInfo.silver;
      this.characterInfo.gold = characterStatus.gold || this.characterInfo.gold;
      logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));
    }
    
    try {
      // 获取银行账户信息
      logger.debug('[Bank] 开始获取银行账户信息, ID:', this.characterInfo.id);
      const accountResponse = await bankService.getAccountInfo(this.characterInfo.id);
      logger.debug('[Bank] 银行账户响应:', JSON.stringify(accountResponse));
      
      if (!accountResponse || !accountResponse.data) {
        throw new Error('获取银行账户信息失败: 响应数据无效');
      }
      
      this.accountInfo = accountResponse.data.account;
      logger.debug('[Bank] 获取到账户信息:', JSON.stringify(this.accountInfo));
    } catch (apiError) {
      logger.error('[Bank] API调用失败:', apiError);
      // 如果API调用失败，显示警告但继续使用默认账户信息
      this.accountInfo = {
        silver: 0,
        gold_ingot: 0
      };
      showMessage('无法获取银行账户信息，将使用临时数据', 'warning');
    }
    
    // 初始化表单
    this.depositForm.amount = Math.min(100, this.characterInfo.silver);
    this.withdrawForm.amount = Math.min(100, this.accountInfo.silver);
    
    this.isLoading = false;
  } catch (error) {
    this.error = error.message || '加载数据失败，请刷新重试';
    this.isLoading = false;
    logger.error('[Bank] 加载数据失败:', error);
    
    // 如果是未找到角色信息，则跳转到角色选择页面
    if (error.message && error.message.includes('未找到角色信息')) {
      showMessage('未找到角色信息，请先选择角色', 'error');
      this.$router.push('/setup/character-select');
    }
  }
}, 