<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'leader_id',
        'max_members',
        'min_level',
        'max_level',
        'is_recruiting'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_recruiting' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取队伍队长
     */
    public function leader()
    {
        return $this->belongsTo(Character::class, 'leader_id');
    }

    /**
     * 获取队伍成员
     */
    public function members()
    {
        return $this->hasMany(TeamMember::class);
    }

    /**
     * 获取队伍所有邀请
     */
    public function invites()
    {
        return $this->hasMany(TeamInvite::class);
    }
}
