{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue?vue&type=template&id=1ac7a205&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue", "mtime": 1749703549914}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "storeAuth", "isAuthenticated", "token", "user", "JSON", "stringify", "localStorage", "authToken", "on", "click", "clearAuth", "setTestAuth", "refreshPage", "goToFriends", "goToLogin", "goToMain", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/views/debug/AuthDebug.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"auth-debug\" }, [\n    _c(\"h2\", [_vm._v(\"认证状态调试\")]),\n    _c(\"div\", { staticClass: \"debug-section\" }, [\n      _c(\"h3\", [_vm._v(\"Store 状态\")]),\n      _c(\"div\", { staticClass: \"debug-item\" }, [\n        _c(\"strong\", [_vm._v(\"isAuthenticated:\")]),\n        _vm._v(\" \" + _vm._s(_vm.storeAuth.isAuthenticated) + \" \"),\n      ]),\n      _c(\"div\", { staticClass: \"debug-item\" }, [\n        _c(\"strong\", [_vm._v(\"token:\")]),\n        _vm._v(\" \" + _vm._s(_vm.storeAuth.token ? \"存在\" : \"不存在\") + \" \"),\n      ]),\n      _c(\"div\", { staticClass: \"debug-item\" }, [\n        _c(\"strong\", [_vm._v(\"user:\")]),\n        _vm._v(\n          \" \" +\n            _vm._s(\n              _vm.storeAuth.user ? JSON.stringify(_vm.storeAuth.user) : \"不存在\"\n            ) +\n            \" \"\n        ),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"debug-section\" }, [\n      _c(\"h3\", [_vm._v(\"LocalStorage\")]),\n      _c(\"div\", { staticClass: \"debug-item\" }, [\n        _c(\"strong\", [_vm._v(\"authToken:\")]),\n        _vm._v(\n          \" \" + _vm._s(_vm.localStorage.authToken ? \"存在\" : \"不存在\") + \" \"\n        ),\n      ]),\n      _c(\"div\", { staticClass: \"debug-item\" }, [\n        _c(\"strong\", [_vm._v(\"token:\")]),\n        _vm._v(\" \" + _vm._s(_vm.localStorage.token ? \"存在\" : \"不存在\") + \" \"),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"debug-section\" }, [\n      _c(\"h3\", [_vm._v(\"操作\")]),\n      _c(\"button\", { staticClass: \"debug-btn\", on: { click: _vm.clearAuth } }, [\n        _vm._v(\"清除认证状态\"),\n      ]),\n      _c(\n        \"button\",\n        { staticClass: \"debug-btn\", on: { click: _vm.setTestAuth } },\n        [_vm._v(\"设置测试认证\")]\n      ),\n      _c(\n        \"button\",\n        { staticClass: \"debug-btn\", on: { click: _vm.refreshPage } },\n        [_vm._v(\"刷新页面\")]\n      ),\n    ]),\n    _c(\"div\", { staticClass: \"debug-section\" }, [\n      _c(\"h3\", [_vm._v(\"导航测试\")]),\n      _c(\n        \"button\",\n        { staticClass: \"debug-btn\", on: { click: _vm.goToFriends } },\n        [_vm._v(\"跳转到好友页面\")]\n      ),\n      _c(\"button\", { staticClass: \"debug-btn\", on: { click: _vm.goToLogin } }, [\n        _vm._v(\"跳转到登录页面\"),\n      ]),\n      _c(\"button\", { staticClass: \"debug-btn\", on: { click: _vm.goToMain } }, [\n        _vm._v(\"跳转到游戏主页\"),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC9BH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAC1CJ,GAAG,CAACI,EAAE,CAAC,GAAG,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,SAAS,CAACC,eAAe,CAAC,GAAG,GAAG,CAAC,CAC1D,CAAC,EACFN,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAChCJ,GAAG,CAACI,EAAE,CAAC,GAAG,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,SAAS,CAACE,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAC/D,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC/BJ,GAAG,CAACI,EAAE,CACJ,GAAG,GACDJ,GAAG,CAACK,EAAE,CACJL,GAAG,CAACM,SAAS,CAACG,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACX,GAAG,CAACM,SAAS,CAACG,IAAI,CAAC,GAAG,KAC5D,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACFR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EACpCJ,GAAG,CAACI,EAAE,CACJ,GAAG,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACY,YAAY,CAACC,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,GAC5D,CAAC,CACF,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAChCJ,GAAG,CAACI,EAAE,CAAC,GAAG,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACY,YAAY,CAACJ,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAClE,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxBH,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE,WAAW;IAAEW,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACgB;IAAU;EAAE,CAAC,EAAE,CACvEhB,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFH,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,WAAW;IAAEW,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACiB;IAAY;EAAE,CAAC,EAC5D,CAACjB,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,WAAW;IAAEW,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACkB;IAAY;EAAE,CAAC,EAC5D,CAAClB,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BH,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,WAAW;IAAEW,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACmB;IAAY;EAAE,CAAC,EAC5D,CAACnB,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDH,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE,WAAW;IAAEW,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACoB;IAAU;EAAE,CAAC,EAAE,CACvEpB,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFH,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE,WAAW;IAAEW,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACqB;IAAS;EAAE,CAAC,EAAE,CACtErB,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIkB,eAAe,GAAG,EAAE;AACxBvB,MAAM,CAACwB,aAAa,GAAG,IAAI;AAE3B,SAASxB,MAAM,EAAEuB,eAAe", "ignoreList": []}]}