/**
 * 地图系统API服务
 * 提供地图相关的接口调用
 */
import { get, post } from '../request.js';
import logger from '../../utils/logger.js';
import { getCache, setCache, removeCache } from './cacheService.js';

// 缓存键
const CACHE_KEYS = {
    MAP_DATA: 'map_data',
    LOCATION_DETAILS: 'location_details',
    AVAILABLE_LOCATIONS: 'available_locations',
    MOVEMENT_COST: 'movement_cost'
};

/**
 * 地图服务
 */
const mapService = {
    /**
     * 获取当前地图数据
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 地图数据
     */
    getCurrentMap(characterId) {
        logger.debug('[MapService] 获取当前地图数据, characterId:', characterId);
        
        // 尝试从缓存获取
        const cachedData = getCache(CACHE_KEYS.MAP_DATA, characterId);
        if (cachedData) {
            return Promise.resolve(cachedData);
        }

        return get(`/characters/${characterId}/map`, {}, {
            loading: true,
            loadingText: '加载地图数据...'
        }).then(res => {
            logger.debug('[MapService] 地图数据响应:', res);
            // 缓存结果
            setCache(CACHE_KEYS.MAP_DATA, characterId, res.data, 300000); // 缓存5分钟
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 获取地图数据失败:', error);
            throw error;
        });
    },

    /**
     * 获取可移动的位置列表
     * @param {string} characterId - 角色ID
     * @param {string} currentLocation - 当前位置
     * @returns {Promise<Array>} - 可移动位置列表
     */
    getAvailableLocations(characterId, currentLocation) {
        logger.debug('[MapService] 获取可移动位置, characterId:', characterId, 'currentLocation:', currentLocation);
        
        // 尝试从缓存获取
        const cacheKey = `${characterId}_${currentLocation}`;
        const cachedData = getCache(CACHE_KEYS.AVAILABLE_LOCATIONS, cacheKey);
        if (cachedData) {
            return Promise.resolve(cachedData);
        }

        return get(`/characters/${characterId}/map/locations`, { 
            current_location: currentLocation 
        }, {
            loading: false
        }).then(res => {
            logger.debug('[MapService] 可移动位置响应:', res);
            // 缓存结果
            setCache(CACHE_KEYS.AVAILABLE_LOCATIONS, cacheKey, res.data, 180000); // 缓存3分钟
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 获取可移动位置失败:', error);
            throw error;
        });
    },

    /**
     * 获取位置详情
     * @param {string} locationId - 位置ID
     * @returns {Promise<Object>} - 位置详情
     */
    getLocationDetails(locationId) {
        logger.debug('[MapService] 获取位置详情, locationId:', locationId);
        
        // 尝试从缓存获取
        const cachedData = getCache(CACHE_KEYS.LOCATION_DETAILS, locationId);
        if (cachedData) {
            return Promise.resolve(cachedData);
        }

        return get(`/map/locations/${locationId}`, {}, {
            loading: true,
            loadingText: '加载位置信息...'
        }).then(res => {
            logger.debug('[MapService] 位置详情响应:', res);
            // 缓存结果
            setCache(CACHE_KEYS.LOCATION_DETAILS, locationId, res.data, 600000); // 缓存10分钟
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 获取位置详情失败:', error);
            throw error;
        });
    },

    /**
     * 移动到指定位置
     * @param {string} characterId - 角色ID
     * @param {string} targetLocation - 目标位置
     * @returns {Promise<Object>} - 移动结果
     */
    moveToLocation(characterId, targetLocation) {
        logger.debug('[MapService] 移动到位置, characterId:', characterId, 'targetLocation:', targetLocation);
        
        return post(`/characters/${characterId}/map/move`, {
            target_location: targetLocation
        }, {
            loading: true,
            loadingText: '移动中...'
        }).then(res => {
            logger.debug('[MapService] 移动响应:', res);
            
            // 清除相关缓存
            this.clearLocationCache(characterId);
            
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 移动失败:', error);
            throw error;
        });
    },

    /**
     * 获取移动消耗
     * @param {string} characterId - 角色ID
     * @param {string} fromLocation - 起始位置
     * @param {string} toLocation - 目标位置
     * @returns {Promise<Object>} - 移动消耗信息
     */
    getMovementCost(characterId, fromLocation, toLocation) {
        logger.debug('[MapService] 获取移动消耗, characterId:', characterId, 'from:', fromLocation, 'to:', toLocation);
        
        // 尝试从缓存获取
        const cacheKey = `${fromLocation}_${toLocation}`;
        const cachedData = getCache(CACHE_KEYS.MOVEMENT_COST, cacheKey);
        if (cachedData) {
            return Promise.resolve(cachedData);
        }

        return get(`/characters/${characterId}/map/movement-cost`, {
            from_location: fromLocation,
            to_location: toLocation
        }, {
            loading: false
        }).then(res => {
            logger.debug('[MapService] 移动消耗响应:', res);
            // 缓存结果
            setCache(CACHE_KEYS.MOVEMENT_COST, cacheKey, res.data, 300000); // 缓存5分钟
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 获取移动消耗失败:', error);
            throw error;
        });
    },

    /**
     * 获取位置的NPC和怪物
     * @param {string} characterId - 角色ID
     * @param {string} locationId - 位置ID
     * @returns {Promise<Object>} - NPC和怪物数据
     */
    getLocationEntities(characterId, locationId) {
        logger.debug('[MapService] 获取位置实体, characterId:', characterId, 'locationId:', locationId);
        
        return get(`/characters/${characterId}/map/locations/${locationId}/entities`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[MapService] 位置实体响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 获取位置实体失败:', error);
            throw error;
        });
    },

    /**
     * 清除位置相关缓存
     * @param {string} characterId - 角色ID
     */
    clearLocationCache(characterId) {
        try {
            // 清除地图数据缓存
            removeCache(CACHE_KEYS.MAP_DATA, characterId);

            // 清除可移动位置缓存
            // 注意：这里简化处理，实际应用中可能需要更复杂的缓存清理逻辑
            logger.debug('[MapService] 位置缓存已清除');
        } catch (error) {
            logger.error('[MapService] 清除缓存失败:', error);
        }
    },

    /**
     * 获取世界地图数据
     * @returns {Promise<Object>} - 世界地图数据
     */
    getWorldMap() {
        logger.debug('[MapService] 获取世界地图数据');
        
        // 尝试从缓存获取
        const cachedData = getCache('world_map', 'data');
        if (cachedData) {
            return Promise.resolve(cachedData);
        }

        return get('/map/world', {}, {
            loading: true,
            loadingText: '加载世界地图...'
        }).then(res => {
            logger.debug('[MapService] 世界地图响应:', res);
            // 缓存结果
            setCache('world_map', 'data', res.data, 3600000); // 缓存1小时
            return res.data;
        }).catch(error => {
            logger.error('[MapService] 获取世界地图失败:', error);
            throw error;
        });
    }
};

export default mapService;
