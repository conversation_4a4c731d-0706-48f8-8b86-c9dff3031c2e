<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('battles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained();
            $table->morphs('opponent'); // 可以是enemy或character(PVP)
            $table->enum('status', ['active', 'victory', 'defeat', 'draw', 'fled'])->default('active');
            $table->enum('type', ['pve', 'pvp', 'boss', 'event']);
            $table->json('battle_data')->nullable(); // 存储战斗中的临时数据
            $table->json('rewards')->nullable(); // 战斗奖励
            $table->timestamp('started_at')->useCurrent();
            $table->timestamp('ended_at')->nullable();
            $table->timestamps();
        });

        Schema::create('battle_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('battle_id')->constrained()->onDelete('cascade');
            $table->integer('turn');
            $table->string('action');
            $table->string('actor_type'); // character or enemy
            $table->unsignedBigInteger('actor_id');
            $table->string('target_type')->nullable(); // character or enemy
            $table->unsignedBigInteger('target_id')->nullable();
            $table->json('result')->nullable(); // 行动结果 {damage: 10, critical: true, ...}
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('battle_logs');
        Schema::dropIfExists('battles');
    }
};
