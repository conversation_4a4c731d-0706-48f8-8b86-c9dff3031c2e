<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('enemies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['normal', 'elite', 'boss']);
            $table->integer('level')->default(1);
            $table->string('image')->nullable();
            $table->json('stats'); // {hp: 100, attack: 10, defense: 5, ...}
            $table->json('skills')->nullable(); // [{id: 1, chance: 0.3}, ...]
            $table->json('drops')->nullable(); // [{item_id: 1, chance: 0.1, min: 1, max: 1}, ...]
            $table->integer('experience_reward')->default(0);
            $table->integer('gold_reward')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('enemies');
    }
};
