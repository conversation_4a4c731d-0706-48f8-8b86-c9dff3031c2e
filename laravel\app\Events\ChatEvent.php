<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ChatEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 事件名称
     */
    public $event;

    /**
     * 事件数据
     */
    public $data;

    /**
     * 创建一个新的事件实例
     *
     * @param string $event
     * @param array $data
     * @return void
     */
    public function __construct($event, $data)
    {
        $this->event = $event;
        $this->data = $data;
    }

    /**
     * 获取事件广播的频道
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // 根据事件类型选择不同的频道
        switch ($this->event) {
            case 'world_message':
                // 世界聊天广播到公共频道
                return new Channel('chat.world');

            case 'team_message':
                // 队伍聊天广播到队伍频道
                $teamId = $this->data['team_id'] ?? null;
                if ($teamId) {
                    return new PresenceChannel('team.'.$teamId);
                }
                break;

            case 'private_message':
                // 私聊广播到发送者和接收者的个人频道
                $senderId = $this->data['sender_id'] ?? null;
                $receiverId = $this->data['receiver_id'] ?? null;
                if ($senderId && $receiverId) {
                    return [
                        new PrivateChannel('chat.user.'.$senderId),
                        new PrivateChannel('chat.user.'.$receiverId)
                    ];
                }
                break;

            case 'channel_message':
                // 自定义频道消息
                $channel = $this->data['channel'] ?? 'general';
                return new Channel('chat.'.$channel);

            default:
                // 默认情况，广播到通用频道
                return new Channel('chat.general');
        }
    }

    /**
     * 获取广播事件名称
     *
     * @return string
     */
    public function broadcastAs()
    {
        return $this->event;
    }
}
