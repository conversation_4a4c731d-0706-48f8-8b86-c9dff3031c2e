{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\store\\modules\\region.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\store\\modules\\region.js", "mtime": 1749735994793}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["logger", "state", "regions", "currentRegion", "loading", "error", "getters", "allRegions", "currentRegionId", "id", "currentRegionName", "name", "isLoading", "mutations", "SET_REGIONS", "SET_CURRENT_REGION", "region", "SET_LOADING", "SET_ERROR", "actions", "initRegion", "commit", "savedRegion", "localStorage", "getItem", "JSON", "parse", "selectRegion", "setItem", "stringify", "debug", "clearRegion", "removeItem", "namespaced"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/store/modules/region.js"], "sourcesContent": ["/**\r\n * 大区状态Vuex模块\r\n */\r\nimport logger from '@/utils/logger';\r\n\r\n// 初始状态\r\nconst state = {\r\n  regions: [],\r\n  currentRegion: null,\r\n  loading: false,\r\n  error: null\r\n};\r\n\r\n// getter\r\nconst getters = {\r\n  // 获取所有大区\r\n  allRegions: state => state.regions,\r\n  \r\n  // 获取当前大区\r\n  currentRegion: state => state.currentRegion,\r\n  \r\n  // 获取当前大区ID\r\n  currentRegionId: state => state.currentRegion ? state.currentRegion.id : null,\r\n  \r\n  // 获取当前大区名称\r\n  currentRegionName: state => state.currentRegion ? state.currentRegion.name : '',\r\n  \r\n  // 加载状态\r\n  isLoading: state => state.loading\r\n};\r\n\r\n// 变更事件类型\r\nconst mutations = {\r\n  // 设置大区列表\r\n  SET_REGIONS(state, regions) {\r\n    state.regions = regions;\r\n  },\r\n  \r\n  // 设置当前大区\r\n  SET_CURRENT_REGION(state, region) {\r\n    state.currentRegion = region;\r\n  },\r\n  \r\n  // 设置加载状态\r\n  SET_LOADING(state, loading) {\r\n    state.loading = loading;\r\n  },\r\n  \r\n  // 设置错误信息\r\n  SET_ERROR(state, error) {\r\n    state.error = error;\r\n  }\r\n};\r\n\r\n// 动作\r\nconst actions = {\r\n  // 初始化大区信息\r\n  initRegion({ commit }) {\r\n    try {\r\n      const savedRegion = localStorage.getItem('selectedRegion');\r\n      if (savedRegion) {\r\n        commit('SET_CURRENT_REGION', JSON.parse(savedRegion));\r\n      }\r\n    } catch (error) {\r\n      logger.error('[Region Store] 初始化大区信息失败:', error);\r\n    }\r\n  },\r\n  \r\n  // 选择大区\r\n  selectRegion({ commit }, region) {\r\n    if (!region || !region.id) {\r\n      commit('SET_ERROR', '无效的大区数据');\r\n      return false;\r\n    }\r\n    \r\n    try {\r\n      // 保存到Vuex状态\r\n      commit('SET_CURRENT_REGION', region);\r\n      \r\n      // 同时保存到本地存储\r\n      localStorage.setItem('selectedRegion', JSON.stringify(region));\r\n      logger.debug('[Region Store] 已选择大区:', region.name);\r\n      \r\n      return true;\r\n    } catch (error) {\r\n      logger.error('[Region Store] 选择大区失败:', error);\r\n      commit('SET_ERROR', '选择大区失败');\r\n      return false;\r\n    }\r\n  },\r\n  \r\n  // 清除当前大区\r\n  clearRegion({ commit }) {\r\n    commit('SET_CURRENT_REGION', null);\r\n    localStorage.removeItem('selectedRegion');\r\n  }\r\n};\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  getters,\r\n  mutations,\r\n  actions\r\n}; "], "mappings": "AAAA;AACA;AACA;AACA,OAAOA,MAAM,MAAM,gBAAgB;;AAEnC;AACA,MAAMC,KAAK,GAAG;EACZC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,IAAI;EACnBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,OAAO,GAAG;EACd;EACAC,UAAU,EAAEN,KAAK,IAAIA,KAAK,CAACC,OAAO;EAElC;EACAC,aAAa,EAAEF,KAAK,IAAIA,KAAK,CAACE,aAAa;EAE3C;EACAK,eAAe,EAAEP,KAAK,IAAIA,KAAK,CAACE,aAAa,GAAGF,KAAK,CAACE,aAAa,CAACM,EAAE,GAAG,IAAI;EAE7E;EACAC,iBAAiB,EAAET,KAAK,IAAIA,KAAK,CAACE,aAAa,GAAGF,KAAK,CAACE,aAAa,CAACQ,IAAI,GAAG,EAAE;EAE/E;EACAC,SAAS,EAAEX,KAAK,IAAIA,KAAK,CAACG;AAC5B,CAAC;;AAED;AACA,MAAMS,SAAS,GAAG;EAChB;EACAC,WAAWA,CAACb,KAAK,EAAEC,OAAO,EAAE;IAC1BD,KAAK,CAACC,OAAO,GAAGA,OAAO;EACzB,CAAC;EAED;EACAa,kBAAkBA,CAACd,KAAK,EAAEe,MAAM,EAAE;IAChCf,KAAK,CAACE,aAAa,GAAGa,MAAM;EAC9B,CAAC;EAED;EACAC,WAAWA,CAAChB,KAAK,EAAEG,OAAO,EAAE;IAC1BH,KAAK,CAACG,OAAO,GAAGA,OAAO;EACzB,CAAC;EAED;EACAc,SAASA,CAACjB,KAAK,EAAEI,KAAK,EAAE;IACtBJ,KAAK,CAACI,KAAK,GAAGA,KAAK;EACrB;AACF,CAAC;;AAED;AACA,MAAMc,OAAO,GAAG;EACd;EACAC,UAAUA,CAAC;IAAEC;EAAO,CAAC,EAAE;IACrB,IAAI;MACF,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAC1D,IAAIF,WAAW,EAAE;QACfD,MAAM,CAAC,oBAAoB,EAAEI,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAAC;MACvD;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdL,MAAM,CAACK,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED;EACAsB,YAAYA,CAAC;IAAEN;EAAO,CAAC,EAAEL,MAAM,EAAE;IAC/B,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACP,EAAE,EAAE;MACzBY,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC;MAC9B,OAAO,KAAK;IACd;IAEA,IAAI;MACF;MACAA,MAAM,CAAC,oBAAoB,EAAEL,MAAM,CAAC;;MAEpC;MACAO,YAAY,CAACK,OAAO,CAAC,gBAAgB,EAAEH,IAAI,CAACI,SAAS,CAACb,MAAM,CAAC,CAAC;MAC9DhB,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEd,MAAM,CAACL,IAAI,CAAC;MAElD,OAAO,IAAI;IACb,CAAC,CAAC,OAAON,KAAK,EAAE;MACdL,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7CgB,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC;MAC7B,OAAO,KAAK;IACd;EACF,CAAC;EAED;EACAU,WAAWA,CAAC;IAAEV;EAAO,CAAC,EAAE;IACtBA,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC;IAClCE,YAAY,CAACS,UAAU,CAAC,gBAAgB,CAAC;EAC3C;AACF,CAAC;AAED,eAAe;EACbC,UAAU,EAAE,IAAI;EAChBhC,KAAK;EACLK,OAAO;EACPO,SAAS;EACTM;AACF,CAAC", "ignoreList": []}]}