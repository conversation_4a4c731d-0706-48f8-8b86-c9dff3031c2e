{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue?vue&type=template&id=0012e22c", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue", "mtime": 1749700394407}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}