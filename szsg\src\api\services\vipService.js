import axios from 'axios'
import logger from '@/utils/logger'
import store from '@/store'
import { get, post } from '@/api/request'

// 获取认证头信息的辅助函数
const getAuthHeader = () => {
  const token = store.state.auth?.token || localStorage.getItem('token') || localStorage.getItem('authToken')
  return token ? { Authorization: `Bear<PERSON> ${token}` } : {}
}

/**
 * 获取VIP信息
 * @returns {Promise<Object>} VIP信息
 */
export const getVipInfo = async () => {
  try {
    // 使用封装的请求方法
    const response = await get('vip/info', {}, { loading: false });
    return response;
  } catch (error) {
    logger.error('获取VIP信息失败', error)
    // 模拟数据，仅用于开发测试
    return {
      level: 0,
      exp: 0,
      next_exp: 500,
      can_claim_reward: true,
      privileges: [
        '每日签到奖励增加50%',
        '商城购买折扣10%',
        '每日可额外领取100体力',
        '战斗经验增加15%'
      ],
      reward_history: [
        { level: 1, date: '2023-06-15', desc: '金砖x10，银两x1000，高级装备箱x1' },
        { level: 2, date: '2023-07-20', desc: '金砖x20，银两x2000，稀有武器箱x1' },
        { level: 3, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 4, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 5, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 6, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 7, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 8, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 9, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 10, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 11, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
        { level: 12, date: null, desc: '金砖x30，银两x3000，传说装备箱x1' },
      ],
      can_claim_daily: true
    }
  }
}

/**
 * 获取VIP等级配置
 * @returns {Promise<Array>} VIP等级配置列表
 */
export const getVipLevels = async () => {
  try {
    // 使用封装的请求方法
    const response = await get('vip/levels', {}, { loading: false });
    return response;
  } catch (error) {
    logger.error('获取VIP等级配置失败', error)
    // 模拟数据，仅用于开发测试
    return [
      { level: 0, exp: 0, reward: '无', privileges: ['基础游戏功能'] },
      { level: 1, exp: 100, reward: '金砖x10，银两x1000', privileges: ['每日签到奖励增加10%', '商城购买折扣5%'] },
      { level: 2, exp: 300, reward: '金砖x20，银两x2000', privileges: ['每日签到奖励增加20%', '商城购买折扣7%', '每日可额外领取50体力'] },
      { level: 3, exp: 500, reward: '金砖x30，银两x3000', privileges: ['每日签到奖励增加50%', '商城购买折扣10%', '每日可额外领取100体力', '战斗经验增加15%'] },
      { level: 4, exp: 1000, reward: '金砖x50，银两x5000', privileges: ['每日签到奖励增加70%', '商城购买折扣12%', '每日可额外领取150体力', '战斗经验增加20%', '自动战斗功能'] },
      { level: 5, exp: 2000, reward: '金砖x100，银两x10000', privileges: ['每日签到奖励增加100%', '商城购买折扣15%', '每日可额外领取200体力', '战斗经验增加25%', '自动战斗功能', '每日免费抽奖次数+1'] }
    ]
  }
}

/**
 * 领取VIP等级奖励
 * @returns {Promise<Object>} 领取结果
 */
export const claimVipReward = async () => {
  try {
    // 使用封装的请求方法
    const response = await post('vip/claim-reward', {}, { loading: true });
    return response;
  } catch (error) {
    logger.error('领取VIP奖励失败', error)
    throw error
  }
}

/**
 * 领取历史VIP奖励
 * @param {Number} level VIP等级
 * @returns {Promise<Object>} 领取结果
 */
export const claimHistoryReward = async (level) => {
  try {
    // 使用封装的请求方法
    const response = await post('vip/claim-history-reward', { level }, { loading: true });
    return response;
  } catch (error) {
    logger.error('领取历史VIP奖励失败', error)
    throw error
  }
}

/**
 * 领取每日VIP福利
 * @returns {Promise<Object>} 领取结果
 */
export const claimDailyReward = async () => {
  try {
    // 使用封装的请求方法
    const response = await post('vip/daily-reward', {}, { loading: true });
    return response;
  } catch (error) {
    logger.error('领取每日VIP福利失败', error)
    throw error
  }
}

/**
 * 领取每周VIP福利
 * @returns {Promise<Object>} 领取结果
 */
export const claimWeeklyReward = async () => {
  try {
    // 使用封装的请求方法
    const response = await post('vip/weekly-reward', {}, { loading: true });
    return response;
  } catch (error) {
    logger.error('领取每周VIP福利失败', error)
    throw error
  }
}

/**
 * 领取每月VIP福利
 * @returns {Promise<Object>} 领取结果
 */
export const claimMonthlyReward = async () => {
  try {
    // 使用封装的请求方法
    const response = await post('vip/monthly-reward', {}, { loading: true });
    return response;
  } catch (error) {
    logger.error('领取每月VIP福利失败', error)
    throw error
  }
}

/**
 * 添加VIP经验
 * @param {Number} amount 经验值
 * @returns {Promise<Object>} 添加结果
 */
export const addVipExp = async (amount) => {
  try {
    // 使用封装的请求方法
    const response = await post('vip/add-exp', { amount }, { loading: true });
    return response;
  } catch (error) {
    logger.error('添加VIP经验失败', error)
    throw error
  }
}

/**
 * 计算VIP经验值
 * @param {Number} amount 充值金额
 * @returns {Number} VIP经验值
 */
export const calculateVipExp = (amount) => {
  // 假设每充值1元获得10点VIP经验
  return Math.floor(amount * 10)
}