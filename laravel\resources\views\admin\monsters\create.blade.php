@extends('admin.layouts.app')

@section('title', '创建怪物')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        创建新怪物
        <a href="{{ route('admin.monsters.index') }}" class="layui-btn layui-btn-xs layui-btn-primary" style="float: right;">返回列表</a>
    </div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <form class="layui-form" action="{{ route('admin.monsters.store') }}" method="POST">
            @csrf
            <div class="layui-form-item">
                <label class="layui-form-label required">怪物名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" class="layui-input" required>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">怪物描述</label>
                <div class="layui-input-block">
                    <textarea name="description" class="layui-textarea">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">等级</label>
                        <div class="layui-input-block">
                            <input type="number" name="level" value="{{ old('level', 1) }}" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">生命值</label>
                        <div class="layui-input-block">
                            <input type="number" name="hp" value="{{ old('hp', 100) }}" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">魔法值</label>
                        <div class="layui-input-block">
                            <input type="number" name="mp" value="{{ old('mp', 0) }}" class="layui-input" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">攻击力</label>
                        <div class="layui-input-block">
                            <input type="number" name="attack" value="{{ old('attack', 10) }}" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">防御力</label>
                        <div class="layui-input-block">
                            <input type="number" name="defense" value="{{ old('defense', 5) }}" class="layui-input" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">速度</label>
                        <div class="layui-input-block">
                            <input type="number" name="speed" value="{{ old('speed', 5) }}" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">经验奖励</label>
                        <div class="layui-input-block">
                            <input type="number" name="exp_reward" value="{{ old('exp_reward', 10) }}" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">银两奖励</label>
                        <div class="layui-input-block">
                            <input type="number" name="silver_reward" value="{{ old('silver_reward', 5) }}" class="layui-input" min="0" required>
                        </div>
                    </div>
                </div>
            </div>

            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                <legend>怪物出现位置</legend>
            </fieldset>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    @foreach($locations as $location)
                    <input type="checkbox" name="location_ids[]" value="{{ $location->id }}" title="{{ $location->region_name }} - {{ $location->name }}" lay-skin="primary">
                    @endforeach
                </div>
            </div>

            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                <legend>怪物掉落物品</legend>
            </fieldset>

            <div class="layui-form-item">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div id="drop_items">
                            <div class="layui-form-item drop-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">物品</label>
                                    <div class="layui-input-inline">
                                        <select name="drop_items[0][item_id]">
                                            <option value="">请选择</option>
                                            @foreach($items as $item)
                                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">掉落率(%)</label>
                                    <div class="layui-input-inline" style="width: 80px;">
                                        <input type="number" name="drop_items[0][drop_rate]" class="layui-input" value="10" min="0" max="100" step="0.1">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button type="button" class="layui-btn layui-btn-danger" onclick="removeDropItem(this)">删除</button>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <button type="button" class="layui-btn" onclick="addDropItem()">添加掉落物品</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top: 20px;">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit>保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 表单验证
    form.verify({
        name: function(value) {
            if(value.length < 1 || value.length > 50) {
                return '怪物名称必须在1-50个字符之间';
            }
        }
    });
});

// 掉落物品计数器
var dropItemCount = 1;

// 添加掉落物品
function addDropItem() {
    var html = '<div class="layui-form-item drop-item">' +
        '<div class="layui-inline">' +
        '<label class="layui-form-label">物品</label>' +
        '<div class="layui-input-inline">' +
        '<select name="drop_items[' + dropItemCount + '][item_id]">' +
        '<option value="">请选择</option>';

    // 添加所有物品选项
    @foreach($items as $item)
    html += '<option value="{{ $item->id }}">{{ $item->name }}</option>';
    @endforeach

    html += '</select>' +
        '</div>' +
        '</div>' +
        '<div class="layui-inline">' +
        '<label class="layui-form-label">掉落率(%)</label>' +
        '<div class="layui-input-inline" style="width: 80px;">' +
        '<input type="number" name="drop_items[' + dropItemCount + '][drop_rate]" class="layui-input" value="10" min="0" max="100" step="0.1">' +
        '</div>' +
        '</div>' +
        '<div class="layui-inline">' +
        '<button type="button" class="layui-btn layui-btn-danger" onclick="removeDropItem(this)">删除</button>' +
        '</div>' +
        '</div>';

    $('#drop_items').append(html);
    dropItemCount++;

    // 重新渲染表单
    layui.form.render();
}

// 删除掉落物品
function removeDropItem(btn) {
    $(btn).closest('.drop-item').remove();
}
</script>
@endsection
