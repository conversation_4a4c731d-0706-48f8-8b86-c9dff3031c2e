<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use App\Models\CharacterVip;
use App\Models\VipLevel;
use App\Models\VipRewardClaim;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class VipController extends Controller
{
    /**
     * 获取角色VIP信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInfo(Request $request)
    {
        // 获取当前角色
        $character = $request->user()->currentCharacter();
        if (!$character) {
            return response()->json(['error' => '未选择角色'], 400);
        }

        // 获取或创建VIP信息
        $vip = CharacterVip::firstOrCreate(
            ['character_id' => $character->id],
            ['level' => 0, 'exp' => 0]
        );

        // 获取当前VIP等级信息
        $currentVipLevel = $vip->vipLevel() ?? VipLevel::where('level', 0)->first();

        // 获取下一个VIP等级信息
        $nextVipLevel = $vip->nextVipLevel();

        // 获取已领取的奖励记录
        $claimedRewards = $vip->rewardClaims()->pluck('vip_level')->toArray();

        // 检查是否可以领取当前等级奖励
        $canClaimReward = !in_array($vip->level, $claimedRewards) && $vip->level > 0;

        // 检查是否可以领取每日奖励
        $canClaimDaily = $vip->canClaimDailyReward();

        // 获取未领取的历史奖励
        $rewardHistory = [];
        for ($i = 1; $i <= $vip->level; $i++) {
            $level = VipLevel::where('level', $i)->first();
            if ($level) {
                $claim = VipRewardClaim::where('character_id', $character->id)
                    ->where('vip_level', $i)
                    ->first();

                $rewardHistory[] = [
                    'level' => $i,
                    'date' => $claim ? $claim->claimed_at->format('Y-m-d H:i:s') : null,
                    'desc' => $level->reward
                ];
            }
        }

        return response()->json([
            'level' => $vip->level,
            'exp' => $vip->exp,
            'next_exp' => $nextVipLevel ? $nextVipLevel->exp_required : $vip->exp,
            'privileges' => $currentVipLevel ? $currentVipLevel->privileges : [],
            'can_claim_reward' => $canClaimReward,
            'can_claim_daily' => $canClaimDaily,
            'reward_history' => $rewardHistory
        ]);
    }

    /**
     * 获取所有VIP等级信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLevels()
    {
        $levels = VipLevel::orderBy('level')->get()->map(function ($level) {
            return [
                'level' => $level->level,
                'exp' => $level->exp_required,
                'reward' => $level->reward,
                'privileges' => $level->privileges
            ];
        });

        return response()->json($levels);
    }

    /**
     * 领取VIP等级奖励
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function claimReward(Request $request)
    {
        // 获取当前角色
        $character = $request->user()->currentCharacter();
        if (!$character) {
            return response()->json(['error' => '未选择角色'], 400);
        }

        // 获取VIP信息
        $vip = CharacterVip::where('character_id', $character->id)->first();
        if (!$vip || $vip->level <= 0) {
            return response()->json(['error' => '未达到VIP等级要求'], 400);
        }

        // 检查是否已领取
        $claimed = VipRewardClaim::where('character_id', $character->id)
            ->where('vip_level', $vip->level)
            ->exists();

        if ($claimed) {
            return response()->json(['error' => '该等级奖励已领取'], 400);
        }

        // 获取当前VIP等级信息
        $vipLevel = VipLevel::where('level', $vip->level)->first();
        if (!$vipLevel) {
            return response()->json(['error' => 'VIP等级配置不存在'], 500);
        }

        // 开始事务
        DB::beginTransaction();
        try {
            // 记录领取
            VipRewardClaim::create([
                'character_id' => $character->id,
                'vip_level' => $vip->level,
                'claimed_at' => Carbon::now(),
                'reward_description' => $vipLevel->reward
            ]);

            // 发放奖励
            $this->grantVipReward($character, $vip->level);

            DB::commit();
            return response()->json(['message' => '奖励领取成功', 'reward' => $vipLevel->reward]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => '领取奖励失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 领取历史VIP等级奖励
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function claimHistoryReward(Request $request)
    {
        $request->validate([
            'level' => 'required|integer|min:1'
        ]);

        $level = $request->input('level');

        // 获取当前角色
        $character = $request->user()->currentCharacter();
        if (!$character) {
            return response()->json(['error' => '未选择角色'], 400);
        }

        // 获取VIP信息
        $vip = CharacterVip::where('character_id', $character->id)->first();
        if (!$vip || $vip->level < $level) {
            return response()->json(['error' => '未达到该VIP等级'], 400);
        }

        // 检查是否已领取
        $claimed = VipRewardClaim::where('character_id', $character->id)
            ->where('vip_level', $level)
            ->exists();

        if ($claimed) {
            return response()->json(['error' => '该等级奖励已领取'], 400);
        }

        // 获取指定VIP等级信息
        $vipLevel = VipLevel::where('level', $level)->first();
        if (!$vipLevel) {
            return response()->json(['error' => 'VIP等级配置不存在'], 500);
        }

        // 开始事务
        DB::beginTransaction();
        try {
            // 记录领取
            VipRewardClaim::create([
                'character_id' => $character->id,
                'vip_level' => $level,
                'claimed_at' => Carbon::now(),
                'reward_description' => $vipLevel->reward
            ]);

            // 发放奖励
            $this->grantVipReward($character, $level);

            DB::commit();
            return response()->json(['message' => '历史奖励领取成功', 'reward' => $vipLevel->reward]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => '领取奖励失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 领取每日VIP福利
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function claimDailyReward(Request $request)
    {
        // 获取当前角色
        $character = $request->user()->currentCharacter();
        if (!$character) {
            return response()->json(['error' => '未选择角色'], 400);
        }

        // 获取VIP信息
        $vip = CharacterVip::where('character_id', $character->id)->first();
        if (!$vip || $vip->level <= 0) {
            return response()->json(['error' => '未达到VIP等级要求'], 400);
        }

        // 检查是否可以领取
        if (!$vip->canClaimDailyReward()) {
            return response()->json(['error' => '今日已领取每日福利'], 400);
        }

        // 开始事务
        DB::beginTransaction();
        try {
            // 标记为已领取
            $vip->claimDailyReward();

            // 发放每日福利
            $this->grantDailyVipReward($character, $vip->level);

            DB::commit();
            return response()->json(['message' => '每日福利领取成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => '领取福利失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 添加VIP经验
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addExp(Request $request)
    {
        $request->validate([
            'amount' => 'required|integer|min:1'
        ]);

        $amount = $request->input('amount');

        // 获取当前角色
        $character = $request->user()->currentCharacter();
        if (!$character) {
            return response()->json(['error' => '未选择角色'], 400);
        }

        // 获取或创建VIP信息
        $vip = CharacterVip::firstOrCreate(
            ['character_id' => $character->id],
            ['level' => 0, 'exp' => 0]
        );

        // 记录之前的等级
        $oldLevel = $vip->level;

        // 添加经验
        $vip->addExp($amount);

        // 检查是否升级
        $levelUp = $vip->level > $oldLevel;

        return response()->json([
            'message' => '成功添加VIP经验',
            'level' => $vip->level,
            'exp' => $vip->exp,
            'level_up' => $levelUp
        ]);
    }

    /**
     * 发放VIP等级奖励
     *
     * @param Character $character
     * @param int $level
     * @return void
     */
    private function grantVipReward(Character $character, int $level)
    {
        // 根据不同等级发放不同奖励
        switch ($level) {
            case 1:
                $character->addGold(10);
                $character->addSilver(1000);
                // 添加高级装备箱物品
                break;
            case 2:
                $character->addGold(20);
                $character->addSilver(2000);
                // 添加稀有武器箱物品
                break;
            case 3:
                $character->addGold(30);
                $character->addSilver(3000);
                // 添加传说装备箱物品
                break;
            case 4:
                $character->addGold(50);
                $character->addSilver(5000);
                // 添加更高级物品
                break;
            case 5:
                $character->addGold(100);
                $character->addSilver(10000);
                // 添加更高级物品
                break;
            // 更多等级奖励...
            default:
                $character->addGold($level * 10);
                $character->addSilver($level * 1000);
                break;
        }

        $character->save();
    }

    /**
     * 发放每日VIP福利
     *
     * @param Character $character
     * @param int $level
     * @return void
     */
    private function grantDailyVipReward(Character $character, int $level)
    {
        // 根据VIP等级发放每日福利
        $character->addGold($level * 10);
        $character->addSilver($level * 1000);
        $character->addEnergy($level * 5);
        $character->save();
    }
}
