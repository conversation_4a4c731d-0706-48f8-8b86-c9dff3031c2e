@extends('admin.layouts.app')

@section('title', '添加位置连接')

@section('content')
@section('breadcrumb')
<a href="{{ route('admin.location-connections.index') }}">位置连接管理</a>
<span class="layui-breadcrumb-separator">/</span>
<span>添加连接</span>
@endsection

@section('page-title', '添加位置连接')

<div class="layui-card">
    <div class="layui-card-header">
        <a href="{{ route('admin.location-connections.index') }}" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        添加位置连接
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.location-connections.store') }}">
            @csrf

            <div class="layui-form-item">
                <label class="layui-form-label">起始位置</label>
                <div class="layui-input-block">
                    <select name="from_location_id" lay-verify="required" lay-search>
                        <option value="">请选择起始位置</option>
                        @foreach($locations as $location)
                        <option value="{{ $location->id }}" {{ old('from_location_id') == $location->id ? 'selected' : '' }}>
                            {{ $location->name }} ({{ $location->type }})
                        </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">目标位置</label>
                <div class="layui-input-block">
                    <select name="to_location_id" lay-verify="required" lay-search>
                        <option value="">请选择目标位置</option>
                        @foreach($locations as $location)
                        <option value="{{ $location->id }}" {{ old('to_location_id') == $location->id ? 'selected' : '' }}>
                            {{ $location->name }} ({{ $location->type }})
                        </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">距离</label>
                <div class="layui-input-block">
                    <input type="number" name="distance" value="{{ old('distance', 1) }}" required lay-verify="required|number" placeholder="请输入距离" class="layui-input">
                    <div class="layui-form-mid layui-text-em">用于计算移动时间和消耗</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">时间消耗</label>
                <div class="layui-input-block">
                    <input type="number" name="time_cost" value="{{ old('time_cost', 5) }}" required lay-verify="required|number" placeholder="请输入时间消耗" class="layui-input">
                    <div class="layui-form-mid layui-text-em">单位：分钟</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">银两消耗</label>
                <div class="layui-input-block">
                    <input type="number" name="silver_cost" value="{{ old('silver_cost', 0) }}" required lay-verify="required|number" placeholder="请输入银两消耗" class="layui-input">
                    <div class="layui-form-mid layui-text-em">单位：两银子</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级要求</label>
                <div class="layui-input-block">
                    <input type="number" name="level_requirement" value="{{ old('level_requirement', 1) }}" required lay-verify="required|number" placeholder="请输入等级要求" class="layui-input">
                    <div class="layui-form-mid layui-text-em">角色需要达到的最低等级</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_bidirectional" value="1" title="创建双向连接" {{ old('is_bidirectional') ? 'checked' : '' }}>
                    <div class="layui-form-mid layui-text-em">同时创建反向连接，允许双向移动</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit>保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <a href="{{ route('admin.location-connections.index') }}" class="layui-btn layui-btn-primary">取消</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('js')
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    // 表单验证
    form.verify({
        number: function(value) {
            if(value && !/^\d+$/.test(value)) {
                return '请输入正整数';
            }
        }
    });

    // 监听起始位置选择
    form.on('select(from_location)', function(data){
        var toLocationSelect = $('select[name="to_location_id"]');
        var selectedValue = toLocationSelect.val();
        
        // 重新渲染目标位置选择器，排除已选择的起始位置
        toLocationSelect.find('option').each(function(){
            if($(this).val() === data.value) {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
        
        form.render('select');
    });

    // 监听目标位置选择
    form.on('select(to_location)', function(data){
        var fromLocationSelect = $('select[name="from_location_id"]');
        var selectedValue = fromLocationSelect.val();
        
        // 重新渲染起始位置选择器，排除已选择的目标位置
        fromLocationSelect.find('option').each(function(){
            if($(this).val() === data.value) {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
        
        form.render('select');
    });

    // 表单提交
    form.on('submit', function(data){
        var fromLocation = data.field.from_location_id;
        var toLocation = data.field.to_location_id;
        
        if(fromLocation === toLocation) {
            layer.msg('起始位置和目标位置不能相同');
            return false;
        }
        
        return true;
    });
});
</script>
@endsection
