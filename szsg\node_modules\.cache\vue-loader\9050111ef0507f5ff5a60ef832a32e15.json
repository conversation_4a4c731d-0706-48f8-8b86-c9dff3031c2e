{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\StorageCleanup.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\StorageCleanup.vue", "mtime": 1749868572976}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["StorageCleanup.vue"], "names": [], "mappings": ";AA+DA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "StorageCleanup.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"storage-cleanup\">\n    <div class=\"header\">\n      <h2>存储空间清理</h2>\n      <p>如果遇到\"请求头过大\"错误，可以使用此工具清理存储空间</p>\n    </div>\n\n    <div class=\"storage-info\">\n      <h3>当前存储状态</h3>\n      <div class=\"info-item\">\n        <span>总存储大小:</span>\n        <span>{{ formatBytes(storageInfo.currentSize) }}</span>\n      </div>\n      <div class=\"info-item\">\n        <span>存储项目数量:</span>\n        <span>{{ storageInfo.keys.length }}</span>\n      </div>\n    </div>\n\n    <div class=\"storage-items\">\n      <h3>存储项目详情</h3>\n      <div class=\"item-list\">\n        <div \n          v-for=\"item in storageItems\" \n          :key=\"item.key\"\n          class=\"storage-item\"\n          :class=\"{ 'large-item': item.size > 50000 }\"\n        >\n          <div class=\"item-info\">\n            <span class=\"item-key\">{{ item.key }}</span>\n            <span class=\"item-size\">{{ formatBytes(item.size) }}</span>\n          </div>\n          <button \n            @click=\"removeItem(item.key)\"\n            class=\"remove-btn\"\n            :disabled=\"item.key === 'szxy-game-state'\"\n          >\n            删除\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"actions\">\n      <button @click=\"clearExpiredCache\" class=\"action-btn\">清理过期缓存</button>\n      <button @click=\"clearAllCache\" class=\"action-btn warning\">清理所有缓存</button>\n      <button @click=\"clearAllStorage\" class=\"action-btn danger\">清空所有存储</button>\n      <button @click=\"refreshInfo\" class=\"action-btn\">刷新信息</button>\n    </div>\n\n    <div class=\"tips\">\n      <h3>使用提示</h3>\n      <ul>\n        <li>如果遇到HTTP 431错误，建议先点击\"清理过期缓存\"</li>\n        <li>红色标记的项目是大型存储项目（>50KB），可能导致请求头过大</li>\n        <li>\"szxy-game-state\"是游戏核心状态，不建议删除</li>\n        <li>清理后需要重新登录和选择大区</li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getStorageInfoSync, clearExpiredCache, cleanupLargeStorageItems } from '@/utils/storage.js'\n\nexport default {\n  name: 'StorageCleanup',\n  data() {\n    return {\n      storageInfo: {\n        keys: [],\n        currentSize: 0\n      },\n      storageItems: []\n    }\n  },\n  mounted() {\n    this.refreshInfo()\n  },\n  methods: {\n    refreshInfo() {\n      this.storageInfo = getStorageInfoSync()\n      this.storageItems = this.getStorageItems()\n    },\n\n    getStorageItems() {\n      const items = []\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        const value = localStorage.getItem(key) || ''\n        items.push({\n          key,\n          size: key.length + value.length,\n          value: value.substring(0, 100) + (value.length > 100 ? '...' : '')\n        })\n      }\n      return items.sort((a, b) => b.size - a.size)\n    },\n\n    formatBytes(bytes) {\n      if (bytes === 0) return '0 Bytes'\n      const k = 1024\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    },\n\n    removeItem(key) {\n      if (confirm(`确定要删除存储项目 \"${key}\" 吗？`)) {\n        localStorage.removeItem(key)\n        this.refreshInfo()\n        this.$message?.success?.('删除成功')\n      }\n    },\n\n    clearExpiredCache() {\n      try {\n        clearExpiredCache()\n        this.refreshInfo()\n        this.$message?.success?.('过期缓存清理完成')\n      } catch (error) {\n        this.$message?.error?.('清理失败: ' + error.message)\n      }\n    },\n\n    clearAllCache() {\n      if (confirm('确定要清理所有缓存吗？这将删除所有以\"SZXY_CACHE_\"开头的存储项目。')) {\n        try {\n          const keys = []\n          for (let i = 0; i < localStorage.length; i++) {\n            const key = localStorage.key(i)\n            if (key && key.startsWith('SZXY_CACHE_')) {\n              keys.push(key)\n            }\n          }\n          keys.forEach(key => localStorage.removeItem(key))\n          this.refreshInfo()\n          this.$message?.success?.(`清理了 ${keys.length} 个缓存项目`)\n        } catch (error) {\n          this.$message?.error?.('清理失败: ' + error.message)\n        }\n      }\n    },\n\n    clearAllStorage() {\n      if (confirm('确定要清空所有本地存储吗？这将删除所有游戏数据，需要重新登录！')) {\n        if (confirm('再次确认：这将删除所有本地数据，包括登录状态、游戏设置等！')) {\n          localStorage.clear()\n          this.refreshInfo()\n          this.$message?.success?.('所有存储已清空')\n          // 3秒后刷新页面\n          setTimeout(() => {\n            window.location.reload()\n          }, 3000)\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.storage-cleanup {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.header {\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.header h2 {\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.storage-info {\n  background: #f5f5f5;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.storage-info h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.storage-items {\n  margin-bottom: 30px;\n}\n\n.storage-items h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.item-list {\n  max-height: 400px;\n  overflow-y: auto;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n\n.storage-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #eee;\n}\n\n.storage-item.large-item {\n  background-color: #fff5f5;\n  border-left: 4px solid #ff4757;\n}\n\n.item-info {\n  flex: 1;\n  display: flex;\n  justify-content: space-between;\n  margin-right: 15px;\n}\n\n.item-key {\n  font-weight: bold;\n  color: #333;\n}\n\n.item-size {\n  color: #666;\n  font-size: 12px;\n}\n\n.remove-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 5px 10px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n.remove-btn:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.actions {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-bottom: 30px;\n}\n\n.action-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  background: #007bff;\n  color: white;\n}\n\n.action-btn.warning {\n  background: #ffc107;\n  color: #333;\n}\n\n.action-btn.danger {\n  background: #dc3545;\n}\n\n.action-btn:hover {\n  opacity: 0.8;\n}\n\n.tips {\n  background: #e7f3ff;\n  padding: 15px;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n}\n\n.tips h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.tips ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.tips li {\n  margin-bottom: 5px;\n  color: #666;\n  font-size: 14px;\n}\n</style>\n"]}]}