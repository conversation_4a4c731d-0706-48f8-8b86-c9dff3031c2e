<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVipTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // VIP等级配置表
        Schema::create('vip_levels', function (Blueprint $table) {
            $table->id();
            $table->integer('level')->unique()->comment('VIP等级');
            $table->integer('exp_required')->comment('所需经验值');
            $table->string('reward')->comment('升级奖励');
            $table->json('privileges')->comment('特权列表');
            $table->timestamps();
        });

        // 用户VIP信息表
        Schema::create('character_vip', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained()->onDelete('cascade');
            $table->integer('level')->default(0)->comment('当前VIP等级');
            $table->integer('exp')->default(0)->comment('当前VIP经验');
            $table->boolean('daily_reward_claimed')->default(false)->comment('今日是否已领取每日奖励');
            $table->timestamp('daily_reward_reset_at')->nullable()->comment('每日奖励重置时间');
            $table->timestamps();
        });

        // VIP奖励领取记录表
        Schema::create('vip_reward_claims', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained()->onDelete('cascade');
            $table->integer('vip_level')->comment('领取的VIP等级奖励');
            $table->timestamp('claimed_at')->useCurrent()->comment('领取时间');
            $table->string('reward_description')->comment('奖励描述');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vip_reward_claims');
        Schema::dropIfExists('character_vip');
        Schema::dropIfExists('vip_levels');
    }
}
