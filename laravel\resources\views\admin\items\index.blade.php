@extends('admin.layouts.app')

@section('title', '物品管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        物品列表
        <a href="{{ route('admin.items.create') }}" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加物品</a>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>类型</th>
                    <th>等级要求</th>
                    <th>购买价格</th>
                    <th>出售价格</th>
                    <th>稀有度</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($items as $item)
                <tr>
                    <td>{{ $item->id }}</td>
                    <td>{{ $item->name }}</td>
                    <td>
                        @php
                        $typeMap = [
                            'weapon' => '武器',
                            'armor' => '防具',
                            'accessory' => '饰品',
                            'consumable' => '消耗品',
                            'material' => '材料',
                            'quest' => '任务物品',
                        ];
                        @endphp
                        {{ $typeMap[$item->type] ?? $item->type }}
                    </td>
                    <td>{{ $item->level_requirement ?? 0 }}</td>
                    <td>{{ $item->buy_price ?? 0 }}</td>
                    <td>{{ $item->sell_price ?? 0 }}</td>
                    <td>
                        @for($i = 0; $i < $item->rarity; $i++)
                        <i class="layui-icon layui-icon-star" style="color: #FFB800;"></i>
                        @endfor
                    </td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.items.show', $item->id) }}" class="layui-btn layui-btn-xs layui-btn-primary">查看</a>
                            <a href="{{ route('admin.items.edit', $item->id) }}" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteItem({{ $item->id }}, '{{ $item->name }}')">删除</button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="layui-center">暂无物品数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $items->links('admin.layouts.pagination') }}
    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@section('scripts')
<script>
function deleteItem(id, name) {
    layer.confirm('确定要删除物品 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "{{ route('admin.items.destroy', '') }}/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
