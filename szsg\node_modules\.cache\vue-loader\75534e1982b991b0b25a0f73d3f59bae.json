{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\RequestTest.vue?vue&type=style&index=0&id=c8d1fe8a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\RequestTest.vue", "mtime": 1749872688134}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yZXF1ZXN0LXRlc3QgewogIHBhZGRpbmc6IDIwcHg7CiAgbWF4LXdpZHRoOiAxMDAwcHg7CiAgbWFyZ2luOiAwIGF1dG87Cn0KCi50ZXN0LXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBiYWNrZ3JvdW5kOiAjZjlmOWY5Owp9CgoudGVzdC1zZWN0aW9uIGgzIHsKICBtYXJnaW4tdG9wOiAwOwogIGNvbG9yOiAjMzMzOwp9Cgouc3RvcmFnZS1pbmZvIHAgewogIG1hcmdpbjogNXB4IDA7CiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsKfQoKLnRlc3QtYnV0dG9ucyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEwcHg7CiAgZmxleC13cmFwOiB3cmFwOwp9CgoudGVzdC1idXR0b25zIGJ1dHRvbiB7CiAgcGFkZGluZzogMTBweCAyMHB4OwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYmFja2dyb3VuZDogIzAwN2JmZjsKICBjb2xvcjogd2hpdGU7CiAgY3Vyc29yOiBwb2ludGVyOwp9CgoudGVzdC1idXR0b25zIGJ1dHRvbjpkaXNhYmxlZCB7CiAgYmFja2dyb3VuZDogI2NjYzsKICBjdXJzb3I6IG5vdC1hbGxvd2VkOwp9CgoudGVzdC1idXR0b25zIGJ1dHRvbjpob3Zlcjpub3QoOmRpc2FibGVkKSB7CiAgYmFja2dyb3VuZDogIzAwNTZiMzsKfQoKLnJlc3VsdC1hcmVhIHsKICBiYWNrZ3JvdW5kOiAjMDAwOwogIGNvbG9yOiAjMGYwOwogIHBhZGRpbmc6IDE1cHg7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7CiAgZm9udC1zaXplOiAxMnB4OwogIG1heC1oZWlnaHQ6IDMwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5oZWFkZXJzLWluZm8gcCB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKLmhlYWRlcnMtbGlzdCB7CiAgbWF4LWhlaWdodDogMjAwcHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBib3JkZXI6IDFweCBzb2xpZCAjY2NjOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBwYWRkaW5nOiAxMHB4OwogIGJhY2tncm91bmQ6IHdoaXRlOwp9CgouaGVhZGVyLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgbWFyZ2luLWJvdHRvbTogNXB4OwogIGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7CiAgZm9udC1zaXplOiAxMnB4Owp9CgouaGVhZGVyLWtleSB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICMwMDdiZmY7CiAgbWluLXdpZHRoOiAxNTBweDsKfQoKLmhlYWRlci12YWx1ZSB7CiAgZmxleDogMTsKICBtYXJnaW46IDAgMTBweDsKICBjb2xvcjogIzMzMzsKfQoKLmhlYWRlci1zaXplIHsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDEwcHg7Cn0K"}, {"version": 3, "sources": ["RequestTest.vue"], "names": [], "mappings": ";AAmSA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "RequestTest.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"request-test\">\n    <h2>请求测试工具</h2>\n    <p>用于测试和调试HTTP 431错误</p>\n\n    <div class=\"test-section\">\n      <h3>存储状态</h3>\n      <div class=\"storage-info\">\n        <p>总存储大小: {{ formatBytes(storageSize) }}</p>\n        <p>存储项目数: {{ storageCount }}</p>\n        <p>最大项目: {{ largestItem.key }} ({{ formatBytes(largestItem.size) }})</p>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>请求测试</h3>\n      <div class=\"test-buttons\">\n        <button @click=\"testRegionRequest\" :disabled=\"isLoading\">\n          {{ isLoading ? '测试中...' : '测试大区API' }}\n        </button>\n        <button @click=\"emergencyClean\" :disabled=\"isLoading\">\n          紧急清理存储\n        </button>\n        <button @click=\"clearCookies\" :disabled=\"isLoading\">\n          清理Cookies\n        </button>\n        <button @click=\"testDirectRequest\" :disabled=\"isLoading\">\n          直接请求测试\n        </button>\n        <button @click=\"fix431AndTest\" :disabled=\"isLoading\">\n          修复431错误并测试\n        </button>\n        <button @click=\"refreshInfo\">\n          刷新信息\n        </button>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试结果</h3>\n      <div class=\"result-area\">\n        <pre>{{ testResult }}</pre>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>请求头信息</h3>\n      <div class=\"headers-info\">\n        <p>预估请求头大小: {{ estimatedHeaderSize }} bytes</p>\n        <div class=\"headers-list\">\n          <div v-for=\"(value, key) in estimatedHeaders\" :key=\"key\" class=\"header-item\">\n            <span class=\"header-key\">{{ key }}:</span>\n            <span class=\"header-value\">{{ value.substring(0, 100) }}{{ value.length > 100 ? '...' : '' }}</span>\n            <span class=\"header-size\">({{ key.length + value.length }} bytes)</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getRegions } from '@/api/services/regionService.js'\nimport { emergencyCleanupStorage, clearAllCookies } from '@/utils/storage.js'\nimport { fix431Error, check431Risk, minimalFetch } from '@/utils/fix431Error.js'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'RequestTest',\n  data() {\n    return {\n      isLoading: false,\n      testResult: '点击\"测试大区API\"开始测试',\n      storageSize: 0,\n      storageCount: 0,\n      largestItem: { key: '', size: 0 },\n      estimatedHeaders: {},\n      estimatedHeaderSize: 0\n    }\n  },\n  mounted() {\n    this.refreshInfo()\n  },\n  methods: {\n    async testRegionRequest() {\n      this.isLoading = true\n      this.testResult = '正在测试大区API请求...'\n      \n      try {\n        const startTime = Date.now()\n        const result = await getRegions()\n        const endTime = Date.now()\n        \n        this.testResult = `✅ 请求成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(result, null, 2)}`\n      } catch (error) {\n        this.testResult = `❌ 请求失败！\n错误代码: ${error.code}\n错误信息: ${error.message}\n需要清理: ${error.needCleanup ? '是' : '否'}\n详细信息: ${JSON.stringify(error, null, 2)}`\n        \n        logger.error('[RequestTest] 测试失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async emergencyClean() {\n      this.isLoading = true\n      this.testResult = '正在执行紧急清理...'\n\n      try {\n        const success = emergencyCleanupStorage()\n        if (success) {\n          this.testResult = '✅ 紧急清理完成！'\n        } else {\n          this.testResult = '❌ 紧急清理失败！'\n        }\n      } catch (error) {\n        this.testResult = `❌ 紧急清理出错: ${error.message}`\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async clearCookies() {\n      this.isLoading = true\n      this.testResult = '正在清理Cookies...'\n\n      try {\n        const success = clearAllCookies()\n        if (success) {\n          this.testResult = '✅ Cookies清理完成！'\n        } else {\n          this.testResult = '❌ Cookies清理失败！'\n        }\n      } catch (error) {\n        this.testResult = `❌ 清理Cookies出错: ${error.message}`\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async testDirectRequest() {\n      this.isLoading = true\n      this.testResult = '正在执行直接请求测试...'\n\n      try {\n        const startTime = Date.now()\n\n        // 使用最小化的fetch请求\n        const data = await minimalFetch('/api/regions')\n\n        const endTime = Date.now()\n\n        this.testResult = `✅ 直接请求成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(data, null, 2)}`\n      } catch (error) {\n        this.testResult = `❌ 直接请求出错: ${error.message}`\n        logger.error('[RequestTest] 直接请求失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    async fix431AndTest() {\n      this.isLoading = true\n      this.testResult = '正在修复431错误并测试...'\n\n      try {\n        // 1. 检查风险\n        const riskCheck = check431Risk()\n        this.testResult += `\\n\\n风险检查结果:\\n${JSON.stringify(riskCheck, null, 2)}`\n\n        // 2. 执行修复\n        if (riskCheck.hasRisk) {\n          this.testResult += '\\n\\n检测到风险，正在修复...'\n          const fixResult = fix431Error()\n          this.testResult += `\\n修复结果: ${fixResult ? '成功' : '失败'}`\n        }\n\n        // 3. 测试请求\n        this.testResult += '\\n\\n正在测试大区API...'\n        const startTime = Date.now()\n\n        try {\n          const result = await getRegions()\n          const endTime = Date.now()\n\n          this.testResult += `\\n\\n✅ 大区API测试成功！\n时间: ${endTime - startTime}ms\n结果: ${JSON.stringify(result, null, 2)}`\n        } catch (apiError) {\n          this.testResult += `\\n\\n❌ 大区API测试失败: ${apiError.message}`\n\n          // 尝试最小化请求\n          try {\n            this.testResult += '\\n\\n尝试最小化请求...'\n            const minimalResult = await minimalFetch('/api/regions')\n            this.testResult += `\\n\\n✅ 最小化请求成功！\n结果: ${JSON.stringify(minimalResult, null, 2)}`\n          } catch (minimalError) {\n            this.testResult += `\\n\\n❌ 最小化请求也失败: ${minimalError.message}`\n          }\n        }\n\n      } catch (error) {\n        this.testResult += `\\n\\n❌ 修复过程出错: ${error.message}`\n        logger.error('[RequestTest] 431修复失败:', error)\n      } finally {\n        this.isLoading = false\n        this.refreshInfo()\n      }\n    },\n\n    refreshInfo() {\n      this.calculateStorageInfo()\n      this.calculateHeaderInfo()\n    },\n\n    calculateStorageInfo() {\n      let totalSize = 0\n      let count = 0\n      let largest = { key: '', size: 0 }\n\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        const value = localStorage.getItem(key) || ''\n        const size = key.length + value.length\n        \n        totalSize += size\n        count++\n        \n        if (size > largest.size) {\n          largest = { key, size }\n        }\n      }\n\n      this.storageSize = totalSize\n      this.storageCount = count\n      this.largestItem = largest\n    },\n\n    calculateHeaderInfo() {\n      // 模拟请求头\n      const headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'User-Agent': navigator.userAgent,\n        'Referer': window.location.href,\n        'Accept-Language': navigator.language,\n        'Accept-Encoding': 'gzip, deflate, br',\n        'Connection': 'keep-alive',\n        'Cache-Control': 'no-cache'\n      }\n\n      // 添加可能的认证头\n      try {\n        const token = this.$store?.state?.auth?.token\n        if (token && typeof token === 'string') {\n          headers['Authorization'] = `Bearer ${token}`\n        }\n      } catch (e) {\n        // 忽略错误\n      }\n\n      this.estimatedHeaders = headers\n      this.estimatedHeaderSize = Object.keys(headers).reduce((size, key) => {\n        return size + key.length + (headers[key] || '').length\n      }, 0)\n    },\n\n    formatBytes(bytes) {\n      if (bytes === 0) return '0 Bytes'\n      const k = 1024\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.request-test {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background: #f9f9f9;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.storage-info p {\n  margin: 5px 0;\n  font-family: monospace;\n}\n\n.test-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.test-buttons button {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  background: #007bff;\n  color: white;\n  cursor: pointer;\n}\n\n.test-buttons button:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.test-buttons button:hover:not(:disabled) {\n  background: #0056b3;\n}\n\n.result-area {\n  background: #000;\n  color: #0f0;\n  padding: 15px;\n  border-radius: 4px;\n  font-family: monospace;\n  font-size: 12px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.headers-info p {\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.headers-list {\n  max-height: 200px;\n  overflow-y: auto;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  padding: 10px;\n  background: white;\n}\n\n.header-item {\n  display: flex;\n  margin-bottom: 5px;\n  font-family: monospace;\n  font-size: 12px;\n}\n\n.header-key {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 150px;\n}\n\n.header-value {\n  flex: 1;\n  margin: 0 10px;\n  color: #333;\n}\n\n.header-size {\n  color: #666;\n  font-size: 10px;\n}\n</style>\n"]}]}