<?php

namespace App\Http\Controllers;

use App\Models\Character;
use App\Models\Location;
use App\Models\LocationConnection;
use App\Models\CharacterLocation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MapController extends Controller
{
    /**
     * 获取角色当前地图数据
     */
    public function getCurrentMap(Request $request, $characterId): JsonResponse
    {
        try {
            // 检查角色是否存在
            $character = Character::find($characterId);

            if (!$character) {
                return response()->json([
                    'success' => false,
                    'message' => '角色未找到'
                ], 404);
            }

            // 检查角色是否属于当前用户
            if (auth()->check() && $character->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问此角色'
                ], 403);
            }

            // 获取地图数据（龙宫）
            $region = \App\Models\Region::where('code', 'dragon_palace')->first();

            if (!$region) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到地图数据'
                ], 404);
            }

            // 获取位置数据
            $locations = $region->locations;

            if ($locations->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => '地图中没有位置数据'
                ], 404);
            }

            // 默认位置为第一个位置（龙宫大门）
            $currentLocation = $locations->first();

            // 获取角色当前位置（如果有）
            $characterLocation = CharacterLocation::getCurrentLocation($characterId);

            // 如果没有位置记录，创建一个
            if (!$characterLocation) {
                $characterLocation = CharacterLocation::setLocation($characterId, $currentLocation->id);
            }

            $currentLocation = $characterLocation->location;

            // 返回地图数据
            return response()->json([
                'success' => true,
                'data' => [
                    'region' => [
                        'id' => $region->id,
                        'name' => $region->name,
                        'code' => $region->code,
                        'type' => $region->type,
                    ],
                    'current_location' => [
                        'id' => $currentLocation->id,
                        'name' => $currentLocation->name,
                        'type' => $currentLocation->type,
                        'description' => $currentLocation->description,
                        'coordinates' => [
                            'x' => $currentLocation->x,
                            'y' => $currentLocation->y
                        ],
                        'is_safe' => (bool)$currentLocation->is_safe,
                    ],
                    'available_locations' => $locations->map(function ($location) {
                        return [
                            'id' => $location->id,
                            'name' => $location->name,
                            'type' => $location->type,
                            'coordinates' => [
                                'x' => $location->x,
                                'y' => $location->y
                            ],
                            'is_safe' => (bool)$location->is_safe,
                        ];
                    })->toArray(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取地图数据失败', [
                'character_id' => $characterId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取地图数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取可移动位置列表
     */
    public function getAvailableLocations(Request $request, $characterId): JsonResponse
    {
        try {
            $character = Character::findOrFail($characterId);

            // 检查角色是否属于当前用户
            if ($character->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问此角色'
                ], 403);
            }

            $currentLocationId = $request->get('current_location');
            if (!$currentLocationId) {
                $characterLocation = CharacterLocation::getCurrentLocation($characterId);
                $currentLocationId = $characterLocation ? $characterLocation->location_id : null;
            }

            if (!$currentLocationId) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到当前位置'
                ], 404);
            }

            $currentLocation = Location::findOrFail($currentLocationId);
            $availableLocations = $currentLocation->getAvailableLocations($character->level);

            return response()->json([
                'success' => true,
                'data' => $availableLocations->map(function ($location) use ($currentLocation) {
                    $connection = $currentLocation->getMovementCost($location->id);
                    return [
                        'id' => $location->id,
                        'name' => $location->name,
                        'type' => $location->type,
                        'description' => $location->description,
                        'distance' => $connection ? $connection->distance : 1,
                        'cost' => $connection ? $connection->getCostInfo() : null,
                    ];
                })
            ]);

        } catch (\Exception $e) {
            Log::error('获取可移动位置失败', [
                'character_id' => $characterId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取可移动位置失败'
            ], 500);
        }
    }

    /**
     * 移动到指定位置
     */
    public function moveToLocation(Request $request, $characterId): JsonResponse
    {
        try {
            $request->validate([
                'target_location' => 'required|integer|exists:locations,id'
            ]);

            $character = Character::findOrFail($characterId);

            // 检查角色是否属于当前用户
            if ($character->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问此角色'
                ], 403);
            }

            $targetLocationId = $request->get('target_location');

            // 获取当前位置
            $characterLocation = CharacterLocation::getCurrentLocation($characterId);
            if (!$characterLocation) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到当前位置'
                ], 404);
            }

            $currentLocation = $characterLocation->location;
            $targetLocation = Location::findOrFail($targetLocationId);

            // 检查是否可以移动到目标位置
            if (!$currentLocation->canMoveTo($targetLocationId, $character->level)) {
                return response()->json([
                    'success' => false,
                    'message' => '无法移动到该位置'
                ], 400);
            }

            // 获取移动消耗
            $connection = $currentLocation->getMovementCost($targetLocationId);
            if (!$connection) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到移动路径'
                ], 404);
            }

            // 检查移动条件
            $requirements = $connection->checkRequirements($character);
            if (!$requirements['can_move']) {
                return response()->json([
                    'success' => false,
                    'message' => '移动条件不满足：' . implode(', ', $requirements['reasons'])
                ], 400);
            }

            DB::beginTransaction();

            try {
                // 扣除消耗（已完全移除体力消耗功能）
                if ($connection->silver_cost > 0) {
                    $character->silver -= $connection->silver_cost;
                    $character->save();
                }

                // 更新角色位置
                CharacterLocation::setLocation($characterId, $targetLocationId);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'data' => [
                        'new_location' => [
                            'id' => $targetLocation->id,
                            'name' => $targetLocation->name,
                            'type' => $targetLocation->type,
                            'description' => $targetLocation->description,
                            'coordinates' => [
                                'x' => $targetLocation->x,
                                'y' => $targetLocation->y
                            ],
                            'is_safe' => $targetLocation->is_safe,
                            'facilities' => $targetLocation->facilities ?? [],
                            'npcs' => $targetLocation->npcs ?? [],
                            'monsters' => $targetLocation->monsters ?? [],
                        ],
                        'cost' => $connection->getCostInfo(),
                        'character_updates' => [
                            'silver' => $character->silver,
                        ]
                    ],
                    'message' => "成功移动到{$targetLocation->name}"
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('移动失败', [
                'character_id' => $characterId,
                'target_location' => $request->get('target_location'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '移动失败'
            ], 500);
        }
    }

    /**
     * 获取移动消耗
     */
    public function getMovementCost(Request $request, $characterId): JsonResponse
    {
        try {
            $request->validate([
                'from_location' => 'required|integer|exists:locations,id',
                'to_location' => 'required|integer|exists:locations,id'
            ]);

            $character = Character::findOrFail($characterId);

            // 检查角色是否属于当前用户
            if ($character->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问此角色'
                ], 403);
            }

            $fromLocationId = $request->get('from_location');
            $toLocationId = $request->get('to_location');

            $connection = LocationConnection::where('from_location_id', $fromLocationId)
                ->where('to_location_id', $toLocationId)
                ->where('is_active', true)
                ->first();

            if (!$connection) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到移动路径'
                ], 404);
            }

            // 检查移动条件
            $requirements = $connection->checkRequirements($character);

            return response()->json([
                'success' => true,
                'data' => [
                    'cost' => $connection->getCostInfo(),
                    'can_move' => $requirements['can_move'],
                    'requirements' => $requirements['reasons']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取移动消耗失败', [
                'character_id' => $characterId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取移动消耗失败'
            ], 500);
        }
    }

    /**
     * 获取位置详情
     */
    public function getLocationDetails($locationId): JsonResponse
    {
        try {
            $location = Location::findOrFail($locationId);

            // 获取在此位置的角色数量
            $characterCount = CharacterLocation::where('location_id', $locationId)->count();

            // 获取连接的位置
            $connectedLocations = $location->connectedLocations()->get()->map(function ($connectedLocation) {
                return [
                    'id' => $connectedLocation->id,
                    'name' => $connectedLocation->name,
                    'type' => $connectedLocation->type,
                    'distance' => $connectedLocation->pivot->distance,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $location->id,
                    'name' => $location->name,
                    'type' => $location->type,
                    'description' => $location->description,
                    'coordinates' => [
                        'x' => $location->x,
                        'y' => $location->y
                    ],
                    'level_requirement' => $location->level_requirement,
                    'is_safe' => $location->is_safe,
                    'facilities' => $location->facilities ?? [],
                    'npcs' => $location->npcs ?? [],
                    'monsters' => $location->monsters ?? [],
                    'character_count' => $characterCount,
                    'connected_locations' => $connectedLocations
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取位置详情失败', [
                'location_id' => $locationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取位置详情失败'
            ], 500);
        }
    }

    /**
     * 获取位置的NPC和怪物
     */
    public function getLocationEntities(Request $request, $characterId, $locationId): JsonResponse
    {
        try {
            $character = Character::findOrFail($characterId);

            // 检查角色是否属于当前用户
            if ($character->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问此角色'
                ], 403);
            }

            $location = Location::findOrFail($locationId);

            return response()->json([
                'success' => true,
                'data' => [
                    'npcs' => $location->npcs ?? [],
                    'monsters' => $location->monsters ?? [],
                    'facilities' => $location->facilities ?? []
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取位置实体失败', [
                'character_id' => $characterId,
                'location_id' => $locationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取位置实体失败'
            ], 500);
        }
    }

    /**
     * 获取世界地图
     */
    public function getWorldMap(): JsonResponse
    {
        try {
            $locations = Location::with(['connectionsFrom.toLocation'])->get();

            $worldMap = [
                'locations' => $locations->map(function ($location) {
                    return [
                        'id' => $location->id,
                        'name' => $location->name,
                        'type' => $location->type,
                        'coordinates' => [
                            'x' => $location->x,
                            'y' => $location->y
                        ],
                        'level_requirement' => $location->level_requirement,
                        'is_safe' => $location->is_safe,
                    ];
                }),
                'connections' => LocationConnection::where('is_active', true)
                    ->get()
                    ->map(function ($connection) {
                        return [
                            'from' => $connection->from_location_id,
                            'to' => $connection->to_location_id,
                            'distance' => $connection->distance,
                            'cost' => $connection->getCostInfo()
                        ];
                    })
            ];

            return response()->json([
                'success' => true,
                'data' => $worldMap
            ]);

        } catch (\Exception $e) {
            Log::error('获取世界地图失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取世界地图失败'
            ], 500);
        }
    }
}
