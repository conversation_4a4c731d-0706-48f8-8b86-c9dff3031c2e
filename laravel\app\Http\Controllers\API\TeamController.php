<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Character;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\TeamInvite;
use App\Events\TeamEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class TeamController extends Controller
{
    // 组队系统控制器

    /**
     * 创建队伍
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function createTeam(Request $request, $characterId)
    {
        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 检查角色是否已经在队伍中
        if ($character->teamMember) {
            return response()->json(['message' => '已经在队伍中，无法创建新队伍'], 400);
        }

        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:30',
            'description' => 'nullable|string|max:200',
            'maxMembers' => 'required|integer|min:2|max:5',
            'minLevel' => 'nullable|integer|min:1',
            'maxLevel' => 'nullable|integer|min:1',
            'isRecruiting' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 创建队伍
        $team = new Team();
        $team->name = $request->name;
        $team->description = $request->description;
        $team->leader_id = $character->id;
        $team->max_members = $request->maxMembers;
        $team->min_level = $request->minLevel ?? 1;
        $team->max_level = $request->maxLevel;
        $team->is_recruiting = $request->isRecruiting ?? true;
        $team->save();

        // 添加队长作为队伍成员
        $teamMember = new TeamMember();
        $teamMember->team_id = $team->id;
        $teamMember->character_id = $character->id;
        $teamMember->role = 'leader';
        $teamMember->joined_at = now();
        $teamMember->save();

        // 广播组队事件
        event(new TeamEvent('team_created', $team->id, [
            'team' => $team,
            'leader' => $character->only(['id', 'name', 'level', 'class'])
        ]));

        return response()->json([
            'message' => '队伍创建成功',
            'team' => $this->getTeamDetails($team->id)
        ]);
    }

    /**
     * 获取队伍列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getTeamList(Request $request)
    {
        $query = Team::with(['leader:id,name,level,class,avatar'])
            ->where('is_recruiting', true)
            ->select(['id', 'name', 'description', 'leader_id', 'max_members', 'min_level', 'max_level', 'created_at']);

        // 搜索
        if ($request->has('searchText') && !empty($request->searchText)) {
            $query->where('name', 'like', '%' . $request->searchText . '%');
        }

        // 等级过滤
        if ($request->has('minLevel') && is_numeric($request->minLevel)) {
            $query->where('min_level', '>=', $request->minLevel);
        }

        if ($request->has('maxLevel') && is_numeric($request->maxLevel)) {
            $query->where('max_level', '<=', $request->maxLevel);
        }

        // 分页
        $perPage = $request->pageSize ?? 10;
        $teams = $query->withCount('members')->paginate($perPage);

        return response()->json($teams);
    }

    /**
     * 获取指定队伍的详情
     *
     * @param  int  $teamId
     * @return \Illuminate\Http\Response
     */
    public function getTeamDetail($teamId)
    {
        $team = $this->getTeamDetails($teamId);

        if (!$team) {
            return response()->json(['message' => '队伍不存在'], 404);
        }

        return response()->json($team);
    }

    /**
     * 获取当前队伍
     *
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function getCurrentTeam($characterId)
    {
        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 查找角色的队伍
        $teamMember = TeamMember::where('character_id', $characterId)->first();

        if (!$teamMember) {
            return response()->json(['message' => '未加入任何队伍'], 404);
        }

        $team = $this->getTeamDetails($teamMember->team_id);
        return response()->json($team);
    }

    /**
     * 加入队伍
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function joinTeam(Request $request, $characterId)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'teamId' => 'required|exists:teams,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 检查角色是否已经在队伍中
        if (TeamMember::where('character_id', $characterId)->exists()) {
            return response()->json(['message' => '已经在队伍中，请先离开当前队伍'], 400);
        }

        // 获取队伍信息
        $team = Team::find($request->teamId);

        // 检查队伍是否存在且正在招募
        if (!$team || !$team->is_recruiting) {
            return response()->json(['message' => '队伍不存在或未开放招募'], 404);
        }

        // 检查队伍是否已满
        $memberCount = TeamMember::where('team_id', $team->id)->count();
        if ($memberCount >= $team->max_members) {
            return response()->json(['message' => '队伍已满员'], 400);
        }

        // 检查等级限制
        if ($team->min_level && $character->level < $team->min_level) {
            return response()->json(['message' => '角色等级不足'], 400);
        }

        if ($team->max_level && $character->level > $team->max_level) {
            return response()->json(['message' => '角色等级超出限制'], 400);
        }

        // 添加为队伍成员
        $teamMember = new TeamMember();
        $teamMember->team_id = $team->id;
        $teamMember->character_id = $character->id;
        $teamMember->role = 'member';
        $teamMember->joined_at = now();
        $teamMember->save();

        // 广播组队事件
        event(new TeamEvent('member_joined', $team->id, [
            'team_id' => $team->id,
            'member' => $character->only(['id', 'name', 'level', 'class', 'avatar'])
        ]));

        return response()->json([
            'message' => '成功加入队伍',
            'team' => $this->getTeamDetails($team->id)
        ]);
    }

    /**
     * 离开队伍
     *
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function leaveTeam($characterId)
    {
        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 获取队伍成员信息
        $teamMember = TeamMember::where('character_id', $characterId)->first();

        if (!$teamMember) {
            return response()->json(['message' => '未加入任何队伍'], 404);
        }

        $teamId = $teamMember->team_id;
        $team = Team::find($teamId);

        // 如果是队长
        if ($team->leader_id == $characterId) {
            return response()->json(['message' => '队长无法直接离开队伍，请先将队长转让给其他成员或解散队伍'], 400);
        }

        // 移除队伍成员
        $teamMember->delete();

        // 广播组队事件
        event(new TeamEvent('member_left', $teamId, [
            'team_id' => $teamId,
            'member_id' => $characterId,
            'member_name' => $character->name
        ]));

        return response()->json(['message' => '已离开队伍']);
    }

    /**
     * 踢出队员
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function kickMember(Request $request, $characterId)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'memberId' => 'required|exists:characters,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 获取队伍成员信息
        $teamMember = TeamMember::where('character_id', $characterId)->first();

        if (!$teamMember) {
            return response()->json(['message' => '未加入任何队伍'], 404);
        }

        $team = Team::find($teamMember->team_id);

        // 检查是否为队长
        if ($team->leader_id != $characterId) {
            return response()->json(['message' => '只有队长可以踢出队员'], 403);
        }

        // 检查被踢出的成员是否在队伍中
        $memberToKick = TeamMember::where('team_id', $team->id)
            ->where('character_id', $request->memberId)
            ->first();

        if (!$memberToKick) {
            return response()->json(['message' => '该成员不在队伍中'], 404);
        }

        // 获取被踢出的角色信息
        $kickedCharacter = Character::find($request->memberId);

        // 移除队伍成员
        $memberToKick->delete();

        // 广播组队事件
        event(new TeamEvent('member_kicked', $team->id, [
            'team_id' => $team->id,
            'member_id' => $request->memberId,
            'member_name' => $kickedCharacter->name,
            'kicked_by' => $character->name
        ]));

        return response()->json([
            'message' => '已踢出队员',
            'team' => $this->getTeamDetails($team->id)
        ]);
    }

    /**
     * 解散队伍
     *
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function disbandTeam($characterId)
    {
        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 获取队伍信息
        $team = Team::where('leader_id', $characterId)->first();

        if (!$team) {
            return response()->json(['message' => '你不是队长，无法解散队伍'], 403);
        }

        $teamId = $team->id;
        $teamName = $team->name;

        // 移除所有队伍邀请
        TeamInvite::where('team_id', $teamId)->delete();

        // 移除所有队伍成员
        TeamMember::where('team_id', $teamId)->delete();

        // 删除队伍
        $team->delete();

        // 广播组队事件
        event(new TeamEvent('team_disbanded', $teamId, [
            'team_id' => $teamId,
            'team_name' => $teamName,
            'leader_name' => $character->name
        ]));

        return response()->json(['message' => '队伍已解散']);
    }

    /**
     * 邀请好友加入队伍
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function inviteToTeam(Request $request, $characterId)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'friendId' => 'required|exists:characters,id',
            'teamId' => 'required|exists:teams,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 检查是否为队长
        $team = Team::find($request->teamId);
        if ($team->leader_id != $characterId) {
            return response()->json(['message' => '只有队长可以发送邀请'], 403);
        }

        // 检查队伍是否已满
        $memberCount = TeamMember::where('team_id', $team->id)->count();
        if ($memberCount >= $team->max_members) {
            return response()->json(['message' => '队伍已满员'], 400);
        }

        // 检查被邀请者是否已在队伍中
        if (TeamMember::where('character_id', $request->friendId)->exists()) {
            return response()->json(['message' => '该角色已经在其他队伍中'], 400);
        }

        // 检查是否已有未处理的邀请
        $existingInvite = TeamInvite::where('team_id', $request->teamId)
            ->where('character_id', $request->friendId)
            ->where('status', 'pending')
            ->exists();

        if ($existingInvite) {
            return response()->json(['message' => '已经向该角色发送过邀请'], 400);
        }

        // 创建邀请
        $invite = new TeamInvite();
        $invite->team_id = $request->teamId;
        $invite->inviter_id = $characterId;
        $invite->character_id = $request->friendId;
        $invite->expires_at = now()->addHours(1); // 邀请1小时后过期
        $invite->status = 'pending';
        $invite->save();

        // 获取被邀请者角色信息
        $invitedCharacter = Character::find($request->friendId);

        // 广播组队事件
        event(new TeamEvent('invite_sent', $team->id, [
            'team_id' => $team->id,
            'invite_id' => $invite->id,
            'inviter' => [
                'id' => $character->id,
                'name' => $character->name
            ],
            'invited' => [
                'id' => $invitedCharacter->id,
                'name' => $invitedCharacter->name
            ]
        ]));

        return response()->json([
            'message' => '已发送邀请',
            'invite' => $invite
        ]);
    }

    /**
     * 获取队伍邀请列表
     *
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function getTeamInvites($characterId)
    {
        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 获取未过期且未处理的邀请
        $invites = TeamInvite::where('character_id', $characterId)
            ->where('status', 'pending')
            ->where('expires_at', '>', now())
            ->with(['team:id,name,leader_id', 'inviter:id,name'])
            ->get();

        return response()->json($invites);
    }

    /**
     * 回应队伍邀请
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $characterId
     * @return \Illuminate\Http\Response
     */
    public function respondToInvite(Request $request, $characterId)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'inviteId' => 'required|exists:team_invites,id',
            'accept' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()], 422);
        }

        // 验证角色所有权
        $character = Character::where('id', $characterId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$character) {
            return response()->json(['message' => '未找到角色或无权操作'], 403);
        }

        // 获取邀请信息
        $invite = TeamInvite::where('id', $request->inviteId)
            ->where('character_id', $characterId)
            ->where('status', 'pending')
            ->where('expires_at', '>', now())
            ->first();

        if (!$invite) {
            return response()->json(['message' => '邀请不存在或已过期'], 404);
        }

        // 获取队伍信息
        $team = Team::find($invite->team_id);
        if (!$team) {
            return response()->json(['message' => '队伍不存在'], 404);
        }

        // 更新邀请状态
        $invite->status = $request->accept ? 'accepted' : 'declined';
        $invite->save();

        if ($request->accept) {
            // 检查是否已经在队伍中
            if (TeamMember::where('character_id', $characterId)->exists()) {
                return response()->json(['message' => '已经在其他队伍中，无法加入新队伍'], 400);
            }

            // 检查队伍是否已满
            $memberCount = TeamMember::where('team_id', $team->id)->count();
            if ($memberCount >= $team->max_members) {
                return response()->json(['message' => '队伍已满员'], 400);
            }

            // 添加为队伍成员
            $teamMember = new TeamMember();
            $teamMember->team_id = $team->id;
            $teamMember->character_id = $characterId;
            $teamMember->role = 'member';
            $teamMember->joined_at = now();
            $teamMember->save();

            // 广播组队事件
            event(new TeamEvent('member_joined', $team->id, [
                'team_id' => $team->id,
                'member' => $character->only(['id', 'name', 'level', 'class', 'avatar']),
                'invite_id' => $invite->id
            ]));

            return response()->json([
                'message' => '已接受邀请并加入队伍',
                'team' => $this->getTeamDetails($team->id)
            ]);
        } else {
            // 广播拒绝事件
            event(new TeamEvent('invite_declined', $team->id, [
                'team_id' => $team->id,
                'character_id' => $characterId,
                'character_name' => $character->name,
                'invite_id' => $invite->id
            ]));

            return response()->json(['message' => '已拒绝邀请']);
        }
    }

    /**
     * 获取队伍详情
     *
     * @param  int  $teamId
     * @return array
     */
    private function getTeamDetails($teamId)
    {
        // 获取队伍信息
        $team = Team::with(['leader:id,name,level,class,avatar'])
            ->find($teamId);

        if (!$team) {
            return null;
        }

        // 获取队伍成员
        $members = TeamMember::where('team_id', $teamId)
            ->with(['character:id,name,level,class,avatar'])
            ->get()
            ->map(function ($member) use ($team) {
                return [
                    'id' => $member->character->id,
                    'name' => $member->character->name,
                    'level' => $member->character->level,
                    'class' => $member->character->class,
                    'avatar' => $member->character->avatar,
                    'role' => $member->role,
                    'isLeader' => $member->character->id == $team->leader_id,
                    'joined_at' => $member->joined_at
                ];
            });

        return [
            'id' => $team->id,
            'name' => $team->name,
            'description' => $team->description,
            'leader' => $team->leader->only(['id', 'name', 'level', 'class', 'avatar']),
            'max_members' => $team->max_members,
            'min_level' => $team->min_level,
            'max_level' => $team->max_level,
            'is_recruiting' => $team->is_recruiting,
            'members' => $members,
            'created_at' => $team->created_at
        ];
    }
}
