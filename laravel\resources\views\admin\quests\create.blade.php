@extends('admin.layouts.app')

@section('title', '创建任务')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">创建新任务</div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.quests.store') }}">
            @csrf

            <div class="layui-form-item">
                <label class="layui-form-label">任务名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{{ old('name') }}" required lay-verify="required" placeholder="请输入任务名称" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">任务描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入任务描述" class="layui-textarea">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">任务类型</label>
                <div class="layui-input-block">
                    <select name="type" lay-verify="required">
                        <option value="">请选择任务类型</option>
                        <option value="main" {{ old('type') == 'main' ? 'selected' : '' }}>主线任务</option>
                        <option value="side" {{ old('type') == 'side' ? 'selected' : '' }}>支线任务</option>
                        <option value="daily" {{ old('type') == 'daily' ? 'selected' : '' }}>日常任务</option>
                        <option value="weekly" {{ old('type') == 'weekly' ? 'selected' : '' }}>周常任务</option>
                        <option value="event" {{ old('type') == 'event' ? 'selected' : '' }}>活动任务</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级要求</label>
                <div class="layui-input-inline">
                    <input type="number" name="level_requirement" value="{{ old('level_requirement', 1) }}" required lay-verify="required|number" placeholder="请输入等级要求" autocomplete="off" class="layui-input" min="1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">前置任务</label>
                <div class="layui-input-block">
                    <select name="prerequisite_quest_id">
                        <option value="">无前置任务</option>
                        @foreach($quests as $quest)
                        <option value="{{ $quest->id }}" {{ old('prerequisite_quest_id') == $quest->id ? 'selected' : '' }}>{{ $quest->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">开始地图</label>
                <div class="layui-input-block">
                    <select name="start_map_id">
                        <option value="">请选择开始地图</option>
                        @foreach($maps as $map)
                        <option value="{{ $map->id }}" {{ old('start_map_id') == $map->id ? 'selected' : '' }}>{{ $map->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">结束地图</label>
                <div class="layui-input-block">
                    <select name="end_map_id">
                        <option value="">请选择结束地图</option>
                        @foreach($maps as $map)
                        <option value="{{ $map->id }}" {{ old('end_map_id') == $map->id ? 'selected' : '' }}>{{ $map->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">经验奖励</label>
                <div class="layui-input-inline">
                    <input type="number" name="exp_reward" value="{{ old('exp_reward', 0) }}" required lay-verify="required|number" placeholder="请输入经验奖励" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">银两奖励</label>
                <div class="layui-input-inline">
                    <input type="number" name="silver_reward" value="{{ old('silver_reward', 0) }}" required lay-verify="required|number" placeholder="请输入银两奖励" autocomplete="off" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">物品奖励</label>
                <div class="layui-input-block">
                    <select name="item_rewards[]" multiple>
                        <option value="">无物品奖励</option>
                        @foreach($items as $item)
                        <option value="{{ $item->id }}">{{ $item->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">技能奖励</label>
                <div class="layui-input-block">
                    <select name="skill_rewards[]" multiple>
                        <option value="">无技能奖励</option>
                        @foreach($skills as $skill)
                        <option value="{{ $skill->id }}">{{ $skill->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">任务状态</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="is_active" value="1" title="启用" checked>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
                    <a href="{{ route('admin.quests.index') }}" class="layui-btn layui-btn-primary">返回</a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 重新渲染表单
    form.render();
});
</script>
@endsection
