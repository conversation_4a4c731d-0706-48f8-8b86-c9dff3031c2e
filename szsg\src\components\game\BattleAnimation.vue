<template>
  <div class="battle-animations">
    <!-- 默认战斗场景 -->
    <div class="battle-scene">
      <div class="battle-ground">
        <div class="ground-line"></div>
        <div class="battle-text" v-if="!hasActiveAnimations">
          <p>战斗进行中...</p>
          <p class="battle-tip">点击攻击按钮开始战斗</p>
        </div>
      </div>
    </div>

    <!-- 动画效果 -->
    <div
      v-for="animation in animations"
      :key="animation.id"
      :class="['animation-element', animation.className]"
      :style="animation.style"
      @animationend="removeAnimation(animation.id)"
    >
      {{ animation.text }}
    </div>

    <!-- Lottie 动画 (用于复杂特效) -->
    <lottie-player
      v-if="showLottieEffect"
      :src="lottieAnimationUrl"
      background="transparent"
      speed="1"
      style="width: 300px; height: 300px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
      autoplay
      @complete="onLottieComplete"
    ></lottie-player>

    <!-- 粒子效果容器 -->
    <div id="particles-container" v-show="showParticles" class="particles-container"></div>
  </div>
</template>

<script>
export default {
  name: 'BattleAnimation',
  
  data() {
    return {
      animations: [],
      showLottieEffect: false,
      lottieAnimationUrl: '',
      showParticles: false,
      animationIdCounter: 0
    }
  },

  computed: {
    hasActiveAnimations() {
      return this.animations.length > 0 || this.showLottieEffect || this.showParticles
    }
  },
  
  methods: {
    // 播放攻击动画
    playAttackAnimation(type = 'bounce') {
      const animation = {
        id: this.generateAnimationId(),
        text: '攻击！',
        className: 'attack-text bounce-in',
        style: {
          animation: 'bounceIn 1s ease-out'
        },
        duration: 1000
      };
      this.animations.push(animation);

      // 自动移除动画
      setTimeout(() => {
        this.removeAnimation(animation.id);
      }, animation.duration);
    },

    // 播放伤害数字动画
    playDamageAnimation(damage, isCritical = false) {
      const animation = {
        id: this.generateAnimationId(),
        text: `-${damage}`,
        className: isCritical ? 'critical-damage fade-up' : 'normal-damage fade-up',
        style: {
          animation: isCritical ? 'criticalDamage 1.5s ease-out' : 'normalDamage 1.5s ease-out'
        },
        duration: 1500
      };
      this.animations.push(animation);

      // 自动移除动画
      setTimeout(() => {
        this.removeAnimation(animation.id);
      }, animation.duration);
    },

    // 播放治疗动画
    playHealAnimation(healing) {
      const animation = {
        id: this.generateAnimationId(),
        type: 'fadeInUp',
        text: `+${healing}`,
        className: 'heal-text',
        duration: 1500
      };
      this.animations.push(animation);
      
      // 自动移除动画
      setTimeout(() => {
        this.removeAnimation(animation.id);
      }, animation.duration);
    },

    // 播放技能特效 (使用Lottie)
    playSkillEffect(skillType) {
      const lottieUrls = {
        fire: 'https://assets2.lottiefiles.com/packages/lf20_XZ3pkn.json',
        ice: 'https://assets9.lottiefiles.com/packages/lf20_dmw9cg8h.json',
        lightning: 'https://assets4.lottiefiles.com/packages/lf20_tl52xr3o.json',
        heal: 'https://assets1.lottiefiles.com/packages/lf20_qp1spzqv.json'
      };
      
      this.lottieAnimationUrl = lottieUrls[skillType] || lottieUrls.fire;
      this.showLottieEffect = true;
    },

    // 播放粒子效果
    playParticleEffect(type = 'explosion') {
      this.showParticles = true;
      
      // 根据类型配置不同的粒子效果
      const configs = {
        explosion: {
          particles: {
            number: { value: 100 },
            color: { value: ["#ff0000", "#ff8800", "#ffff00"] },
            shape: { type: "circle" },
            size: { value: 4, random: true },
            move: {
              enable: true,
              speed: 8,
              direction: "none",
              out_mode: "out"
            }
          }
        },
        magic: {
          particles: {
            number: { value: 50 },
            color: { value: ["#0066ff", "#8800ff", "#ff00ff"] },
            shape: { type: "star" },
            size: { value: 3, random: true },
            move: {
              enable: true,
              speed: 4,
              direction: "top",
              out_mode: "out"
            }
          }
        }
      };

      if (window.particlesJS) {
        window.particlesJS('particles-container', configs[type] || configs.explosion);
        
        // 3秒后隐藏粒子效果
        setTimeout(() => {
          this.showParticles = false;
        }, 3000);
      }
    },

    // 播放Miss动画
    playMissAnimation() {
      const animation = {
        id: this.generateAnimationId(),
        type: 'fadeOutUp',
        text: 'MISS',
        className: 'miss-text',
        duration: 1000
      };
      this.animations.push(animation);
      
      setTimeout(() => {
        this.removeAnimation(animation.id);
      }, animation.duration);
    },

    // 播放状态效果动画
    playStatusAnimation(statusType, text) {
      const animationTypes = {
        buff: 'pulse',
        debuff: 'shake',
        poison: 'wobble',
        stun: 'flash'
      };
      
      const animation = {
        id: this.generateAnimationId(),
        type: animationTypes[statusType] || 'pulse',
        text: text,
        className: `status-${statusType}`,
        duration: 2000
      };
      this.animations.push(animation);
      
      setTimeout(() => {
        this.removeAnimation(animation.id);
      }, animation.duration);
    },

    // 生成动画ID
    generateAnimationId() {
      return ++this.animationIdCounter;
    },

    // 移除动画
    removeAnimation(id) {
      this.animations = this.animations.filter(anim => anim.id !== id);
    },

    // Lottie动画完成回调
    onLottieComplete() {
      this.showLottieEffect = false;
    },

    // 清除所有动画
    clearAllAnimations() {
      this.animations = [];
      this.showLottieEffect = false;
      this.showParticles = false;
    }
  },

  mounted() {
    // 检查动画库是否加载
    if (window.particlesJS) {
      // Particles.js 已加载
    }
  },

  beforeDestroy() {
    // 清理动画
    this.clearAllAnimations();
  }
}
</script>

<style scoped>
.battle-animations {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  background: linear-gradient(180deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);
  border: 2px solid #666;
  border-radius: 8px;
}

/* 战斗场景 */
.battle-scene {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.battle-ground {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ground-line {
  position: absolute;
  bottom: 20px;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #8B4513 50%, transparent 100%);
}

.battle-text {
  text-align: center;
  color: #e2e8f0;
}

.battle-text p {
  margin: 5px 0;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.battle-tip {
  font-size: 14px !important;
  color: #a0aec0 !important;
  font-weight: normal !important;
}

.animation-element {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  z-index: 10;
  pointer-events: none;
}

/* 攻击文字样式 */
.attack-text {
  color: #fff;
  font-size: 20px;
}

/* 伤害数字样式 */
.normal-damage {
  color: #ff4444;
  font-size: 28px;
}

.critical-damage {
  color: #ff0000;
  font-size: 32px;
  text-shadow: 0 0 10px #ff0000;
}

/* 治疗数字样式 */
.heal-text {
  color: #44ff44;
  font-size: 24px;
}

/* Miss文字样式 */
.miss-text {
  color: #888;
  font-size: 20px;
  font-style: italic;
}

/* 状态效果样式 */
.status-buff {
  color: #00ff00;
  font-size: 18px;
}

.status-debuff {
  color: #ff8800;
  font-size: 18px;
}

.status-poison {
  color: #8800ff;
  font-size: 18px;
}

.status-stun {
  color: #ffff00;
  font-size: 18px;
}

/* 粒子效果容器 */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
}

/* Animate.css 自定义动画时长 */
.animate__animated {
  animation-duration: 1s;
}

.animate__bounceInDown {
  animation-duration: 1.5s;
}

.animate__fadeInUp {
  animation-duration: 1.2s;
}

.animate__fadeOutUp {
  animation-duration: 1s;
}

.animate__pulse {
  animation-duration: 2s;
}

.animate__shake {
  animation-duration: 1s;
}

.animate__wobble {
  animation-duration: 1s;
}

.animate__flash {
  animation-duration: 1s;
}

/* CSS 动画关键帧 */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.3);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
  70% {
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes normalDamage {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(20px);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(0px);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(-30px);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(-50px);
  }
}

@keyframes criticalDamage {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) translateY(20px);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2) translateY(0px);
  }
  40% {
    transform: translate(-50%, -50%) scale(1) translateY(-10px);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) translateY(-40px);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) translateY(-60px);
  }
}
</style>
