{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750347661761}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GameChat.vue"], "names": [], "mappings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file": "GameChat.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n    <div class=\"chat-container\" :class=\"{ 'minimized': minimized, 'compact': compactMode }\">\r\n        <div class=\"chat-header\" @click=\"toggleMinimize\">\r\n            <div class=\"chat-title-section\">\r\n                <span class=\"chat-title\">聊天</span>\r\n                <div class=\"chat-status\" :class=\"{ 'connected': isConnected }\" :title=\"isConnected ? '已连接' : '未连接'\"></div>\r\n            </div>\r\n            <div class=\"chat-controls\">\r\n                <button v-if=\"!minimized\" class=\"control-btn\" @click.stop=\"clearMessages\" title=\"清空消息\">\r\n                    <span class=\"icon\">🗑</span>\r\n                </button>\r\n                <button v-if=\"!minimized\" class=\"control-btn\" @click.stop=\"toggleCompactMode\" title=\"紧凑模式\">\r\n                    <span class=\"icon\">{{ compactMode ? '📖' : '📄' }}</span>\r\n                </button>\r\n                <button class=\"control-btn minimize-btn\" :title=\"minimized ? '展开聊天' : '收起聊天'\">\r\n                    <span class=\"icon\">{{ minimized ? '⬆' : '⬇' }}</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n\r\n        <div v-if=\"!minimized\" class=\"chat-body\">\r\n            <div class=\"chat-channels\">\r\n                <button\r\n                    v-for=\"(channel, index) in channels\"\r\n                    :key=\"index\"\r\n                    class=\"channel-tab\"\r\n                    :class=\"{ active: currentChannelIndex === index }\"\r\n                    @click=\"switchChannel(index)\"\r\n                >\r\n                    <span class=\"channel-name\">{{ channel.name }}</span>\r\n                    <span v-if=\"channel.unread > 0\" class=\"channel-badge\">{{ channel.unread > 99 ? '99+' : channel.unread }}</span>\r\n                </button>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"chatMessagesContainer\">\r\n                <template v-if=\"filteredMessages.length > 0\">\r\n                    <div\r\n                        v-for=\"(msg, index) in filteredMessages\"\r\n                        :key=\"index\"\r\n                        class=\"chat-message\"\r\n                        :class=\"{\r\n                            'system-message': msg.type === 'system',\r\n                            'npc-message': msg.type === 'npc',\r\n                            'player-message': msg.type === 'player',\r\n                            'self-message': msg.isSelf,\r\n                            'compact': compactMode\r\n                        }\"\r\n                    >\r\n                        <div v-if=\"msg.type !== 'system'\" class=\"message-header\">\r\n                            <span class=\"message-sender\" @click=\"handleSenderClick(msg)\">{{ msg.sender }}</span>\r\n                            <span v-if=\"msg.timestamp && !compactMode\" class=\"message-time\">{{ formatTime(msg.timestamp) }}</span>\r\n                        </div>\r\n                        <div class=\"message-content\">{{ msg.content }}</div>\r\n                        <div v-if=\"msg.timestamp && compactMode\" class=\"message-time-compact\">{{ formatTime(msg.timestamp) }}</div>\r\n                    </div>\r\n                </template>\r\n                <div v-else class=\"empty-messages\">\r\n                    <div class=\"empty-icon\">💬</div>\r\n                    <div class=\"empty-text\">暂无消息</div>\r\n                    <div class=\"empty-hint\">在下方输入框发送消息开始聊天</div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input-container\">\r\n                <div class=\"input-wrapper\">\r\n                    <input\r\n                        class=\"chat-input\"\r\n                        v-model=\"newMessage\"\r\n                        :placeholder=\"getInputPlaceholder()\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        @focus=\"onInputFocus\"\r\n                        @blur=\"onInputBlur\"\r\n                        :disabled=\"!isConnected\"\r\n                        maxlength=\"200\"\r\n                    />\r\n                    <div class=\"input-counter\" v-if=\"newMessage.length > 150\">{{ newMessage.length }}/200</div>\r\n                </div>\r\n                <button\r\n                    class=\"send-btn\"\r\n                    @click=\"sendMessage\"\r\n                    :disabled=\"!isConnected || !newMessage.trim()\"\r\n                    :title=\"!isConnected ? '未连接到聊天服务器' : '发送消息'\"\r\n                >\r\n                    <span class=\"send-icon\">📤</span>\r\n                    <span class=\"send-text\">发送</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport socketManager from '../utils/socketManager';\r\n\r\n// 聊天频道配置\r\nconst CHANNELS = [\r\n    { id: 'world', name: '世界', unread: 0 },\r\n    { id: 'trade', name: '交易', unread: 0 },\r\n    { id: 'team', name: '队伍', unread: 0 },\r\n    { id: 'private', name: '私聊', unread: 0 }\r\n];\r\n\r\nexport default {\r\n    name: 'GameChat',\r\n    props: {\r\n        characterInfo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        autoConnect: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        initialMinimized: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            isConnected: false,\r\n            channels: [...CHANNELS],\r\n            currentChannelIndex: 0,\r\n            messages: [],\r\n            newMessage: '',\r\n            minimized: this.initialMinimized,\r\n            reconnecting: false,\r\n            compactMode: false,\r\n            inputFocused: false,\r\n            lastMessageTime: null,\r\n            messageHistory: [], // 消息历史记录，用于上下键切换\r\n            historyIndex: -1\r\n        };\r\n    },\r\n    computed: {\r\n        currentChannel() {\r\n            return this.channels[this.currentChannelIndex]?.id || 'world';\r\n        },\r\n        filteredMessages() {\r\n            // 如果是私聊频道，显示所有私聊消息\r\n            if (this.currentChannel === 'private') {\r\n                return this.messages.filter(msg => msg.channel === 'private');\r\n            }\r\n            \r\n            // 如果是队伍频道，根据characterInfo中的teamId过滤队伍消息\r\n            if (this.currentChannel === 'team' && this.characterInfo.teamId) {\r\n                return this.messages.filter(msg => \r\n                    msg.channel === 'team' && \r\n                    msg.teamId === this.characterInfo.teamId\r\n                );\r\n            }\r\n            \r\n            // 否则只显示当前频道的消息\r\n            return this.messages.filter(msg => msg.channel === this.currentChannel);\r\n        }\r\n    },\r\n    watch: {\r\n        characterInfo: {\r\n            handler(newInfo) {\r\n                if (newInfo && newInfo.id && this.autoConnect) {\r\n                    this.connectChat();\r\n                }\r\n            },\r\n            immediate: true\r\n        },\r\n        currentChannelIndex() {\r\n            // 切换频道时，重置未读消息数\r\n            if (this.channels[this.currentChannelIndex]) {\r\n                this.channels[this.currentChannelIndex].unread = 0;\r\n            }\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        }\r\n    },\r\n    mounted() {\r\n        // 当前组件已挂载\r\n        if (this.characterInfo?.id && this.autoConnect) {\r\n            this.connectChat();\r\n        }\r\n    },\r\n    beforeUnmount() {\r\n        // 组件销毁前，移除所有事件监听器\r\n        this.disconnectChat();\r\n    },\r\n    methods: {\r\n        async connectChat() {\r\n            if (!this.characterInfo?.id) {\r\n                console.error('[GameChat] 无法连接聊天，缺少角色信息');\r\n                return;\r\n            }\r\n            \r\n            try {\r\n                // 初始化Socket连接\r\n                await socketManager.init();\r\n                \r\n                // 添加事件监听\r\n                this.setupEventListeners();\r\n                \r\n                // 加入相关频道\r\n                this.joinChannels();\r\n                \r\n                // 设置连接状态\r\n                this.isConnected = true;\r\n                this.reconnecting = false;\r\n                \r\n                // 添加系统消息\r\n                this.addSystemMessage('已连接到聊天服务器');\r\n                \r\n            } catch (error) {\r\n                console.error('[GameChat] 连接失败:', error);\r\n                \r\n                this.isConnected = false;\r\n                if (!this.reconnecting) {\r\n                    this.reconnecting = true;\r\n                    this.addSystemMessage('连接失败，正在尝试重新连接...');\r\n                }\r\n            }\r\n        },\r\n        \r\n        disconnectChat() {\r\n            // 移除事件监听\r\n            if (this.unsubscribers) {\r\n                this.unsubscribers.forEach(unsubscribe => unsubscribe());\r\n                this.unsubscribers = [];\r\n            }\r\n            \r\n            // 断开socket连接\r\n            socketManager.disconnect();\r\n            \r\n            // 更新状态\r\n            this.isConnected = false;\r\n        },\r\n        \r\n        setupEventListeners() {\r\n            // 存储所有取消订阅的函数\r\n            this.unsubscribers = [];\r\n            \r\n            // 监听连接事件\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('connect', () => {\r\n                    this.isConnected = true;\r\n                    this.reconnecting = false;\r\n                })\r\n            );\r\n            \r\n            // 监听断开连接事件\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('disconnect', (reason) => {\r\n                    this.isConnected = false;\r\n                    this.addSystemMessage(`连接已断开 (${reason})`);\r\n                })\r\n            );\r\n            \r\n            // 世界消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('world_message', (data) => {\r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'world',\r\n                        sender: data.sender.name,\r\n                        content: data.message,\r\n                        timestamp: data.timestamp,\r\n                        sender_id: data.sender.id,\r\n                        isSelf: data.sender.id === this.characterInfo?.id\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 队伍消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('team_message', (data) => {\r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'team',\r\n                        teamId: data.team_id,\r\n                        sender: data.message.sender.name,\r\n                        content: data.message.message,\r\n                        timestamp: data.message.timestamp,\r\n                        sender_id: data.message.sender.id,\r\n                        isSelf: data.message.sender.id === this.characterInfo?.id\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 私聊消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('private_message', (data) => {\r\n                    const isSelf = data.sender_id === this.characterInfo?.id;\r\n                    const sender = isSelf ? this.characterInfo?.name : data.message.sender.name;\r\n                    const receiverId = isSelf ? data.receiver_id : data.sender_id;\r\n                    \r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'private',\r\n                        sender,\r\n                        content: data.message.message,\r\n                        timestamp: data.message.timestamp,\r\n                        sender_id: data.message.sender.id,\r\n                        receiver_id: receiverId,\r\n                        isSelf\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 系统消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('system_message', (data) => {\r\n                    this.addSystemMessage(data.message || '系统通知');\r\n                })\r\n            );\r\n        },\r\n        \r\n        joinChannels() {\r\n            // 加入世界频道\r\n            socketManager.joinChannel('chat.world');\r\n            \r\n            // 如果角色有队伍，加入队伍频道\r\n            if (this.characterInfo.teamId) {\r\n                socketManager.joinChannel(`team.${this.characterInfo.teamId}`);\r\n            }\r\n            \r\n            // 加入角色私聊频道\r\n            socketManager.joinChannel(`chat.user.${this.characterInfo.id}`);\r\n        },\r\n        \r\n        handleChatMessage(messageData) {\r\n            // 添加到消息列表\r\n            this.messages.push(messageData);\r\n            \r\n            // 控制消息列表大小，防止过长\r\n            if (this.messages.length > 200) {\r\n                this.messages = this.messages.slice(-150);\r\n            }\r\n            \r\n            // 如果消息不是当前频道，更新未读计数\r\n            if (messageData.channel !== this.currentChannel) {\r\n                const channelIndex = this.channels.findIndex(c => c.id === messageData.channel);\r\n                if (channelIndex !== -1) {\r\n                    this.channels[channelIndex].unread++;\r\n                }\r\n            }\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n        \r\n        addSystemMessage(content, timestamp = null) {\r\n            this.messages.push({\r\n                type: 'system',\r\n                channel: 'system',\r\n                sender: '系统',\r\n                content,\r\n                timestamp: timestamp || Date.now()\r\n            });\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n        \r\n        switchChannel(index) {\r\n            this.currentChannelIndex = index;\r\n        },\r\n        \r\n        sendMessage() {\r\n            if (!this.isConnected) {\r\n                this.addSystemMessage('未连接到聊天服务器，无法发送消息');\r\n                return;\r\n            }\r\n            \r\n            const message = this.newMessage.trim();\r\n            if (!message) return;\r\n            \r\n            // 获取当前频道\r\n            const channel = this.currentChannel;\r\n            \r\n            // 构建消息数据\r\n            let messageOptions = {\r\n                channel: channel,\r\n                message: message,\r\n                character_id: this.characterInfo.id\r\n            };\r\n            \r\n            switch (channel) {\r\n                case 'team':\r\n                    if (!this.characterInfo.teamId) {\r\n                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');\r\n                        return;\r\n                    }\r\n                    messageOptions.team_id = this.characterInfo.teamId;\r\n                    break;\r\n                    \r\n                case 'private':\r\n                    // 私聊需要指定接收者\r\n                    // TODO: 添加私聊目标选择功能\r\n                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');\r\n                    return;\r\n            }\r\n            \r\n            // 使用socketManager的sendChatMessage方法\r\n            socketManager.sendChatMessage(messageOptions);\r\n            \r\n            // 清空输入框\r\n            this.newMessage = '';\r\n        },\r\n        \r\n        handleSenderClick(msg) {\r\n            // 点击发送者名称\r\n            if (msg.type === 'player' && !msg.isSelf) {\r\n                this.$emit('player-click', {\r\n                    id: msg.sender_id,\r\n                    name: msg.sender\r\n                });\r\n            }\r\n        },\r\n        \r\n        scrollToBottom() {\r\n            const container = this.$refs.chatMessagesContainer;\r\n            if (container) {\r\n                // 注意: uni-app环境中可能需要特殊处理\r\n                if (container.$el) {\r\n                    container.$el.scrollTop = container.$el.scrollHeight;\r\n                } else if (container.scrollTop !== undefined) {\r\n                    container.scrollTop = container.scrollHeight;\r\n                }\r\n            }\r\n        },\r\n        \r\n        toggleMinimize() {\r\n            this.minimized = !this.minimized;\r\n            \r\n            if (!this.minimized) {\r\n                // 展开时，滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n            }\r\n        },\r\n        \r\n        formatTime(timestamp) {\r\n            if (!timestamp) return '';\r\n\r\n            const date = new Date(timestamp);\r\n            const now = new Date();\r\n            const hours = date.getHours().toString().padStart(2, '0');\r\n            const minutes = date.getMinutes().toString().padStart(2, '0');\r\n\r\n            // 如果是今天，只显示时间\r\n            if (date.toDateString() === now.toDateString()) {\r\n                return `${hours}:${minutes}`;\r\n            }\r\n\r\n            // 如果不是今天，显示月日和时间\r\n            const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n            const day = date.getDate().toString().padStart(2, '0');\r\n            return `${month}-${day} ${hours}:${minutes}`;\r\n        },\r\n\r\n        // 新增方法\r\n        toggleCompactMode() {\r\n            this.compactMode = !this.compactMode;\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n\r\n        clearMessages() {\r\n            this.messages = [];\r\n            this.addSystemMessage('消息已清空');\r\n        },\r\n\r\n        getInputPlaceholder() {\r\n            const channel = this.currentChannel;\r\n            const placeholders = {\r\n                'world': '对所有人说...',\r\n                'trade': '发布交易信息...',\r\n                'team': '对队友说...',\r\n                'private': '私聊消息...'\r\n            };\r\n            return placeholders[channel] || '输入消息...';\r\n        },\r\n\r\n        onInputFocus() {\r\n            this.inputFocused = true;\r\n        },\r\n\r\n        onInputBlur() {\r\n            this.inputFocused = false;\r\n        },\r\n\r\n        // 处理消息历史记录\r\n        addToHistory(message) {\r\n            if (message && message.trim()) {\r\n                // 移除重复的消息\r\n                const index = this.messageHistory.indexOf(message);\r\n                if (index > -1) {\r\n                    this.messageHistory.splice(index, 1);\r\n                }\r\n\r\n                // 添加到历史记录开头\r\n                this.messageHistory.unshift(message);\r\n\r\n                // 限制历史记录数量\r\n                if (this.messageHistory.length > 20) {\r\n                    this.messageHistory = this.messageHistory.slice(0, 20);\r\n                }\r\n            }\r\n            this.historyIndex = -1;\r\n        },\r\n\r\n        // 增强的消息发送\r\n        sendMessage() {\r\n            if (!this.isConnected) {\r\n                this.addSystemMessage('未连接到聊天服务器，无法发送消息');\r\n                return;\r\n            }\r\n\r\n            const message = this.newMessage.trim();\r\n            if (!message) return;\r\n\r\n            // 检查消息长度\r\n            if (message.length > 200) {\r\n                this.addSystemMessage('消息长度不能超过200个字符');\r\n                return;\r\n            }\r\n\r\n            // 防止刷屏（限制发送频率）\r\n            const now = Date.now();\r\n            if (this.lastMessageTime && (now - this.lastMessageTime) < 1000) {\r\n                this.addSystemMessage('发送消息过于频繁，请稍后再试');\r\n                return;\r\n            }\r\n            this.lastMessageTime = now;\r\n\r\n            // 添加到历史记录\r\n            this.addToHistory(message);\r\n\r\n            // 获取当前频道\r\n            const channel = this.currentChannel;\r\n\r\n            // 构建消息数据\r\n            let messageOptions = {\r\n                channel: channel,\r\n                message: message,\r\n                character_id: this.characterInfo.id\r\n            };\r\n\r\n            switch (channel) {\r\n                case 'team':\r\n                    if (!this.characterInfo.teamId) {\r\n                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');\r\n                        return;\r\n                    }\r\n                    messageOptions.team_id = this.characterInfo.teamId;\r\n                    break;\r\n\r\n                case 'private':\r\n                    // 私聊需要指定接收者\r\n                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');\r\n                    return;\r\n            }\r\n\r\n            // 使用socketManager的sendChatMessage方法\r\n            socketManager.sendChatMessage(messageOptions);\r\n\r\n            // 清空输入框\r\n            this.newMessage = '';\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.chat-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(20,20,30,0.9) 100%);\r\n    backdrop-filter: blur(15px);\r\n    border-radius: 12px 12px 0 0;\r\n    overflow: hidden;\r\n    height: 420px;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: 1px solid rgba(255,255,255,0.1);\r\n    box-shadow: 0 8px 32px rgba(0,0,0,0.3);\r\n}\r\n\r\n.chat-container.minimized {\r\n    height: 48px;\r\n}\r\n\r\n.chat-container.compact {\r\n    height: 320px;\r\n}\r\n\r\n.chat-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 8px 12px;\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(30,30,40,0.8) 100%);\r\n    border-bottom: 1px solid rgba(255,255,255,0.15);\r\n    height: 48px;\r\n    box-sizing: border-box;\r\n    cursor: pointer;\r\n    user-select: none;\r\n}\r\n\r\n.chat-header:hover {\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(30,30,40,0.9) 100%);\r\n}\r\n\r\n.chat-title-section {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.chat-title {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    font-weight: 600;\r\n    text-shadow: 0 1px 2px rgba(0,0,0,0.5);\r\n}\r\n\r\n.chat-controls {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.control-btn {\r\n    background: rgba(255,255,255,0.1);\r\n    border: none;\r\n    border-radius: 6px;\r\n    width: 28px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    color: #ccc;\r\n}\r\n\r\n.control-btn:hover {\r\n    background: rgba(255,255,255,0.2);\r\n    color: #fff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.control-btn .icon {\r\n    font-size: 12px;\r\n}\r\n\r\n.chat-status {\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #ff4444;\r\n    border-radius: 50%;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 0 8px rgba(255,68,68,0.5);\r\n}\r\n\r\n.chat-status.connected {\r\n    background-color: #4CAF50;\r\n    box-shadow: 0 0 8px rgba(76,175,80,0.5);\r\n}\r\n\r\n.chat-body {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n}\r\n\r\n.chat-channels {\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.3) 0%, rgba(20,20,30,0.4) 100%);\r\n    border-bottom: 1px solid rgba(255,255,255,0.1);\r\n    padding: 4px;\r\n    gap: 2px;\r\n}\r\n\r\n.channel-tab {\r\n    background: none;\r\n    border: none;\r\n    padding: 8px 16px;\r\n    font-size: 12px;\r\n    color: #aaa;\r\n    border-radius: 6px;\r\n    position: relative;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    min-width: 0;\r\n}\r\n\r\n.channel-tab:hover {\r\n    background: rgba(255,255,255,0.1);\r\n    color: #ddd;\r\n}\r\n\r\n.channel-tab.active {\r\n    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);\r\n    color: #fff;\r\n    box-shadow: 0 2px 8px rgba(232,81,28,0.3);\r\n}\r\n\r\n.channel-name {\r\n    white-space: nowrap;\r\n}\r\n\r\n.channel-badge {\r\n    background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);\r\n    color: white;\r\n    border-radius: 12px;\r\n    padding: 2px 6px;\r\n    font-size: 10px;\r\n    font-weight: 600;\r\n    min-width: 16px;\r\n    height: 16px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    line-height: 1;\r\n    box-shadow: 0 2px 4px rgba(255,68,68,0.3);\r\n}\r\n\r\n.chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 8px 12px;\r\n    background: rgba(0,0,0,0.1);\r\n    scrollbar-width: thin;\r\n    scrollbar-color: rgba(255,255,255,0.3) transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 4px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(255,255,255,0.3);\r\n    border-radius: 2px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(255,255,255,0.5);\r\n}\r\n\r\n.chat-message {\r\n    margin-bottom: 8px;\r\n    padding: 6px 8px;\r\n    border-radius: 8px;\r\n    background: rgba(255,255,255,0.02);\r\n    transition: all 0.2s ease;\r\n    border-left: 3px solid transparent;\r\n}\r\n\r\n.chat-message:hover {\r\n    background: rgba(255,255,255,0.05);\r\n}\r\n\r\n.chat-message.compact {\r\n    margin-bottom: 4px;\r\n    padding: 4px 6px;\r\n}\r\n\r\n.message-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 2px;\r\n}\r\n\r\n.message-sender {\r\n    font-size: 11px;\r\n    color: #4fc3f7;\r\n    cursor: pointer;\r\n    font-weight: 600;\r\n    transition: color 0.2s ease;\r\n}\r\n\r\n.message-sender:hover {\r\n    color: #29b6f6;\r\n    text-decoration: underline;\r\n}\r\n\r\n.self-message .message-sender {\r\n    color: #90CAF9;\r\n}\r\n\r\n.npc-message .message-sender {\r\n    color: #a5d6a7;\r\n}\r\n\r\n.player-message {\r\n    border-left-color: #4fc3f7;\r\n}\r\n\r\n.self-message {\r\n    border-left-color: #90CAF9;\r\n    background: rgba(144,202,249,0.05);\r\n}\r\n\r\n.npc-message {\r\n    border-left-color: #a5d6a7;\r\n}\r\n\r\n.message-content {\r\n    font-size: 12px;\r\n    color: #f0f0f0;\r\n    line-height: 1.4;\r\n    word-break: break-word;\r\n    margin: 2px 0;\r\n}\r\n\r\n.message-time {\r\n    font-size: 10px;\r\n    color: #888;\r\n    opacity: 0.7;\r\n}\r\n\r\n.message-time-compact {\r\n    font-size: 9px;\r\n    color: #666;\r\n    text-align: right;\r\n    margin-top: 2px;\r\n}\r\n\r\n.system-message {\r\n    border-left-color: #90caf9;\r\n    background: rgba(144,202,249,0.08);\r\n    font-style: italic;\r\n}\r\n\r\n.system-message .message-content {\r\n    color: #90caf9;\r\n    font-size: 11px;\r\n}\r\n\r\n.empty-messages {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    opacity: 0.6;\r\n    color: #aaa;\r\n    text-align: center;\r\n    padding: 20px;\r\n}\r\n\r\n.empty-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n    opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.empty-hint {\r\n    font-size: 11px;\r\n    opacity: 0.7;\r\n}\r\n\r\n.chat-input-container {\r\n    display: flex;\r\n    padding: 8px 12px;\r\n    border-top: 1px solid rgba(255,255,255,0.15);\r\n    gap: 8px;\r\n    flex-shrink: 0;\r\n    background: rgba(0,0,0,0.2);\r\n}\r\n\r\n.input-wrapper {\r\n    flex: 1;\r\n    position: relative;\r\n}\r\n\r\n.chat-input {\r\n    width: 100%;\r\n    background: rgba(255,255,255,0.1);\r\n    border: 1px solid rgba(255,255,255,0.2);\r\n    border-radius: 8px;\r\n    padding: 8px 12px;\r\n    color: #fff;\r\n    font-size: 12px;\r\n    height: 36px;\r\n    box-sizing: border-box;\r\n    transition: all 0.2s ease;\r\n    outline: none;\r\n}\r\n\r\n.chat-input:focus {\r\n    background: rgba(255,255,255,0.15);\r\n    border-color: #e8511c;\r\n    box-shadow: 0 0 0 2px rgba(232,81,28,0.2);\r\n}\r\n\r\n.chat-input:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n}\r\n\r\n.chat-input::placeholder {\r\n    color: #aaa;\r\n}\r\n\r\n.input-counter {\r\n    position: absolute;\r\n    right: 8px;\r\n    bottom: -16px;\r\n    font-size: 9px;\r\n    color: #888;\r\n}\r\n\r\n.send-btn {\r\n    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 0 16px;\r\n    border-radius: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 4px;\r\n    height: 36px;\r\n    font-size: 11px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    min-width: 60px;\r\n}\r\n\r\n.send-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d64516 0%, #e55a2b 100%);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(232,81,28,0.3);\r\n}\r\n\r\n.send-btn:active:not(:disabled) {\r\n    transform: translateY(0);\r\n}\r\n\r\n.send-btn:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.send-icon {\r\n    font-size: 12px;\r\n}\r\n\r\n.send-text {\r\n    font-size: 11px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .chat-container {\r\n        height: 300px;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 240px;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 6px 10px;\r\n        height: 40px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 24px;\r\n        height: 24px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 6px 12px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .chat-messages {\r\n        padding: 6px 8px;\r\n    }\r\n\r\n    .chat-input-container {\r\n        padding: 6px 8px;\r\n    }\r\n\r\n    .send-btn {\r\n        min-width: 50px;\r\n        padding: 0 12px;\r\n    }\r\n}\r\n</style> "]}]}