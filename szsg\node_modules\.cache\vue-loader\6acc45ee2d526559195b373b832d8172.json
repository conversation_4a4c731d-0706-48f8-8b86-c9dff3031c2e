{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\RegionSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\RegionSelect.vue", "mtime": 1750328969952}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBHYW1lTGF5b3V0IGZyb20gJ0AvbGF5b3V0cy9HYW1lTGF5b3V0LnZ1ZScKaW1wb3J0IHsgbWFwU3RhdGUsIG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4JwppbXBvcnQgbG9nZ2VyIGZyb20gJ0AvdXRpbHMvbG9nZ2VyJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdSZWdpb25TZWxlY3QnLAogIGNvbXBvbmVudHM6IHsKICAgIEdhbWVMYXlvdXQKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzZWxlY3RlZFJlZ2lvbjogbnVsbCwKICAgICAgaXNMb2FkaW5nOiBmYWxzZSwKICAgICAgZXJyb3I6IG51bGwsCiAgICAgIGlzRW50ZXJQcmVzc2VkOiBmYWxzZSwKICAgICAgaXNCYWNrUHJlc3NlZDogZmFsc2UsCiAgICAgIHNob3dDbGVhbnVwQnV0dG9uOiBmYWxzZQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcFN0YXRlKCdnYW1lJywgWydyZWdpb25zJ10pCiAgfSwKICBhc3luYyBjcmVhdGVkKCkgewogICAgbG9nZ2VyLmRlYnVnKCdbUmVnaW9uU2VsZWN0XSDpobXpnaLliJ3lp4vljJYnKQogICAgYXdhaXQgdGhpcy5sb2FkUmVnaW9uTGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICAuLi5tYXBBY3Rpb25zKCdnYW1lJywgWydsb2FkUmVnaW9ucycsICdzZWxlY3RSZWdpb24nLCAnbG9hZFJlY29tbWVuZGVkUmVnaW9ucyddKSwKCiAgICBhc3luYyBsb2FkUmVnaW9uTGlzdCgpIHsKICAgICAgdGhpcy5pc0xvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMuZXJyb3IgPSBudWxsCgogICAgICB0cnkgewogICAgICAgIGF3YWl0IHRoaXMubG9hZFJlZ2lvbnMoKQogICAgICAgIGlmICh0aGlzLnJlZ2lvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICB0aGlzLmVycm9yID0gJ+aaguaXoOWPr+eUqOWkp+WMuicKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgbG9nZ2VyLmVycm9yKCdbUmVnaW9uU2VsZWN0XSDliqDovb3lpKfljLrlpLHotKU6JywgZXJyb3IpCiAgICAgICAgdGhpcy5lcnJvciA9IGVycm9yLm1lc3NhZ2UgfHwgJ+WKoOi9veWkp+WMuuWksei0pe+8jOivt+mHjeivlScKICAgICAgICB0aGlzLnNob3dDbGVhbnVwQnV0dG9uID0gZmFsc2UKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzTG9hZGluZyA9IGZhbHNlCiAgICAgIH0KICAgIH0sCgogICAgc2VsZWN0UmVnaW9uTG9jYWwocmVnaW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSZWdpb24gPSByZWdpb24KICAgICAgbG9nZ2VyLmRlYnVnKCdbUmVnaW9uU2VsZWN0XSDpgInmi6nlpKfljLo6JywgcmVnaW9uLm5hbWUpCiAgICB9LAoKICAgIGFzeW5jIGNvbmZpcm1TZWxlY3Rpb24oKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFJlZ2lvbikgewogICAgICAgIHRoaXMuc2hvd1RvYXN0KCfor7flhYjpgInmi6nkuIDkuKrlpKfljLonKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCB0aGlzLnNlbGVjdFJlZ2lvbih0aGlzLnNlbGVjdGVkUmVnaW9uKQogICAgICAgIGlmIChzdWNjZXNzKSB7CiAgICAgICAgICB0aGlzLnNob3dUb2FzdCgn5aSn5Yy66YCJ5oup5oiQ5YqfJykKICAgICAgICAgIC8vIOi3s+i9rOWIsOinkuiJsumAieaLqemhtemdogogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9zZXR1cC9jaGFyYWN0ZXItc2VsZWN0JykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5zaG93VG9hc3QoJ+mAieaLqeWkp+WMuuWksei0pe+8jOivt+mHjeivlScpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGxvZ2dlci5lcnJvcignW1JlZ2lvblNlbGVjdF0g56Gu6K6k6YCJ5oup5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuc2hvd1RvYXN0KCfpgInmi6nlpKfljLrlpLHotKXvvIzor7fph43or5UnKQogICAgICB9CiAgICB9LAoKICAgIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKQogICAgfSwKCiAgICBnb1RvQ2xlYW51cCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9kZWJ1Zy9zdG9yYWdlJykKICAgIH0sCgogICAgZ2V0U3RhdHVzVGV4dChpc0FjdGl2ZSkgewogICAgICByZXR1cm4gaXNBY3RpdmUgPyAn5q2j5bi4JyA6ICfnu7TmiqTkuK0nOwogICAgfSwKCiAgICBoYW5kbGVFbnRlck1vdXNlRG93bigpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSZWdpb24pIHsKICAgICAgICB0aGlzLmlzRW50ZXJQcmVzc2VkID0gdHJ1ZQogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZUVudGVyTW91c2VVcCgpIHsKICAgICAgdGhpcy5pc0VudGVyUHJlc3NlZCA9IGZhbHNlCiAgICB9LAoKICAgIGdldEVudGVyQnV0dG9uSW1hZ2UoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFJlZ2lvbikgewogICAgICAgIHJldHVybiAnL3N0YXRpYy9nYW1lL1VJL2Fubml1L2pyXzMucG5nJyAvLyDnpoHnlKjnirbmgIHkuZ/kvb/nlKjpu5jorqTlm77niYcKICAgICAgfQogICAgICByZXR1cm4gdGhpcy5pc0VudGVyUHJlc3NlZAogICAgICAgID8gJy9zdGF0aWMvZ2FtZS9VSS9hbm5pdS9qcl80LnBuZycKICAgICAgICA6ICcvc3RhdGljL2dhbWUvVUkvYW5uaXUvanJfMy5wbmcnCiAgICB9LAoKICAgIGhhbmRsZUJhY2tNb3VzZURvd24oKSB7CiAgICAgIHRoaXMuaXNCYWNrUHJlc3NlZCA9IHRydWUKICAgIH0sCgogICAgaGFuZGxlQmFja01vdXNlVXAoKSB7CiAgICAgIHRoaXMuaXNCYWNrUHJlc3NlZCA9IGZhbHNlCiAgICB9LAoKICAgIGdldEJhY2tCdXR0b25JbWFnZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuaXNCYWNrUHJlc3NlZAogICAgICAgID8gJy9zdGF0aWMvZ2FtZS9VSS9hbm5pdS9maHVpXy5wbmcnCiAgICAgICAgOiAnL3N0YXRpYy9nYW1lL1VJL2Fubml1L2ZodWlfMi5wbmcnCiAgICB9LAoKICAgIHNob3dUb2FzdChtZXNzYWdlKSB7CiAgICAgIC8vIOeugOWNleeahOaPkOekuuWunueOsAogICAgICBjb25zdCB0b2FzdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpCiAgICAgIHRvYXN0LnRleHRDb250ZW50ID0gbWVzc2FnZQogICAgICB0b2FzdC5zdHlsZS5jc3NUZXh0ID0gYAogICAgICAgIHBvc2l0aW9uOiBmaXhlZDsKICAgICAgICB0b3A6IDUwJTsKICAgICAgICBsZWZ0OiA1MCU7CiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7CiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjgpOwogICAgICAgIGNvbG9yOiB3aGl0ZTsKICAgICAgICBwYWRkaW5nOiAxMnB4IDIwcHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4OwogICAgICAgIHotaW5kZXg6IDEwMDAwOwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgYAogICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRvYXN0KQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKHRvYXN0KQogICAgICB9LCAyMDAwKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["RegionSelect.vue"], "names": [], "mappings": ";AA2FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RegionSelect.vue", "sourceRoot": "src/views/setup", "sourcesContent": ["<template>\n  <GameLayout\n    page-type=\"region-select\"\n    custom-title=\" \"\n  >\n    <div class=\"region-select-container\">\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-spinner\"></div>\n        <p class=\"loading-text\">正在加载大区列表...</p>\n      </div>\n\n      <!-- 错误提示 -->\n      <div v-if=\"error && !isLoading\" class=\"error-container\">\n        <div class=\"error-message\">\n          <i class=\"error-icon\">⚠️</i>\n          <p>{{ error }}</p>\n          <div class=\"error-actions\">\n            <button @click=\"loadRegionList\" class=\"retry-btn\">重试</button>\n            <button v-if=\"showCleanupButton\" @click=\"goToCleanup\" class=\"cleanup-btn\">清理存储</button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 大区列表 -->\n      <div v-if=\"!isLoading && !error\" class=\"regions-container\">\n\n        <div class=\"regions-list\">\n          <div\n            v-for=\"region in regions\"\n            :key=\"region.id\"\n            class=\"region-item\"\n            :class=\"{\n              'selected': selectedRegion?.id === region.id,\n              'maintenance': !region.isActive,\n              'busy': region.isPvp\n            }\"\n            @click=\"selectRegionLocal(region)\"\n          >\n            <div class=\"region-name-section\">\n              <h3 class=\"region-name\">{{ region.name }}</h3>\n              <div class=\"region-status\" :class=\"region.isActive ? 'online' : 'maintenance'\">\n                {{ region.isActive ? '正常' : '维护中' }}\n              </div>\n            </div>\n\n          </div>\n        </div>\n\n        <!-- 分页信息 -->\n        <div class=\"pagination-info\">\n          第1/1页\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <div\n            @click=\"goBack\"\n            @mousedown=\"handleBackMouseDown\"\n            @mouseup=\"handleBackMouseUp\"\n            @mouseleave=\"handleBackMouseUp\"\n            class=\"btn-back-image\"\n            :class=\"{ 'pressed': isBackPressed }\"\n          >\n            <img\n              :src=\"getBackButtonImage()\"\n              alt=\"返回\"\n              draggable=\"false\"\n            />\n          </div>\n          <div\n            @click=\"confirmSelection\"\n            @mousedown=\"handleEnterMouseDown\"\n            @mouseup=\"handleEnterMouseUp\"\n            @mouseleave=\"handleEnterMouseUp\"\n            class=\"btn-enter-image\"\n            :class=\"{ 'disabled': !selectedRegion, 'pressed': isEnterPressed }\"\n          >\n            <img\n              :src=\"getEnterButtonImage()\"\n              alt=\"进入游戏\"\n              draggable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapState, mapActions } from 'vuex'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'RegionSelect',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      selectedRegion: null,\n      isLoading: false,\n      error: null,\n      isEnterPressed: false,\n      isBackPressed: false,\n      showCleanupButton: false\n    }\n  },\n  computed: {\n    ...mapState('game', ['regions'])\n  },\n  async created() {\n    logger.debug('[RegionSelect] 页面初始化')\n    await this.loadRegionList()\n  },\n  methods: {\n    ...mapActions('game', ['loadRegions', 'selectRegion', 'loadRecommendedRegions']),\n\n    async loadRegionList() {\n      this.isLoading = true\n      this.error = null\n\n      try {\n        await this.loadRegions()\n        if (this.regions.length === 0) {\n          this.error = '暂无可用大区'\n        }\n      } catch (error) {\n        logger.error('[RegionSelect] 加载大区失败:', error)\n        this.error = error.message || '加载大区失败，请重试'\n        this.showCleanupButton = false\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    selectRegionLocal(region) {\n      this.selectedRegion = region\n      logger.debug('[RegionSelect] 选择大区:', region.name)\n    },\n\n    async confirmSelection() {\n      if (!this.selectedRegion) {\n        this.showToast('请先选择一个大区')\n        return\n      }\n\n      try {\n        const success = await this.selectRegion(this.selectedRegion)\n        if (success) {\n          this.showToast('大区选择成功')\n          // 跳转到角色选择页面\n          this.$router.push('/setup/character-select')\n        } else {\n          this.showToast('选择大区失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[RegionSelect] 确认选择失败:', error)\n        this.showToast('选择大区失败，请重试')\n      }\n    },\n\n    goBack() {\n      this.$router.go(-1)\n    },\n\n    goToCleanup() {\n      this.$router.push('/debug/storage')\n    },\n\n    getStatusText(isActive) {\n      return isActive ? '正常' : '维护中';\n    },\n\n    handleEnterMouseDown() {\n      if (this.selectedRegion) {\n        this.isEnterPressed = true\n      }\n    },\n\n    handleEnterMouseUp() {\n      this.isEnterPressed = false\n    },\n\n    getEnterButtonImage() {\n      if (!this.selectedRegion) {\n        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片\n      }\n      return this.isEnterPressed\n        ? '/static/game/UI/anniu/jr_4.png'\n        : '/static/game/UI/anniu/jr_3.png'\n    },\n\n    handleBackMouseDown() {\n      this.isBackPressed = true\n    },\n\n    handleBackMouseUp() {\n      this.isBackPressed = false\n    },\n\n    getBackButtonImage() {\n      return this.isBackPressed\n        ? '/static/game/UI/anniu/fhui_.png'\n        : '/static/game/UI/anniu/fhui_2.png'\n    },\n\n    showToast(message) {\n      // 简单的提示实现\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.region-select-container {\n  padding: 30px 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n\n.region-select-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);\n  pointer-events: none;\n  z-index: -1;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 50px;\n  position: relative;\n}\n\n.title-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n}\n\n.title-background {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n}\n\n.title-image {\n  max-width: 100%;\n  height: auto;\n  display: block;\n}\n\n.page-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  margin: 0;\n  font-style: italic;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #333;\n  border-top: 4px solid #d4af37;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  color: #ccc;\n  font-size: 16px;\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.error-message {\n  text-align: center;\n  color: #ff6b6b;\n  background: rgba(255, 107, 107, 0.1);\n  padding: 30px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.error-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  margin-top: 15px;\n}\n\n.retry-btn {\n  padding: 8px 16px;\n  background: #d4af37;\n  color: #000;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.retry-btn:hover {\n  background: #b8941f;\n}\n\n.cleanup-btn {\n  padding: 8px 16px;\n  background: #ff6b6b;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.cleanup-btn:hover {\n  background: #ff5252;\n}\n\n.regions-container {\n  flex: 1;\n}\n\n.regions-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.regions-title {\n  font-size: 28px;\n  color: #d4af37;\n  margin: 0 0 10px 0;\n  text-shadow: 0 0 8px rgba(212, 175, 55, 0.4);\n  font-weight: bold;\n}\n\n.regions-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  font-style: italic;\n}\n\n.regions-list {\n  max-width: 600px;\n  margin: 0 auto 30px auto;\n}\n\n.region-item {\n  position: relative;\n  height: 60px;\n  margin-bottom: 12px;\n  border: 3px solid #4a5568;\n  border-radius: 8px;\n  cursor: pointer;\n  overflow: hidden;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n}\n\n.region-item:hover {\n  border-color: #d4af37;\n}\n\n.region-item.selected {\n  border-color: #d4af37;\n  box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);\n}\n\n.region-item.maintenance {\n  opacity: 0.6;\n  border-color: #666;\n}\n\n.region-item.busy {\n  border-color: #ff9800;\n}\n\n.region-name-section {\n  position: relative;\n  z-index: 3;\n  padding: 0 16px;\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background: rgba(0, 0, 0, 0.8);\n  height: 100%;\n  border-radius: 5px;\n}\n\n.region-name {\n  font-size: 20px;\n  color: #d4af37;\n  margin: 0;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n}\n\n.region-status {\n  font-size: 12px;\n  padding: 3px 6px;\n  border-radius: 3px;\n  font-weight: bold;\n}\n\n.region-status.online {\n  background: #4caf50;\n  color: white;\n}\n\n.region-status.busy {\n  background: #ff9800;\n  color: white;\n}\n\n.region-status.maintenance {\n  background: #f44336;\n  color: white;\n}\n\n.region-status.offline {\n  background: #666;\n  color: white;\n}\n\n.pagination-info {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #d4af37;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 40px;\n  padding: 40px 0;\n  margin-top: 20px;\n}\n\n.btn-back-image {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-back-image img {\n  max-width: 100%;\n  height: auto;\n  display: block;\n  transition: all 0.1s ease;\n}\n\n.btn-back-image:hover img {\n  transform: scale(1.05);\n}\n\n.btn-back-image.pressed img {\n  transform: scale(0.95);\n}\n\n.btn-enter-image {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-enter-image img {\n  max-width: 100%;\n  height: auto;\n  display: block;\n  transition: all 0.1s ease;\n}\n\n.btn-enter-image:hover:not(.disabled) img {\n  transform: scale(1.05);\n}\n\n.btn-enter-image.pressed img {\n  transform: scale(0.95);\n}\n\n.btn-enter-image.disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n\n.btn-enter-image.disabled img {\n  filter: grayscale(100%);\n}\n\n.page-footer {\n  text-align: center;\n  margin-top: 40px;\n  padding: 20px 0;\n}\n\n.footer-text {\n  font-size: 14px;\n  color: #888;\n  font-style: italic;\n}\n</style>\n"]}]}