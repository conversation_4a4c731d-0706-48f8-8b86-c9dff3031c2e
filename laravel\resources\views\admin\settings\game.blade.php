@extends('admin.layouts.app')

@section('title', '游戏设置')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">游戏设置</div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" action="{{ route('admin.settings.game.save') }}" method="POST">
            @csrf
            <div class="layui-form-item">
                <label class="layui-form-label">初始银两</label>
                <div class="layui-input-block">
                    <input type="number" name="initial_silver" value="{{ $settings['initial_silver'] ?? 100 }}" min="0" class="layui-input" required>
                    <div class="layui-form-mid layui-word-aux">新角色创建时获得的初始银两</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">初始生命值</label>
                <div class="layui-input-block">
                    <input type="number" name="initial_health" value="{{ $settings['initial_health'] ?? 100 }}" min="1" class="layui-input" required>
                    <div class="layui-form-mid layui-word-aux">新角色创建时的初始生命值</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">经验倍率</label>
                <div class="layui-input-block">
                    <input type="number" name="experience_rate" value="{{ $settings['experience_rate'] ?? 1.0 }}" min="0.1" max="10" step="0.1" class="layui-input" required>
                    <div class="layui-form-mid layui-word-aux">经验获取倍率，1.0为正常速度，数值越大升级越快</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">掉落倍率</label>
                <div class="layui-input-block">
                    <input type="number" name="drop_rate" value="{{ $settings['drop_rate'] ?? 1.0 }}" min="0.1" max="10" step="0.1" class="layui-input" required>
                    <div class="layui-form-mid layui-word-aux">物品掉落倍率，1.0为正常概率，数值越大掉落概率越高</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn">保存设置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 表单事件
    form.on('submit(settingsForm)', function(data){
        return true;
    });
});
</script>
@endsection
