<?php

namespace App\Services;

use App\Models\Battle;
use App\Models\Character;
use App\Models\Monster;
use App\Models\CharacterStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BattleManager
{
    /**
     * 初始化战斗
     */
    public function initBattle(int $characterId, int $monsterId, string $locationId = null): Battle
    {
        try {
            DB::beginTransaction();

            $character = Character::findOrFail($characterId);
            $monster = Monster::findOrFail($monsterId);

            // 检查角色是否已在战斗中
            $existingBattle = Battle::where('character_id', $characterId)
                ->where('status', 'ongoing')
                ->first();

            if ($existingBattle) {
                throw new \Exception('角色已在战斗中');
            }

            // 创建战斗记录
            $battle = Battle::create([
                'character_id' => $characterId,
                'monster_id' => $monsterId,
                'location_id' => $locationId,
                'battle_type' => 'normal',
                'status' => 'ongoing',
                'rounds' => 0,
                'start_time' => now()
            ]);

            // 添加战斗开始日志
            $battle->addLog('battle_start', [
                'character' => [
                    'name' => $character->name,
                    'level' => $character->level,
                    'hp' => $character->characterStatus->hp ?? $character->characterStatus->max_hp,
                    'mp' => $character->characterStatus->mp ?? $character->characterStatus->max_mp
                ],
                'monster' => [
                    'name' => $monster->name,
                    'level' => $monster->level,
                    'hp' => $monster->max_health,
                    'mp' => $monster->max_mp
                ]
            ]);

            $battle->save();

            DB::commit();

            Log::info('战斗初始化成功', [
                'battle_id' => $battle->id,
                'character_id' => $characterId,
                'monster_id' => $monsterId
            ]);

            return $battle;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('战斗初始化失败', [
                'character_id' => $characterId,
                'monster_id' => $monsterId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 执行攻击
     */
    public function executeAttack(int $battleId, string $attackerType = 'character'): array
    {
        try {
            DB::beginTransaction();

            $battle = Battle::with(['character.characterStatus', 'monster'])->findOrFail($battleId);

            if (!$battle->isOngoing()) {
                throw new \Exception('战斗已结束');
            }

            $result = [];

            if ($attackerType === 'character') {
                $result = $this->characterAttack($battle);
            } else {
                $result = $this->monsterAttack($battle);
            }

            // 增加回合数
            if ($attackerType === 'character') {
                $battle->rounds += 1;
            }

            // 检查战斗是否结束
            $battleEndResult = $this->checkBattleEnd($battle);
            if ($battleEndResult) {
                $result['battle_end'] = $battleEndResult;
            }

            $battle->save();
            DB::commit();

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('执行攻击失败', [
                'battle_id' => $battleId,
                'attacker_type' => $attackerType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 角色攻击怪物
     */
    private function characterAttack(Battle $battle): array
    {
        $character = $battle->character;
        $monster = $battle->monster;
        $characterStatus = $character->characterStatus;

        // 计算伤害
        $damage = $this->calculateDamage($characterStatus, $monster);
        $isCritical = $this->isCriticalHit($characterStatus);

        if ($isCritical) {
            $damage = intval($damage * 1.5);
        }

        // 记录攻击日志
        $battle->addLog('character_attack', [
            'damage' => $damage,
            'is_critical' => $isCritical,
            'monster_hp_before' => $monster->getCurrentHealth(),
            'monster_hp_after' => max(0, $monster->getCurrentHealth() - $damage)
        ]);

        return [
            'type' => 'character_attack',
            'damage' => $damage,
            'is_critical' => $isCritical,
            'target_hp' => max(0, $monster->getCurrentHealth() - $damage)
        ];
    }

    /**
     * 怪物攻击角色
     */
    private function monsterAttack(Battle $battle): array
    {
        $character = $battle->character;
        $monster = $battle->monster;
        $characterStatus = $character->characterStatus;

        // 计算伤害
        $damage = $this->calculateDamage($monster, $characterStatus);
        $isCritical = $this->isCriticalHit($monster);

        if ($isCritical) {
            $damage = intval($damage * 1.5);
        }

        // 扣除角色生命值
        $newHp = max(0, $characterStatus->hp - $damage);
        $characterStatus->update(['hp' => $newHp]);

        // 记录攻击日志
        $battle->addLog('monster_attack', [
            'damage' => $damage,
            'is_critical' => $isCritical,
            'character_hp_before' => $characterStatus->hp + $damage,
            'character_hp_after' => $newHp
        ]);

        return [
            'type' => 'monster_attack',
            'damage' => $damage,
            'is_critical' => $isCritical,
            'target_hp' => $newHp
        ];
    }

    /**
     * 计算伤害
     */
    private function calculateDamage($attacker, $target): int
    {
        // 基础攻击力
        $baseAttack = $attacker->attack ?? 10;
        $strength = $attacker->strength ?? 10;
        
        // 基础伤害 = 攻击力 + 力量/2
        $baseDamage = $baseAttack + intval($strength / 2);
        
        // 随机波动 ±20%
        $variation = $baseDamage * 0.2;
        $finalDamage = $baseDamage + rand(-$variation, $variation);
        
        // 防御减免
        $defense = $target->defense ?? 5;
        $constitution = $target->constitution ?? 10;
        $defenseValue = $defense + intval($constitution / 3);
        
        $reduction = intval($finalDamage * ($defenseValue / ($defenseValue + 100)));
        $finalDamage = max(1, $finalDamage - $reduction);
        
        return intval($finalDamage);
    }

    /**
     * 判断是否暴击
     */
    private function isCriticalHit($attacker): bool
    {
        $agility = $attacker->agility ?? 10;
        $criticalChance = min(50, $agility / 2); // 最大50%暴击率
        return rand(1, 100) <= $criticalChance;
    }

    /**
     * 检查战斗结束条件
     */
    private function checkBattleEnd(Battle $battle): ?array
    {
        $character = $battle->character;
        $monster = $battle->monster;
        $characterStatus = $character->characterStatus;

        // 角色死亡
        if ($characterStatus->hp <= 0) {
            $this->endBattle($battle, 'defeat');
            return [
                'result' => 'defeat',
                'message' => '战斗失败！'
            ];
        }

        // 怪物死亡（这里需要实际的怪物HP跟踪，暂时用简化逻辑）
        // 实际应用中需要在战斗过程中跟踪怪物当前HP
        $monsterCurrentHp = $this->getMonsterCurrentHp($battle);
        if ($monsterCurrentHp <= 0) {
            $rewards = $this->distributeRewards($battle);
            $this->endBattle($battle, 'victory', $rewards['exp'], $rewards['gold'], $rewards['items']);
            return [
                'result' => 'victory',
                'message' => '战斗胜利！',
                'rewards' => $rewards
            ];
        }

        return null;
    }

    /**
     * 获取怪物当前HP（从战斗日志计算）
     */
    private function getMonsterCurrentHp(Battle $battle): int
    {
        $monster = $battle->monster;
        $currentHp = $monster->max_health;

        $logs = $battle->battle_log ?? [];
        foreach ($logs as $log) {
            if ($log['action'] === 'character_attack') {
                $currentHp -= $log['data']['damage'] ?? 0;
            }
        }

        return max(0, $currentHp);
    }

    /**
     * 分配奖励
     */
    private function distributeRewards(Battle $battle): array
    {
        $character = $battle->character;
        $monster = $battle->monster;

        $expReward = $monster->getExpReward($character->level);
        $goldReward = $monster->getGoldReward($character->level);
        $itemRewards = $monster->getDroppedItems();

        // 给角色添加经验和金币
        $character->increment('exp', $expReward);
        $character->increment('gold', $goldReward);

        return [
            'exp' => $expReward,
            'gold' => $goldReward,
            'items' => $itemRewards
        ];
    }

    /**
     * 结束战斗
     */
    private function endBattle(Battle $battle, string $result, int $exp = 0, int $gold = 0, array $items = []): void
    {
        $battle->finish($result, $exp, $gold, $items);
        
        $battle->addLog('battle_end', [
            'result' => $result,
            'exp_gained' => $exp,
            'gold_gained' => $gold,
            'items_gained' => $items
        ]);
        
        $battle->save();
    }

    /**
     * 逃跑
     */
    public function flee(int $battleId): array
    {
        try {
            DB::beginTransaction();

            $battle = Battle::findOrFail($battleId);

            if (!$battle->isOngoing()) {
                throw new \Exception('战斗已结束');
            }

            // 逃跑成功率基于敏捷
            $character = $battle->character;
            $characterStatus = $character->characterStatus;
            $fleeChance = min(90, 50 + $characterStatus->agility); // 基础50%，敏捷加成，最大90%

            $success = rand(1, 100) <= $fleeChance;

            if ($success) {
                $this->endBattle($battle, 'fled');
                $battle->addLog('flee_success', ['flee_chance' => $fleeChance]);
                
                DB::commit();
                return [
                    'success' => true,
                    'message' => '成功逃脱！'
                ];
            } else {
                $battle->addLog('flee_failed', ['flee_chance' => $fleeChance]);
                $battle->save();
                
                DB::commit();
                return [
                    'success' => false,
                    'message' => '逃跑失败！'
                ];
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('逃跑失败', [
                'battle_id' => $battleId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
