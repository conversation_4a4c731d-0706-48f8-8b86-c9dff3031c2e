<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('skills', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->enum('type', ['active', 'passive', 'ultimate']);
            $table->enum('target_type', ['self', 'single', 'aoe', 'buff', 'debuff']);
            $table->integer('mp_cost')->default(0);
            $table->integer('cooldown')->default(0); // 冷却时间（回合）
            $table->integer('base_power')->default(0);
            $table->string('element')->nullable(); // 技能元素类型
            $table->json('effects')->nullable(); // 技能效果 [{type: 'damage', value: 10}, {type: 'stun', chance: 0.3}, ...]
            $table->string('icon')->nullable();
            $table->json('profession_restrictions')->nullable(); // 职业限制
            $table->integer('required_level')->default(1);
            $table->timestamps();
        });

        Schema::create('character_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained()->onDelete('cascade');
            $table->foreignId('skill_id')->constrained()->onDelete('cascade');
            $table->integer('level')->default(1);
            $table->integer('experience')->default(0); // 技能熟练度
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('character_skills');
        Schema::dropIfExists('skills');
    }
};
