<template>
  <GameLayout>
    <div class="character-select-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <p class="page-subtitle">请选择您要使用的角色，或创建新角色</p>
        <div v-if="currentRegion" class="current-region">
          当前大区: <span class="region-name">{{ currentRegion.name }}</span>
        </div>

        <!-- 调试信息 -->
        <div v-if="$route.query.debug" class="debug-info">
          <h4>调试信息:</h4>
          <p>加载状态: {{ isLoading }}</p>
          <p>错误信息: {{ error || '无' }}</p>
          <p>角色数量: {{ characters.length }}</p>
          <p>当前大区ID: {{ currentRegion?.id }}</p>
          <details>
            <summary>角色数据</summary>
            <pre>{{ JSON.stringify(characters, null, 2) }}</pre>
          </details>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载角色列表...</p>
      </div>

      <!-- 错误提示 -->
      <div v-if="error && !isLoading" class="error-container">
        <div class="error-message">
          <i class="error-icon">⚠️</i>
          <p>{{ error }}</p>
          <button @click="loadCharacterList" class="retry-btn">重试</button>
        </div>
      </div>

      <!-- 角色列表 -->
      <div v-if="!isLoading && !error" class="characters-container">
        <!-- 空状态提示 -->
        <div v-if="characters.length === 0" class="empty-state">
          <div class="empty-icon">👤</div>
          <h3>还没有角色</h3>
          <p>在当前大区 "{{ currentRegion?.name }}" 中还没有创建角色</p>
        </div>

        <!-- 角色卡片网格 -->
        <div v-else class="characters-grid">
          <!-- 现有角色 -->
          <div 
            v-for="character in characters" 
            :key="character.id"
            class="character-card"
            :class="{ 'selected': selectedCharacter?.id === character.id }"
            @click="selectCharacterLocal(character)"
          >
            <div class="character-avatar">
              <img :src="character.avatar || '/static/game/avatars/default.png'" :alt="character.name" />
              <div class="character-level">Lv.{{ character.level }}</div>
            </div>
            <div class="character-info">
              <h3 class="character-name">{{ character.name }}</h3>
              <p class="character-class">{{ getClassName(character.profession || character.class) }}</p>
              <div class="character-stats">
                <span class="stat">经验: {{ character.experience || character.exp || 0 }}</span>
                <span class="stat">创建时间: {{ formatDate(character.created_at || character.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 创建新角色按钮 -->
          <div 
            v-if="characters.length < 3"
            class="character-card create-new"
            @click="createNewCharacter"
          >
            <div class="create-icon">
              <i class="plus-icon">+</i>
            </div>
            <div class="create-text">
              <h3>创建新角色</h3>
              <p>还可以创建 {{ 3 - characters.length }} 个角色</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <!-- 返回大区选择图片按钮 -->
          <div
            @click="goBack"
            @mousedown="handleBackMouseDown"
            @mouseup="handleBackMouseUp"
            @mouseleave="handleBackMouseUp"
            class="return-btn"
            :class="{ 'pressed': isBackPressed }"
          >
            <img
              :src="getBackButtonImage()"
              alt="返回大区选择"
              class="return-btn-img"
              draggable="false"
            />
          </div>

          <!-- 如果没有角色，显示创建角色图片按钮 -->
          <div
            v-if="characters.length === 0"
            @click="createNewCharacter"
            @mousedown="handleCreateMouseDown"
            @mouseup="handleCreateMouseUp"
            @mouseleave="handleCreateMouseUp"
            class="create-character-btn"
            :class="{ 'pressed': isCreatePressed }"
          >
            <img
              :src="getCreateButtonImage()"
              alt="创建第一个角色"
              class="create-btn-img"
              draggable="false"
            />
          </div>

          <!-- 如果有角色，显示进入游戏图片按钮 -->
          <div
            v-else
            @click="confirmSelection"
            @mousedown="handleEnterMouseDown"
            @mouseup="handleEnterMouseUp"
            @mouseleave="handleEnterMouseUp"
            class="enter-game-btn"
            :class="{ 'disabled': !selectedCharacter, 'pressed': isEnterPressed }"
          >
            <img
              :src="getEnterButtonImage()"
              alt="进入游戏"
              class="enter-btn-img"
              draggable="false"
            />
          </div>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import { mapState, mapActions } from 'vuex'
import { regionService } from '@/api'
import logger from '@/utils/logger'

export default {
  name: 'CharacterSelect',
  components: {
    GameLayout
  },
  data() {
    return {
      selectedCharacter: null,
      currentRegion: null,
      // 按钮状态
      isBackPressed: false,
      isCreatePressed: false,
      isEnterPressed: false
    }
  },
  computed: {
    ...mapState('character', ['isLoading', 'error']),

    // 获取当前大区的角色列表
    characters() {
      if (!this.currentRegion) return [];
      return this.$store.getters['character/charactersByRegion'](this.currentRegion.id);
    }
  },
  async created() {
    logger.debug('[CharacterSelect] 页面初始化')

    // 获取当前选择的大区
    this.currentRegion = regionService.getCurrentRegion()
    logger.debug('[CharacterSelect] 当前大区:', this.currentRegion)

    if (!this.currentRegion) {
      this.showToast('请先选择大区')
      this.$router.push('/setup/region-select')
      return
    }

    await this.loadCharacterList()
  },
  methods: {
    ...mapActions('character', ['loadCharacters', 'selectCharacter']),

    async loadCharacterList() {
      if (!this.currentRegion) return

      try {
        logger.debug('[CharacterSelect] 开始加载角色列表，大区ID:', this.currentRegion.id)
        await this.loadCharacters(this.currentRegion.id)
        logger.debug('[CharacterSelect] 角色列表加载完成，角色数量:', this.characters.length)

        // 如果没有角色，显示提示
        if (this.characters.length === 0) {
          logger.info('[CharacterSelect] 当前大区没有角色')
        }
      } catch (error) {
        logger.error('[CharacterSelect] 加载角色列表失败:', error)
        this.showToast('加载角色列表失败: ' + (error.message || '未知错误'))
      }
    },

    selectCharacterLocal(character) {
      this.selectedCharacter = character
      logger.debug('[CharacterSelect] 选择角色:', character.name)
    },

    async confirmSelection() {
      if (!this.selectedCharacter) {
        this.showToast('请先选择一个角色')
        return
      }

      try {
        const success = await this.selectCharacter(this.selectedCharacter)
        if (success) {
          this.showToast('角色选择成功，正在进入游戏...')
          // 跳转到游戏主界面
          setTimeout(() => {
            this.$router.push('/game/main')
          }, 1000)
        } else {
          this.showToast('选择角色失败，请重试')
        }
      } catch (error) {
        logger.error('[CharacterSelect] 确认选择失败:', error)
        this.showToast('选择角色失败，请重试')
      }
    },

    createNewCharacter() {
      this.$router.push('/setup/create-character')
    },

    goBack() {
      this.$router.push('/setup/region-select')
    },

    // 按钮状态处理方法
    handleBackMouseDown() {
      this.isBackPressed = true
    },

    handleBackMouseUp() {
      this.isBackPressed = false
    },

    handleCreateMouseDown() {
      this.isCreatePressed = true
    },

    handleCreateMouseUp() {
      this.isCreatePressed = false
    },

    handleEnterMouseDown() {
      if (!this.selectedCharacter) return
      this.isEnterPressed = true
    },

    handleEnterMouseUp() {
      this.isEnterPressed = false
    },

    // 按钮图片获取方法
    getBackButtonImage() {
      return this.isBackPressed
        ? '/static/game/UI/anniu/fhui_.png'
        : '/static/game/UI/anniu/fhui_2.png'
    },

    getCreateButtonImage() {
      return this.isCreatePressed
        ? '/static/game/UI/anniu/cj_1.png'
        : '/static/game/UI/anniu/cj.png'
    },

    getEnterButtonImage() {
      if (!this.selectedCharacter) {
        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片
      }
      return this.isEnterPressed
        ? '/static/game/UI/anniu/jr_4.png'
        : '/static/game/UI/anniu/jr_3.png'
    },

    getClassName(profession) {
      const classMap = {
        'warrior': '武士',
        'scholar': '文人',
        'mystic': '异人'
      }
      return classMap[profession] || profession
    },

    formatDate(dateString) {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    showToast(message) {
      const toast = document.createElement('div')
      toast.textContent = message
      toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
      `
      document.body.appendChild(toast)
      setTimeout(() => {
        document.body.removeChild(toast)
      }, 2000)
    }
  }
}
</script>

<style scoped>
.character-select-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  color: #d4af37;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.page-subtitle {
  font-size: 16px;
  color: #ccc;
  margin: 0 0 10px 0;
}

.current-region {
  font-size: 14px;
  color: #999;
}

.region-name {
  color: #d4af37;
  font-weight: bold;
}

.debug-info {
  margin-top: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid #444;
  border-radius: 5px;
  font-size: 12px;
  text-align: left;
}

.debug-info h4 {
  color: #d4af37;
  margin: 0 0 10px 0;
}

.debug-info p {
  margin: 5px 0;
  color: #ccc;
}

.debug-info pre {
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  font-size: 11px;
  color: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 300px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #ccc;
  font-size: 16px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 300px;
}

.error-message {
  text-align: center;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  padding: 30px;
  border-radius: 10px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.retry-btn {
  margin-top: 15px;
  padding: 8px 16px;
  background: #d4af37;
  color: #000;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background: #b8941f;
}

.characters-container {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #ccc;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  color: #d4af37;
  margin: 0 0 10px 0;
  font-size: 24px;
}

.empty-state p {
  margin: 0 0 30px 0;
  font-size: 16px;
}

.characters-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.character-card {
  background: linear-gradient(135deg, rgba(30, 30, 80, 0.9), rgba(50, 50, 120, 0.8));
  border: 2px solid #4a5568;
  border-radius: 8px;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 100px;
  position: relative;
  overflow: hidden;
}

.character-card:hover {
  border-color: #d4af37;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(255, 215, 0, 0.1));
  transform: translateY(-2px);
}

.character-card.selected {
  border-color: #d4af37;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(255, 215, 0, 0.2));
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);
  transform: translateY(-2px);
}

.character-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

.character-card.create-new {
  justify-content: center;
  align-items: center;
  min-height: 100px;
  border-style: dashed;
  border-color: #666;
  background: linear-gradient(135deg, rgba(60, 60, 60, 0.3), rgba(80, 80, 80, 0.2));
}

.character-card.create-new:hover {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(255, 215, 0, 0.05));
  border-color: #d4af37;
}

.character-avatar {
  position: relative;
  margin-right: 20px;
  flex-shrink: 0;
}

.character-avatar img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #4a5568;
}

.character-level {
  position: absolute;
  bottom: -5px;
  right: -5px;
  background: linear-gradient(135deg, #d4af37, #ffd700);
  color: #000;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  border: 1px solid #b8941f;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.character-info {
  text-align: center;
}

.character-name {
  font-size: 18px;
  color: #d4af37;
  margin: 0 0 5px 0;
}

.character-class {
  font-size: 14px;
  color: #ccc;
  margin: 0 0 10px 0;
}

.character-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat {
  font-size: 12px;
  color: #999;
}

.create-icon {
  margin-bottom: 15px;
}

.plus-icon {
  font-size: 48px;
  color: #666;
  font-style: normal;
}

.create-text h3 {
  color: #d4af37;
  margin: 0 0 8px 0;
  font-size: 18px;
}

.create-text p {
  color: #999;
  margin: 0;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  padding: 20px 0;
  flex-wrap: nowrap; /* 确保按钮不换行 */
}



/* 图片按钮通用样式 */
.create-character-btn,
.return-btn,
.enter-game-btn {
  cursor: pointer;
  display: inline-block;
  transition: all 0.2s ease;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.enter-game-btn.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 创建角色图片按钮样式 */
.create-character-btn.pressed .create-btn-img {
  transform: scale(0.95);
}

.create-btn-img {
  display: block;
  max-width: 200px;
  height: auto;
  transition: transform 0.1s ease;
}

/* 返回按钮图片样式 */
.return-btn.pressed .return-btn-img {
  transform: scale(0.95);
}

.return-btn-img {
  display: block;
  max-width: 200px;
  height: auto;
  transition: transform 0.1s ease;
}

/* 进入游戏按钮图片样式 */
.enter-game-btn.pressed .enter-btn-img {
  transform: scale(0.95);
}

.enter-btn-img {
  display: block;
  max-width: 200px;
  height: auto;
  transition: transform 0.1s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .character-select-container {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .characters-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: nowrap; /* 保持按钮在同一行 */
    justify-content: center;
    gap: 15px;
    padding: 15px 0;
  }

  .create-character-btn,
  .return-btn,
  .enter-game-btn {
    display: flex;
    justify-content: center;
    flex-shrink: 0; /* 防止按钮被压缩 */
  }

  .create-btn-img,
  .return-btn-img,
  .enter-btn-img {
    max-width: 120px; /* 在移动设备上稍微缩小按钮 */
  }

  .empty-state {
    padding: 40px 15px;
  }

  .empty-state h3 {
    font-size: 20px;
  }

  .empty-state p {
    font-size: 14px;
  }
}
</style>
