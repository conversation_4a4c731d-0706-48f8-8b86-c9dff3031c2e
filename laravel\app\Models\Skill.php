<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Skill extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'target_type',
        'mp_cost',
        'cooldown',
        'base_power',
        'element',
        'effects',
        'icon',
        'profession_restrictions',
        'required_level',
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'effects' => 'array',
        'profession_restrictions' => 'array',
        'mp_cost' => 'integer',
        'cooldown' => 'integer',
        'base_power' => 'integer',
        'required_level' => 'integer',
    ];

    /**
     * 获取拥有此技能的角色
     */
    public function characters()
    {
        return $this->belongsToMany(Character::class, 'character_skills')
            ->withPivot('level', 'experience')
            ->withTimestamps();
    }
}
