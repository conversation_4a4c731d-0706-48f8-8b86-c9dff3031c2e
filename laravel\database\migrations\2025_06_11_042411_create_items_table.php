<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('items', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['equipment', 'consumable', 'material', 'quest']);
            $table->enum('quality', ['common', 'uncommon', 'rare', 'epic', 'legendary', 'quest'])->default('common');
            $table->enum('equipment_type', ['weapon', 'armor', 'helmet', 'accessory'])->nullable();
            $table->json('stats')->nullable();
            $table->string('icon')->nullable();
            $table->boolean('stackable')->default(false);
            $table->integer('max_stack')->default(1);
            $table->integer('base_price')->default(0);
            $table->timestamps();
        });

        // 创建角色物品表（背包）
        Schema::create('character_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained()->onDelete('cascade');
            $table->foreignId('item_id')->constrained();
            $table->integer('quantity')->default(1);
            $table->boolean('equipped')->default(false);
            $table->json('custom_stats')->nullable(); // 自定义/随机属性
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('character_items');
        Schema::dropIfExists('items');
    }
};
