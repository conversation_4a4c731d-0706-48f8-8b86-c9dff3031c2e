{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue?vue&type=style&index=0&id=8100a840&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue", "mtime": 1749890251457}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BattleTest.vue"], "names": [], "mappings": ";AA8OA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "BattleTest.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"battle-test\">\n    <h1>战斗系统测试</h1>\n    \n    <div class=\"test-section\">\n      <h2>测试怪物列表</h2>\n      <div class=\"monster-list\">\n        <div \n          v-for=\"monster in testMonsters\" \n          :key=\"monster.id\"\n          class=\"monster-card\"\n          @click=\"startTestBattle(monster)\"\n        >\n          <img :src=\"monster.avatar\" :alt=\"monster.name\" class=\"monster-avatar\" />\n          <div class=\"monster-info\">\n            <h3>{{ monster.name }}</h3>\n            <p>等级: {{ monster.level }}</p>\n            <p>类型: {{ monster.type }}</p>\n            <p>生命: {{ monster.max_health }}</p>\n            <p>攻击: {{ monster.attack }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h2>动画测试</h2>\n      <div class=\"animation-test\">\n        <BattleAnimation ref=\"animationTest\" />\n        <div class=\"animation-controls\">\n          <button @click=\"testAttackAnimation\">测试攻击动画</button>\n          <button @click=\"testDamageAnimation\">测试伤害动画</button>\n          <button @click=\"testCriticalAnimation\">测试暴击动画</button>\n          <button @click=\"testHealAnimation\">测试治疗动画</button>\n          <button @click=\"testSkillAnimation\">测试技能特效</button>\n          <button @click=\"testParticleAnimation\">测试粒子效果</button>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h2>API测试</h2>\n      <div class=\"api-test\">\n        <button @click=\"testBattleAPI\" :disabled=\"isTestingAPI\">\n          {{ isTestingAPI ? '测试中...' : '测试战斗API' }}\n        </button>\n        <div v-if=\"apiTestResult\" class=\"api-result\">\n          <h4>API测试结果:</h4>\n          <pre>{{ JSON.stringify(apiTestResult, null, 2) }}</pre>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport BattleAnimation from '@/components/game/BattleAnimation.vue'\nimport battleService from '@/api/services/battleService.js'\nimport logger from '@/utils/logger.js'\n\nexport default {\n  name: 'BattleTest',\n  components: {\n    BattleAnimation\n  },\n  \n  data() {\n    return {\n      testMonsters: [\n        {\n          id: 1,\n          name: '灵猴',\n          level: 5,\n          type: 'beast',\n          max_health: 80,\n          attack: 15,\n          avatar: '/static/game/UI/tx/monster/monkey.png'\n        },\n        {\n          id: 2,\n          name: '山魈',\n          level: 8,\n          type: 'demon',\n          max_health: 120,\n          attack: 22,\n          avatar: '/static/game/UI/tx/monster/demon.png'\n        },\n        {\n          id: 3,\n          name: '水灵',\n          level: 6,\n          type: 'elemental',\n          max_health: 90,\n          attack: 18,\n          avatar: '/static/game/UI/tx/monster/water_spirit.png'\n        }\n      ],\n      isTestingAPI: false,\n      apiTestResult: null\n    }\n  },\n  \n  methods: {\n    // 开始测试战斗\n    startTestBattle(monster) {\n      // 获取当前用户的角色ID\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      let testCharacterId = 1; // 默认值\n\n      if (currentCharacter && currentCharacter.id) {\n        testCharacterId = currentCharacter.id;\n      } else {\n        // 尝试从localStorage获取\n        try {\n          const storedCharacter = localStorage.getItem('selectedCharacter');\n          if (storedCharacter) {\n            const character = JSON.parse(storedCharacter);\n            testCharacterId = character.id || 1;\n          }\n        } catch (error) {\n          // 无法获取角色信息，使用默认ID\n        }\n      }\n\n      // 显示将要使用的参数\n      this.$toast(`开始战斗测试 - 角色ID: ${testCharacterId}, 怪物ID: ${monster.id}`)\n\n      this.$router.push({\n        path: '/game/battle',\n        query: {\n          characterId: testCharacterId,\n          monsterId: monster.id,\n          locationId: 'test_location'\n        }\n      });\n    },\n\n    // 测试攻击动画\n    testAttackAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playAttackAnimation();\n      }\n    },\n\n    // 测试伤害动画\n    testDamageAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playDamageAnimation(25, false);\n      }\n    },\n\n    // 测试暴击动画\n    testCriticalAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playDamageAnimation(45, true);\n      }\n    },\n\n    // 测试治疗动画\n    testHealAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playHealAnimation(30);\n      }\n    },\n\n    // 测试技能特效\n    testSkillAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playSkillEffect('fire');\n      }\n    },\n\n    // 测试粒子效果\n    testParticleAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playParticleEffect('explosion');\n      }\n    },\n\n    // 测试战斗API\n    async testBattleAPI() {\n      this.isTestingAPI = true;\n      this.apiTestResult = null;\n\n      try {\n        logger.info('[BattleTest] 开始测试战斗API...');\n\n        // 首先测试简单的API调用\n        const testData = {\n          character_id: 1,\n          monster_id: 1,\n          location_id: 'test_location'\n        };\n\n        logger.info('[BattleTest] 测试数据:', testData);\n\n        // 测试开始战斗API\n        const result = await battleService.startBattle(1, 1, 'test_location');\n        this.apiTestResult = {\n          success: true,\n          data: result,\n          timestamp: new Date().toISOString()\n        };\n        logger.info('[BattleTest] API测试成功:', result);\n      } catch (error) {\n        this.apiTestResult = {\n          success: false,\n          error: error.message,\n          details: {\n            name: error.name,\n            message: error.message,\n            response: error.response?.data,\n            status: error.response?.status,\n            config: error.config\n          },\n          timestamp: new Date().toISOString()\n        };\n        logger.error('[BattleTest] API测试失败:', error);\n      } finally {\n        this.isTestingAPI = false;\n      }\n    }\n  },\n\n  mounted() {\n    logger.info('[BattleTest] 战斗测试页面已加载');\n    \n    // 检查动画库是否加载\n    if (window.particlesJS) {\n      logger.info('[BattleTest] Particles.js 已加载');\n    } else {\n      logger.warn('[BattleTest] Particles.js 未加载');\n    }\n  }\n}\n</script>\n\n<style scoped>\n.battle-test {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.battle-test h1 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 30px;\n}\n\n.test-section {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.test-section h2 {\n  color: #555;\n  margin-bottom: 15px;\n  border-bottom: 2px solid #eee;\n  padding-bottom: 10px;\n}\n\n/* 怪物列表样式 */\n.monster-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 15px;\n}\n\n.monster-card {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.monster-card:hover {\n  border-color: #007bff;\n  background: #f8f9fa;\n  transform: translateY(-2px);\n}\n\n.monster-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid #ccc;\n}\n\n.monster-info h3 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.monster-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n/* 动画测试样式 */\n.animation-test {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.animation-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.animation-controls button {\n  padding: 10px 15px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.animation-controls button:hover {\n  background: #0056b3;\n}\n\n/* API测试样式 */\n.api-test button {\n  padding: 12px 24px;\n  background: #28a745;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: background 0.3s ease;\n}\n\n.api-test button:hover:not(:disabled) {\n  background: #218838;\n}\n\n.api-test button:disabled {\n  background: #6c757d;\n  cursor: not-allowed;\n}\n\n.api-result {\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 5px;\n}\n\n.api-result h4 {\n  margin: 0 0 10px 0;\n  color: #495057;\n}\n\n.api-result pre {\n  background: #e9ecef;\n  padding: 10px;\n  border-radius: 3px;\n  overflow-x: auto;\n  font-size: 12px;\n  color: #495057;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .battle-test {\n    padding: 10px;\n  }\n  \n  .monster-list {\n    grid-template-columns: 1fr;\n  }\n  \n  .monster-card {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .animation-controls {\n    flex-direction: column;\n  }\n}\n</style>\n"]}]}