<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Battle;
use App\Models\Character;
use App\Models\Monster;
use Illuminate\Support\Facades\DB;

class BattleController extends Controller
{
    /**
     * 显示战斗记录列表
     */
    public function index(Request $request)
    {
        try {
            $query = Battle::with(['character', 'monster']);

            // 筛选条件
            if ($request->filled('character_id')) {
                $query->where('character_id', $request->character_id);
            }

            if ($request->filled('monster_id')) {
                $query->where(function($q) use ($request) {
                    $q->where('opponent_id', $request->monster_id)
                      ->where('opponent_type', Monster::class);
                });
            }

            if ($request->filled('result')) {
                // 兼容status字段的多种可能值
                $statusMap = [
                    'win' => ['victory', 'win'],
                    'lose' => ['defeat', 'lose'],
                    'escape' => ['fled', 'escape']
                ];

                if (isset($statusMap[$request->result])) {
                    $query->whereIn('status', $statusMap[$request->result]);
                } else {
                    $query->where('status', $request->result);
                }
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $battles = $query->orderBy('created_at', 'desc')
                ->paginate(15);

            // 获取所有角色和怪物，用于筛选
            $characters = Character::select('id', 'name')->get();
            $monsters = Monster::select('id', 'name')->get();

            return view('admin.battles.index', compact('battles', 'characters', 'monsters'));
        } catch (\Exception $e) {
            // 记录错误日志
            \Log::error('获取战斗记录失败: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // 创建一个空的分页对象而不是简单的集合
            $battles = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            $characters = [];
            $monsters = [];

            return view('admin.battles.index', compact('battles', 'characters', 'monsters'))
                ->with('error', '获取战斗记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示战斗记录详情
     */
    public function show($id)
    {
        try {
            $battle = Battle::with(['character', 'monster'])
                ->findOrFail($id);

            return view('admin.battles.show', compact('battle'));
        } catch (\Exception $e) {
            \Log::error('获取战斗记录详情失败: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return redirect()->route('admin.battles.index')
                ->with('error', '无法获取战斗记录: ' . $e->getMessage());
        }
    }

    /**
     * 删除战斗记录
     */
    public function destroy($id)
    {
        try {
            $battle = Battle::findOrFail($id);
            $battle->delete();

            return redirect()->route('admin.battles.index')
                ->with('success', '战斗记录已成功删除');
        } catch (\Exception $e) {
            \Log::error('删除战斗记录失败: ' . $e->getMessage());

            return redirect()->route('admin.battles.index')
                ->with('error', '删除战斗记录失败: ' . $e->getMessage());
        }
    }
}
