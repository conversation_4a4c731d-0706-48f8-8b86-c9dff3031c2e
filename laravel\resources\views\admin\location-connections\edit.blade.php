@extends('admin.layouts.app')

@section('title', '编辑位置连接')

@section('content')
@section('breadcrumb')
<a href="{{ route('admin.location-connections.index') }}">位置连接管理</a>
<span class="layui-breadcrumb-separator">/</span>
<span>编辑连接</span>
@endsection

@section('page-title', '编辑位置连接')

<div class="layui-card">
    <div class="layui-card-header">
        <a href="{{ route('admin.location-connections.index') }}" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        编辑位置连接 - {{ $locationConnection->fromLocation->name ?? 'N/A' }} → {{ $locationConnection->toLocation->name ?? 'N/A' }}
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if($errors->any())
        <div class="layui-alert layui-alert-danger">
            <ul>
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <form class="layui-form" method="POST" action="{{ route('admin.location-connections.update', $locationConnection->id) }}">
            @csrf
            @method('PUT')

            <div class="layui-form-item">
                <label class="layui-form-label">起始位置</label>
                <div class="layui-input-block">
                    <input type="text" value="{{ $locationConnection->fromLocation->name ?? 'N/A' }}" disabled class="layui-input layui-disabled">
                    <div class="layui-form-mid layui-text-em">连接的起始和目标位置不可修改</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">目标位置</label>
                <div class="layui-input-block">
                    <input type="text" value="{{ $locationConnection->toLocation->name ?? 'N/A' }}" disabled class="layui-input layui-disabled">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">距离</label>
                <div class="layui-input-block">
                    <input type="number" name="distance" value="{{ old('distance', $locationConnection->distance) }}" required lay-verify="required|number" placeholder="请输入距离" class="layui-input">
                    <div class="layui-form-mid layui-text-em">用于计算移动时间和消耗</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">时间消耗</label>
                <div class="layui-input-block">
                    <input type="number" name="time_cost" value="{{ old('time_cost', $locationConnection->time_cost) }}" required lay-verify="required|number" placeholder="请输入时间消耗" class="layui-input">
                    <div class="layui-form-mid layui-text-em">单位：分钟</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">银两消耗</label>
                <div class="layui-input-block">
                    <input type="number" name="silver_cost" value="{{ old('silver_cost', $locationConnection->silver_cost) }}" required lay-verify="required|number" placeholder="请输入银两消耗" class="layui-input">
                    <div class="layui-form-mid layui-text-em">单位：两银子</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">等级要求</label>
                <div class="layui-input-block">
                    <input type="number" name="level_requirement" value="{{ old('level_requirement', $locationConnection->level_requirement) }}" required lay-verify="required|number" placeholder="请输入等级要求" class="layui-input">
                    <div class="layui-form-mid layui-text-em">角色需要达到的最低等级</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_active" value="1" title="启用连接" {{ old('is_active', $locationConnection->is_active) ? 'checked' : '' }}>
                    <div class="layui-form-mid layui-text-em">禁用后角色无法通过此连接移动</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit>保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <a href="{{ route('admin.location-connections.index') }}" class="layui-btn layui-btn-primary">取消</a>
                </div>
            </div>
        </form>

        <!-- 连接信息 -->
        <div class="layui-card" style="margin-top: 20px;">
            <div class="layui-card-header">连接信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td>连接ID</td>
                        <td>{{ $locationConnection->id }}</td>
                    </tr>
                    <tr>
                        <td>创建时间</td>
                        <td>{{ $locationConnection->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <td>更新时间</td>
                        <td>{{ $locationConnection->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <td>反向连接</td>
                        <td>
                            @php
                            $reverseConnection = \App\Models\LocationConnection::where('from_location_id', $locationConnection->to_location_id)
                                ->where('to_location_id', $locationConnection->from_location_id)
                                ->first();
                            @endphp
                            @if($reverseConnection)
                            <span class="layui-badge layui-bg-green">存在</span>
                            <a href="{{ route('admin.location-connections.edit', $reverseConnection->id) }}" class="layui-btn layui-btn-xs">编辑反向连接</a>
                            @else
                            <span class="layui-badge layui-bg-gray">不存在</span>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="createReverseConnection()">创建反向连接</button>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    // 表单验证
    form.verify({
        number: function(value) {
            if(value && !/^\d+$/.test(value)) {
                return '请输入正整数';
            }
        }
    });
});

function createReverseConnection() {
    layui.use('layer', function(){
        var layer = layui.layer;

        layer.confirm('确定要创建反向连接吗？这将允许双向移动。', {
            btn: ['确认创建','取消']
        }, function(){
            // 创建反向连接的逻辑
            $.ajax({
                url: '{{ route("admin.location-connections.store") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    from_location_id: {{ $locationConnection->to_location_id }},
                    to_location_id: {{ $locationConnection->from_location_id }},
                    distance: {{ $locationConnection->distance }},
                    time_cost: {{ $locationConnection->time_cost }},
                    silver_cost: {{ $locationConnection->silver_cost }},
                    level_requirement: {{ $locationConnection->level_requirement }}
                },
                success: function(res) {
                    layer.msg('反向连接创建成功');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                },
                error: function() {
                    layer.msg('创建失败，请稍后再试');
                }
            });
        });
    });
}
</script>
@endsection
