<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Character;
use App\Models\BankAccount;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixBankAccounts extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'bank:fix-accounts';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '修复银行账户数据，确保每个角色都有银行账户';

    /**
     * 创建命令实例
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        try {
            $this->info('开始修复银行账户...');
            Log::info('开始修复银行账户...');

            // 检查bank_accounts表是否存在
            if (!Schema::hasTable('bank_accounts')) {
                $this->warn('bank_accounts表不存在，尝试运行迁移...');
                \Artisan::call('migrate', ['--path' => 'database/migrations/2025_06_15_000001_create_bank_accounts_table.php']);
                $this->info('银行账户表迁移完成');
            }

            // 检查bank_transactions表是否存在
            if (!Schema::hasTable('bank_transactions')) {
                $this->warn('bank_transactions表不存在，尝试运行迁移...');
                \Artisan::call('migrate', ['--path' => 'database/migrations/2025_06_15_000002_create_bank_transactions_table.php']);
                $this->info('银行交易表迁移完成');
            }

            // 获取所有角色
            $this->info('正在获取所有角色...');
            $characters = Character::all();
            $this->info("找到 {$characters->count()} 个角色");

            // 为每个角色创建银行账户（如果不存在）
            $created = 0;
            $updated = 0;
            $errors = 0;

            foreach ($characters as $character) {
                try {
                    $account = BankAccount::firstWhere('character_id', $character->id);

                    if ($account) {
                        $this->line("角色 {$character->name} (ID: {$character->id}) 已有银行账户");
                        $updated++;
                    } else {
                        $account = new BankAccount();
                        $account->character_id = $character->id;
                        $account->silver = 0;
                        $account->gold_ingot = 0;
                        $account->save();

                        $this->info("为角色 {$character->name} (ID: {$character->id}) 创建了银行账户");
                        $created++;
                    }
                } catch (\Exception $e) {
                    $this->error("处理角色 {$character->id} 时出错: {$e->getMessage()}");
                    Log::error("处理角色 {$character->id} 时出错: {$e->getMessage()}");
                    $errors++;
                }
            }

            $this->info('银行账户修复完成:');
            $this->info("- 创建账户: {$created}");
            $this->info("- 已存在账户: {$updated}");
            $this->info("- 处理错误: {$errors}");

            Log::info("银行账户修复完成: 创建={$created}, 已存在={$updated}, 错误={$errors}");

            return 0;
        } catch (\Exception $e) {
            $this->error("修复银行账户时发生错误: {$e->getMessage()}");
            Log::error("修复银行账户时发生错误: {$e->getMessage()}");
            Log::error("堆栈跟踪: {$e->getTraceAsString()}");
            return 1;
        }
    }
}
