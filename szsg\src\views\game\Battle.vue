<template>
  <GameLayout>
    <div class="battle-container">
      <!-- 怪物信息区域 -->
      <div class="monster-section">
        <div class="monster-info">
          <img :src="monster.avatar || '/static/game/ui/default_monster.png'" class="monster-avatar" />
          <div class="monster-details">
            <h3>{{ monster.name }} Lv.{{ monster.level }}</h3>
            <div class="hp-bar">
              <div class="bar-fill hp-fill" :style="{ width: monsterHpPercent + '%' }"></div>
              <span class="bar-text">{{ monster.hp }}/{{ monster.max_hp }}</span>
            </div>
            <div class="mp-bar">
              <div class="bar-fill mp-fill" :style="{ width: monsterMpPercent + '%' }"></div>
              <span class="bar-text">{{ monster.mp }}/{{ monster.max_mp }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 战斗动画区域 -->
      <div class="battle-animation-area" ref="animationArea">
        <BattleAnimation ref="battleAnimation" />
      </div>

      <!-- 角色信息区域 -->
      <div class="character-section">
        <div class="character-info">
          <img :src="character.avatar || '/static/game/ui/default_avatar.png'" class="character-avatar" />
          <div class="character-details">
            <h3>{{ character.name }} Lv.{{ character.level }}</h3>
            <div class="hp-bar">
              <div class="bar-fill hp-fill" :style="{ width: characterHpPercent + '%' }"></div>
              <span class="bar-text">{{ character.hp }}/{{ character.max_hp }}</span>
            </div>
            <div class="mp-bar">
              <div class="bar-fill mp-fill" :style="{ width: characterMpPercent + '%' }"></div>
              <span class="bar-text">{{ character.mp }}/{{ character.max_mp }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <button 
          @click="attack" 
          :disabled="!canAct" 
          class="action-btn attack-btn"
        >
          攻击
        </button>
        <button 
          @click="openItems" 
          :disabled="!canAct" 
          class="action-btn item-btn"
        >
          物品
        </button>
        <button 
          @click="flee" 
          :disabled="!canAct" 
          class="action-btn flee-btn"
        >
          逃跑
        </button>
        <button 
          @click="returnToMain" 
          v-if="battleFinished"
          class="action-btn return-btn"
        >
          返回
        </button>
      </div>

      <!-- 战斗日志 -->
      <div class="battle-log" v-if="showLog">
        <div class="log-header">
          <h4>战斗记录</h4>
          <button @click="toggleLog" class="close-btn">×</button>
        </div>
        <div class="log-content">
          <div 
            v-for="(log, index) in battleLogs" 
            :key="index"
            class="log-entry"
          >
            <span class="log-time">第{{ log.round }}回合</span>
            <span class="log-message">{{ formatLogMessage(log) }}</span>
          </div>
        </div>
      </div>

      <!-- 战斗结果弹窗 -->
      <div v-if="showResult" class="battle-result-modal">
        <div class="result-content">
          <h3 :class="resultClass">{{ resultTitle }}</h3>
          <div v-if="battleResult === 'victory'" class="rewards">
            <p>获得经验: {{ rewards.exp_gained }}</p>
            <p>获得金币: {{ rewards.gold_gained }}</p>
            <div v-if="rewards.items_gained && rewards.items_gained.length > 0">
              <p>获得物品:</p>
              <ul>
                <li v-for="item in rewards.items_gained" :key="item.item_id">
                  {{ item.name }} × {{ item.quantity }}
                </li>
              </ul>
            </div>
          </div>
          <button @click="closeResult" class="confirm-btn">确定</button>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import BattleAnimation from '@/components/game/BattleAnimation.vue'
import battleService from '@/api/services/battleService.js'
import logger from '@/utils/logger.js'

export default {
  name: 'Battle',
  components: {
    GameLayout,
    BattleAnimation
  },
  
  data() {
    return {
      battleId: null,
      character: {
        id: null,
        name: '',
        level: 1,
        avatar: '',
        hp: 100,
        max_hp: 100,
        mp: 50,
        max_mp: 50
      },
      monster: {
        id: null,
        name: '',
        level: 1,
        avatar: '',
        hp: 100,
        max_hp: 100,
        mp: 50,
        max_mp: 50
      },
      battleStatus: 'ongoing', // ongoing, victory, defeat, fled
      canAct: true,
      isProcessing: false,
      battleLogs: [],
      showLog: false,
      showResult: false,
      battleResult: null,
      rewards: {},
      animationManager: null
    }
  },

  computed: {
    characterHpPercent() {
      return this.character.max_hp > 0 ? (this.character.hp / this.character.max_hp) * 100 : 0
    },
    
    characterMpPercent() {
      return this.character.max_mp > 0 ? (this.character.mp / this.character.max_mp) * 100 : 0
    },
    
    monsterHpPercent() {
      return this.monster.max_hp > 0 ? (this.monster.hp / this.monster.max_hp) * 100 : 0
    },
    
    monsterMpPercent() {
      return this.monster.max_mp > 0 ? (this.monster.mp / this.monster.max_mp) * 100 : 0
    },
    
    battleFinished() {
      return ['victory', 'defeat', 'fled'].includes(this.battleStatus)
    },
    
    resultTitle() {
      switch (this.battleResult) {
        case 'victory': return '战斗胜利！'
        case 'defeat': return '战斗失败！'
        case 'fled': return '成功逃脱！'
        default: return ''
      }
    },
    
    resultClass() {
      switch (this.battleResult) {
        case 'victory': return 'victory-text'
        case 'defeat': return 'defeat-text'
        case 'fled': return 'flee-text'
        default: return ''
      }
    }
  },

  async mounted() {
    try {
      // 从路由参数获取战斗信息
      const { characterId, monsterId, locationId } = this.$route.query
      logger.debug('[Battle] 路由参数:', { characterId, monsterId, locationId })

      if (!characterId || !monsterId) {
        logger.error('[Battle] 缺少战斗参数')
        this.showToast('缺少战斗参数')
        this.$router.push('/game/main')
        return
      }

      // 验证用户认证状态
      const isAuthenticated = this.$store.state.auth.isAuthenticated
      logger.debug('[Battle] 用户认证状态:', isAuthenticated)

      if (!isAuthenticated) {
        logger.error('[Battle] 用户未认证')
        this.showToast('请先登录')
        this.$router.push('/login')
        return
      }

      // 开始战斗
      await this.initBattle(parseInt(characterId), parseInt(monsterId), locationId)

    } catch (error) {
      logger.error('[Battle] 初始化战斗失败:', error)
      this.showToast('初始化战斗失败')
      this.$router.push('/game/main')
    }
  },

  methods: {
    // 初始化战斗
    async initBattle(characterId, monsterId, locationId) {
      try {
        logger.debug('[Battle] 开始初始化战斗:', { characterId, monsterId, locationId })

        // 验证参数
        if (!characterId || !monsterId) {
          throw new Error('缺少必要的战斗参数')
        }

        const result = await battleService.startBattle(characterId, monsterId, locationId)
        logger.debug('[Battle] 战斗API响应:', result)

        // 检查API响应结构 - battleService返回的是res.data，所以直接检查battle_id
        if (result && result.battle_id && result.battle) {
          this.battleId = result.battle_id
          this.updateBattleData(result.battle)
          this.showToast('战斗开始！')
        } else {
          // 显示更详细的错误信息
          const errorMessage = result?.message || result?.error || '开始战斗失败'
          logger.error('[Battle] 战斗失败详情:', result)
          throw new Error(errorMessage)
        }
      } catch (error) {
        logger.error('[Battle] 开始战斗失败:', error)
        // 如果是网络错误，显示更友好的错误信息
        if (error.response) {
          logger.error('[Battle] HTTP错误:', error.response.status, error.response.data)
          throw new Error(`服务器错误 (${error.response.status}): ${error.response.data?.message || '未知错误'}`)
        } else if (error.request) {
          logger.error('[Battle] 网络错误:', error.request)
          throw new Error('网络连接失败，请检查网络连接')
        } else {
          throw error
        }
      }
    },

    // 更新战斗数据
    updateBattleData(battleData) {
      this.character = { ...battleData.character }
      this.monster = { ...battleData.monster }
      this.battleStatus = battleData.status
      this.canAct = battleData.can_act
      this.rewards = battleData.rewards || {}
    },

    // 攻击
    async attack() {
      if (!this.canAct || this.isProcessing) return
      
      try {
        this.isProcessing = true
        this.canAct = false
        
        const result = await battleService.performAction(this.battleId, this.character.id, 'attack')
        
        if (result.success) {
          // 播放攻击动画
          if (this.$refs.battleAnimation) {
            this.$refs.battleAnimation.playAttackAnimation()
            
            // 处理角色攻击结果
            if (result.data.action_results.character_action) {
              const charAction = result.data.action_results.character_action
              this.$refs.battleAnimation.playDamageAnimation(charAction.damage, charAction.is_critical)
            }
            
            // 处理怪物反击
            if (result.data.action_results.monster_action) {
              const monsterAction = result.data.action_results.monster_action
              setTimeout(() => {
                this.$refs.battleAnimation.playDamageAnimation(monsterAction.damage, monsterAction.is_critical)
              }, 1500)
            }
          }
          
          // 更新战斗数据
          this.updateBattleData(result.data.battle)
          
          // 检查战斗是否结束
          if (result.data.action_results.character_action?.battle_end) {
            this.handleBattleEnd(result.data.action_results.character_action.battle_end)
          }
          
        } else {
          this.showToast(result.message || '攻击失败')
        }
      } catch (error) {
        logger.error('[Battle] 攻击失败:', error)
        this.showToast('攻击失败')
      } finally {
        this.isProcessing = false
        if (this.battleStatus === 'ongoing') {
          this.canAct = true
        }
      }
    },

    // 逃跑
    async flee() {
      if (!this.canAct || this.isProcessing) return
      
      try {
        this.isProcessing = true
        this.canAct = false
        
        const result = await battleService.performAction(this.battleId, this.character.id, 'flee')
        
        if (result.success) {
          if (result.data.action_results.flee_result?.success) {
            this.battleResult = 'fled'
            this.battleStatus = 'fled'
            this.showResult = true
          } else {
            this.showToast('逃跑失败！')
            this.canAct = true
          }
        } else {
          this.showToast(result.message || '逃跑失败')
          this.canAct = true
        }
      } catch (error) {
        logger.error('[Battle] 逃跑失败:', error)
        this.showToast('逃跑失败')
        this.canAct = true
      } finally {
        this.isProcessing = false
      }
    },

    // 处理战斗结束
    handleBattleEnd(endResult) {
      this.battleResult = endResult.result
      this.battleStatus = endResult.result
      this.canAct = false
      
      if (endResult.rewards) {
        this.rewards = endResult.rewards
      }
      
      // 延迟显示结果，让动画播放完
      setTimeout(() => {
        this.showResult = true
      }, 2000)
    },

    // 打开物品界面
    openItems() {
      // TODO: 实现物品使用界面
      this.showToast('物品功能开发中...')
    },

    // 切换日志显示
    toggleLog() {
      this.showLog = !this.showLog
      if (this.showLog) {
        this.loadBattleLog()
      }
    },

    // 加载战斗日志
    async loadBattleLog() {
      try {
        const result = await battleService.getBattleLog(this.battleId)
        if (result.success) {
          this.battleLogs = result.data.battle_log || []
        }
      } catch (error) {
        logger.error('[Battle] 加载战斗日志失败:', error)
      }
    },

    // 格式化日志消息
    formatLogMessage(log) {
      switch (log.action) {
        case 'battle_start':
          return '战斗开始！'
        case 'character_attack': {
          const damage = log.data.damage || 0
          const critical = log.data.is_critical ? '(暴击)' : ''
          return `你对${this.monster.name}造成了${damage}点伤害${critical}`
        }
        case 'monster_attack': {
          const monsterDamage = log.data.damage || 0
          const monsterCritical = log.data.is_critical ? '(暴击)' : ''
          return `${this.monster.name}对你造成了${monsterDamage}点伤害${monsterCritical}`
        }
        case 'battle_end':
          return `战斗结束，结果：${log.data.result}`
        default:
          return log.action
      }
    },

    // 关闭结果弹窗
    closeResult() {
      this.showResult = false
      this.returnToMain()
    },

    // 返回主界面
    returnToMain() {
      this.$router.push('/game/main')
    },

    // 显示提示
    showToast(message) {
      // 使用全局提示组件
      if (this.$toast) {
        this.$toast(message)
      } else {
        alert(message)
      }
    }
  }
}
</script>

<style scoped>
.battle-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 怪物信息区域 */
.monster-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: rgba(139, 69, 19, 0.3);
  border-bottom: 2px solid #8B4513;
}

.monster-info {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(0, 0, 0, 0.5);
  padding: 15px;
  border-radius: 10px;
  border: 2px solid #666;
}

.monster-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid #ff6b6b;
  object-fit: cover;
}

.monster-details h3 {
  margin: 0 0 10px 0;
  color: #ff6b6b;
  font-size: 18px;
}

/* 战斗动画区域 */
.battle-animation-area {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
}

/* 角色信息区域 */
.character-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: rgba(0, 100, 0, 0.2);
  border-top: 2px solid #228B22;
}

.character-info {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(0, 0, 0, 0.5);
  padding: 15px;
  border-radius: 10px;
  border: 2px solid #666;
}

.character-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid #87ceeb;
  object-fit: cover;
}

.character-details h3 {
  margin: 0 0 10px 0;
  color: #87ceeb;
  font-size: 18px;
}

/* 血条和蓝条样式 */
.hp-bar, .mp-bar {
  position: relative;
  width: 200px;
  height: 20px;
  background: #333;
  border: 1px solid #666;
  border-radius: 10px;
  margin: 5px 0;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  transition: width 0.5s ease;
  border-radius: 9px;
}

.hp-fill {
  background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%);
}

.mp-fill {
  background: linear-gradient(90deg, #4444ff 0%, #6666ff 100%);
}

.bar-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.5);
  border-top: 2px solid #666;
}

.action-btn {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: bold;
  border: 2px solid;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  min-width: 80px;
}

.attack-btn {
  border-color: #ff4444;
  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
}

.attack-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff6666 0%, #ff4444 100%);
  transform: translateY(-2px);
}

.item-btn {
  border-color: #44ff44;
  background: linear-gradient(135deg, #44ff44 0%, #33cc33 100%);
}

.item-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #66ff66 0%, #44ff44 100%);
  transform: translateY(-2px);
}

.flee-btn {
  border-color: #ffff44;
  background: linear-gradient(135deg, #ffff44 0%, #cccc33 100%);
  color: #333;
}

.flee-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffff66 0%, #ffff44 100%);
  transform: translateY(-2px);
}

.return-btn {
  border-color: #888;
  background: linear-gradient(135deg, #888 0%, #666 100%);
}

.return-btn:hover {
  background: linear-gradient(135deg, #aaa 0%, #888 100%);
  transform: translateY(-2px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 战斗日志 */
.battle-log {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-height: 300px;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid #666;
  border-radius: 10px;
  z-index: 1000;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid #666;
}

.log-header h4 {
  margin: 0;
  color: #fff;
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
}

.log-entry {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
  font-size: 14px;
}

.log-time {
  color: #888;
  min-width: 60px;
}

.log-message {
  color: #fff;
}

/* 战斗结果弹窗 */
.battle-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.result-content {
  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);
  border: 3px solid #666;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  min-width: 300px;
}

.result-content h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
}

.victory-text {
  color: #44ff44;
  text-shadow: 0 0 10px #44ff44;
}

.defeat-text {
  color: #ff4444;
  text-shadow: 0 0 10px #ff4444;
}

.flee-text {
  color: #ffff44;
  text-shadow: 0 0 10px #ffff44;
}

.rewards {
  margin: 20px 0;
  text-align: left;
}

.rewards p {
  margin: 8px 0;
  color: #fff;
}

.rewards ul {
  margin: 10px 0;
  padding-left: 20px;
}

.rewards li {
  color: #87ceeb;
  margin: 5px 0;
}

.confirm-btn {
  padding: 10px 30px;
  font-size: 16px;
  font-weight: bold;
  background: linear-gradient(135deg, #4444ff 0%, #3333cc 100%);
  color: #fff;
  border: 2px solid #4444ff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-btn:hover {
  background: linear-gradient(135deg, #6666ff 0%, #4444ff 100%);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monster-info, .character-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .monster-avatar, .character-avatar {
    width: 60px;
    height: 60px;
  }

  .hp-bar, .mp-bar {
    width: 150px;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 10px;
  }

  .action-btn {
    padding: 10px 20px;
    font-size: 14px;
    min-width: 70px;
  }

  .battle-log {
    width: 90%;
    max-width: 350px;
  }
}
</style>
