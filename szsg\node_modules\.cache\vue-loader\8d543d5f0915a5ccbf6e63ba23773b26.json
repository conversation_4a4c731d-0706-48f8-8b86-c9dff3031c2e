{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue?vue&type=style&index=0&id=1ac7a205&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue", "mtime": 1749703549914}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hdXRoLWRlYnVnIHsKICBwYWRkaW5nOiAyMHB4OwogIG1heC13aWR0aDogODAwcHg7CiAgbWFyZ2luOiAwIGF1dG87CiAgZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOwp9CgouZGVidWctc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKICBwYWRkaW5nOiAxNXB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogNXB4OwogIGJhY2tncm91bmQ6ICNmOWY5Zjk7Cn0KCi5kZWJ1Zy1zZWN0aW9uIGgzIHsKICBtYXJnaW4tdG9wOiAwOwogIGNvbG9yOiAjMzMzOwp9CgouZGVidWctaXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBwYWRkaW5nOiA1cHggMDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsKfQoKLmRlYnVnLWJ0biB7CiAgbWFyZ2luOiA1cHg7CiAgcGFkZGluZzogOHB4IDE2cHg7CiAgYmFja2dyb3VuZDogIzAwN2JmZjsKICBjb2xvcjogd2hpdGU7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjdXJzb3I6IHBvaW50ZXI7Cn0KCi5kZWJ1Zy1idG46aG92ZXIgewogIGJhY2tncm91bmQ6ICMwMDU2YjM7Cn0K"}, {"version": 3, "sources": ["AuthDebug.vue"], "names": [], "mappings": ";AA4HA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "AuthDebug.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"auth-debug\">\n    <h2>认证状态调试</h2>\n    \n    <div class=\"debug-section\">\n      <h3>Store 状态</h3>\n      <div class=\"debug-item\">\n        <strong>isAuthenticated:</strong> {{ storeAuth.isAuthenticated }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>token:</strong> {{ storeAuth.token ? '存在' : '不存在' }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>user:</strong> {{ storeAuth.user ? JSON.stringify(storeAuth.user) : '不存在' }}\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>LocalStorage</h3>\n      <div class=\"debug-item\">\n        <strong>authToken:</strong> {{ localStorage.authToken ? '存在' : '不存在' }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>token:</strong> {{ localStorage.token ? '存在' : '不存在' }}\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>操作</h3>\n      <button @click=\"clearAuth\" class=\"debug-btn\">清除认证状态</button>\n      <button @click=\"setTestAuth\" class=\"debug-btn\">设置测试认证</button>\n      <button @click=\"refreshPage\" class=\"debug-btn\">刷新页面</button>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>导航测试</h3>\n      <button @click=\"goToFriends\" class=\"debug-btn\">跳转到好友页面</button>\n      <button @click=\"goToLogin\" class=\"debug-btn\">跳转到登录页面</button>\n      <button @click=\"goToMain\" class=\"debug-btn\">跳转到游戏主页</button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      localStorage: {\n        authToken: null,\n        token: null\n      }\n    }\n  },\n  computed: {\n    storeAuth() {\n      return {\n        isAuthenticated: this.$store.state.auth?.isAuthenticated || false,\n        token: this.$store.state.auth?.token || null,\n        user: this.$store.state.auth?.user || null\n      }\n    }\n  },\n  created() {\n    this.updateLocalStorageInfo()\n  },\n  methods: {\n    updateLocalStorageInfo() {\n      this.localStorage.authToken = localStorage.getItem('authToken')\n      this.localStorage.token = localStorage.getItem('token')\n    },\n    \n    clearAuth() {\n      // 清除 store\n      this.$store.dispatch('auth/logout')\n      \n      // 清除 localStorage\n      localStorage.removeItem('authToken')\n      localStorage.removeItem('token')\n      \n      this.updateLocalStorageInfo()\n      alert('认证状态已清除')\n    },\n    \n    setTestAuth() {\n      const testToken = 'test-token-' + Date.now()\n      const testUser = {\n        id: 1,\n        name: 'TestUser',\n        username: 'testuser'\n      }\n      \n      // 设置到 store\n      this.$store.dispatch('auth/login', {\n        token: testToken,\n        userInfo: testUser\n      })\n      \n      // 设置到 localStorage\n      localStorage.setItem('authToken', testToken)\n      \n      this.updateLocalStorageInfo()\n      alert('测试认证状态已设置')\n    },\n    \n    refreshPage() {\n      window.location.reload()\n    },\n    \n    goToFriends() {\n      this.$router.push('/game/friends')\n    },\n    \n    goToLogin() {\n      this.$router.push('/login')\n    },\n    \n    goToMain() {\n      this.$router.push('/game/main')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.auth-debug {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n  font-family: Arial, sans-serif;\n}\n\n.debug-section {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  background: #f9f9f9;\n}\n\n.debug-section h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.debug-item {\n  margin-bottom: 10px;\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.debug-btn {\n  margin: 5px;\n  padding: 8px 16px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.debug-btn:hover {\n  background: #0056b3;\n}\n</style>\n"]}]}