<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\VipLevel;

class VipLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 清除现有数据
        VipLevel::truncate();

        // 创建VIP等级配置
        $vipLevels = [
            [
                'level' => 0,
                'exp_required' => 0,
                'reward' => '无',
                'privileges' => ['基础游戏功能']
            ],
            [
                'level' => 1,
                'exp_required' => 100,
                'reward' => '金砖x10，银两x1000，高级装备箱x1',
                'privileges' => ['每日签到奖励增加10%', '商城购买折扣5%']
            ],
            [
                'level' => 2,
                'exp_required' => 300,
                'reward' => '金砖x20，银两x2000，稀有武器箱x1',
                'privileges' => ['每日签到奖励增加20%', '商城购买折扣7%', '每日可额外领取50体力']
            ],
            [
                'level' => 3,
                'exp_required' => 500,
                'reward' => '金砖x30，银两x3000，传说装备箱x1',
                'privileges' => ['每日签到奖励增加50%', '商城购买折扣10%', '每日可额外领取100体力', '战斗经验增加15%']
            ],
            [
                'level' => 4,
                'exp_required' => 1000,
                'reward' => '金砖x50，银两x5000，神器碎片x10',
                'privileges' => ['每日签到奖励增加70%', '商城购买折扣12%', '每日可额外领取150体力', '战斗经验增加20%', '自动战斗功能']
            ],
            [
                'level' => 5,
                'exp_required' => 2000,
                'reward' => '金砖x100，银两x10000，神器碎片x20',
                'privileges' => ['每日签到奖励增加100%', '商城购买折扣15%', '每日可额外领取200体力', '战斗经验增加25%', '自动战斗功能', '每日免费抽奖次数+1']
            ],
            [
                'level' => 6,
                'exp_required' => 3500,
                'reward' => '金砖x150，银两x15000，神器碎片x30',
                'privileges' => ['每日签到奖励增加120%', '商城购买折扣18%', '每日可额外领取250体力', '战斗经验增加30%', '自动战斗功能', '每日免费抽奖次数+2', '专属时装']
            ],
            [
                'level' => 7,
                'exp_required' => 5000,
                'reward' => '金砖x200，银两x20000，神器碎片x40',
                'privileges' => ['每日签到奖励增加150%', '商城购买折扣20%', '每日可额外领取300体力', '战斗经验增加35%', '自动战斗功能', '每日免费抽奖次数+3', '专属时装', '专属坐骑']
            ],
            [
                'level' => 8,
                'exp_required' => 8000,
                'reward' => '金砖x300，银两x30000，神器碎片x50',
                'privileges' => ['每日签到奖励增加180%', '商城购买折扣25%', '每日可额外领取350体力', '战斗经验增加40%', '自动战斗功能', '每日免费抽奖次数+4', '专属时装', '专属坐骑', '专属称号']
            ],
            [
                'level' => 9,
                'exp_required' => 12000,
                'reward' => '金砖x400，银两x40000，神器碎片x60',
                'privileges' => ['每日签到奖励增加200%', '商城购买折扣30%', '每日可额外领取400体力', '战斗经验增加45%', '自动战斗功能', '每日免费抽奖次数+5', '专属时装', '专属坐骑', '专属称号', '专属技能']
            ],
            [
                'level' => 10,
                'exp_required' => 20000,
                'reward' => '金砖x500，银两x50000，神器碎片x100',
                'privileges' => ['每日签到奖励增加250%', '商城购买折扣35%', '每日可额外领取500体力', '战斗经验增加50%', '自动战斗功能', '每日免费抽奖次数+6', '专属时装', '专属坐骑', '专属称号', '专属技能', '专属宠物']
            ],
        ];

        foreach ($vipLevels as $vipLevel) {
            VipLevel::create($vipLevel);
        }
    }
}
