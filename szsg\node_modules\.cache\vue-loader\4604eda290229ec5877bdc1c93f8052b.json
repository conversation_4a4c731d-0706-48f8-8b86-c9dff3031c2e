{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Clinic.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Clinic.vue", "mtime": 1749792050849}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Clinic.vue"], "names": [], "mappings": ";AAmKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Clinic.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"clinic-page\">\r\n      <div class=\"clinic-container\">\r\n        <!-- 医馆标题 -->\r\n        <div class=\"clinic-header\">\r\n          <h1>医馆</h1>\r\n          <div class=\"character-status\">\r\n            <div class=\"status-box\">\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">气血:</div>\r\n                <div class=\"status-value\">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">精力:</div>\r\n                <div class=\"status-value\">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">银两:</div>\r\n                <div class=\"status-value silver-value\">{{ characterInfo.silver }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 选项卡导航 -->\r\n        <div class=\"tabs-container\">\r\n          <div \r\n            v-for=\"tab in tabs\" \r\n            :key=\"tab.id\" \r\n            class=\"tab\" \r\n            :class=\"{ 'active': activeTab === tab.id }\"\r\n            @click=\"activeTab = tab.id\"\r\n          >\r\n            <span class=\"tab-name\">{{ tab.name }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容区域 -->\r\n        <div class=\"content-area\">\r\n          <!-- 气血药品 -->\r\n          <div v-if=\"activeTab === 'health'\" class=\"tab-content\">\r\n            <div class=\"service-list\">\r\n              <div \r\n                v-for=\"potion in healthPotions\" \r\n                :key=\"potion.id\" \r\n                class=\"service-list-item\" \r\n                :class=\"{ 'disabled': !canUseService(potion) }\"\r\n              >\r\n                <div class=\"service-list-left\">\r\n                  <div class=\"service-list-name\">{{ potion.name }}</div>\r\n                  <div class=\"service-list-info\">\r\n                    <div class=\"service-list-description\">恢复 {{ potion.effect_value }} 气血</div>\r\n                    <div class=\"service-list-price\">价格: {{ potion.price }} 银两</div>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  class=\"service-list-button\"\r\n                  @click=\"purchasePotion(potion.id, 'health')\"\r\n                  :disabled=\"!canUseService(potion)\"\r\n                >\r\n                  购买\r\n                </button>\r\n              </div>\r\n              <div v-if=\"healthPotions.length === 0\" class=\"no-items\">\r\n                暂无可用药品\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 精力药品 -->\r\n          <div v-if=\"activeTab === 'mana'\" class=\"tab-content\">\r\n            <div class=\"service-list\">\r\n              <div \r\n                v-for=\"potion in manaPotions\" \r\n                :key=\"potion.id\" \r\n                class=\"service-list-item\" \r\n                :class=\"{ 'disabled': !canUseService(potion) }\"\r\n              >\r\n                <div class=\"service-list-left\">\r\n                  <div class=\"service-list-name\">{{ potion.name }}</div>\r\n                  <div class=\"service-list-info\">\r\n                    <div class=\"service-list-description\">恢复 {{ potion.effect_value }} 精力</div>\r\n                    <div class=\"service-list-price\">价格: {{ potion.price }} 银两</div>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  class=\"service-list-button\"\r\n                  @click=\"purchasePotion(potion.id, 'mana')\"\r\n                  :disabled=\"!canUseService(potion)\"\r\n                >\r\n                  购买\r\n                </button>\r\n              </div>\r\n              <div v-if=\"manaPotions.length === 0\" class=\"no-items\">\r\n                暂无可用药品\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 全员治疗 -->\r\n          <div v-if=\"activeTab === 'team'\" class=\"tab-content\">\r\n            <div class=\"service-list\">\r\n              <div \r\n                v-for=\"service in teamServices\" \r\n                :key=\"service.id\" \r\n                class=\"service-list-item\" \r\n                :class=\"{ 'disabled': !canUseService(service) }\"\r\n              >\r\n                <div class=\"service-list-left\">\r\n                  <div class=\"service-list-name\">{{ service.name }}</div>\r\n                  <div class=\"service-list-info\">\r\n                    <div class=\"service-list-description\">{{ service.description }}</div>\r\n                    <div class=\"service-list-price\">价格: {{ service.price }} 银两</div>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  class=\"service-list-button\"\r\n                  @click=\"purchaseTeamService(service.id)\"\r\n                  :disabled=\"!canUseService(service)\"\r\n                >\r\n                  购买\r\n                </button>\r\n              </div>\r\n              <div v-if=\"teamServices.length === 0\" class=\"no-items\">\r\n                暂无可用服务\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 返回按钮 -->\r\n        <div class=\"bottom-actions\">\r\n          <button class=\"back-button\" @click=\"$router.push('/game/main')\">返回城镇</button>\r\n        </div>\r\n\r\n        <!-- 购买结果弹窗 -->\r\n        <div v-if=\"showResult\" class=\"result-modal\">\r\n          <div class=\"result-content\" :class=\"{ 'error': resultError }\">\r\n            <h3>{{ resultError ? '购买失败' : '购买成功' }}</h3>\r\n            <p>{{ resultMessage }}</p>\r\n            <div v-if=\"healResult && !resultError\" class=\"heal-result\">\r\n              <div v-if=\"healResult.hpHealed\">恢复气血: +{{ healResult.hpHealed }}</div>\r\n              <div v-if=\"healResult.mpHealed\">恢复精力: +{{ healResult.mpHealed }}</div>\r\n            </div>\r\n            <button @click=\"showResult = false\">确定</button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 加载中和错误提示 -->\r\n        <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n          <div class=\"loading-spinner\"></div>\r\n          <div>加载中...</div>\r\n        </div>\r\n        <div v-if=\"error\" class=\"error-message\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nimport clinicService from '@/api/services/clinicService';\r\nimport { showMessage } from '@/utils/message';\r\nimport logger from '@/utils/logger';\r\nimport { getCurrentCharacter } from '@/api/services/characterService';\r\n\r\nexport default {\r\n  name: 'Clinic',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      isLoading: true,\r\n      error: null,\r\n      activeTab: 'health', // 默认选中气血药品选项卡\r\n      tabs: [\r\n        {\r\n          id: 'health',\r\n          name: '气血药品'\r\n        },\r\n        {\r\n          id: 'mana',\r\n          name: '精力药品'\r\n        },\r\n        {\r\n          id: 'team',\r\n          name: '完全恢复'\r\n        }\r\n      ],\r\n      healthPotions: [],\r\n      manaPotions: [],\r\n      teamServices: [],\r\n      characterInfo: {\r\n        id: null,\r\n        level: 1,\r\n        hp: 100,\r\n        maxHp: 100,\r\n        mp: 50,\r\n        maxMp: 50,\r\n        silver: 0,\r\n        gold: 0\r\n      },\r\n      showResult: false,\r\n      resultMessage: '',\r\n      resultError: false,\r\n      healResult: null,\r\n      touchStartY: 0,\r\n      touchMoveY: 0,\r\n      scrolling: false\r\n    };\r\n  },\r\n  computed: {\r\n    hpPercent() {\r\n      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100;\r\n    },\r\n    mpPercent() {\r\n      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100;\r\n    }\r\n  },\r\n  created() {\r\n    this.initCharacterInfo();\r\n    this.loadAllServices();\r\n  },\r\n  mounted() {\r\n    // 添加触摸事件监听\r\n    this.addTouchListeners();\r\n  },\r\n  beforeDestroy() {\r\n    // 移除触摸事件监听\r\n    this.removeTouchListeners();\r\n  },\r\n  methods: {\r\n    // 添加触摸事件监听\r\n    addTouchListeners() {\r\n      const serviceList = this.$el.querySelector('.service-list');\r\n      const clinicPage = this.$el.querySelector('.clinic-page');\r\n      \r\n      if (serviceList) {\r\n        serviceList.addEventListener('touchstart', this.handleTouchStart, { passive: true });\r\n        serviceList.addEventListener('touchmove', this.handleTouchMove, { passive: false });\r\n        serviceList.addEventListener('touchend', this.handleTouchEnd, { passive: true });\r\n      }\r\n      \r\n      if (clinicPage) {\r\n        clinicPage.addEventListener('touchstart', this.handleTouchStart, { passive: true });\r\n        clinicPage.addEventListener('touchmove', this.handleTouchMove, { passive: false });\r\n        clinicPage.addEventListener('touchend', this.handleTouchEnd, { passive: true });\r\n      }\r\n    },\r\n    // 移除触摸事件监听\r\n    removeTouchListeners() {\r\n      const serviceList = this.$el.querySelector('.service-list');\r\n      const clinicPage = this.$el.querySelector('.clinic-page');\r\n      \r\n      if (serviceList) {\r\n        serviceList.removeEventListener('touchstart', this.handleTouchStart);\r\n        serviceList.removeEventListener('touchmove', this.handleTouchMove);\r\n        serviceList.removeEventListener('touchend', this.handleTouchEnd);\r\n      }\r\n      \r\n      if (clinicPage) {\r\n        clinicPage.removeEventListener('touchstart', this.handleTouchStart);\r\n        clinicPage.removeEventListener('touchmove', this.handleTouchMove);\r\n        clinicPage.removeEventListener('touchend', this.handleTouchEnd);\r\n      }\r\n    },\r\n    // 处理触摸开始事件\r\n    handleTouchStart(event) {\r\n      this.touchStartY = event.touches[0].clientY;\r\n      this.scrolling = false;\r\n    },\r\n    // 处理触摸移动事件\r\n    handleTouchMove(event) {\r\n      this.touchMoveY = event.touches[0].clientY;\r\n      const deltaY = this.touchStartY - this.touchMoveY;\r\n      \r\n      // 判断是否是垂直滚动\r\n      if (Math.abs(deltaY) > 10) {\r\n        this.scrolling = true;\r\n      }\r\n      \r\n      // 如果是垂直滚动，不阻止默认行为\r\n      if (this.scrolling) {\r\n        return;\r\n      }\r\n      \r\n      // 对于非垂直滚动的触摸移动，阻止默认行为以防止页面整体滚动\r\n      event.preventDefault();\r\n    },\r\n    // 处理触摸结束事件\r\n    handleTouchEnd() {\r\n      this.touchStartY = 0;\r\n      this.touchMoveY = 0;\r\n      this.scrolling = false;\r\n    },\r\n    initCharacterInfo() {\r\n      // 获取当前角色信息\r\n      const character = getCurrentCharacter();\r\n      if (character) {\r\n        this.characterInfo.id = character.id;\r\n        \r\n        // 从Vuex获取更详细的角色信息\r\n        const characterStatus = this.$store.state.character.characterStatus;\r\n        if (characterStatus) {\r\n          this.characterInfo = {\r\n            ...this.characterInfo,\r\n            level: characterStatus.level || 1,\r\n            hp: characterStatus.hp || 100,\r\n            maxHp: characterStatus.maxHp || 100,\r\n            mp: characterStatus.mp || 50,\r\n            maxMp: characterStatus.maxMp || 50,\r\n            silver: characterStatus.silver || 0,\r\n            gold: characterStatus.gold || 0\r\n          };\r\n        }\r\n      } else {\r\n        this.error = '未找到角色信息，请先选择角色';\r\n        showMessage('未找到角色信息，请先选择角色', 'error');\r\n        this.$router.push('/setup/character-select');\r\n      }\r\n    },\r\n    async loadAllServices() {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      \r\n      try {\r\n        // 加载服务类型\r\n        await this.loadServiceTypes();\r\n        \r\n        // 加载各类药品和服务\r\n        await Promise.all([\r\n          this.loadHealthPotions(),\r\n          this.loadManaPotions(),\r\n          this.loadTeamServices()\r\n        ]);\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载服务失败:', error);\r\n        this.error = '加载服务失败，请重试';\r\n        this.isLoading = false;\r\n        showMessage('加载服务失败，请重试', 'error');\r\n      }\r\n    },\r\n    async loadServiceTypes() {\r\n      try {\r\n        const serviceTypes = await clinicService.getServiceTypes();\r\n        if (serviceTypes && serviceTypes.length > 0) {\r\n          this.tabs = serviceTypes.map(type => ({\r\n            id: type.id,\r\n            name: type.name\r\n          }));\r\n          logger.debug('[医馆] 服务类型加载成功:', this.tabs);\r\n        }\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载服务类型失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    async loadHealthPotions() {\r\n      try {\r\n        const healthPotions = await clinicService.getHealthPotions();\r\n        this.healthPotions = healthPotions || [];\r\n        logger.debug('[医馆] 气血药品加载成功:', this.healthPotions);\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载气血药品失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    async loadManaPotions() {\r\n      try {\r\n        const manaPotions = await clinicService.getManaPotions();\r\n        this.manaPotions = manaPotions || [];\r\n        logger.debug('[医馆] 精力药品加载成功:', this.manaPotions);\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载精力药品失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    async loadTeamServices() {\r\n      try {\r\n        const teamServices = await clinicService.getTeamServices();\r\n        this.teamServices = teamServices || [];\r\n        logger.debug('[医馆] 全员治疗服务加载成功:', this.teamServices);\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载全员治疗服务失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    canUseService(service) {\r\n      // 检查角色是否有足够的银两\r\n      if (this.characterInfo.silver < service.price) {\r\n        return false;\r\n      }\r\n      \r\n      // 对于气血药品，检查角色是否已经满血\r\n      if (service.type === 'health' && this.characterInfo.hp >= this.characterInfo.maxHp) {\r\n        return false;\r\n      }\r\n      \r\n      // 对于精力药品，检查角色是否已经满精力\r\n      if (service.type === 'mana' && this.characterInfo.mp >= this.characterInfo.maxMp) {\r\n        return false;\r\n      }\r\n      \r\n      // 对于全员治疗，检查角色是否已经满血和满精力\r\n      if (service.type === 'team' && \r\n          this.characterInfo.hp >= this.characterInfo.maxHp && \r\n          this.characterInfo.mp >= this.characterInfo.maxMp) {\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    },\r\n    async purchasePotion(potionId, type) {\r\n      if (!this.characterInfo.id) {\r\n        showMessage('未找到角色信息', 'error');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        const response = await clinicService.purchasePotion(this.characterInfo.id, potionId, type);\r\n        logger.debug('[医馆] 购买药品成功:', response);\r\n        \r\n        // 更新角色状态\r\n        if (response.character) {\r\n          this.characterInfo.hp = response.character.hp;\r\n          this.characterInfo.mp = response.character.mp;\r\n          this.characterInfo.silver = response.character.silver;\r\n          this.characterInfo.gold = response.character.gold;\r\n          \r\n          // 更新Vuex中的角色状态\r\n          this.$store.commit('character/updateCharacterStatus', {\r\n            hp: response.character.hp,\r\n            mp: response.character.mp,\r\n            silver: response.character.silver,\r\n            gold: response.character.gold\r\n          });\r\n        }\r\n        \r\n        // 显示结果\r\n        this.resultMessage = response.message || '购买成功';\r\n        this.resultError = false;\r\n        this.healResult = {\r\n          hpHealed: response.hp_recovered,\r\n          mpHealed: response.mp_recovered\r\n        };\r\n        this.showResult = true;\r\n        \r\n        // 刷新药品列表\r\n        if (type === 'health') {\r\n          this.loadHealthPotions();\r\n        } else if (type === 'mana') {\r\n          this.loadManaPotions();\r\n        }\r\n      } catch (error) {\r\n        logger.error('[医馆] 购买药品失败:', error);\r\n        this.resultMessage = error.message || '购买失败';\r\n        this.resultError = true;\r\n        this.healResult = null;\r\n        this.showResult = true;\r\n      }\r\n    },\r\n    async purchaseTeamService(serviceId) {\r\n      if (!this.characterInfo.id) {\r\n        showMessage('未找到角色信息', 'error');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        const response = await clinicService.useTeamService(this.characterInfo.id, serviceId);\r\n        logger.debug('[医馆] 使用全员治疗成功:', response);\r\n        \r\n        // 更新角色状态\r\n        if (response.character) {\r\n          this.characterInfo.hp = response.character.hp;\r\n          this.characterInfo.mp = response.character.mp;\r\n          this.characterInfo.silver = response.character.silver;\r\n          this.characterInfo.gold = response.character.gold;\r\n          \r\n          // 更新Vuex中的角色状态\r\n          this.$store.commit('character/updateCharacterStatus', {\r\n            hp: response.character.hp,\r\n            mp: response.character.mp,\r\n            silver: response.character.silver,\r\n            gold: response.character.gold\r\n          });\r\n        }\r\n        \r\n        // 显示结果\r\n        this.resultMessage = response.message || '治疗成功';\r\n        this.resultError = false;\r\n        this.healResult = {\r\n          hpHealed: response.hp_recovered,\r\n          mpHealed: response.mp_recovered\r\n        };\r\n        this.showResult = true;\r\n        \r\n        // 刷新全员治疗服务列表\r\n        this.loadTeamServices();\r\n      } catch (error) {\r\n        logger.error('[医馆] 使用全员治疗失败:', error);\r\n        this.resultMessage = error.message || '治疗失败';\r\n        this.resultError = true;\r\n        this.healResult = null;\r\n        this.showResult = true;\r\n      }\r\n    },\r\n    closeResult() {\r\n      this.showResult = false;\r\n      this.healResult = null;\r\n    },\r\n    goBack() {\r\n      this.$router.push('/game/main');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.clinic-page {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background-color: #000033;\r\n  color: #ffffff;\r\n  padding: 8px;\r\n  position: relative;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  max-height: 100vh;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.clinic-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  width: 100%;\r\n  padding-bottom: 65px;\r\n}\r\n\r\n.clinic-header {\r\n  margin-bottom: 15px;\r\n  padding: 12px;\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.clinic-header h1 {\r\n  margin: 0 0 10px 0;\r\n  text-align: center;\r\n  color: #ffcc00;\r\n  font-size: 22px;\r\n  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);\r\n}\r\n\r\n.character-status {\r\n  margin-top: 10px;\r\n}\r\n\r\n.status-box {\r\n  background-color: rgba(153, 0, 0, 0.2);\r\n  border: 1px solid rgba(153, 0, 0, 0.5);\r\n  border-radius: 5px;\r\n  padding: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n  padding: 3px 5px;\r\n}\r\n\r\n.status-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.status-label {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-value {\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.silver-value {\r\n  color: #ffcc00;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.tab {\r\n  flex: 1;\r\n  padding: 10px 8px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  border-bottom: 3px solid transparent;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.tab.active {\r\n  border-bottom-color: #ffcc00;\r\n  background-color: rgba(255, 204, 0, 0.1);\r\n}\r\n\r\n.tab-name {\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n}\r\n\r\n.tab.active .tab-name {\r\n  color: #ffcc00;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.tab-content {\r\n  padding: 5px 0;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  max-height: calc(70vh - 180px);\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n  padding-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.service-list-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 12px;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.service-list-item:hover {\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-color: #3333cc;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.service-list-item.disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.service-list-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 75%;\r\n}\r\n\r\n.service-list-name {\r\n  font-weight: bold;\r\n  color: #ffcc00;\r\n  font-size: 16px;\r\n  text-shadow: 0 0 3px rgba(255, 204, 0, 0.3);\r\n}\r\n\r\n.service-list-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n.service-list-description {\r\n  font-size: 14px;\r\n  color: #aaaaff;\r\n}\r\n\r\n.service-list-price {\r\n  color: #ffcc00;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-left: 10px;\r\n  background-color: rgba(153, 0, 0, 0.3);\r\n  border: 1px solid rgba(153, 0, 0, 0.7);\r\n  border-radius: 4px;\r\n  padding: 2px 6px;\r\n}\r\n\r\n.service-list-right {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 30%;\r\n}\r\n\r\n.service-list-button {\r\n  padding: 6px 15px;\r\n  background-color: #990000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: bold;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  width: 60px;\r\n}\r\n\r\n.service-list-button:hover:not(:disabled) {\r\n  background-color: #cc0000;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.service-list-button:active:not(:disabled) {\r\n  transform: translateY(0);\r\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.service-list-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n  box-shadow: none;\r\n}\r\n\r\n.gold {\r\n  color: #ffcc00;\r\n}\r\n\r\n.silver {\r\n  color: #cccccc;\r\n}\r\n\r\n.service-list-btn {\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  padding: 6px 14px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.service-list-btn:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.service-list-btn:disabled {\r\n  background-color: #222255;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.bottom-actions {\r\n  position: fixed;\r\n  bottom: 15px;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 12px 10px;\r\n  z-index: 100;\r\n  background: linear-gradient(to top, rgba(0, 0, 51, 0.95) 0%, rgba(0, 0, 51, 0.8) 50%, rgba(0, 0, 51, 0) 100%);\r\n  padding-top: 25px;\r\n}\r\n\r\n.back-button {\r\n  padding: 10px 25px;\r\n  background-color: #cc3333;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #dd4444;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.back-button:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.result-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.result-content {\r\n  background-color: #000033;\r\n  border: 2px solid #3333cc;\r\n  border-radius: 6px;\r\n  width: 80%;\r\n  max-width: 400px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #3333cc;\r\n}\r\n\r\n.result-header h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  color: #ffcc00;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #aaaaff;\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n}\r\n\r\n.result-body {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.result-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 30px;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n.result-icon.success {\r\n  background-color: #33cc33;\r\n  color: white;\r\n}\r\n\r\n.result-icon.error {\r\n  background-color: #cc3333;\r\n  color: white;\r\n}\r\n\r\n.result-message {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.result-details {\r\n  text-align: left;\r\n  margin-top: 15px;\r\n  border-top: 1px solid #3333cc;\r\n  padding-top: 15px;\r\n}\r\n\r\n.result-item {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.result-label {\r\n  color: #aaaaff;\r\n  display: inline-block;\r\n  width: 100px;\r\n}\r\n\r\n.result-value {\r\n  font-weight: bold;\r\n}\r\n\r\n.result-footer {\r\n  padding: 10px 15px;\r\n  border-top: 1px solid #3333cc;\r\n  text-align: center;\r\n}\r\n\r\n.confirm-btn {\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.confirm-btn:hover {\r\n  background-color: #4444dd;\r\n}\r\n\r\n@media (max-width: 600px) {\r\n  .service-list-item {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .service-list-left {\r\n    width: 100%;\r\n  }\r\n  \r\n  .service-list-right {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .service-list-details {\r\n    text-align: left;\r\n  }\r\n  \r\n  .status-label {\r\n    width: 50px;\r\n  }\r\n  \r\n  .status-bar {\r\n    width: calc(100% - 60px);\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.service-list::-webkit-scrollbar {\r\n  width: 5px;\r\n}\r\n\r\n.service-list::-webkit-scrollbar-track {\r\n  background: rgba(0, 0, 51, 0.3);\r\n  border-radius: 10px;\r\n}\r\n\r\n.service-list::-webkit-scrollbar-thumb {\r\n  background: #3333cc;\r\n  border-radius: 10px;\r\n}\r\n\r\n.service-list::-webkit-scrollbar-thumb:hover {\r\n  background: #4444dd;\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 480px) {\r\n  .clinic-page {\r\n    padding: 5px;\r\n  }\r\n  \r\n  .clinic-header h1 {\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .tab {\r\n    padding: 8px 5px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .tab-icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n  \r\n  .service-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .service-description, .service-price {\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .buy-button {\r\n    padding: 6px 10px;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .bottom-actions {\r\n    bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 确保内容区域可以滚动 */\r\n.content-area {\r\n  flex: 1;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n</style> "]}]}