<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('items', function (Blueprint $table) {
            // 添加新的字段
            $table->string('model')->nullable()->comment('3D模型路径')->after('icon');
            $table->string('subtype')->default('none')->comment('物品子类型')->after('type');
            $table->integer('level_requirement')->default(1)->comment('等级需求')->after('quality');
            $table->enum('profession_requirement', ['none', 'warrior', 'mage', 'archer', 'monk', 'all'])->default('none')->comment('职业需求')->after('level_requirement');

            // 属性加成字段
            $table->integer('attack_bonus')->default(0)->comment('攻击力加成')->after('profession_requirement');
            $table->integer('defense_bonus')->default(0)->comment('防御力加成')->after('attack_bonus');
            $table->integer('magic_attack_bonus')->default(0)->comment('法术攻击加成')->after('defense_bonus');
            $table->integer('magic_defense_bonus')->default(0)->comment('法术防御加成')->after('magic_attack_bonus');
            $table->integer('health_bonus')->default(0)->comment('生命值加成')->after('magic_defense_bonus');
            $table->integer('mana_bonus')->default(0)->comment('法力值加成')->after('health_bonus');
            $table->integer('speed_bonus')->default(0)->comment('速度加成')->after('mana_bonus');
            $table->integer('accuracy_bonus')->default(0)->comment('命中率加成')->after('speed_bonus');
            $table->integer('dodge_bonus')->default(0)->comment('闪避率加成')->after('accuracy_bonus');
            $table->integer('critical_bonus')->default(0)->comment('暴击率加成')->after('dodge_bonus');

            // 特殊属性
            $table->json('special_effects')->nullable()->comment('特殊效果')->after('critical_bonus');
            $table->json('set_bonus')->nullable()->comment('套装效果')->after('special_effects');
            $table->string('element')->nullable()->comment('元素属性')->after('set_bonus');
            $table->json('skills')->nullable()->comment('附带技能')->after('element');

            // 使用和交易属性
            $table->boolean('is_consumable')->default(false)->comment('是否为消耗品')->after('skills');
            $table->boolean('is_stackable')->default(false)->comment('是否可堆叠')->after('is_consumable');
            $table->boolean('is_tradeable')->default(true)->comment('是否可交易')->after('max_stack');
            $table->boolean('is_droppable')->default(true)->comment('是否可丢弃')->after('is_tradeable');
            $table->boolean('is_sellable')->default(true)->comment('是否可出售')->after('is_droppable');

            // 价格和稀有度
            $table->integer('buy_price')->default(0)->comment('购买价格')->after('is_sellable');
            $table->integer('sell_price')->default(0)->comment('出售价格')->after('buy_price');
            $table->decimal('drop_rate', 5, 2)->default(1.00)->comment('掉落率(%)')->after('sell_price');
            $table->integer('durability')->default(100)->comment('耐久度')->after('drop_rate');
            $table->integer('max_durability')->default(100)->comment('最大耐久度')->after('durability');

            // 制作和强化
            $table->boolean('is_craftable')->default(false)->comment('是否可制作')->after('max_durability');
            $table->json('craft_materials')->nullable()->comment('制作材料')->after('is_craftable');
            $table->boolean('is_enhanceable')->default(false)->comment('是否可强化')->after('craft_materials');
            $table->integer('max_enhance_level')->default(0)->comment('最大强化等级')->after('is_enhanceable');
            $table->json('enhance_materials')->nullable()->comment('强化材料')->after('max_enhance_level');

            // 使用效果(消耗品)
            $table->integer('health_restore')->default(0)->comment('恢复生命值')->after('enhance_materials');
            $table->integer('mana_restore')->default(0)->comment('恢复法力值')->after('health_restore');
            $table->json('buff_effects')->nullable()->comment('增益效果')->after('mana_restore');
            $table->integer('effect_duration')->default(0)->comment('效果持续时间(秒)')->after('buff_effects');

            // 获取方式
            $table->json('obtain_methods')->nullable()->comment('获取方式')->after('effect_duration');
            $table->json('drop_monsters')->nullable()->comment('掉落怪物ID列表')->after('obtain_methods');
            $table->json('sold_by_npcs')->nullable()->comment('出售NPC ID列表')->after('drop_monsters');

            // 状态
            $table->boolean('is_active')->default(true)->comment('是否激活')->after('sold_by_npcs');
            $table->boolean('is_rare')->default(false)->comment('是否稀有')->after('is_active');
            $table->timestamp('available_from')->nullable()->comment('可用开始时间')->after('is_rare');
            $table->timestamp('available_until')->nullable()->comment('可用结束时间')->after('available_from');

            // 修改现有字段
            $table->dropColumn('base_price');

            // 修改type字段的枚举值
            $table->dropColumn('type');
        });

        // 重新添加type字段，因为Laravel不支持直接修改枚举值
        Schema::table('items', function (Blueprint $table) {
            $table->enum('type', [
                'weapon', 'armor', 'accessory', 'consumable', 'material',
                'quest', 'treasure', 'book', 'pill', 'talisman', 'other'
            ])->default('other')->comment('物品类型')->after('model');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('items', function (Blueprint $table) {
            // 删除新添加的字段
            $table->dropColumn([
                'model', 'subtype', 'level_requirement', 'profession_requirement',
                'attack_bonus', 'defense_bonus', 'magic_attack_bonus', 'magic_defense_bonus',
                'health_bonus', 'mana_bonus', 'speed_bonus', 'accuracy_bonus', 'dodge_bonus', 'critical_bonus',
                'special_effects', 'set_bonus', 'element', 'skills',
                'is_consumable', 'is_stackable', 'is_tradeable', 'is_droppable', 'is_sellable',
                'buy_price', 'sell_price', 'drop_rate', 'durability', 'max_durability',
                'is_craftable', 'craft_materials', 'is_enhanceable', 'max_enhance_level', 'enhance_materials',
                'health_restore', 'mana_restore', 'buff_effects', 'effect_duration',
                'obtain_methods', 'drop_monsters', 'sold_by_npcs',
                'is_active', 'is_rare', 'available_from', 'available_until'
            ]);

            // 恢复原来的字段
            $table->dropColumn('type');
            $table->enum('type', ['equipment', 'consumable', 'material', 'quest'])->after('description');
            $table->integer('base_price')->default(0)->after('max_stack');
        });
    }
};
