<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BankTransaction extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'character_id',
        'type',
        'currency',
        'amount',
        'balance',
        'description',
    ];

    /**
     * 获取拥有该交易记录的角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 获取该交易记录所属的银行账户
     */
    public function bankAccount()
    {
        return $this->belongsTo(BankAccount::class, 'character_id', 'character_id');
    }
}
