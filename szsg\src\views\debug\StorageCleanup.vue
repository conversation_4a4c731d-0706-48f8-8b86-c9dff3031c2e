<template>
  <div class="storage-cleanup">
    <div class="header">
      <h2>存储空间清理</h2>
      <p>如果遇到"请求头过大"错误，可以使用此工具清理存储空间</p>
    </div>

    <div class="storage-info">
      <h3>当前存储状态</h3>
      <div class="info-item">
        <span>总存储大小:</span>
        <span>{{ formatBytes(storageInfo.currentSize) }}</span>
      </div>
      <div class="info-item">
        <span>存储项目数量:</span>
        <span>{{ storageInfo.keys.length }}</span>
      </div>
    </div>

    <div class="storage-items">
      <h3>存储项目详情</h3>
      <div class="item-list">
        <div 
          v-for="item in storageItems" 
          :key="item.key"
          class="storage-item"
          :class="{ 'large-item': item.size > 50000 }"
        >
          <div class="item-info">
            <span class="item-key">{{ item.key }}</span>
            <span class="item-size">{{ formatBytes(item.size) }}</span>
          </div>
          <button 
            @click="removeItem(item.key)"
            class="remove-btn"
            :disabled="item.key === 'szxy-game-state'"
          >
            删除
          </button>
        </div>
      </div>
    </div>

    <div class="actions">
      <button @click="clearExpiredCache" class="action-btn">清理过期缓存</button>
      <button @click="clearAllCache" class="action-btn warning">清理所有缓存</button>
      <button @click="clearAllStorage" class="action-btn danger">清空所有存储</button>
      <button @click="refreshInfo" class="action-btn">刷新信息</button>
    </div>

    <div class="tips">
      <h3>使用提示</h3>
      <ul>
        <li>如果遇到HTTP 431错误，建议先点击"清理过期缓存"</li>
        <li>红色标记的项目是大型存储项目（>50KB），可能导致请求头过大</li>
        <li>"szxy-game-state"是游戏核心状态，不建议删除</li>
        <li>清理后需要重新登录和选择大区</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getStorageInfoSync, clearExpiredCache, cleanupLargeStorageItems } from '@/utils/storage.js'

export default {
  name: 'StorageCleanup',
  data() {
    return {
      storageInfo: {
        keys: [],
        currentSize: 0
      },
      storageItems: []
    }
  },
  mounted() {
    this.refreshInfo()
  },
  methods: {
    refreshInfo() {
      this.storageInfo = getStorageInfoSync()
      this.storageItems = this.getStorageItems()
    },

    getStorageItems() {
      const items = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        const value = localStorage.getItem(key) || ''
        items.push({
          key,
          size: key.length + value.length,
          value: value.substring(0, 100) + (value.length > 100 ? '...' : '')
        })
      }
      return items.sort((a, b) => b.size - a.size)
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    removeItem(key) {
      if (confirm(`确定要删除存储项目 "${key}" 吗？`)) {
        localStorage.removeItem(key)
        this.refreshInfo()
        this.$message?.success?.('删除成功')
      }
    },

    clearExpiredCache() {
      try {
        clearExpiredCache()
        this.refreshInfo()
        this.$message?.success?.('过期缓存清理完成')
      } catch (error) {
        this.$message?.error?.('清理失败: ' + error.message)
      }
    },

    clearAllCache() {
      if (confirm('确定要清理所有缓存吗？这将删除所有以"SZXY_CACHE_"开头的存储项目。')) {
        try {
          const keys = []
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i)
            if (key && key.startsWith('SZXY_CACHE_')) {
              keys.push(key)
            }
          }
          keys.forEach(key => localStorage.removeItem(key))
          this.refreshInfo()
          this.$message?.success?.(`清理了 ${keys.length} 个缓存项目`)
        } catch (error) {
          this.$message?.error?.('清理失败: ' + error.message)
        }
      }
    },

    clearAllStorage() {
      if (confirm('确定要清空所有本地存储吗？这将删除所有游戏数据，需要重新登录！')) {
        if (confirm('再次确认：这将删除所有本地数据，包括登录状态、游戏设置等！')) {
          localStorage.clear()
          this.refreshInfo()
          this.$message?.success?.('所有存储已清空')
          // 3秒后刷新页面
          setTimeout(() => {
            window.location.reload()
          }, 3000)
        }
      }
    }
  }
}
</script>

<style scoped>
.storage-cleanup {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 14px;
}

.storage-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.storage-info h3 {
  margin-bottom: 15px;
  color: #333;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.storage-items {
  margin-bottom: 30px;
}

.storage-items h3 {
  margin-bottom: 15px;
  color: #333;
}

.item-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.storage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.storage-item.large-item {
  background-color: #fff5f5;
  border-left: 4px solid #ff4757;
}

.item-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  margin-right: 15px;
}

.item-key {
  font-weight: bold;
  color: #333;
}

.item-size {
  color: #666;
  font-size: 12px;
}

.remove-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.remove-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  background: #007bff;
  color: white;
}

.action-btn.warning {
  background: #ffc107;
  color: #333;
}

.action-btn.danger {
  background: #dc3545;
}

.action-btn:hover {
  opacity: 0.8;
}

.tips {
  background: #e7f3ff;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.tips h3 {
  margin-bottom: 10px;
  color: #333;
}

.tips ul {
  margin: 0;
  padding-left: 20px;
}

.tips li {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}
</style>
