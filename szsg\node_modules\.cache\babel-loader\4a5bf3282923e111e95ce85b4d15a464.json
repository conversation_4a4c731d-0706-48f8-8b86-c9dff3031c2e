{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue?vue&type=template&id=524b12ae&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue", "mtime": 1749699239704}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm$currentRegion", "_vm$currentRegion2", "_vm", "_c", "_self", "staticClass", "_v", "currentRegion", "_s", "name", "_e", "$route", "query", "debug", "isLoading", "error", "characters", "length", "id", "JSON", "stringify", "on", "click", "loadCharacterList", "_l", "character", "_vm$selectedCharacter", "key", "class", "selected", "<PERSON><PERSON><PERSON><PERSON>", "$event", "selectCharacterLocal", "attrs", "src", "avatar", "alt", "level", "getClassName", "profession", "experience", "exp", "formatDate", "created_at", "createdAt", "createNewCharacter", "pressed", "isBackPressed", "goBack", "mousedown", "handleBackMouseDown", "mouseup", "handleBackMouseUp", "mouseleave", "getBackButtonImage", "draggable", "isCreatePressed", "handleCreateMouseDown", "handleCreateMouseUp", "getCreateButtonImage", "disabled", "isEnterPressed", "confirmSelection", "handleEnterMouseDown", "handleEnterMouseUp", "getEnterButtonImage", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/views/setup/CharacterSelect.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"GameLayout\", [\n    _c(\"div\", { staticClass: \"character-select-container\" }, [\n      _c(\"div\", { staticClass: \"page-header\" }, [\n        _c(\"p\", { staticClass: \"page-subtitle\" }, [\n          _vm._v(\"请选择您要使用的角色，或创建新角色\"),\n        ]),\n        _vm.currentRegion\n          ? _c(\"div\", { staticClass: \"current-region\" }, [\n              _vm._v(\" 当前大区: \"),\n              _c(\"span\", { staticClass: \"region-name\" }, [\n                _vm._v(_vm._s(_vm.currentRegion.name)),\n              ]),\n            ])\n          : _vm._e(),\n        _vm.$route.query.debug\n          ? _c(\"div\", { staticClass: \"debug-info\" }, [\n              _c(\"h4\", [_vm._v(\"调试信息:\")]),\n              _c(\"p\", [_vm._v(\"加载状态: \" + _vm._s(_vm.isLoading))]),\n              _c(\"p\", [_vm._v(\"错误信息: \" + _vm._s(_vm.error || \"无\"))]),\n              _c(\"p\", [_vm._v(\"角色数量: \" + _vm._s(_vm.characters.length))]),\n              _c(\"p\", [_vm._v(\"当前大区ID: \" + _vm._s(_vm.currentRegion?.id))]),\n              _c(\"details\", [\n                _c(\"summary\", [_vm._v(\"角色数据\")]),\n                _c(\"pre\", [\n                  _vm._v(_vm._s(JSON.stringify(_vm.characters, null, 2))),\n                ]),\n              ]),\n            ])\n          : _vm._e(),\n      ]),\n      _vm.isLoading\n        ? _c(\"div\", { staticClass: \"loading-container\" }, [\n            _c(\"div\", { staticClass: \"loading-spinner\" }),\n            _c(\"p\", { staticClass: \"loading-text\" }, [\n              _vm._v(\"正在加载角色列表...\"),\n            ]),\n          ])\n        : _vm._e(),\n      _vm.error && !_vm.isLoading\n        ? _c(\"div\", { staticClass: \"error-container\" }, [\n            _c(\"div\", { staticClass: \"error-message\" }, [\n              _c(\"i\", { staticClass: \"error-icon\" }, [_vm._v(\"⚠️\")]),\n              _c(\"p\", [_vm._v(_vm._s(_vm.error))]),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"retry-btn\",\n                  on: { click: _vm.loadCharacterList },\n                },\n                [_vm._v(\"重试\")]\n              ),\n            ]),\n          ])\n        : _vm._e(),\n      !_vm.isLoading && !_vm.error\n        ? _c(\"div\", { staticClass: \"characters-container\" }, [\n            _vm.characters.length === 0\n              ? _c(\"div\", { staticClass: \"empty-state\" }, [\n                  _c(\"div\", { staticClass: \"empty-icon\" }, [_vm._v(\"👤\")]),\n                  _c(\"h3\", [_vm._v(\"还没有角色\")]),\n                  _c(\"p\", [\n                    _vm._v(\n                      '在当前大区 \"' +\n                        _vm._s(_vm.currentRegion?.name) +\n                        '\" 中还没有创建角色'\n                    ),\n                  ]),\n                ])\n              : _c(\n                  \"div\",\n                  { staticClass: \"characters-grid\" },\n                  [\n                    _vm._l(_vm.characters, function (character) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: character.id,\n                          staticClass: \"character-card\",\n                          class: {\n                            selected:\n                              _vm.selectedCharacter?.id === character.id,\n                          },\n                          on: {\n                            click: function ($event) {\n                              return _vm.selectCharacterLocal(character)\n                            },\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"character-avatar\" }, [\n                            _c(\"img\", {\n                              attrs: {\n                                src:\n                                  character.avatar ||\n                                  \"/static/game/avatars/default.png\",\n                                alt: character.name,\n                              },\n                            }),\n                            _c(\"div\", { staticClass: \"character-level\" }, [\n                              _vm._v(\"Lv.\" + _vm._s(character.level)),\n                            ]),\n                          ]),\n                          _c(\"div\", { staticClass: \"character-info\" }, [\n                            _c(\"h3\", { staticClass: \"character-name\" }, [\n                              _vm._v(_vm._s(character.name)),\n                            ]),\n                            _c(\"p\", { staticClass: \"character-class\" }, [\n                              _vm._v(\n                                _vm._s(\n                                  _vm.getClassName(\n                                    character.profession || character.class\n                                  )\n                                )\n                              ),\n                            ]),\n                            _c(\"div\", { staticClass: \"character-stats\" }, [\n                              _c(\"span\", { staticClass: \"stat\" }, [\n                                _vm._v(\n                                  \"经验: \" +\n                                    _vm._s(\n                                      character.experience || character.exp || 0\n                                    )\n                                ),\n                              ]),\n                              _c(\"span\", { staticClass: \"stat\" }, [\n                                _vm._v(\n                                  \"创建时间: \" +\n                                    _vm._s(\n                                      _vm.formatDate(\n                                        character.created_at ||\n                                          character.createdAt\n                                      )\n                                    )\n                                ),\n                              ]),\n                            ]),\n                          ]),\n                        ]\n                      )\n                    }),\n                    _vm.characters.length < 3\n                      ? _c(\n                          \"div\",\n                          {\n                            staticClass: \"character-card create-new\",\n                            on: { click: _vm.createNewCharacter },\n                          },\n                          [\n                            _c(\"div\", { staticClass: \"create-icon\" }, [\n                              _c(\"i\", { staticClass: \"plus-icon\" }, [\n                                _vm._v(\"+\"),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"create-text\" }, [\n                              _c(\"h3\", [_vm._v(\"创建新角色\")]),\n                              _c(\"p\", [\n                                _vm._v(\n                                  \"还可以创建 \" +\n                                    _vm._s(3 - _vm.characters.length) +\n                                    \" 个角色\"\n                                ),\n                              ]),\n                            ]),\n                          ]\n                        )\n                      : _vm._e(),\n                  ],\n                  2\n                ),\n            _c(\"div\", { staticClass: \"action-buttons\" }, [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"return-btn\",\n                  class: { pressed: _vm.isBackPressed },\n                  on: {\n                    click: _vm.goBack,\n                    mousedown: _vm.handleBackMouseDown,\n                    mouseup: _vm.handleBackMouseUp,\n                    mouseleave: _vm.handleBackMouseUp,\n                  },\n                },\n                [\n                  _c(\"img\", {\n                    staticClass: \"return-btn-img\",\n                    attrs: {\n                      src: _vm.getBackButtonImage(),\n                      alt: \"返回大区选择\",\n                      draggable: \"false\",\n                    },\n                  }),\n                ]\n              ),\n              _vm.characters.length === 0\n                ? _c(\n                    \"div\",\n                    {\n                      staticClass: \"create-character-btn\",\n                      class: { pressed: _vm.isCreatePressed },\n                      on: {\n                        click: _vm.createNewCharacter,\n                        mousedown: _vm.handleCreateMouseDown,\n                        mouseup: _vm.handleCreateMouseUp,\n                        mouseleave: _vm.handleCreateMouseUp,\n                      },\n                    },\n                    [\n                      _c(\"img\", {\n                        staticClass: \"create-btn-img\",\n                        attrs: {\n                          src: _vm.getCreateButtonImage(),\n                          alt: \"创建第一个角色\",\n                          draggable: \"false\",\n                        },\n                      }),\n                    ]\n                  )\n                : _c(\n                    \"div\",\n                    {\n                      staticClass: \"enter-game-btn\",\n                      class: {\n                        disabled: !_vm.selectedCharacter,\n                        pressed: _vm.isEnterPressed,\n                      },\n                      on: {\n                        click: _vm.confirmSelection,\n                        mousedown: _vm.handleEnterMouseDown,\n                        mouseup: _vm.handleEnterMouseUp,\n                        mouseleave: _vm.handleEnterMouseUp,\n                      },\n                    },\n                    [\n                      _c(\"img\", {\n                        staticClass: \"enter-btn-img\",\n                        attrs: {\n                          src: _vm.getEnterButtonImage(),\n                          alt: \"进入游戏\",\n                          draggable: \"false\",\n                        },\n                      }),\n                    ]\n                  ),\n            ]),\n          ])\n        : _vm._e(),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAAA,IAAAC,iBAAA,EAAAC,kBAAA;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,YAAY,EAAE,CACtBA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAAE,CACvDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CAAC,mBAAmB,CAAC,CAC5B,CAAC,EACFJ,GAAG,CAACK,aAAa,GACbJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,EACjBH,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACM,EAAE,CAACN,GAAG,CAACK,aAAa,CAACE,IAAI,CAAC,CAAC,CACvC,CAAC,CACH,CAAC,GACFP,GAAG,CAACQ,EAAE,CAAC,CAAC,EACZR,GAAG,CAACS,MAAM,CAACC,KAAK,CAACC,KAAK,GAClBV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,GAAGJ,GAAG,CAACM,EAAE,CAACN,GAAG,CAACY,SAAS,CAAC,CAAC,CAAC,CAAC,EACnDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,GAAGJ,GAAG,CAACM,EAAE,CAACN,GAAG,CAACa,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EACtDZ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,GAAGJ,GAAG,CAACM,EAAE,CAACN,GAAG,CAACc,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,EAC3Dd,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,GAAGJ,GAAG,CAACM,EAAE,EAAAR,iBAAA,GAACE,GAAG,CAACK,aAAa,cAAAP,iBAAA,uBAAjBA,iBAAA,CAAmBkB,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7Df,EAAE,CAAC,SAAS,EAAE,CACZA,EAAE,CAAC,SAAS,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC/BH,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACM,EAAE,CAACW,IAAI,CAACC,SAAS,CAAClB,GAAG,CAACc,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CACxD,CAAC,CACH,CAAC,CACH,CAAC,GACFd,GAAG,CAACQ,EAAE,CAAC,CAAC,CACb,CAAC,EACFR,GAAG,CAACY,SAAS,GACTX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC7CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACvCH,GAAG,CAACI,EAAE,CAAC,aAAa,CAAC,CACtB,CAAC,CACH,CAAC,GACFJ,GAAG,CAACQ,EAAE,CAAC,CAAC,EACZR,GAAG,CAACa,KAAK,IAAI,CAACb,GAAG,CAACY,SAAS,GACvBX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACtDH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACM,EAAE,CAACN,GAAG,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC,EACpCZ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,WAAW;IACxBgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACqB;IAAkB;EACrC,CAAC,EACD,CAACrB,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,CAAC,CACH,CAAC,GACFJ,GAAG,CAACQ,EAAE,CAAC,CAAC,EACZ,CAACR,GAAG,CAACY,SAAS,IAAI,CAACZ,GAAG,CAACa,KAAK,GACxBZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDH,GAAG,CAACc,UAAU,CAACC,MAAM,KAAK,CAAC,GACvBd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxDH,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BH,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACI,EAAE,CACJ,SAAS,GACPJ,GAAG,CAACM,EAAE,EAAAP,kBAAA,GAACC,GAAG,CAACK,aAAa,cAAAN,kBAAA,uBAAjBA,kBAAA,CAAmBQ,IAAI,CAAC,GAC/B,YACJ,CAAC,CACF,CAAC,CACH,CAAC,GACFN,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEH,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACc,UAAU,EAAE,UAAUS,SAAS,EAAE;IAAA,IAAAC,qBAAA;IAC1C,OAAOvB,EAAE,CACP,KAAK,EACL;MACEwB,GAAG,EAAEF,SAAS,CAACP,EAAE;MACjBb,WAAW,EAAE,gBAAgB;MAC7BuB,KAAK,EAAE;QACLC,QAAQ,EACN,EAAAH,qBAAA,GAAAxB,GAAG,CAAC4B,iBAAiB,cAAAJ,qBAAA,uBAArBA,qBAAA,CAAuBR,EAAE,MAAKO,SAAS,CAACP;MAC5C,CAAC;MACDG,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUS,MAAM,EAAE;UACvB,OAAO7B,GAAG,CAAC8B,oBAAoB,CAACP,SAAS,CAAC;QAC5C;MACF;IACF,CAAC,EACD,CACEtB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;MACR8B,KAAK,EAAE;QACLC,GAAG,EACDT,SAAS,CAACU,MAAM,IAChB,kCAAkC;QACpCC,GAAG,EAAEX,SAAS,CAAChB;MACjB;IACF,CAAC,CAAC,EACFN,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACI,EAAE,CAAC,KAAK,GAAGJ,GAAG,CAACM,EAAE,CAACiB,SAAS,CAACY,KAAK,CAAC,CAAC,CACxC,CAAC,CACH,CAAC,EACFlC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACM,EAAE,CAACiB,SAAS,CAAChB,IAAI,CAAC,CAAC,CAC/B,CAAC,EACFN,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC1CH,GAAG,CAACI,EAAE,CACJJ,GAAG,CAACM,EAAE,CACJN,GAAG,CAACoC,YAAY,CACdb,SAAS,CAACc,UAAU,IAAId,SAAS,CAACG,KACpC,CACF,CACF,CAAC,CACF,CAAC,EACFzB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAO,CAAC,EAAE,CAClCH,GAAG,CAACI,EAAE,CACJ,MAAM,GACJJ,GAAG,CAACM,EAAE,CACJiB,SAAS,CAACe,UAAU,IAAIf,SAAS,CAACgB,GAAG,IAAI,CAC3C,CACJ,CAAC,CACF,CAAC,EACFtC,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAO,CAAC,EAAE,CAClCH,GAAG,CAACI,EAAE,CACJ,QAAQ,GACNJ,GAAG,CAACM,EAAE,CACJN,GAAG,CAACwC,UAAU,CACZjB,SAAS,CAACkB,UAAU,IAClBlB,SAAS,CAACmB,SACd,CACF,CACJ,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF1C,GAAG,CAACc,UAAU,CAACC,MAAM,GAAG,CAAC,GACrBd,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,2BAA2B;IACxCgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAAC2C;IAAmB;EACtC,CAAC,EACD,CACE1C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACpCH,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,CACH,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BH,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACI,EAAE,CACJ,QAAQ,GACNJ,GAAG,CAACM,EAAE,CAAC,CAAC,GAAGN,GAAG,CAACc,UAAU,CAACC,MAAM,CAAC,GACjC,MACJ,CAAC,CACF,CAAC,CACH,CAAC,CAEN,CAAC,GACDf,GAAG,CAACQ,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACLP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,YAAY;IACzBuB,KAAK,EAAE;MAAEkB,OAAO,EAAE5C,GAAG,CAAC6C;IAAc,CAAC;IACrC1B,EAAE,EAAE;MACFC,KAAK,EAAEpB,GAAG,CAAC8C,MAAM;MACjBC,SAAS,EAAE/C,GAAG,CAACgD,mBAAmB;MAClCC,OAAO,EAAEjD,GAAG,CAACkD,iBAAiB;MAC9BC,UAAU,EAAEnD,GAAG,CAACkD;IAClB;EACF,CAAC,EACD,CACEjD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7B4B,KAAK,EAAE;MACLC,GAAG,EAAEhC,GAAG,CAACoD,kBAAkB,CAAC,CAAC;MAC7BlB,GAAG,EAAE,QAAQ;MACbmB,SAAS,EAAE;IACb;EACF,CAAC,CAAC,CAEN,CAAC,EACDrD,GAAG,CAACc,UAAU,CAACC,MAAM,KAAK,CAAC,GACvBd,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,sBAAsB;IACnCuB,KAAK,EAAE;MAAEkB,OAAO,EAAE5C,GAAG,CAACsD;IAAgB,CAAC;IACvCnC,EAAE,EAAE;MACFC,KAAK,EAAEpB,GAAG,CAAC2C,kBAAkB;MAC7BI,SAAS,EAAE/C,GAAG,CAACuD,qBAAqB;MACpCN,OAAO,EAAEjD,GAAG,CAACwD,mBAAmB;MAChCL,UAAU,EAAEnD,GAAG,CAACwD;IAClB;EACF,CAAC,EACD,CACEvD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7B4B,KAAK,EAAE;MACLC,GAAG,EAAEhC,GAAG,CAACyD,oBAAoB,CAAC,CAAC;MAC/BvB,GAAG,EAAE,SAAS;MACdmB,SAAS,EAAE;IACb;EACF,CAAC,CAAC,CAEN,CAAC,GACDpD,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BuB,KAAK,EAAE;MACLgC,QAAQ,EAAE,CAAC1D,GAAG,CAAC4B,iBAAiB;MAChCgB,OAAO,EAAE5C,GAAG,CAAC2D;IACf,CAAC;IACDxC,EAAE,EAAE;MACFC,KAAK,EAAEpB,GAAG,CAAC4D,gBAAgB;MAC3Bb,SAAS,EAAE/C,GAAG,CAAC6D,oBAAoB;MACnCZ,OAAO,EAAEjD,GAAG,CAAC8D,kBAAkB;MAC/BX,UAAU,EAAEnD,GAAG,CAAC8D;IAClB;EACF,CAAC,EACD,CACE7D,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,eAAe;IAC5B4B,KAAK,EAAE;MACLC,GAAG,EAAEhC,GAAG,CAAC+D,mBAAmB,CAAC,CAAC;MAC9B7B,GAAG,EAAE,MAAM;MACXmB,SAAS,EAAE;IACb;EACF,CAAC,CAAC,CAEN,CAAC,CACN,CAAC,CACH,CAAC,GACFrD,GAAG,CAACQ,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIwD,eAAe,GAAG,EAAE;AACxBnE,MAAM,CAACoE,aAAa,GAAG,IAAI;AAE3B,SAASpE,MAAM,EAAEmE,eAAe", "ignoreList": []}]}