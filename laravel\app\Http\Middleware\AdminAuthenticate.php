<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAuthenticate
{
    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 检查管理员是否已登录
        if (!Auth::guard('admin')->check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => '未授权访问'], 401);
            }

            return redirect()->route('admin.login');
        }

        // 权限检查逻辑 - 根据请求路径和管理员权限进行检查
        $admin = Auth::guard('admin')->user();
        $permissions = json_decode($admin->permissions, true) ?? [];

        // 权限检查可以根据实际需求完善
        // 这里简单检查超级管理员可以访问所有，其他角色需要检查权限
        if ($admin->role !== 'super_admin') {
            // 这里可以根据请求路径来判断所需权限
            // 示例: 根据当前路由和控制器确定所需权限
            // 暂时简单实现，可根据需要完善
        }

        return $next($request);
    }
}
