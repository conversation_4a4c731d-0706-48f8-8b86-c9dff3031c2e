<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sender_id');
            $table->unsignedBigInteger('receiver_id')->nullable();
            $table->string('channel', 50);  // world, team, private, system...
            $table->text('message');
            $table->boolean('is_system')->default(false);
            $table->timestamps();

            // 外键约束
            $table->foreign('sender_id')->references('id')->on('characters')->onDelete('cascade');
            $table->foreign('receiver_id')->references('id')->on('characters')->onDelete('cascade');

            // 索引，便于按频道和时间查询
            $table->index(['channel', 'created_at']);
            $table->index(['sender_id', 'receiver_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_messages');
    }
};
