<?php if($paginator->hasPages()): ?>
<div class="layui-box layui-laypage layui-laypage-default">
    
    <?php if($paginator->onFirstPage()): ?>
        <a href="javascript:;" class="layui-laypage-prev layui-disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
            <i class="layui-icon">&lt;</i>
        </a>
    <?php else: ?>
        <a href="<?php echo e($paginator->previousPageUrl()); ?>" class="layui-laypage-prev" rel="prev" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
            <i class="layui-icon">&lt;</i>
        </a>
    <?php endif; ?>

    
    <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        
        <?php if(is_string($element)): ?>
            <span class="layui-laypage-spr"><?php echo e($element); ?></span>
        <?php endif; ?>

        
        <?php if(is_array($element)): ?>
            <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($page == $paginator->currentPage()): ?>
                    <span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em><?php echo e($page); ?></em></span>
                <?php else: ?>
                    <a href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    
    <?php if($paginator->hasMorePages()): ?>
        <a href="<?php echo e($paginator->nextPageUrl()); ?>" class="layui-laypage-next" rel="next" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
            <i class="layui-icon">&gt;</i>
        </a>
    <?php else: ?>
        <a href="javascript:;" class="layui-laypage-next layui-disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
            <i class="layui-icon">&gt;</i>
        </a>
    <?php endif; ?>
</div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\szxy\laravel\resources\views/admin/layouts/pagination.blade.php ENDPATH**/ ?>