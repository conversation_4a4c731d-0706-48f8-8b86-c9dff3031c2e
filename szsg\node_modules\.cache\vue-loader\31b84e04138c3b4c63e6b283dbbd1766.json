{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue", "mtime": 1749890706309}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GameLayout.vue"], "names": [], "mappings": ";AAgEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "GameLayout.vue", "sourceRoot": "src/layouts", "sourcesContent": ["<template>\r\n  <div class=\"game-layout-container\">\r\n    <!-- 动态背景效果 -->\r\n    <div class=\"background-effects\">\r\n      <div class=\"floating-particles\">\r\n        <div v-for=\"i in 20\" :key=\"i\" class=\"particle\" :style=\"getParticleStyle(i)\"></div>\r\n      </div>\r\n      <div class=\"background-gradient\"></div>\r\n    </div>\r\n\r\n    <!-- 顶部装饰边框 -->\r\n    <div class=\"top-border-frame\" :style=\"{ backgroundImage: `url(${topBorderImage})` }\">\r\n      <div class=\"top-border-overlay\">\r\n        <div class=\"border-content\">\r\n          <!-- 页面标题和地图名 -->\r\n          <div class=\"page-title-container\">\r\n            <div class=\"page-title\">{{ pageTitle }}</div>\r\n            <div v-if=\"showLocationName && currentLocationName\" class=\"location-name\">\r\n              <span class=\"location-text\">{{ currentLocationName }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 左右固定边框 -->\r\n    <div class=\"left-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n    <div class=\"right-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n\r\n    <!-- 页面实际内容插入点 -->\r\n    <div class=\"main-content-area\">\r\n      <div class=\"content-wrapper\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部装饰边框 -->\r\n    <div class=\"bottom-border-frame\">\r\n      <div class=\"footer-decoration\">\r\n        <div class=\"footer-pattern\"></div>\r\n        <div class=\"footer-text\">\r\n          <span class=\"version-info\">Version 1.0.0</span>\r\n          <span class=\"copyright\">© 2025 神之西游</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载遮罩 -->\r\n    <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n      <div class=\"loading-content\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <div class=\"loading-text\">{{ loadingText || '加载中...' }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logger from '@/utils/logger';\r\n\r\nexport default {\r\n  name: 'GameLayout',\r\n  props: {\r\n    // 页面类型：'setup', 'game', 'main', 'battle' 等\r\n    pageType: {\r\n      type: String,\r\n      default: 'main'\r\n    },\r\n    // 自定义页面标题\r\n    customTitle: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 是否显示装饰元素\r\n    showDecorations: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isLoading: false,\r\n      loadingText: ''\r\n    };\r\n  },\r\n  computed: {\r\n    // 从store获取全局加载状态\r\n    globalLoading() {\r\n      return this.$store.state.isLoading;\r\n    },\r\n\r\n    // 根据页面类型或路由获取顶部边框图片\r\n    topBorderImage() {\r\n      // 如果有自定义标题，优先使用pageType\r\n      if (this.pageType && this.pageType !== 'main') {\r\n        return this.getImageByPageType(this.pageType);\r\n      }\r\n\r\n      // 根据当前路由自动判断\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '/static/game/UI/bj/xuanzefenqu.png';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '/static/game/UI/bj/xuanzejuese.png';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '/static/game/UI/bj/chuangjian.png';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '/static/game/UI/bj/top.jpg';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '/static/game/UI/bj/zhandou.png';\r\n      }\r\n\r\n      // 默认主页面\r\n      return '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 页面标题\r\n    pageTitle() {\r\n      if (this.customTitle) {\r\n        return this.customTitle;\r\n      }\r\n\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '选择大区';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '选择角色';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '创建角色';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '战斗场景';\r\n      }\r\n\r\n      return '神之西游';\r\n    },\r\n\r\n    // 是否显示地图名\r\n    showLocationName() {\r\n      const currentPath = this.$route.path;\r\n      return currentPath.includes('/game');\r\n    },\r\n\r\n    // 当前地图名\r\n    currentLocationName() {\r\n      try {\r\n        // 从store获取当前位置信息\r\n        const currentLocation = this.$store.state.map?.currentLocation;\r\n        if (currentLocation && currentLocation.name) {\r\n          return currentLocation.name;\r\n        }\r\n\r\n        // 如果store中没有数据，返回默认值\r\n        return '未知位置';\r\n      } catch (error) {\r\n        logger.error('[GameLayout] 获取当前位置失败:', error);\r\n        return '未知位置';\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    globalLoading(newVal) {\r\n      this.isLoading = newVal;\r\n    }\r\n  },\r\n  created() {\r\n    logger.debug('[GameLayout] 初始化');\r\n\r\n    // 获取当前页面路径\r\n    const currentPath = this.$route.path;\r\n    logger.debug('[GameLayout] 当前路径:', currentPath);\r\n\r\n    // 设置页面列表（这些页面只需要验证登录，不需要验证角色选择）\r\n    const setupPages = [\r\n      '/setup/region-select',\r\n      '/setup/character-select',\r\n      '/setup/create-character'\r\n    ];\r\n\r\n    // 检查认证状态（路由守卫已经处理了基本的登录检查，这里只做额外检查）\r\n    try {\r\n      const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');\r\n      const storeAuth = this.$store.state.auth?.isAuthenticated;\r\n      const isAuthenticated = storeAuth || !!authToken;\r\n\r\n      // 如果没有认证信息，让路由守卫处理重定向\r\n      if (!isAuthenticated) {\r\n        logger.debug('[GameLayout] 未登录，由路由守卫处理');\r\n        return;\r\n      }\r\n\r\n      logger.debug('[GameLayout] 已登录，继续检查角色状态');\r\n\r\n      // 只有非设置页面才需要检查角色选择状态\r\n      const isSetupPage = setupPages.some(page => currentPath.includes(page));\r\n      if (!isSetupPage) {\r\n        const selectedCharacter = localStorage.getItem('selectedCharacter');\r\n        if (!selectedCharacter) {\r\n          logger.debug('[GameLayout] 未选择角色，重定向到区域选择页');\r\n          this.showToast('请先选择角色');\r\n          // 使用nextTick避免在created中立即重定向\r\n          this.$nextTick(() => {\r\n            this.$router.replace('/setup/region-select');\r\n          });\r\n          return;\r\n        }\r\n      }\r\n\r\n      logger.debug('[GameLayout] 认证检查通过，路径:', currentPath);\r\n    } catch (error) {\r\n      logger.error('[GameLayout] 认证检查失败:', error);\r\n    }\r\n  },\r\n  methods: {\r\n    // 根据页面类型获取图片\r\n    getImageByPageType(pageType) {\r\n      const imageMap = {\r\n        'setup': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'region-select': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'character-select': '/static/game/UI/bj/xuanzejuese.png',\r\n        'create-character': '/static/game/UI/bj/chuangjian.png',\r\n        'game': '/static/game/UI/bj/youxi.png',\r\n        'battle': '/static/game/UI/bj/zhandou.png',\r\n        'main': '/static/game/UI/bj/szxy_1.png'\r\n      };\r\n      return imageMap[pageType] || '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 获取粒子样式\r\n    getParticleStyle() {\r\n      const delay = Math.random() * 20;\r\n      const duration = 15 + Math.random() * 10;\r\n      const size = 2 + Math.random() * 4;\r\n      const left = Math.random() * 100;\r\n\r\n      return {\r\n        left: `${left}%`,\r\n        animationDelay: `${delay}s`,\r\n        animationDuration: `${duration}s`,\r\n        width: `${size}px`,\r\n        height: `${size}px`\r\n      };\r\n    },\r\n\r\n    // 显示提示框\r\n    showToast(message, duration = 2000) {\r\n      logger.debug('[GameLayout] 显示提示:', message);\r\n      const toast = document.createElement('div');\r\n      toast.innerHTML = message;\r\n      toast.style.cssText = `\r\n        position: fixed;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        background: linear-gradient(135deg, rgba(212, 175, 55, 0.9), rgba(184, 148, 31, 0.9));\r\n        color: #000;\r\n        padding: 15px 25px;\r\n        border-radius: 8px;\r\n        border: 2px solid #d4af37;\r\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.3);\r\n        z-index: 10000;\r\n        font-weight: bold;\r\n        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);\r\n        animation: toastSlideIn 0.3s ease-out;\r\n      `;\r\n\r\n      // 添加动画样式\r\n      const style = document.createElement('style');\r\n      style.textContent = `\r\n        @keyframes toastSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translate(-50%, -50%) scale(0.8);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translate(-50%, -50%) scale(1);\r\n          }\r\n        }\r\n      `;\r\n      document.head.appendChild(style);\r\n\r\n      document.body.appendChild(toast);\r\n      setTimeout(() => {\r\n        toast.style.animation = 'toastSlideIn 0.3s ease-out reverse';\r\n        setTimeout(() => {\r\n          if (document.body.contains(toast)) {\r\n            document.body.removeChild(toast);\r\n          }\r\n          if (document.head.contains(style)) {\r\n            document.head.removeChild(style);\r\n          }\r\n        }, 300);\r\n      }, duration);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 应用全局背景和基本布局 */\r\n.game-layout-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  box-sizing: border-box;\r\n  background:\r\n    linear-gradient(135deg, rgba(10, 10, 10, 0.3) 0%, rgba(26, 26, 46, 0.2) 50%, rgba(22, 33, 62, 0.3) 100%),\r\n    url('/static/game/UI/bj/zise.png');\r\n  background-size: cover, cover;\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-attachment: fixed;\r\n  overflow-x: hidden;\r\n\r\n  /* 为边框留出空间 */\r\n  padding: 70px 40px 50px 30px;\r\n}\r\n\r\n/* 动态背景效果 */\r\n.background-effects {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: -1;\r\n}\r\n\r\n.background-gradient {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background:\r\n    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),\r\n    linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.02) 50%, transparent 70%);\r\n  animation: gradientShift 20s ease-in-out infinite;\r\n}\r\n\r\n@keyframes gradientShift {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n/* 浮动粒子效果 */\r\n.floating-particles {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  background: #d4af37;\r\n  border-radius: 50%;\r\n  opacity: 0.6;\r\n  animation: float-up linear infinite;\r\n}\r\n\r\n@keyframes float-up {\r\n  0% {\r\n    transform: translateY(100vh) rotate(0deg);\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 0.6;\r\n  }\r\n  90% {\r\n    opacity: 0.6;\r\n  }\r\n  100% {\r\n    transform: translateY(-100px) rotate(360deg);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 顶部边框装饰 */\r\n.top-border-frame {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 60px;\r\n  z-index: 1500;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.top-border-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.border-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 页面标题容器 */\r\n.page-title-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 26px;\r\n  font-weight: bold;\r\n  color: #d4af37;\r\n  text-shadow:\r\n    2px 2px 4px rgba(0, 0, 0, 0.8),\r\n    0 0 15px rgba(212, 175, 55, 0.6);\r\n  letter-spacing: 2px;\r\n}\r\n\r\n/* 地图位置显示 */\r\n.location-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  padding: 6px 16px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(212, 175, 55, 0.4);\r\n  backdrop-filter: blur(8px);\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.location-label {\r\n  color: #ccc;\r\n  font-size: 16px;\r\n}\r\n\r\n.location-text {\r\n  color: #ffd700;\r\n  font-weight: bold;\r\n  font-size: 22px;\r\n  text-shadow:\r\n    1px 1px 2px rgba(0, 0, 0, 0.8),\r\n    0 0 8px rgba(255, 215, 0, 0.4);\r\n}\r\n\r\n\r\n\r\n@keyframes sparkle {\r\n  0%, 100% { opacity: 0.7; transform: scale(1); }\r\n  50% { opacity: 1; transform: scale(1.1); }\r\n}\r\n\r\n/* 左右边框样式 */\r\n.left-border-frame,\r\n.right-border-frame {\r\n  position: fixed;\r\n  top: 60px;\r\n  bottom: 40px;\r\n  z-index: 1400;\r\n  pointer-events: none;\r\n}\r\n\r\n.left-border-frame {\r\n  left: 0;\r\n  width: 30px;\r\n  background: url('/static/game/UI/bk/gnl_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.right-border-frame {\r\n  right: 0;\r\n  width: 40px;\r\n  background: url('/static/game/UI/bk/gnl1_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.border-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: repeating-linear-gradient(\r\n    0deg,\r\n    transparent,\r\n    transparent 20px,\r\n    rgba(212, 175, 55, 0.1) 21px,\r\n    rgba(212, 175, 55, 0.1) 22px\r\n  );\r\n}\r\n\r\n.border-glow {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 2px;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, transparent, #d4af37, transparent);\r\n  opacity: 0.6;\r\n  animation: borderPulse 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderPulse {\r\n  0%, 100% { opacity: 0.3; }\r\n  50% { opacity: 0.8; }\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 50px 15px 35px 15px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .game-title {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 60px 25px 45px 20px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    height: 50px;\r\n  }\r\n\r\n  .left-border-frame,\r\n  .right-border-frame {\r\n    top: 50px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n\r\n  /* 移动端标题样式调整 */\r\n  .page-title-container {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .location-name {\r\n    font-size: 16px;\r\n    padding: 4px 12px;\r\n  }\r\n\r\n  .location-label {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .location-text {\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .page-title-container {\r\n    gap: 6px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .location-name {\r\n    font-size: 14px;\r\n    padding: 3px 8px;\r\n  }\r\n\r\n  .location-label {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .location-text {\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n</style> "]}]}