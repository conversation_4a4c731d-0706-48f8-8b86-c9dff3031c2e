{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\game\\BattleAnimation.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\game\\BattleAnimation.vue", "mtime": 1750337441932}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmlsdGVyLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdCYXR0bGVBbmltYXRpb24nLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhbmltYXRpb25zOiBbXSwKICAgICAgc2hvd0xvdHRpZUVmZmVjdDogZmFsc2UsCiAgICAgIGxvdHRpZUFuaW1hdGlvblVybDogJycsCiAgICAgIHNob3dQYXJ0aWNsZXM6IGZhbHNlLAogICAgICBhbmltYXRpb25JZENvdW50ZXI6IDAKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgaGFzQWN0aXZlQW5pbWF0aW9ucygpIHsKICAgICAgcmV0dXJuIHRoaXMuYW5pbWF0aW9ucy5sZW5ndGggPiAwIHx8IHRoaXMuc2hvd0xvdHRpZUVmZmVjdCB8fCB0aGlzLnNob3dQYXJ0aWNsZXM7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDmkq3mlL7mlLvlh7vliqjnlLsKICAgIHBsYXlBdHRhY2tBbmltYXRpb24odHlwZSA9ICdib3VuY2UnKSB7CiAgICAgIGNvbnN0IGFuaW1hdGlvbiA9IHsKICAgICAgICBpZDogdGhpcy5nZW5lcmF0ZUFuaW1hdGlvbklkKCksCiAgICAgICAgdGV4dDogJ+aUu+WHu++8gScsCiAgICAgICAgY2xhc3NOYW1lOiAnYXR0YWNrLXRleHQgYm91bmNlLWluJywKICAgICAgICBzdHlsZTogewogICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlSW4gMXMgZWFzZS1vdXQnCiAgICAgICAgfSwKICAgICAgICBkdXJhdGlvbjogMTAwMAogICAgICB9OwogICAgICB0aGlzLmFuaW1hdGlvbnMucHVzaChhbmltYXRpb24pOwoKICAgICAgLy8g6Ieq5Yqo56e76Zmk5Yqo55S7CiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIHRoaXMucmVtb3ZlQW5pbWF0aW9uKGFuaW1hdGlvbi5pZCk7CiAgICAgIH0sIGFuaW1hdGlvbi5kdXJhdGlvbik7CiAgICB9LAogICAgLy8g5pKt5pS+5Lyk5a6z5pWw5a2X5Yqo55S7CiAgICBwbGF5RGFtYWdlQW5pbWF0aW9uKGRhbWFnZSwgaXNDcml0aWNhbCA9IGZhbHNlKSB7CiAgICAgIGNvbnN0IGFuaW1hdGlvbiA9IHsKICAgICAgICBpZDogdGhpcy5nZW5lcmF0ZUFuaW1hdGlvbklkKCksCiAgICAgICAgdGV4dDogYC0ke2RhbWFnZX1gLAogICAgICAgIGNsYXNzTmFtZTogaXNDcml0aWNhbCA/ICdjcml0aWNhbC1kYW1hZ2UgZmFkZS11cCcgOiAnbm9ybWFsLWRhbWFnZSBmYWRlLXVwJywKICAgICAgICBzdHlsZTogewogICAgICAgICAgYW5pbWF0aW9uOiBpc0NyaXRpY2FsID8gJ2NyaXRpY2FsRGFtYWdlIDEuNXMgZWFzZS1vdXQnIDogJ25vcm1hbERhbWFnZSAxLjVzIGVhc2Utb3V0JwogICAgICAgIH0sCiAgICAgICAgZHVyYXRpb246IDE1MDAKICAgICAgfTsKICAgICAgdGhpcy5hbmltYXRpb25zLnB1c2goYW5pbWF0aW9uKTsKCiAgICAgIC8vIOiHquWKqOenu+mZpOWKqOeUuwogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLnJlbW92ZUFuaW1hdGlvbihhbmltYXRpb24uaWQpOwogICAgICB9LCBhbmltYXRpb24uZHVyYXRpb24pOwogICAgfSwKICAgIC8vIOaSreaUvuayu+eWl+WKqOeUuwogICAgcGxheUhlYWxBbmltYXRpb24oaGVhbGluZykgewogICAgICBjb25zdCBhbmltYXRpb24gPSB7CiAgICAgICAgaWQ6IHRoaXMuZ2VuZXJhdGVBbmltYXRpb25JZCgpLAogICAgICAgIHR5cGU6ICdmYWRlSW5VcCcsCiAgICAgICAgdGV4dDogYCske2hlYWxpbmd9YCwKICAgICAgICBjbGFzc05hbWU6ICdoZWFsLXRleHQnLAogICAgICAgIGR1cmF0aW9uOiAxNTAwCiAgICAgIH07CiAgICAgIHRoaXMuYW5pbWF0aW9ucy5wdXNoKGFuaW1hdGlvbik7CgogICAgICAvLyDoh6rliqjnp7vpmaTliqjnlLsKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5yZW1vdmVBbmltYXRpb24oYW5pbWF0aW9uLmlkKTsKICAgICAgfSwgYW5pbWF0aW9uLmR1cmF0aW9uKTsKICAgIH0sCiAgICAvLyDmkq3mlL7mioDog73nibnmlYggKOS9v+eUqExvdHRpZSkKICAgIHBsYXlTa2lsbEVmZmVjdChza2lsbFR5cGUpIHsKICAgICAgY29uc3QgbG90dGllVXJscyA9IHsKICAgICAgICBmaXJlOiAnaHR0cHM6Ly9hc3NldHMyLmxvdHRpZWZpbGVzLmNvbS9wYWNrYWdlcy9sZjIwX1haM3Brbi5qc29uJywKICAgICAgICBpY2U6ICdodHRwczovL2Fzc2V0czkubG90dGllZmlsZXMuY29tL3BhY2thZ2VzL2xmMjBfZG13OWNnOGguanNvbicsCiAgICAgICAgbGlnaHRuaW5nOiAnaHR0cHM6Ly9hc3NldHM0LmxvdHRpZWZpbGVzLmNvbS9wYWNrYWdlcy9sZjIwX3RsNTJ4cjNvLmpzb24nLAogICAgICAgIGhlYWw6ICdodHRwczovL2Fzc2V0czEubG90dGllZmlsZXMuY29tL3BhY2thZ2VzL2xmMjBfcXAxc3B6cXYuanNvbicKICAgICAgfTsKICAgICAgdGhpcy5sb3R0aWVBbmltYXRpb25VcmwgPSBsb3R0aWVVcmxzW3NraWxsVHlwZV0gfHwgbG90dGllVXJscy5maXJlOwogICAgICB0aGlzLnNob3dMb3R0aWVFZmZlY3QgPSB0cnVlOwogICAgfSwKICAgIC8vIOaSreaUvueykuWtkOaViOaenAogICAgcGxheVBhcnRpY2xlRWZmZWN0KHR5cGUgPSAnZXhwbG9zaW9uJykgewogICAgICB0aGlzLnNob3dQYXJ0aWNsZXMgPSB0cnVlOwoKICAgICAgLy8g5qC55o2u57G75Z6L6YWN572u5LiN5ZCM55qE57KS5a2Q5pWI5p6cCiAgICAgIGNvbnN0IGNvbmZpZ3MgPSB7CiAgICAgICAgZXhwbG9zaW9uOiB7CiAgICAgICAgICBwYXJ0aWNsZXM6IHsKICAgICAgICAgICAgbnVtYmVyOiB7CiAgICAgICAgICAgICAgdmFsdWU6IDEwMAogICAgICAgICAgICB9LAogICAgICAgICAgICBjb2xvcjogewogICAgICAgICAgICAgIHZhbHVlOiBbIiNmZjAwMDAiLCAiI2ZmODgwMCIsICIjZmZmZjAwIl0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgc2hhcGU6IHsKICAgICAgICAgICAgICB0eXBlOiAiY2lyY2xlIgogICAgICAgICAgICB9LAogICAgICAgICAgICBzaXplOiB7CiAgICAgICAgICAgICAgdmFsdWU6IDQsCiAgICAgICAgICAgICAgcmFuZG9tOiB0cnVlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG1vdmU6IHsKICAgICAgICAgICAgICBlbmFibGU6IHRydWUsCiAgICAgICAgICAgICAgc3BlZWQ6IDgsCiAgICAgICAgICAgICAgZGlyZWN0aW9uOiAibm9uZSIsCiAgICAgICAgICAgICAgb3V0X21vZGU6ICJvdXQiCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIG1hZ2ljOiB7CiAgICAgICAgICBwYXJ0aWNsZXM6IHsKICAgICAgICAgICAgbnVtYmVyOiB7CiAgICAgICAgICAgICAgdmFsdWU6IDUwCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGNvbG9yOiB7CiAgICAgICAgICAgICAgdmFsdWU6IFsiIzAwNjZmZiIsICIjODgwMGZmIiwgIiNmZjAwZmYiXQogICAgICAgICAgICB9LAogICAgICAgICAgICBzaGFwZTogewogICAgICAgICAgICAgIHR5cGU6ICJzdGFyIgogICAgICAgICAgICB9LAogICAgICAgICAgICBzaXplOiB7CiAgICAgICAgICAgICAgdmFsdWU6IDMsCiAgICAgICAgICAgICAgcmFuZG9tOiB0cnVlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG1vdmU6IHsKICAgICAgICAgICAgICBlbmFibGU6IHRydWUsCiAgICAgICAgICAgICAgc3BlZWQ6IDQsCiAgICAgICAgICAgICAgZGlyZWN0aW9uOiAidG9wIiwKICAgICAgICAgICAgICBvdXRfbW9kZTogIm91dCIKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfTsKICAgICAgaWYgKHdpbmRvdy5wYXJ0aWNsZXNKUykgewogICAgICAgIHdpbmRvdy5wYXJ0aWNsZXNKUygncGFydGljbGVzLWNvbnRhaW5lcicsIGNvbmZpZ3NbdHlwZV0gfHwgY29uZmlncy5leHBsb3Npb24pOwoKICAgICAgICAvLyAz56eS5ZCO6ZqQ6JeP57KS5a2Q5pWI5p6cCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICB0aGlzLnNob3dQYXJ0aWNsZXMgPSBmYWxzZTsKICAgICAgICB9LCAzMDAwKTsKICAgICAgfQogICAgfSwKICAgIC8vIOaSreaUvk1pc3PliqjnlLsKICAgIHBsYXlNaXNzQW5pbWF0aW9uKCkgewogICAgICBjb25zdCBhbmltYXRpb24gPSB7CiAgICAgICAgaWQ6IHRoaXMuZ2VuZXJhdGVBbmltYXRpb25JZCgpLAogICAgICAgIHR5cGU6ICdmYWRlT3V0VXAnLAogICAgICAgIHRleHQ6ICdNSVNTJywKICAgICAgICBjbGFzc05hbWU6ICdtaXNzLXRleHQnLAogICAgICAgIGR1cmF0aW9uOiAxMDAwCiAgICAgIH07CiAgICAgIHRoaXMuYW5pbWF0aW9ucy5wdXNoKGFuaW1hdGlvbik7CiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIHRoaXMucmVtb3ZlQW5pbWF0aW9uKGFuaW1hdGlvbi5pZCk7CiAgICAgIH0sIGFuaW1hdGlvbi5kdXJhdGlvbik7CiAgICB9LAogICAgLy8g5pKt5pS+54q25oCB5pWI5p6c5Yqo55S7CiAgICBwbGF5U3RhdHVzQW5pbWF0aW9uKHN0YXR1c1R5cGUsIHRleHQpIHsKICAgICAgY29uc3QgYW5pbWF0aW9uVHlwZXMgPSB7CiAgICAgICAgYnVmZjogJ3B1bHNlJywKICAgICAgICBkZWJ1ZmY6ICdzaGFrZScsCiAgICAgICAgcG9pc29uOiAnd29iYmxlJywKICAgICAgICBzdHVuOiAnZmxhc2gnCiAgICAgIH07CiAgICAgIGNvbnN0IGFuaW1hdGlvbiA9IHsKICAgICAgICBpZDogdGhpcy5nZW5lcmF0ZUFuaW1hdGlvbklkKCksCiAgICAgICAgdHlwZTogYW5pbWF0aW9uVHlwZXNbc3RhdHVzVHlwZV0gfHwgJ3B1bHNlJywKICAgICAgICB0ZXh0OiB0ZXh0LAogICAgICAgIGNsYXNzTmFtZTogYHN0YXR1cy0ke3N0YXR1c1R5cGV9YCwKICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICB9OwogICAgICB0aGlzLmFuaW1hdGlvbnMucHVzaChhbmltYXRpb24pOwogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLnJlbW92ZUFuaW1hdGlvbihhbmltYXRpb24uaWQpOwogICAgICB9LCBhbmltYXRpb24uZHVyYXRpb24pOwogICAgfSwKICAgIC8vIOeUn+aIkOWKqOeUu0lECiAgICBnZW5lcmF0ZUFuaW1hdGlvbklkKCkgewogICAgICByZXR1cm4gKyt0aGlzLmFuaW1hdGlvbklkQ291bnRlcjsKICAgIH0sCiAgICAvLyDnp7vpmaTliqjnlLsKICAgIHJlbW92ZUFuaW1hdGlvbihpZCkgewogICAgICB0aGlzLmFuaW1hdGlvbnMgPSB0aGlzLmFuaW1hdGlvbnMuZmlsdGVyKGFuaW0gPT4gYW5pbS5pZCAhPT0gaWQpOwogICAgfSwKICAgIC8vIExvdHRpZeWKqOeUu+WujOaIkOWbnuiwgwogICAgb25Mb3R0aWVDb21wbGV0ZSgpIHsKICAgICAgdGhpcy5zaG93TG90dGllRWZmZWN0ID0gZmFsc2U7CiAgICB9LAogICAgLy8g5riF6Zmk5omA5pyJ5Yqo55S7CiAgICBjbGVhckFsbEFuaW1hdGlvbnMoKSB7CiAgICAgIHRoaXMuYW5pbWF0aW9ucyA9IFtdOwogICAgICB0aGlzLnNob3dMb3R0aWVFZmZlY3QgPSBmYWxzZTsKICAgICAgdGhpcy5zaG93UGFydGljbGVzID0gZmFsc2U7CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgLy8g5qOA5p+l5Yqo55S75bqT5piv5ZCm5Yqg6L29CiAgICBpZiAod2luZG93LnBhcnRpY2xlc0pTKSB7CiAgICAgIC8vIFBhcnRpY2xlcy5qcyDlt7LliqDovb0KICAgIH0KICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmuIXnkIbliqjnlLsKICAgIHRoaXMuY2xlYXJBbGxBbmltYXRpb25zKCk7CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data", "animations", "showLottieEffect", "lottieAnimationUrl", "showParticles", "animationIdCounter", "computed", "hasActiveAnimations", "length", "methods", "playAttackAnimation", "type", "animation", "id", "generateAnimationId", "text", "className", "style", "duration", "push", "setTimeout", "removeAnimation", "playDamageAnimation", "damage", "isCritical", "playHealAnimation", "healing", "playSkillEffect", "skillType", "<PERSON><PERSON><PERSON><PERSON>", "fire", "ice", "lightning", "heal", "playParticleEffect", "configs", "explosion", "particles", "number", "value", "color", "shape", "size", "random", "move", "enable", "speed", "direction", "out_mode", "magic", "window", "particlesJS", "playMissAnimation", "playStatusAnimation", "statusType", "animationTypes", "buff", "debuff", "poison", "stun", "filter", "anim", "onLottieComplete", "clearAllAnimations", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/game/BattleAnimation.vue"], "sourcesContent": ["<template>\n  <div class=\"battle-animations\">\n    <!-- 默认战斗场景 -->\n    <div class=\"battle-scene\">\n      <div class=\"battle-ground\">\n        <div class=\"ground-line\"></div>\n        <div class=\"battle-text\" v-if=\"!hasActiveAnimations\">\n          <p>战斗进行中...</p>\n          <p class=\"battle-tip\">点击攻击按钮开始战斗</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 动画效果 -->\n    <div\n      v-for=\"animation in animations\"\n      :key=\"animation.id\"\n      :class=\"['animation-element', animation.className]\"\n      :style=\"animation.style\"\n      @animationend=\"removeAnimation(animation.id)\"\n    >\n      {{ animation.text }}\n    </div>\n\n    <!-- Lottie 动画 (用于复杂特效) -->\n    <lottie-player\n      v-if=\"showLottieEffect\"\n      :src=\"lottieAnimationUrl\"\n      background=\"transparent\"\n      speed=\"1\"\n      style=\"width: 300px; height: 300px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);\"\n      autoplay\n      @complete=\"onLottieComplete\"\n    ></lottie-player>\n\n    <!-- 粒子效果容器 -->\n    <div id=\"particles-container\" v-show=\"showParticles\" class=\"particles-container\"></div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'BattleAnimation',\n  \n  data() {\n    return {\n      animations: [],\n      showLottieEffect: false,\n      lottieAnimationUrl: '',\n      showParticles: false,\n      animationIdCounter: 0\n    }\n  },\n\n  computed: {\n    hasActiveAnimations() {\n      return this.animations.length > 0 || this.showLottieEffect || this.showParticles\n    }\n  },\n  \n  methods: {\n    // 播放攻击动画\n    playAttackAnimation(type = 'bounce') {\n      const animation = {\n        id: this.generateAnimationId(),\n        text: '攻击！',\n        className: 'attack-text bounce-in',\n        style: {\n          animation: 'bounceIn 1s ease-out'\n        },\n        duration: 1000\n      };\n      this.animations.push(animation);\n\n      // 自动移除动画\n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放伤害数字动画\n    playDamageAnimation(damage, isCritical = false) {\n      const animation = {\n        id: this.generateAnimationId(),\n        text: `-${damage}`,\n        className: isCritical ? 'critical-damage fade-up' : 'normal-damage fade-up',\n        style: {\n          animation: isCritical ? 'criticalDamage 1.5s ease-out' : 'normalDamage 1.5s ease-out'\n        },\n        duration: 1500\n      };\n      this.animations.push(animation);\n\n      // 自动移除动画\n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放治疗动画\n    playHealAnimation(healing) {\n      const animation = {\n        id: this.generateAnimationId(),\n        type: 'fadeInUp',\n        text: `+${healing}`,\n        className: 'heal-text',\n        duration: 1500\n      };\n      this.animations.push(animation);\n      \n      // 自动移除动画\n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放技能特效 (使用Lottie)\n    playSkillEffect(skillType) {\n      const lottieUrls = {\n        fire: 'https://assets2.lottiefiles.com/packages/lf20_XZ3pkn.json',\n        ice: 'https://assets9.lottiefiles.com/packages/lf20_dmw9cg8h.json',\n        lightning: 'https://assets4.lottiefiles.com/packages/lf20_tl52xr3o.json',\n        heal: 'https://assets1.lottiefiles.com/packages/lf20_qp1spzqv.json'\n      };\n      \n      this.lottieAnimationUrl = lottieUrls[skillType] || lottieUrls.fire;\n      this.showLottieEffect = true;\n    },\n\n    // 播放粒子效果\n    playParticleEffect(type = 'explosion') {\n      this.showParticles = true;\n      \n      // 根据类型配置不同的粒子效果\n      const configs = {\n        explosion: {\n          particles: {\n            number: { value: 100 },\n            color: { value: [\"#ff0000\", \"#ff8800\", \"#ffff00\"] },\n            shape: { type: \"circle\" },\n            size: { value: 4, random: true },\n            move: {\n              enable: true,\n              speed: 8,\n              direction: \"none\",\n              out_mode: \"out\"\n            }\n          }\n        },\n        magic: {\n          particles: {\n            number: { value: 50 },\n            color: { value: [\"#0066ff\", \"#8800ff\", \"#ff00ff\"] },\n            shape: { type: \"star\" },\n            size: { value: 3, random: true },\n            move: {\n              enable: true,\n              speed: 4,\n              direction: \"top\",\n              out_mode: \"out\"\n            }\n          }\n        }\n      };\n\n      if (window.particlesJS) {\n        window.particlesJS('particles-container', configs[type] || configs.explosion);\n        \n        // 3秒后隐藏粒子效果\n        setTimeout(() => {\n          this.showParticles = false;\n        }, 3000);\n      }\n    },\n\n    // 播放Miss动画\n    playMissAnimation() {\n      const animation = {\n        id: this.generateAnimationId(),\n        type: 'fadeOutUp',\n        text: 'MISS',\n        className: 'miss-text',\n        duration: 1000\n      };\n      this.animations.push(animation);\n      \n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 播放状态效果动画\n    playStatusAnimation(statusType, text) {\n      const animationTypes = {\n        buff: 'pulse',\n        debuff: 'shake',\n        poison: 'wobble',\n        stun: 'flash'\n      };\n      \n      const animation = {\n        id: this.generateAnimationId(),\n        type: animationTypes[statusType] || 'pulse',\n        text: text,\n        className: `status-${statusType}`,\n        duration: 2000\n      };\n      this.animations.push(animation);\n      \n      setTimeout(() => {\n        this.removeAnimation(animation.id);\n      }, animation.duration);\n    },\n\n    // 生成动画ID\n    generateAnimationId() {\n      return ++this.animationIdCounter;\n    },\n\n    // 移除动画\n    removeAnimation(id) {\n      this.animations = this.animations.filter(anim => anim.id !== id);\n    },\n\n    // Lottie动画完成回调\n    onLottieComplete() {\n      this.showLottieEffect = false;\n    },\n\n    // 清除所有动画\n    clearAllAnimations() {\n      this.animations = [];\n      this.showLottieEffect = false;\n      this.showParticles = false;\n    }\n  },\n\n  mounted() {\n    // 检查动画库是否加载\n    if (window.particlesJS) {\n      // Particles.js 已加载\n    }\n  },\n\n  beforeDestroy() {\n    // 清理动画\n    this.clearAllAnimations();\n  }\n}\n</script>\n\n<style scoped>\n.battle-animations {\n  position: relative;\n  width: 100%;\n  height: 300px;\n  overflow: hidden;\n  background: linear-gradient(180deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);\n  border: 2px solid #666;\n  border-radius: 8px;\n}\n\n/* 战斗场景 */\n.battle-scene {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.battle-ground {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.ground-line {\n  position: absolute;\n  bottom: 20px;\n  left: 10%;\n  right: 10%;\n  height: 2px;\n  background: linear-gradient(90deg, transparent 0%, #8B4513 50%, transparent 100%);\n}\n\n.battle-text {\n  text-align: center;\n  color: #e2e8f0;\n}\n\n.battle-text p {\n  margin: 5px 0;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n}\n\n.battle-tip {\n  font-size: 14px !important;\n  color: #a0aec0 !important;\n  font-weight: normal !important;\n}\n\n.animation-element {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 24px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  z-index: 10;\n  pointer-events: none;\n}\n\n/* 攻击文字样式 */\n.attack-text {\n  color: #fff;\n  font-size: 20px;\n}\n\n/* 伤害数字样式 */\n.normal-damage {\n  color: #ff4444;\n  font-size: 28px;\n}\n\n.critical-damage {\n  color: #ff0000;\n  font-size: 32px;\n  text-shadow: 0 0 10px #ff0000;\n}\n\n/* 治疗数字样式 */\n.heal-text {\n  color: #44ff44;\n  font-size: 24px;\n}\n\n/* Miss文字样式 */\n.miss-text {\n  color: #888;\n  font-size: 20px;\n  font-style: italic;\n}\n\n/* 状态效果样式 */\n.status-buff {\n  color: #00ff00;\n  font-size: 18px;\n}\n\n.status-debuff {\n  color: #ff8800;\n  font-size: 18px;\n}\n\n.status-poison {\n  color: #8800ff;\n  font-size: 18px;\n}\n\n.status-stun {\n  color: #ffff00;\n  font-size: 18px;\n}\n\n/* 粒子效果容器 */\n.particles-container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 5;\n}\n\n/* Animate.css 自定义动画时长 */\n.animate__animated {\n  animation-duration: 1s;\n}\n\n.animate__bounceInDown {\n  animation-duration: 1.5s;\n}\n\n.animate__fadeInUp {\n  animation-duration: 1.2s;\n}\n\n.animate__fadeOutUp {\n  animation-duration: 1s;\n}\n\n.animate__pulse {\n  animation-duration: 2s;\n}\n\n.animate__shake {\n  animation-duration: 1s;\n}\n\n.animate__wobble {\n  animation-duration: 1s;\n}\n\n.animate__flash {\n  animation-duration: 1s;\n}\n</style>\n"], "mappings": ";;;AAyCA;EACAA,IAAA;EAEAC,KAAA;IACA;MACAC,UAAA;MACAC,gBAAA;MACAC,kBAAA;MACAC,aAAA;MACAC,kBAAA;IACA;EACA;EAEAC,QAAA;IACAC,oBAAA;MACA,YAAAN,UAAA,CAAAO,MAAA,aAAAN,gBAAA,SAAAE,aAAA;IACA;EACA;EAEAK,OAAA;IACA;IACAC,oBAAAC,IAAA;MACA,MAAAC,SAAA;QACAC,EAAA,OAAAC,mBAAA;QACAC,IAAA;QACAC,SAAA;QACAC,KAAA;UACAL,SAAA;QACA;QACAM,QAAA;MACA;MACA,KAAAjB,UAAA,CAAAkB,IAAA,CAAAP,SAAA;;MAEA;MACAQ,UAAA;QACA,KAAAC,eAAA,CAAAT,SAAA,CAAAC,EAAA;MACA,GAAAD,SAAA,CAAAM,QAAA;IACA;IAEA;IACAI,oBAAAC,MAAA,EAAAC,UAAA;MACA,MAAAZ,SAAA;QACAC,EAAA,OAAAC,mBAAA;QACAC,IAAA,MAAAQ,MAAA;QACAP,SAAA,EAAAQ,UAAA;QACAP,KAAA;UACAL,SAAA,EAAAY,UAAA;QACA;QACAN,QAAA;MACA;MACA,KAAAjB,UAAA,CAAAkB,IAAA,CAAAP,SAAA;;MAEA;MACAQ,UAAA;QACA,KAAAC,eAAA,CAAAT,SAAA,CAAAC,EAAA;MACA,GAAAD,SAAA,CAAAM,QAAA;IACA;IAEA;IACAO,kBAAAC,OAAA;MACA,MAAAd,SAAA;QACAC,EAAA,OAAAC,mBAAA;QACAH,IAAA;QACAI,IAAA,MAAAW,OAAA;QACAV,SAAA;QACAE,QAAA;MACA;MACA,KAAAjB,UAAA,CAAAkB,IAAA,CAAAP,SAAA;;MAEA;MACAQ,UAAA;QACA,KAAAC,eAAA,CAAAT,SAAA,CAAAC,EAAA;MACA,GAAAD,SAAA,CAAAM,QAAA;IACA;IAEA;IACAS,gBAAAC,SAAA;MACA,MAAAC,UAAA;QACAC,IAAA;QACAC,GAAA;QACAC,SAAA;QACAC,IAAA;MACA;MAEA,KAAA9B,kBAAA,GAAA0B,UAAA,CAAAD,SAAA,KAAAC,UAAA,CAAAC,IAAA;MACA,KAAA5B,gBAAA;IACA;IAEA;IACAgC,mBAAAvB,IAAA;MACA,KAAAP,aAAA;;MAEA;MACA,MAAA+B,OAAA;QACAC,SAAA;UACAC,SAAA;YACAC,MAAA;cAAAC,KAAA;YAAA;YACAC,KAAA;cAAAD,KAAA;YAAA;YACAE,KAAA;cAAA9B,IAAA;YAAA;YACA+B,IAAA;cAAAH,KAAA;cAAAI,MAAA;YAAA;YACAC,IAAA;cACAC,MAAA;cACAC,KAAA;cACAC,SAAA;cACAC,QAAA;YACA;UACA;QACA;QACAC,KAAA;UACAZ,SAAA;YACAC,MAAA;cAAAC,KAAA;YAAA;YACAC,KAAA;cAAAD,KAAA;YAAA;YACAE,KAAA;cAAA9B,IAAA;YAAA;YACA+B,IAAA;cAAAH,KAAA;cAAAI,MAAA;YAAA;YACAC,IAAA;cACAC,MAAA;cACAC,KAAA;cACAC,SAAA;cACAC,QAAA;YACA;UACA;QACA;MACA;MAEA,IAAAE,MAAA,CAAAC,WAAA;QACAD,MAAA,CAAAC,WAAA,wBAAAhB,OAAA,CAAAxB,IAAA,KAAAwB,OAAA,CAAAC,SAAA;;QAEA;QACAhB,UAAA;UACA,KAAAhB,aAAA;QACA;MACA;IACA;IAEA;IACAgD,kBAAA;MACA,MAAAxC,SAAA;QACAC,EAAA,OAAAC,mBAAA;QACAH,IAAA;QACAI,IAAA;QACAC,SAAA;QACAE,QAAA;MACA;MACA,KAAAjB,UAAA,CAAAkB,IAAA,CAAAP,SAAA;MAEAQ,UAAA;QACA,KAAAC,eAAA,CAAAT,SAAA,CAAAC,EAAA;MACA,GAAAD,SAAA,CAAAM,QAAA;IACA;IAEA;IACAmC,oBAAAC,UAAA,EAAAvC,IAAA;MACA,MAAAwC,cAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,IAAA;MACA;MAEA,MAAA/C,SAAA;QACAC,EAAA,OAAAC,mBAAA;QACAH,IAAA,EAAA4C,cAAA,CAAAD,UAAA;QACAvC,IAAA,EAAAA,IAAA;QACAC,SAAA,YAAAsC,UAAA;QACApC,QAAA;MACA;MACA,KAAAjB,UAAA,CAAAkB,IAAA,CAAAP,SAAA;MAEAQ,UAAA;QACA,KAAAC,eAAA,CAAAT,SAAA,CAAAC,EAAA;MACA,GAAAD,SAAA,CAAAM,QAAA;IACA;IAEA;IACAJ,oBAAA;MACA,cAAAT,kBAAA;IACA;IAEA;IACAgB,gBAAAR,EAAA;MACA,KAAAZ,UAAA,QAAAA,UAAA,CAAA2D,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAhD,EAAA,KAAAA,EAAA;IACA;IAEA;IACAiD,iBAAA;MACA,KAAA5D,gBAAA;IACA;IAEA;IACA6D,mBAAA;MACA,KAAA9D,UAAA;MACA,KAAAC,gBAAA;MACA,KAAAE,aAAA;IACA;EACA;EAEA4D,QAAA;IACA;IACA,IAAAd,MAAA,CAAAC,WAAA;MACA;IAAA;EAEA;EAEAc,cAAA;IACA;IACA,KAAAF,kBAAA;EACA;AACA", "ignoreList": []}]}