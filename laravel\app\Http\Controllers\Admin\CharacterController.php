<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CharacterController extends Controller
{
    /**
     * 显示角色列表
     */
    public function index()
    {
        try {
            $characters = DB::table('characters')
                ->leftJoin('users', 'characters.user_id', '=', 'users.id')
                ->select(
                    'characters.*',
                    'users.username as user_username'
                )
                ->orderBy('characters.level', 'desc')
                ->orderBy('characters.name')
                ->paginate(15);

            return view('admin.characters.index', compact('characters'));
        } catch (\Exception $e) {
            // 创建一个空的分页对象而不是简单的集合
            $characters = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.characters.index', compact('characters'));
        }
    }

    /**
     * 显示创建角色表单
     */
    public function create()
    {
        try {
            // 获取所有用户
            $users = DB::table('users')
                ->where('status', 1)
                ->orderBy('username')
                ->get();

            return view('admin.characters.create', compact('users'));
        } catch (\Exception $e) {
            $users = collect([]);
            return view('admin.characters.create', compact('users'));
        }
    }

    /**
     * 保存新角色
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:characters',
            'user_id' => 'required|exists:users,id',
            'level' => 'required|integer|min:1',
            'exp' => 'required|integer|min:0',
            'hp' => 'required|integer|min:1',
            'mp' => 'required|integer|min:0',
            'attack' => 'required|integer|min:1',
            'defense' => 'required|integer|min:0',
            'speed' => 'required|integer|min:1',
            'silver' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::table('characters')->insert([
                'name' => $request->name,
                'user_id' => $request->user_id,
                'level' => $request->level,
                'exp' => $request->exp,
                'hp' => $request->hp,
                'mp' => $request->mp,
                'attack' => $request->attack,
                'defense' => $request->defense,
                'speed' => $request->speed,
                'silver' => $request->silver,
                'status' => $request->has('status') ? 1 : 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return redirect()->route('admin.characters.index')
                ->with('success', '角色创建成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '角色创建失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 显示角色详情
     */
    public function show($id)
    {
        try {
            $character = DB::table('characters')
                ->leftJoin('users', 'characters.user_id', '=', 'users.id')
                ->select(
                    'characters.*',
                    'users.username as user_username'
                )
                ->where('characters.id', $id)
                ->first();

            if (!$character) {
                return redirect()->route('admin.characters.index')
                    ->with('error', '角色不存在');
            }

            // 获取角色装备
            $equipments = DB::table('character_equipments')
                ->join('items', 'character_equipments.item_id', '=', 'items.id')
                ->where('character_equipments.character_id', $id)
                ->select(
                    'character_equipments.*',
                    'items.name as item_name',
                    'items.type as item_type'
                )
                ->get();

            // 获取角色背包
            $inventory = DB::table('character_items')
                ->join('items', 'character_items.item_id', '=', 'items.id')
                ->where('character_items.character_id', $id)
                ->select(
                    'character_items.*',
                    'items.name as item_name',
                    'items.type as item_type'
                )
                ->get();

            // 获取角色技能
            $skills = DB::table('character_skills')
                ->join('skills', 'character_skills.skill_id', '=', 'skills.id')
                ->where('character_skills.character_id', $id)
                ->select(
                    'character_skills.*',
                    'skills.name as skill_name',
                    'skills.description as skill_description'
                )
                ->get();

            return view('admin.characters.show', compact('character', 'equipments', 'inventory', 'skills'));
        } catch (\Exception $e) {
            return redirect()->route('admin.characters.index')
                ->with('error', '无法获取角色信息: ' . $e->getMessage());
        }
    }

    /**
     * 显示编辑角色表单
     */
    public function edit($id)
    {
        try {
            $character = DB::table('characters')->where('id', $id)->first();

            if (!$character) {
                return redirect()->route('admin.characters.index')
                    ->with('error', '角色不存在');
            }

            // 获取所有用户
            $users = DB::table('users')
                ->where('status', 1)
                ->orderBy('username')
                ->get();

            return view('admin.characters.edit', compact('character', 'users'));
        } catch (\Exception $e) {
            return redirect()->route('admin.characters.index')
                ->with('error', '无法获取角色信息: ' . $e->getMessage());
        }
    }

    /**
     * 更新角色信息
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:characters,name,' . $id,
            'user_id' => 'required|exists:users,id',
            'level' => 'required|integer|min:1',
            'exp' => 'required|integer|min:0',
            'hp' => 'required|integer|min:1',
            'mp' => 'required|integer|min:0',
            'attack' => 'required|integer|min:1',
            'defense' => 'required|integer|min:0',
            'speed' => 'required|integer|min:1',
            'silver' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::table('characters')
                ->where('id', $id)
                ->update([
                    'name' => $request->name,
                    'user_id' => $request->user_id,
                    'level' => $request->level,
                    'exp' => $request->exp,
                    'hp' => $request->hp,
                    'mp' => $request->mp,
                    'attack' => $request->attack,
                    'defense' => $request->defense,
                    'speed' => $request->speed,
                    'silver' => $request->silver,
                    'status' => $request->has('status') ? 1 : 0,
                    'updated_at' => now(),
                ]);

            return redirect()->route('admin.characters.index')
                ->with('success', '角色更新成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '角色更新失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 删除角色
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            // 删除角色关联数据
            DB::table('character_equipments')->where('character_id', $id)->delete();
            DB::table('character_items')->where('character_id', $id)->delete();
            DB::table('character_skills')->where('character_id', $id)->delete();

            // 删除角色
            DB::table('characters')->where('id', $id)->delete();

            DB::commit();

            return redirect()->route('admin.characters.index')
                ->with('success', '角色已删除');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->route('admin.characters.index')
                ->with('error', '角色删除失败: ' . $e->getMessage());
        }
    }
}
