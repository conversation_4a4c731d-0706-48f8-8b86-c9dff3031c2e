<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BankAccount extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'character_id',
        'silver',
        'gold_ingot',
    ];

    /**
     * 获取拥有该银行账户的角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 获取该账户的所有交易记录
     */
    public function transactions()
    {
        return $this->hasMany(BankTransaction::class, 'character_id', 'character_id');
    }

    /**
     * 存款
     *
     * @param string $currency 货币类型 (silver/gold_ingot)
     * @param int $amount 金额
     * @return array 操作结果
     */
    public function deposit($currency, $amount)
    {
        if ($amount <= 0) {
            return [
                'success' => false,
                'message' => '存款金额必须大于0'
            ];
        }

        // 获取角色当前的货币数量
        $character = $this->character;

        // 处理字段名不一致的情况：gold_ingot 对应 character 表的 gold 字段
        $characterCurrencyField = ($currency === 'gold_ingot') ? 'gold' : $currency;
        $currentAmount = $character->$characterCurrencyField ?? 0;

        // 检查角色是否有足够的货币
        if ($currentAmount < $amount) {
            return [
                'success' => false,
                'message' => $currency === 'silver' ? '银两不足' : '金砖不足'
            ];
        }

        // 从角色扣除货币
        $character->$characterCurrencyField -= $amount;
        $character->save();

        // 增加账户余额
        $this->$currency += $amount;
        $this->save();

        // 创建交易记录
        $balance = $this->$currency;
        BankTransaction::create([
            'character_id' => $this->character_id,
            'type' => 'deposit',
            'currency' => $currency,
            'amount' => $amount,
            'balance' => $balance,
            'description' => $currency === 'silver' ? "存入 {$amount} 银两" : "存入 {$amount} 金砖"
        ]);

        return [
            'success' => true,
            'message' => $currency === 'silver' ? "成功存入 {$amount} 银两" : "成功存入 {$amount} 金砖",
            'balance' => $balance,
            'character_balance' => $character->$characterCurrencyField
        ];
    }

    /**
     * 取款
     *
     * @param string $currency 货币类型 (silver/gold_ingot)
     * @param int $amount 金额
     * @return array 操作结果
     */
    public function withdraw($currency, $amount)
    {
        if ($amount <= 0) {
            return [
                'success' => false,
                'message' => '取款金额必须大于0'
            ];
        }

        // 检查账户是否有足够的余额
        $currentBalance = $this->$currency;
        if ($currentBalance < $amount) {
            return [
                'success' => false,
                'message' => $currency === 'silver' ? '账户银两不足' : '账户金砖不足'
            ];
        }

        // 减少账户余额
        $this->$currency -= $amount;
        $this->save();

        // 增加角色货币
        $character = $this->character;
        // 处理字段名不一致的情况：gold_ingot 对应 character 表的 gold 字段
        $characterCurrencyField = ($currency === 'gold_ingot') ? 'gold' : $currency;
        $character->$characterCurrencyField = ($character->$characterCurrencyField ?? 0) + $amount;
        $character->save();

        // 创建交易记录
        $balance = $this->$currency;
        BankTransaction::create([
            'character_id' => $this->character_id,
            'type' => 'withdraw',
            'currency' => $currency,
            'amount' => $amount,
            'balance' => $balance,
            'description' => $currency === 'silver' ? "取出 {$amount} 银两" : "取出 {$amount} 金砖"
        ]);

        return [
            'success' => true,
            'message' => $currency === 'silver' ? "成功取出 {$amount} 银两" : "成功取出 {$amount} 金砖",
            'balance' => $balance,
            'character_balance' => $character->$characterCurrencyField
        ];
    }
}
