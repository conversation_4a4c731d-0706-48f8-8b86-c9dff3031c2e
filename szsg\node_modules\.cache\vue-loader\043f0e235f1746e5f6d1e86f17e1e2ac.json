{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\game\\BattleAnimation.vue?vue&type=template&id=7f84ddff&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\game\\BattleAnimation.vue", "mtime": 1750337441932}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "hasActiveAnimations", "_v", "_e", "_l", "animations", "animation", "key", "id", "class", "className", "style", "on", "animationend", "$event", "removeAnimation", "_s", "text", "showLottieEffect", "staticStyle", "width", "height", "position", "top", "left", "transform", "attrs", "src", "lottieAnimationUrl", "background", "speed", "autoplay", "complete", "onLottieComplete", "directives", "name", "rawName", "value", "showParticles", "expression", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/components/game/BattleAnimation.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"battle-animations\" },\n    [\n      _c(\"div\", { staticClass: \"battle-scene\" }, [\n        _c(\"div\", { staticClass: \"battle-ground\" }, [\n          _c(\"div\", { staticClass: \"ground-line\" }),\n          !_vm.hasActiveAnimations\n            ? _c(\"div\", { staticClass: \"battle-text\" }, [\n                _c(\"p\", [_vm._v(\"战斗进行中...\")]),\n                _c(\"p\", { staticClass: \"battle-tip\" }, [\n                  _vm._v(\"点击攻击按钮开始战斗\"),\n                ]),\n              ])\n            : _vm._e(),\n        ]),\n      ]),\n      _vm._l(_vm.animations, function (animation) {\n        return _c(\n          \"div\",\n          {\n            key: animation.id,\n            class: [\"animation-element\", animation.className],\n            style: animation.style,\n            on: {\n              animationend: function ($event) {\n                return _vm.removeAnimation(animation.id)\n              },\n            },\n          },\n          [_vm._v(\" \" + _vm._s(animation.text) + \" \")]\n        )\n      }),\n      _vm.showLottieEffect\n        ? _c(\"lottie-player\", {\n            staticStyle: {\n              width: \"300px\",\n              height: \"300px\",\n              position: \"absolute\",\n              top: \"50%\",\n              left: \"50%\",\n              transform: \"translate(-50%, -50%)\",\n            },\n            attrs: {\n              src: _vm.lottieAnimationUrl,\n              background: \"transparent\",\n              speed: \"1\",\n              autoplay: \"\",\n            },\n            on: { complete: _vm.onLottieComplete },\n          })\n        : _vm._e(),\n      _c(\"div\", {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.showParticles,\n            expression: \"showParticles\",\n          },\n        ],\n        staticClass: \"particles-container\",\n        attrs: { id: \"particles-container\" },\n      }),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,EACzC,CAACH,GAAG,CAACI,mBAAmB,GACpBH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC7BJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACrCH,GAAG,CAACK,EAAE,CAAC,YAAY,CAAC,CACrB,CAAC,CACH,CAAC,GACFL,GAAG,CAACM,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC,EACFN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,UAAU,EAAE,UAAUC,SAAS,EAAE;IAC1C,OAAOR,EAAE,CACP,KAAK,EACL;MACES,GAAG,EAAED,SAAS,CAACE,EAAE;MACjBC,KAAK,EAAE,CAAC,mBAAmB,EAAEH,SAAS,CAACI,SAAS,CAAC;MACjDC,KAAK,EAAEL,SAAS,CAACK,KAAK;MACtBC,EAAE,EAAE;QACFC,YAAY,EAAE,SAAAA,CAAUC,MAAM,EAAE;UAC9B,OAAOjB,GAAG,CAACkB,eAAe,CAACT,SAAS,CAACE,EAAE,CAAC;QAC1C;MACF;IACF,CAAC,EACD,CAACX,GAAG,CAACK,EAAE,CAAC,GAAG,GAAGL,GAAG,CAACmB,EAAE,CAACV,SAAS,CAACW,IAAI,CAAC,GAAG,GAAG,CAAC,CAC7C,CAAC;EACH,CAAC,CAAC,EACFpB,GAAG,CAACqB,gBAAgB,GAChBpB,EAAE,CAAC,eAAe,EAAE;IAClBqB,WAAW,EAAE;MACXC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE;IACb,CAAC;IACDC,KAAK,EAAE;MACLC,GAAG,EAAE9B,GAAG,CAAC+B,kBAAkB;MAC3BC,UAAU,EAAE,aAAa;MACzBC,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE;IACZ,CAAC;IACDnB,EAAE,EAAE;MAAEoB,QAAQ,EAAEnC,GAAG,CAACoC;IAAiB;EACvC,CAAC,CAAC,GACFpC,GAAG,CAACM,EAAE,CAAC,CAAC,EACZL,EAAE,CAAC,KAAK,EAAE;IACRoC,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAExC,GAAG,CAACyC,aAAa;MACxBC,UAAU,EAAE;IACd,CAAC,CACF;IACDvC,WAAW,EAAE,qBAAqB;IAClC0B,KAAK,EAAE;MAAElB,EAAE,EAAE;IAAsB;EACrC,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIgC,eAAe,GAAG,EAAE;AACxB5C,MAAM,CAAC6C,aAAa,GAAG,IAAI;AAE3B,SAAS7C,MAAM,EAAE4C,eAAe", "ignoreList": []}]}