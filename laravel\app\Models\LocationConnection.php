<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LocationConnection extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_location_id',
        'to_location_id',
        'distance',
        'time_cost',
        'silver_cost',
        'level_requirement',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 起始位置
     */
    public function fromLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'from_location_id');
    }

    /**
     * 目标位置
     */
    public function toLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'to_location_id');
    }

    /**
     * 检查角色是否满足移动条件
     * @param \App\Models\Character $character 角色
     * @return array 检查结果
     */
    public function checkRequirements($character)
    {
        $result = [
            'can_move' => true,
            'reasons' => []
        ];

        // 检查等级要求
        if ($character->level < $this->level_requirement) {
            $result['can_move'] = false;
            $result['reasons'][] = "需要等级 {$this->level_requirement}";
        }

        // 体力消耗功能已完全移除

        // 检查银两
        if ($character->silver < $this->silver_cost) {
            $result['can_move'] = false;
            $result['reasons'][] = "银两不足，需要 {$this->silver_cost} 两银子";
        }

        // 检查连接是否可用
        if (!$this->is_active) {
            $result['can_move'] = false;
            $result['reasons'][] = "此路径暂时不可用";
        }

        return $result;
    }

    /**
     * 获取移动消耗信息
     * @return array
     */
    public function getCostInfo()
    {
        return [
            'time' => $this->time_cost,
            'silver' => $this->silver_cost,
            'distance' => $this->distance,
            'level_requirement' => $this->level_requirement
        ];
    }

    /**
     * 根据起始位置获取连接
     * @param int $fromLocationId 起始位置ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function fromLocationQuery($fromLocationId)
    {
        return static::where('from_location_id', $fromLocationId)
            ->where('is_active', true);
    }

    /**
     * 根据目标位置获取连接
     * @param int $toLocationId 目标位置ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function toLocationQuery($toLocationId)
    {
        return static::where('to_location_id', $toLocationId)
            ->where('is_active', true);
    }

    /**
     * 获取双向连接
     * @param int $locationId1 位置1
     * @param int $locationId2 位置2
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function bidirectional($locationId1, $locationId2)
    {
        return static::where(function ($query) use ($locationId1, $locationId2) {
            $query->where('from_location_id', $locationId1)
                  ->where('to_location_id', $locationId2);
        })->orWhere(function ($query) use ($locationId1, $locationId2) {
            $query->where('from_location_id', $locationId2)
                  ->where('to_location_id', $locationId1);
        })->where('is_active', true)->get();
    }
}
