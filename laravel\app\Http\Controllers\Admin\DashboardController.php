<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * 显示管理后台首页
     */
    public function index()
    {
        try {
            // 获取用户统计
            $userCount = DB::table('users')->count();
            $activeUserCount = DB::table('users')->where('status', 1)->count();

            // 获取角色统计
            $characterCount = DB::table('characters')->count();
            $activeCharacterCount = DB::table('characters')->where('status', 1)->count();

            // 获取物品统计
            $itemCount = DB::table('items')->count();

            // 获取任务统计
            $questCount = DB::table('quests')->count();
            $activeQuestCount = DB::table('quests')->where('is_active', 1)->count();

            // 获取怪物统计
            $monsterCount = DB::table('monsters')->count();

            // 获取地图统计
            $mapCount = DB::table('maps')->count();

            // 获取战斗统计
            $battleCount = DB::table('battle_logs')->count();
            $winBattleCount = DB::table('battle_logs')->where('result', 'win')->count();
            $loseBattleCount = DB::table('battle_logs')->where('result', 'lose')->count();

            // 获取最近注册的用户
            $recentUsers = DB::table('users')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            // 获取最近的战斗记录
            $recentBattles = DB::table('battle_logs')
                ->leftJoin('characters', 'battle_logs.character_id', '=', 'characters.id')
                ->leftJoin('monsters', 'battle_logs.monster_id', '=', 'monsters.id')
                ->select(
                    'battle_logs.*',
                    'characters.name as character_name',
                    'monsters.name as monster_name'
                )
                ->orderBy('battle_logs.created_at', 'desc')
                ->limit(5)
                ->get();

            return view('admin.dashboard', compact(
                'userCount',
                'activeUserCount',
                'characterCount',
                'activeCharacterCount',
                'itemCount',
                'questCount',
                'activeQuestCount',
                'monsterCount',
                'mapCount',
                'battleCount',
                'winBattleCount',
                'loseBattleCount',
                'recentUsers',
                'recentBattles'
            ));
        } catch (\Exception $e) {
            return view('admin.dashboard')->with('error', '获取统计数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取统计数据
     */
    public function getStats()
    {
        try {
            // 获取用户统计
            $userCount = DB::table('users')->count();
            $activeUserCount = DB::table('users')->where('status', 1)->count();

            // 获取角色统计
            $characterCount = DB::table('characters')->count();
            $activeCharacterCount = DB::table('characters')->where('status', 1)->count();

            // 获取物品统计
            $itemCount = DB::table('items')->count();

            // 获取任务统计
            $questCount = DB::table('quests')->count();
            $activeQuestCount = DB::table('quests')->where('is_active', 1)->count();

            // 获取怪物统计
            $monsterCount = DB::table('monsters')->count();

            // 获取地图统计
            $mapCount = DB::table('maps')->count();

            // 获取战斗统计
            $battleCount = DB::table('battle_logs')->count();
            $winBattleCount = DB::table('battle_logs')->where('result', 'win')->count();
            $loseBattleCount = DB::table('battle_logs')->where('result', 'lose')->count();

            // 获取最近7天的注册用户数
            $dailyRegistrations = DB::table('users')
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', now()->subDays(7))
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // 获取最近7天的战斗数
            $dailyBattles = DB::table('battle_logs')
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', now()->subDays(7))
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return response()->json([
                'users' => [
                    'total' => $userCount,
                    'active' => $activeUserCount,
                ],
                'characters' => [
                    'total' => $characterCount,
                    'active' => $activeCharacterCount,
                ],
                'items' => $itemCount,
                'quests' => [
                    'total' => $questCount,
                    'active' => $activeQuestCount,
                ],
                'monsters' => $monsterCount,
                'maps' => $mapCount,
                'battles' => [
                    'total' => $battleCount,
                    'win' => $winBattleCount,
                    'lose' => $loseBattleCount,
                ],
                'daily_registrations' => $dailyRegistrations,
                'daily_battles' => $dailyBattles,
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => '获取统计数据失败: ' . $e->getMessage()], 500);
        }
    }
}
