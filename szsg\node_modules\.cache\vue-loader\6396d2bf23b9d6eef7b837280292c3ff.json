{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\AuthDebug.vue", "mtime": 1749703549914}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AuthDebug.vue"], "names": [], "mappings": ";AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AuthDebug.vue", "sourceRoot": "src/views/debug", "sourcesContent": ["<template>\n  <div class=\"auth-debug\">\n    <h2>认证状态调试</h2>\n    \n    <div class=\"debug-section\">\n      <h3>Store 状态</h3>\n      <div class=\"debug-item\">\n        <strong>isAuthenticated:</strong> {{ storeAuth.isAuthenticated }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>token:</strong> {{ storeAuth.token ? '存在' : '不存在' }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>user:</strong> {{ storeAuth.user ? JSON.stringify(storeAuth.user) : '不存在' }}\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>LocalStorage</h3>\n      <div class=\"debug-item\">\n        <strong>authToken:</strong> {{ localStorage.authToken ? '存在' : '不存在' }}\n      </div>\n      <div class=\"debug-item\">\n        <strong>token:</strong> {{ localStorage.token ? '存在' : '不存在' }}\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>操作</h3>\n      <button @click=\"clearAuth\" class=\"debug-btn\">清除认证状态</button>\n      <button @click=\"setTestAuth\" class=\"debug-btn\">设置测试认证</button>\n      <button @click=\"refreshPage\" class=\"debug-btn\">刷新页面</button>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>导航测试</h3>\n      <button @click=\"goToFriends\" class=\"debug-btn\">跳转到好友页面</button>\n      <button @click=\"goToLogin\" class=\"debug-btn\">跳转到登录页面</button>\n      <button @click=\"goToMain\" class=\"debug-btn\">跳转到游戏主页</button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      localStorage: {\n        authToken: null,\n        token: null\n      }\n    }\n  },\n  computed: {\n    storeAuth() {\n      return {\n        isAuthenticated: this.$store.state.auth?.isAuthenticated || false,\n        token: this.$store.state.auth?.token || null,\n        user: this.$store.state.auth?.user || null\n      }\n    }\n  },\n  created() {\n    this.updateLocalStorageInfo()\n  },\n  methods: {\n    updateLocalStorageInfo() {\n      this.localStorage.authToken = localStorage.getItem('authToken')\n      this.localStorage.token = localStorage.getItem('token')\n    },\n    \n    clearAuth() {\n      // 清除 store\n      this.$store.dispatch('auth/logout')\n      \n      // 清除 localStorage\n      localStorage.removeItem('authToken')\n      localStorage.removeItem('token')\n      \n      this.updateLocalStorageInfo()\n      alert('认证状态已清除')\n    },\n    \n    setTestAuth() {\n      const testToken = 'test-token-' + Date.now()\n      const testUser = {\n        id: 1,\n        name: 'TestUser',\n        username: 'testuser'\n      }\n      \n      // 设置到 store\n      this.$store.dispatch('auth/login', {\n        token: testToken,\n        userInfo: testUser\n      })\n      \n      // 设置到 localStorage\n      localStorage.setItem('authToken', testToken)\n      \n      this.updateLocalStorageInfo()\n      alert('测试认证状态已设置')\n    },\n    \n    refreshPage() {\n      window.location.reload()\n    },\n    \n    goToFriends() {\n      this.$router.push('/game/friends')\n    },\n    \n    goToLogin() {\n      this.$router.push('/login')\n    },\n    \n    goToMain() {\n      this.$router.push('/game/main')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.auth-debug {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n  font-family: Arial, sans-serif;\n}\n\n.debug-section {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  background: #f9f9f9;\n}\n\n.debug-section h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.debug-item {\n  margin-bottom: 10px;\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.debug-btn {\n  margin: 5px;\n  padding: 8px 16px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.debug-btn:hover {\n  background: #0056b3;\n}\n</style>\n"]}]}