<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;
use App\Models\Item;
use App\Models\CharacterItem;
use Illuminate\Support\Facades\DB;

class ItemController extends Controller
{
    /**
     * 获取角色背包
     */
    public function getBag(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 获取背包容量（默认为24格）
        $bagCapacity = 24;

        // 获取角色拥有的物品
        $characterItems = DB::table('character_items')
            ->join('items', 'character_items.item_id', '=', 'items.id')
            ->where('character_items.character_id', $character->id)
            ->select(
                'character_items.id',
                'items.name',
                'items.description',
                'items.type',
                'items.quality',
                'items.icon',
                'character_items.quantity',
                'character_items.equipped',
                'character_items.custom_stats'
            )
            ->get()
            ->map(function($item) {
                // 将custom_stats转换为数组（如果有）
                if ($item->custom_stats) {
                    $item->custom_stats = json_decode($item->custom_stats, true);
                }
                return $item;
            });

        return response()->json([
            'success' => true,
            'data' => [
                'capacity' => $bagCapacity,
                'used' => $characterItems->count(),
                'items' => $characterItems
            ]
        ]);
    }

    /**
     * 使用物品
     */
    public function useItem(Request $request, Character $character, $itemId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 获取角色物品
        $characterItem = CharacterItem::where('character_id', $character->id)
            ->where('id', $itemId)
            ->first();

        if (!$characterItem) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'NOT_FOUND',
                    'message' => '物品不存在',
                ]
            ], 404);
        }

        // 获取物品信息
        $item = Item::find($characterItem->item_id);

        // 检查物品类型是否为消耗品
        if ($item->type !== 'consumable') {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_OPERATION',
                    'message' => '只能使用消耗品',
                ]
            ], 400);
        }

        // 物品效果模拟（这里可以根据实际游戏逻辑扩展）
        $effectMessage = '使用了' . $item->name;

        // 减少物品数量或删除
        if ($characterItem->quantity > 1) {
            $characterItem->quantity -= 1;
            $characterItem->save();
        } else {
            $characterItem->delete();
        }

        return response()->json([
            'success' => true,
            'message' => $effectMessage
        ]);
    }

    /**
     * 装备物品
     */
    public function equipItem(Request $request, Character $character, $itemId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 获取角色物品
        $characterItem = CharacterItem::where('character_id', $character->id)
            ->where('id', $itemId)
            ->first();

        if (!$characterItem) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'NOT_FOUND',
                    'message' => '物品不存在',
                ]
            ], 404);
        }

        // 获取物品信息
        $item = Item::find($characterItem->item_id);

        // 检查物品类型是否为装备
        if ($item->type !== 'equipment') {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_OPERATION',
                    'message' => '只能装备装备类物品',
                ]
            ], 400);
        }

        // 先取消同一类型的其他装备
        DB::table('character_items')
            ->join('items', 'character_items.item_id', '=', 'items.id')
            ->where('character_items.character_id', $character->id)
            ->where('items.equipment_type', $item->equipment_type)
            ->where('character_items.equipped', true)
            ->update(['character_items.equipped' => false]);

        // 装备当前物品
        $characterItem->equipped = true;
        $characterItem->save();

        return response()->json([
            'success' => true,
            'message' => $item->name . ' 已装备'
        ]);
    }

    /**
     * 卸下装备
     */
    public function unequipItem(Request $request, Character $character, $itemId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 获取角色物品
        $characterItem = CharacterItem::where('character_id', $character->id)
            ->where('id', $itemId)
            ->first();

        if (!$characterItem) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'NOT_FOUND',
                    'message' => '物品不存在',
                ]
            ], 404);
        }

        // 检查物品是否已装备
        if (!$characterItem->equipped) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_OPERATION',
                    'message' => '该物品未装备',
                ]
            ], 400);
        }

        // 卸下装备
        $characterItem->equipped = false;
        $characterItem->save();

        // 获取物品信息
        $item = Item::find($characterItem->item_id);

        return response()->json([
            'success' => true,
            'message' => $item->name . ' 已卸下'
        ]);
    }

    /**
     * 丢弃物品
     */
    public function discardItem(Request $request, Character $character, $itemId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 获取角色物品
        $characterItem = CharacterItem::where('character_id', $character->id)
            ->where('id', $itemId)
            ->first();

        if (!$characterItem) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'NOT_FOUND',
                    'message' => '物品不存在',
                ]
            ], 404);
        }

        // 获取物品信息
        $item = Item::find($characterItem->item_id);

        // 检查是否为任务物品（不能丢弃）
        if ($item->quality === 'quest') {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_OPERATION',
                    'message' => '任务物品不能丢弃',
                ]
            ], 400);
        }

        // 删除物品
        $characterItem->delete();

        return response()->json([
            'success' => true,
            'message' => $item->name . ' 已丢弃'
        ]);
    }

    /**
     * 获取角色装备
     */
    public function getEquipment(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟装备数据
        $equipment = [
            'weapon' => [
                'id' => 1,
                'name' => '新手剑',
                'type' => 'weapon',
                'quality' => 'common',
                'level' => 1,
                'attack' => 10,
                'durability' => 100,
                'max_durability' => 100,
                'icon' => '/static/game/items/weapons/sword_01.png',
                'description' => '一把普通的铁剑，适合新手使用。'
            ],
            'armor' => [
                'id' => 2,
                'name' => '布甲',
                'type' => 'armor',
                'quality' => 'common',
                'level' => 1,
                'defense' => 5,
                'durability' => 80,
                'max_durability' => 100,
                'icon' => '/static/game/items/armor/cloth_armor.png',
                'description' => '简单的布制护甲，提供基础防护。'
            ],
            'helmet' => null,
            'boots' => [
                'id' => 3,
                'name' => '草鞋',
                'type' => 'boots',
                'quality' => 'common',
                'level' => 1,
                'defense' => 2,
                'speed' => 5,
                'durability' => 60,
                'max_durability' => 80,
                'icon' => '/static/game/items/boots/straw_shoes.png',
                'description' => '用草编织的简易鞋子。'
            ],
            'accessory1' => null,
            'accessory2' => null
        ];

        // 计算总属性加成
        $totalStats = [
            'attack' => 0,
            'defense' => 0,
            'speed' => 0,
            'hp' => 0,
            'mp' => 0
        ];

        foreach ($equipment as $slot => $item) {
            if ($item) {
                $totalStats['attack'] += $item['attack'] ?? 0;
                $totalStats['defense'] += $item['defense'] ?? 0;
                $totalStats['speed'] += $item['speed'] ?? 0;
                $totalStats['hp'] += $item['hp'] ?? 0;
                $totalStats['mp'] += $item['mp'] ?? 0;
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'equipment' => $equipment,
                'total_stats' => $totalStats,
                'equipment_score' => array_sum($totalStats)
            ]
        ]);
    }

    /**
     * 获取市场商品列表
     */
    public function getMarketItems(Request $request)
    {
        // 模拟市场商品数据
        $items = [
            [
                'id' => 1,
                'name' => '生命药水',
                'description' => '恢复50点生命值的药水',
                'type' => 'consumable',
                'quality' => 'common',
                'price' => 10,
                'stock' => 99,
                'icon' => '/static/game/items/potions/health_potion.png'
            ],
            [
                'id' => 2,
                'name' => '魔法药水',
                'description' => '恢复30点魔法值的药水',
                'type' => 'consumable',
                'quality' => 'common',
                'price' => 8,
                'stock' => 99,
                'icon' => '/static/game/items/potions/mana_potion.png'
            ],
            [
                'id' => 3,
                'name' => '铁剑',
                'description' => '锋利的铁制长剑，攻击力+15',
                'type' => 'equipment',
                'quality' => 'uncommon',
                'price' => 100,
                'stock' => 5,
                'icon' => '/static/game/items/weapons/iron_sword.png'
            ],
            [
                'id' => 4,
                'name' => '皮甲',
                'description' => '坚韧的皮制护甲，防御力+8',
                'type' => 'equipment',
                'quality' => 'uncommon',
                'price' => 80,
                'stock' => 3,
                'icon' => '/static/game/items/armor/leather_armor.png'
            ],
            [
                'id' => 5,
                'name' => '敏捷戒指',
                'description' => '增加移动速度的神秘戒指，速度+5',
                'type' => 'equipment',
                'quality' => 'rare',
                'price' => 200,
                'stock' => 1,
                'icon' => '/static/game/items/accessories/agility_ring.png'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $items
            ]
        ]);
    }

    /**
     * 购买市场商品
     */
    public function purchaseItem(Request $request, $itemId)
    {
        $characterId = $request->input('character_id');

        // 验证角色权限
        $character = Character::find($characterId);
        if (!$character || $character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟购买逻辑
        // 这里应该检查角色金币、物品库存等

        return response()->json([
            'success' => true,
            'message' => '购买成功！物品已添加到背包'
        ]);
    }
}
