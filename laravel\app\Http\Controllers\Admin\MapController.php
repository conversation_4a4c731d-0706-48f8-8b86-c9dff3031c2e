<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Location;
use Illuminate\Support\Facades\Validator;

class MapController extends Controller
{
    /**
     * 显示地图列表
     */
    public function index()
    {
        try {
            // 获取所有地图位置
            \Log::info('开始获取地图数据');
            $maps = Location::orderBy('name')->get();
            \Log::info('获取到地图数据: ' . $maps->count() . ' 条记录');
            \Log::info('地图数据: ' . json_encode($maps->toArray()));

            // 统计信息
            $stats = [
                'total_maps' => $maps->count(),
                'active_maps' => $maps->where('is_active', true)->count(),
                'gate_maps' => $maps->where('type', 'gate')->count(),
                'market_maps' => $maps->where('type', 'market')->count(),
                'palace_maps' => $maps->where('type', 'palace')->count(),
                'garden_maps' => $maps->where('type', 'garden')->count(),
            ];
            \Log::info('地图统计信息: ' . json_encode($stats));

            return view('admin.maps.index', compact('maps', 'stats'));
        } catch (\Exception $e) {
            \Log::error('获取地图数据失败: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            return back()->with('error', '获取地图数据失败：' . $e->getMessage());
        }
    }

    /**
     * 显示创建地图表单
     */
    public function create()
    {
        $typeOptions = [
            'gate' => '大门',
            'market' => '市场',
            'palace' => '宫殿',
            'garden' => '花园',
            'wilderness' => '野外',
            'dungeon' => '副本',
            'special' => '特殊'
        ];
        return view('admin.maps.create', compact('typeOptions'));
    }

    /**
     * 保存新地图
     */
    public function store(Request $request)
    {
        // 验证请求数据
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:gate,market,palace,garden,wilderness,dungeon,special',
            'x' => 'required|integer',
            'y' => 'required|integer',
            'level_requirement' => 'required|integer|min:1',
            'is_safe' => 'boolean',
            'is_active' => 'boolean',
            'region_id' => 'nullable|integer|exists:regions,id',
        ]);

        // 处理布尔值
        $validated['is_safe'] = $request->has('is_safe');
        $validated['is_active'] = $request->has('is_active');

        // 如果没有指定region_id，使用默认值
        if (!isset($validated['region_id'])) {
            $validated['region_id'] = 5; // 默认龙宫
        }

        // 创建地图位置
        try {
            $map = Location::create($validated);
            return redirect()->route('admin.maps.edit', $map->id)
                ->with('success', "地图位置 \"{$map->name}\" 创建成功！");
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', '创建地图位置失败：' . $e->getMessage());
        }
    }

    /**
     * 显示编辑地图表单
     */
    public function edit(Location $map)
    {
        $typeOptions = [
            'gate' => '大门',
            'market' => '市场',
            'palace' => '宫殿',
            'garden' => '花园',
            'wilderness' => '野外',
            'dungeon' => '副本',
            'special' => '特殊'
        ];

        // 获取同一区域的所有位置
        $locations = Location::where('region_id', $map->region_id)->get();

        return view('admin.maps.edit', compact('map', 'typeOptions', 'locations'));
    }

    /**
     * 更新地图信息
     */
    public function update(Request $request, Location $map)
    {
        // 验证请求数据
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:gate,market,palace,garden,wilderness,dungeon,special',
            'x' => 'required|integer',
            'y' => 'required|integer',
            'level_requirement' => 'required|integer|min:1',
            'is_safe' => 'boolean',
            'is_active' => 'boolean',
            'region_id' => 'nullable|integer|exists:regions,id',
        ]);

        // 处理布尔值
        $validated['is_safe'] = $request->has('is_safe');
        $validated['is_active'] = $request->has('is_active');

        // 更新地图位置
        try {
            $map->update($validated);
            return redirect()->route('admin.maps.edit', $map->id)
                ->with('success', "地图位置 \"{$map->name}\" 更新成功！");
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', '更新地图位置失败：' . $e->getMessage());
        }
    }

    /**
     * 删除地图位置
     */
    public function destroy($id)
    {
        try {
            $map = Location::findOrFail($id);

            // 检查是否有角色在此位置
            $characterCount = \DB::table('characters')->where('location_id', $map->id)->count();
            if ($characterCount > 0) {
                return redirect()->route('admin.maps.index')
                    ->with('error', '无法删除地图位置，有角色正在此位置');
            }

            $map->delete();

            return redirect()->route('admin.maps.index')
                ->with('success', '地图位置已删除');
        } catch (\Exception $e) {
            return redirect()->route('admin.maps.index')
                ->with('error', '地图位置删除失败: ' . $e->getMessage());
        }
    }

    // 这些方法已移除，因为现在我们直接管理 Location 而不是 Region

    /**
     * 调试方法：查看数据库中的地图信息
     */
    public function debug()
    {
        try {
            // 检查regions表是否存在
            $tablesExist = [
                'regions' => \Schema::hasTable('regions'),
                'locations' => \Schema::hasTable('locations')
            ];

            // 获取表结构信息
            $regionColumns = \Schema::getColumnListing('regions');
            $locationColumns = \Schema::getColumnListing('locations');

            // 获取所有地图数据
            $maps = [];
            $mapCount = 0;
            if($tablesExist['regions']) {
                $maps = \DB::table('regions')->get();
                $mapCount = count($maps);
            }

            // 获取所有位置数据
            $locations = [];
            $locationCount = 0;
            if($tablesExist['locations']) {
                $locations = \DB::table('locations')->get();
                $locationCount = count($locations);
            }

            // 获取数据库连接信息
            $connection = \DB::connection()->getDatabaseName();

            // 返回调试信息
            return response()->json([
                'success' => true,
                'database' => $connection,
                'tables_exist' => $tablesExist,
                'map_count' => $mapCount,
                'location_count' => $locationCount,
                'region_columns' => $regionColumns,
                'location_columns' => $locationColumns,
                'maps' => $maps,
                'locations' => $locations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 显示创建新位置的表单
     */
    public function createLocation()
    {
        return view('admin.locations.create');
    }
}
