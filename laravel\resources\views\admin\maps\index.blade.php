@extends('admin.layouts.app')

@section('title', '地图管理')

@section('content')
<!-- 页面标题在布局模板中显示 -->
@section('breadcrumb')
<a href="{{ route('admin.maps.index') }}">地图管理</a>
@endsection

@section('page-title', '地图管理')

<!-- 统计卡片 -->
<div class="layui-row layui-col-space15">
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">总地图数</div>
            <div class="layui-card-body">
                <h2>{{ $stats['total_maps'] ?? 0 }}</h2>
            </div>
        </div>
                    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">活跃地图</div>
            <div class="layui-card-body">
                <h2>{{ $stats['active_maps'] ?? 0 }}</h2>
            </div>
        </div>
                    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">大门位置</div>
            <div class="layui-card-body">
                <h2>{{ $stats['gate_maps'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">市场位置</div>
            <div class="layui-card-body">
                <h2>{{ $stats['market_maps'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">宫殿位置</div>
            <div class="layui-card-body">
                <h2>{{ $stats['palace_maps'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">花园位置</div>
            <div class="layui-card-body">
                <h2>{{ $stats['garden_maps'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-header">
        地图位置列表
        <a href="{{ route('admin.maps.create') }}" class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;">添加位置</a>
        </div>
    <div class="layui-card-body">
        <table class="layui-table" style="display: table !important; visibility: visible !important;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>类型</th>
                            <th>坐标</th>
                            <th>等级要求</th>
                            <th>安全区</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($maps as $map)
                        <tr>
                            <td>{{ $map->id }}</td>
                            <td>{{ $map->name }}</td>
                            <td>
                                @if($map->type == 'gate')
                            <span class="layui-badge layui-bg-blue">大门</span>
                                @elseif($map->type == 'market')
                            <span class="layui-badge layui-bg-green">市场</span>
                                @elseif($map->type == 'palace')
                            <span class="layui-badge layui-bg-red">宫殿</span>
                                @elseif($map->type == 'garden')
                            <span class="layui-badge layui-bg-orange">花园</span>
                                @elseif($map->type == 'wilderness')
                            <span class="layui-badge layui-bg-cyan">野外</span>
                                @elseif($map->type == 'dungeon')
                            <span class="layui-badge layui-bg-black">副本</span>
                                @else
                            <span class="layui-badge layui-bg-gray">{{ $map->type }}</span>
                                @endif
                            </td>
                            <td>({{ $map->x }}, {{ $map->y }})</td>
                            <td>{{ $map->level_requirement ?? 1 }}</td>
                            <td>
                                @if($map->is_safe)
                            <span class="layui-badge layui-bg-green">安全</span>
                                @else
                            <span class="layui-badge layui-bg-red">危险</span>
                                @endif
                            </td>
                            <td>
                                @if($map->is_active)
                            <span class="layui-badge layui-bg-green">活跃</span>
                                @else
                            <span class="layui-badge layui-bg-gray">关闭</span>
                                @endif
                            </td>
                            <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.maps.edit', $map->id) }}" class="layui-btn layui-btn-xs">
                                <i class="layui-icon layui-icon-edit"></i> 编辑
                                    </a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="confirmDelete({{ $map->id }}, '{{ $map->name }}')">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                    <td colspan="8" style="text-align: center;">暂无地图位置数据</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
    </div>
</div>

<!-- 隐藏的删除表单 -->
<form id="deleteForm" action="" method="POST" style="display: none;">
                    @csrf
                    @method('DELETE')
                </form>
@endsection

@section('js')
<script>
    function confirmDelete(id, name) {
        layui.use('layer', function(){
            var layer = layui.layer;

            layer.confirm('确定要删除地图位置 "' + name + '" 吗？此操作不可逆。', {
                btn: ['确认删除','取消']
            }, function(){
                document.getElementById('deleteForm').action = "{{ route('admin.maps.index') }}/" + id;
                document.getElementById('deleteForm').submit();
            });
        });
    }
</script>
@endsection
