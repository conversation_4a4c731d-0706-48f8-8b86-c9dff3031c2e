<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class QuestController extends Controller
{
    /**
     * 显示任务列表
     */
    public function index()
    {
        try {
            $quests = DB::table('quests')
                ->orderBy('level_requirement')
                ->orderBy('name')
                ->paginate(15);

            return view('admin.quests.index', compact('quests'));
        } catch (\Exception $e) {
            // 创建一个空的分页对象而不是简单的集合
            $quests = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.quests.index', compact('quests'));
        }
    }

    /**
     * 显示创建任务表单
     */
    public function create()
    {
        try {
            // 获取所有任务作为前置任务选项
            $quests = DB::table('quests')
                ->orderBy('level_requirement')
                ->orderBy('name')
                ->get();

            // 获取所有地图
            $maps = DB::table('regions')
                ->orderBy('name')
                ->get();

            // 获取所有NPC
            $npcs = DB::table('npcs')
                ->orderBy('name')
                ->get();

            // 获取所有物品
            $items = DB::table('items')
                ->orderBy('name')
                ->get();

            // 获取所有技能
            $skills = DB::table('skills')
                ->orderBy('name')
                ->get();

            // 获取所有怪物
            $monsters = DB::table('monsters')
                ->orderBy('level')
                ->orderBy('name')
                ->get();

            return view('admin.quests.create', compact('quests', 'maps', 'npcs', 'items', 'skills', 'monsters'));
        } catch (\Exception $e) {
            $quests = collect([]);
            $maps = collect([]);
            $npcs = collect([]);
            $items = collect([]);
            $skills = collect([]);
            $monsters = collect([]);
            return view('admin.quests.create', compact('quests', 'maps', 'npcs', 'items', 'skills', 'monsters'));
        }
    }

    /**
     * 保存新任务
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:quests',
            'description' => 'required|string',
            'level_requirement' => 'required|integer|min:1',
            'npc_id' => 'nullable|integer|exists:npcs,id',
            'exp_reward' => 'required|integer|min:0',
            'silver_reward' => 'required|integer|min:0',
            'is_repeatable' => 'boolean',
            'is_main_quest' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // 创建任务
            $questId = DB::table('quests')->insertGetId([
                'name' => $request->name,
                'description' => $request->description,
                'level_requirement' => $request->level_requirement,
                'npc_id' => $request->npc_id,
                'exp_reward' => $request->exp_reward,
                'silver_reward' => $request->silver_reward,
                'is_repeatable' => $request->has('is_repeatable') ? 1 : 0,
                'is_main_quest' => $request->has('is_main_quest') ? 1 : 0,
                'is_active' => $request->has('is_active') ? 1 : 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // 保存任务所需物品
            if ($request->has('quest_items') && is_array($request->quest_items)) {
                foreach ($request->quest_items as $item) {
                    if (isset($item['item_id']) && isset($item['quantity'])) {
                        DB::table('quest_items')->insert([
                            'quest_id' => $questId,
                            'item_id' => $item['item_id'],
                            'quantity' => $item['quantity'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // 保存任务所需击杀怪物
            if ($request->has('quest_monsters') && is_array($request->quest_monsters)) {
                foreach ($request->quest_monsters as $monster) {
                    if (isset($monster['monster_id']) && isset($monster['quantity'])) {
                        DB::table('quest_monsters')->insert([
                            'quest_id' => $questId,
                            'monster_id' => $monster['monster_id'],
                            'quantity' => $monster['quantity'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // 保存任务奖励物品
            if ($request->has('reward_items') && is_array($request->reward_items)) {
                foreach ($request->reward_items as $reward) {
                    if (isset($reward['item_id']) && isset($reward['quantity'])) {
                        DB::table('quest_rewards')->insert([
                            'quest_id' => $questId,
                            'item_id' => $reward['item_id'],
                            'quantity' => $reward['quantity'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('admin.quests.index')
                ->with('success', '任务创建成功');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', '任务创建失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 显示任务详情
     */
    public function show($id)
    {
        try {
            $quest = DB::table('quests')
                ->leftJoin('npcs', 'quests.npc_id', '=', 'npcs.id')
                ->select(
                    'quests.*',
                    'npcs.name as npc_name'
                )
                ->where('quests.id', $id)
                ->first();

            if (!$quest) {
                return redirect()->route('admin.quests.index')
                    ->with('error', '任务不存在');
            }

            // 获取任务所需物品
            $questItems = DB::table('quest_items')
                ->join('items', 'quest_items.item_id', '=', 'items.id')
                ->where('quest_items.quest_id', $id)
                ->select(
                    'quest_items.*',
                    'items.name as item_name'
                )
                ->get();

            // 获取任务所需击杀怪物
            $questMonsters = DB::table('quest_monsters')
                ->join('monsters', 'quest_monsters.monster_id', '=', 'monsters.id')
                ->where('quest_monsters.quest_id', $id)
                ->select(
                    'quest_monsters.*',
                    'monsters.name as monster_name'
                )
                ->get();

            // 获取任务奖励物品
            $questRewards = DB::table('quest_rewards')
                ->join('items', 'quest_rewards.item_id', '=', 'items.id')
                ->where('quest_rewards.quest_id', $id)
                ->select(
                    'quest_rewards.*',
                    'items.name as item_name'
                )
                ->get();

            return view('admin.quests.show', compact('quest', 'questItems', 'questMonsters', 'questRewards'));
        } catch (\Exception $e) {
            return redirect()->route('admin.quests.index')
                ->with('error', '无法获取任务信息: ' . $e->getMessage());
        }
    }

    /**
     * 显示编辑任务表单
     */
    public function edit($id)
    {
        try {
            $quest = DB::table('quests')->where('id', $id)->first();

            if (!$quest) {
                return redirect()->route('admin.quests.index')
                    ->with('error', '任务不存在');
            }

            // 获取所有NPC
            $npcs = DB::table('npcs')
                ->orderBy('name')
                ->get();

            // 获取所有物品
            $items = DB::table('items')
                ->orderBy('name')
                ->get();

            // 获取所有怪物
            $monsters = DB::table('monsters')
                ->orderBy('level')
                ->orderBy('name')
                ->get();

            // 获取任务所需物品
            $questItems = DB::table('quest_items')
                ->where('quest_id', $id)
                ->get()
                ->keyBy('item_id')
                ->toArray();

            // 获取任务所需击杀怪物
            $questMonsters = DB::table('quest_monsters')
                ->where('quest_id', $id)
                ->get()
                ->keyBy('monster_id')
                ->toArray();

            // 获取任务奖励物品
            $questRewards = DB::table('quest_rewards')
                ->where('quest_id', $id)
                ->get()
                ->keyBy('item_id')
                ->toArray();

            return view('admin.quests.edit', compact(
                'quest',
                'npcs',
                'items',
                'monsters',
                'questItems',
                'questMonsters',
                'questRewards'
            ));
        } catch (\Exception $e) {
            return redirect()->route('admin.quests.index')
                ->with('error', '无法获取任务信息: ' . $e->getMessage());
        }
    }

    /**
     * 更新任务信息
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:quests,name,' . $id,
            'description' => 'required|string',
            'level_requirement' => 'required|integer|min:1',
            'npc_id' => 'nullable|integer|exists:npcs,id',
            'exp_reward' => 'required|integer|min:0',
            'silver_reward' => 'required|integer|min:0',
            'is_repeatable' => 'boolean',
            'is_main_quest' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // 更新任务基本信息
            DB::table('quests')
                ->where('id', $id)
                ->update([
                    'name' => $request->name,
                    'description' => $request->description,
                    'level_requirement' => $request->level_requirement,
                    'npc_id' => $request->npc_id,
                    'exp_reward' => $request->exp_reward,
                    'silver_reward' => $request->silver_reward,
                    'is_repeatable' => $request->has('is_repeatable') ? 1 : 0,
                    'is_main_quest' => $request->has('is_main_quest') ? 1 : 0,
                    'is_active' => $request->has('is_active') ? 1 : 0,
                    'updated_at' => now(),
                ]);

            // 更新任务所需物品
            DB::table('quest_items')->where('quest_id', $id)->delete();

            if ($request->has('quest_items') && is_array($request->quest_items)) {
                foreach ($request->quest_items as $item) {
                    if (isset($item['item_id']) && isset($item['quantity'])) {
                        DB::table('quest_items')->insert([
                            'quest_id' => $id,
                            'item_id' => $item['item_id'],
                            'quantity' => $item['quantity'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // 更新任务所需击杀怪物
            DB::table('quest_monsters')->where('quest_id', $id)->delete();

            if ($request->has('quest_monsters') && is_array($request->quest_monsters)) {
                foreach ($request->quest_monsters as $monster) {
                    if (isset($monster['monster_id']) && isset($monster['quantity'])) {
                        DB::table('quest_monsters')->insert([
                            'quest_id' => $id,
                            'monster_id' => $monster['monster_id'],
                            'quantity' => $monster['quantity'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            // 更新任务奖励物品
            DB::table('quest_rewards')->where('quest_id', $id)->delete();

            if ($request->has('reward_items') && is_array($request->reward_items)) {
                foreach ($request->reward_items as $reward) {
                    if (isset($reward['item_id']) && isset($reward['quantity'])) {
                        DB::table('quest_rewards')->insert([
                            'quest_id' => $id,
                            'item_id' => $reward['item_id'],
                            'quantity' => $reward['quantity'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('admin.quests.index')
                ->with('success', '任务更新成功');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', '任务更新失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 删除任务
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            // 删除任务关联数据
            DB::table('quest_items')->where('quest_id', $id)->delete();
            DB::table('quest_monsters')->where('quest_id', $id)->delete();
            DB::table('quest_rewards')->where('quest_id', $id)->delete();

            // 删除角色任务进度
            DB::table('character_quests')->where('quest_id', $id)->delete();

            // 删除任务
            DB::table('quests')->where('id', $id)->delete();

            DB::commit();

            return redirect()->route('admin.quests.index')
                ->with('success', '任务已删除');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->route('admin.quests.index')
                ->with('error', '任务删除失败: ' . $e->getMessage());
        }
    }
}
