<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'password',
        'current_character_id',
        'current_region_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [];

    /**
     * 获取用户的所有角色
     */
    public function characters()
    {
        return $this->hasMany(Character::class);
    }

    /**
     * 获取用户当前选择的角色
     */
    public function currentCharacter()
    {
        if ($this->current_character_id) {
            return Character::find($this->current_character_id);
        }

        // 如果没有指定当前角色，返回第一个角色
        return $this->characters()->first();
    }

    /**
     * 获取用户当前选择的大区
     */
    public function currentRegion()
    {
        if ($this->current_region_id) {
            return Region::find($this->current_region_id);
        }

        return null;
    }

    /**
     * 设置当前角色
     */
    public function setCurrentCharacter(Character $character)
    {
        $this->current_character_id = $character->id;
        $this->save();

        return $this;
    }

    /**
     * 设置当前大区
     */
    public function setCurrentRegion(Region $region)
    {
        $this->current_region_id = $region->id;
        $this->save();

        return $this;
    }
}
