/**
 * 钱庄系统API服务
 * 提供钱庄相关的接口调用
 */
import { get, post } from '../request.js';
import logger from '../../utils/logger.js';
import { setCache, CACHE_TYPE } from './cacheService.js';
import { API_BASE_URL } from '../config.js';

// 缓存键
const CACHE_KEYS = {
    ACCOUNT_INFO: 'bank_account_info',
    TRANSACTIONS: 'bank_transactions'
};

/**
 * 钱庄服务
 */
const bankService = {
    /**
     * 获取银行账户信息
     * @param {string|number} characterId - 角色ID
     * @returns {Promise<Object>} - 银行账户信息
     */
    getAccountInfo(characterId) {
        // 确保characterId是字符串
        const characterIdStr = String(characterId);
        logger.debug('[BankService] 获取银行账户信息, characterId:', characterIdStr);
        
        // 验证characterId
        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {
            logger.error('[BankService] 无效的角色ID:', characterIdStr);
            return Promise.reject(new Error('无效的角色ID'));
        }
        
        // 清除缓存，确保获取最新数据
        setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);
        
        const apiUrl = `/bank/account/${characterIdStr}`;
        const fullUrl = API_BASE_URL + apiUrl;
        logger.debug('[BankService] 请求 URL:', fullUrl);
        
        // 在请求前记录日志
        logger.debug('[BankService] 开始API请求:', fullUrl);
        
        return get(apiUrl, {}, {
            loading: true,
            loadingText: '获取账户信息...'
        }).then(res => {
            // 增强日志记录 - 成功
            logger.debug('[BankService] 银行账户信息获取成功:', JSON.stringify(res, null, 2));
            logger.debug('[BankService] API请求成功:', fullUrl);
            logger.debug('[BankService] 响应数据:', JSON.stringify(res, null, 2));
            
            // 检查并格式化响应数据
            let formattedData;
            
            // 标准格式：API返回的是包含success和data字段的对象
            if (res && typeof res === 'object') {
                if (res.success === true && res.data) {
                    // 标准格式，直接返回
                    formattedData = res;
                } else if (res.account) {
                    // 只包含account信息，包装成标准格式
                    formattedData = {
                        success: true,
                        data: {
                            account: res.account,
                            character: res.character || {}
                        }
                    };
                } else if ('silver' in res || 'gold_ingot' in res) {
                    // 响应本身就是账户信息
                    formattedData = {
                        success: true,
                        data: {
                            account: {
                                silver: res.silver || 0,
                                gold_ingot: res.gold_ingot || 0
                            }
                        }
                    };
                } else {
                    // 无法识别的格式，但至少是个对象，尝试强制格式化
                    logger.warn('[BankService] 未知的响应格式，尝试格式化:', JSON.stringify(res));
                    formattedData = {
                        success: true,
                        data: {
                            account: {
                                silver: 0,
                                gold_ingot: 0
                            }
                        }
                    };
                }
            } else {
                // 完全无法处理的响应，创建默认响应
                logger.error('[BankService] 无效响应格式:', res);
                formattedData = {
                    success: false,
                    data: {
                        account: {
                            silver: 0,
                            gold_ingot: 0
                        }
                    },
                    error: '无效的响应格式'
                };
            }
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, formattedData.data, 60 * 1000); // 缓存1分钟
            return formattedData;
        }).catch(error => {
            // 增强日志记录 - 失败
            logger.error('[BankService] 获取银行账户信息失败:', error);
            logger.error('[BankService] 请求URL:', fullUrl);
            logger.error('[BankService] API请求失败:', fullUrl);
            logger.error('[BankService] 错误详情:', JSON.stringify(error, null, 2));
            
            if (error.response) {
                logger.error('[BankService] 响应状态:', error.response.status);
                logger.error('[BankService] 响应数据:', JSON.stringify(error.response.data, null, 2));
            }
            
            // 如果后端有详细错误信息，输出它
            if (error.data && error.data.error) {
                logger.error('[BankService] 后端错误详情:', error.data.error);
            }
            
            // 返回错误信息和默认值
            throw {
                success: false,
                message: error.message || '获取银行账户信息失败',
                data: {
                    account: {
                        silver: 0,
                        gold_ingot: 0
                    }
                },
                original_error: error
            };
        });
    },

    /**
     * 存款
     * @param {string|number} characterId - 角色ID
     * @param {string} currency - 货币类型 (silver/gold_ingot)
     * @param {number} amount - 存款金额
     * @returns {Promise<Object>} - 存款结果
     */
    deposit(characterId, currency, amount) {
        // 确保characterId是字符串
        const characterIdStr = String(characterId);
        // 确保金额是有效数字
        const amountNum = parseInt(amount) || 0;
        // 确保货币类型有效
        if (currency !== 'silver' && currency !== 'gold_ingot') {
            logger.error('[BankService] 无效的货币类型:', currency);
            return Promise.reject(new Error(`无效的货币类型: ${currency}`));
        }
        
        logger.debug('[BankService] 存款操作, characterId:', characterIdStr, 'currency:', currency, 'amount:', amountNum);
        
        // 验证characterId
        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {
            logger.error('[BankService] 无效的角色ID:', characterIdStr);
            return Promise.reject(new Error('无效的角色ID'));
        }
        
        // 验证金额
        if (amountNum <= 0) {
            logger.error('[BankService] 无效的存款金额:', amountNum);
            return Promise.reject(new Error('存款金额必须大于0'));
        }
        
        return post(`/bank/deposit/${characterIdStr}`, {
            currency: currency,
            amount: amountNum
        }, {
            loading: true,
            loadingText: '正在存款...'
        }).then(res => {
            logger.debug('[BankService] 存款成功:', JSON.stringify(res, null, 2));
            
            // 清除缓存，确保下次获取最新数据
            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);
            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);
            
            return {
                success: true,
                message: res.data?.message || '存款成功',
                account: res.data?.account || {},
                character: res.data?.character || {}
            };
        }).catch(error => {
            logger.error('[BankService] 存款失败:', error);
            // 详细记录错误信息
            if (error.response) {
                logger.error('[BankService] 错误状态码:', error.response.status);
                logger.error('[BankService] 错误响应:', JSON.stringify(error.response.data, null, 2));
            }
            
            throw {
                success: false,
                message: error.response?.data?.error?.message || '存款失败，请重试',
                details: error.message || ''
            };
        });
    },

    /**
     * 取款
     * @param {string|number} characterId - 角色ID
     * @param {string} currency - 货币类型 (silver/gold_ingot)
     * @param {number} amount - 取款金额
     * @returns {Promise<Object>} - 取款结果
     */
    withdraw(characterId, currency, amount) {
        // 确保characterId是字符串
        const characterIdStr = String(characterId);
        // 确保金额是有效数字
        const amountNum = parseInt(amount) || 0;
        // 确保货币类型有效
        if (currency !== 'silver' && currency !== 'gold_ingot') {
            logger.error('[BankService] 无效的货币类型:', currency);
            return Promise.reject(new Error(`无效的货币类型: ${currency}`));
        }
        
        logger.debug('[BankService] 取款操作, characterId:', characterIdStr, 'currency:', currency, 'amount:', amountNum);
        
        // 验证characterId
        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {
            logger.error('[BankService] 无效的角色ID:', characterIdStr);
            return Promise.reject(new Error('无效的角色ID'));
        }
        
        // 验证金额
        if (amountNum <= 0) {
            logger.error('[BankService] 无效的取款金额:', amountNum);
            return Promise.reject(new Error('取款金额必须大于0'));
        }
        
        return post(`/bank/withdraw/${characterIdStr}`, {
            currency: currency,
            amount: amountNum
        }, {
            loading: true,
            loadingText: '正在取款...'
        }).then(res => {
            logger.debug('[BankService] 取款成功:', JSON.stringify(res, null, 2));
            
            // 清除缓存，确保下次获取最新数据
            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);
            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);
            
            return {
                success: true,
                message: res.data?.message || '取款成功',
                account: res.data?.account || {},
                character: res.data?.character || {}
            };
        }).catch(error => {
            logger.error('[BankService] 取款失败:', error);
            // 详细记录错误信息
            if (error.response) {
                logger.error('[BankService] 错误状态码:', error.response.status);
                logger.error('[BankService] 错误响应:', JSON.stringify(error.response.data, null, 2));
            }
            
            throw {
                success: false,
                message: error.response?.data?.error?.message || '取款失败，请重试',
                details: error.message || ''
            };
        });
    },

    /**
     * 获取交易历史记录
     * @param {string|number} characterId - 角色ID
     * @param {Object} params - 查询参数
     * @param {number} [params.page=1] - 页码
     * @param {number} [params.per_page=10] - 每页条数
     * @param {string} [params.currency] - 货币类型筛选 (silver/gold_ingot)
     * @param {string} [params.type] - 交易类型筛选 (deposit/withdraw)
     * @returns {Promise<Object>} - 交易历史记录
     */
    getTransactionHistory(characterId, params = {}) {
        // 确保characterId是字符串
        const characterIdStr = String(characterId);
        logger.debug('[BankService] 获取交易历史记录, characterId:', characterIdStr, 'params:', params);
        
        // 验证characterId
        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {
            logger.error('[BankService] 无效的角色ID:', characterIdStr);
            return Promise.reject(new Error('无效的角色ID'));
        }
        
        // 清除缓存，确保获取最新数据
        setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);
        
        const apiUrl = `/bank/transactions/${characterIdStr}`;
        const fullUrl = API_BASE_URL + apiUrl;
        logger.debug('[BankService] 请求交易历史URL:', fullUrl, '参数:', params);
        
        return get(apiUrl, params, {
            loading: true,
            loadingText: '获取交易记录...'
        }).then(res => {
            logger.debug('[BankService] 交易历史记录获取成功:', JSON.stringify(res, null, 2));
            
            // 格式化响应数据
            let formattedData;
            
            if (res && typeof res === 'object') {
                if (res.success === true && res.data && res.data.transactions) {
                    // 标准格式: { success: true, data: { transactions: [...], pagination: {...} } }
                    formattedData = res;
                    logger.debug('[BankService] 处理标准格式交易记录, 数量:', res.data.transactions.length);
                } else if (Array.isArray(res)) {
                    // 响应直接是交易数组: [...]
                    logger.debug('[BankService] 处理数组格式交易记录, 数量:', res.length);
                    formattedData = {
                        success: true,
                        data: {
                            transactions: res.map(normalizeTransaction),
                            pagination: {
                                current_page: parseInt(params.page) || 1,
                                per_page: parseInt(params.per_page) || 10,
                                total: res.length,
                                last_page: 1
                            }
                        }
                    };
                } else if (res.transactions || res.data?.transactions) {
                    // 包含交易数据的对象: { transactions: [...], pagination: {...} } 或 { data: { transactions: [...] } }
                    const transactions = res.transactions || res.data?.transactions || [];
                    logger.debug('[BankService] 处理嵌套交易记录, 数量:', transactions.length);
                    
                    const pagination = res.pagination || res.data?.pagination || {
                        current_page: parseInt(params.page) || 1,
                        per_page: parseInt(params.per_page) || 10,
                        total: transactions.length,
                        last_page: 1
                    };
                    
                    formattedData = {
                        success: true,
                        data: {
                            transactions: transactions.map(normalizeTransaction),
                            pagination: pagination
                        }
                    };
                } else if (res.data && Array.isArray(res.data)) {
                    // 响应格式为 { data: [...] }
                    logger.debug('[BankService] 处理data数组交易记录, 数量:', res.data.length);
                    formattedData = {
                        success: true,
                        data: {
                            transactions: res.data.map(normalizeTransaction),
                            pagination: {
                                current_page: parseInt(params.page) || 1,
                                per_page: parseInt(params.per_page) || 10,
                                total: res.data.length,
                                last_page: Math.ceil(res.data.length / (parseInt(params.per_page) || 10))
                            }
                        }
                    };
                } else {
                    // 无法识别的格式，创建空数据
                    logger.warn('[BankService] 未知交易历史响应格式:', JSON.stringify(res));
                    formattedData = {
                        success: true,
                        data: {
                            transactions: [],
                            pagination: {
                                current_page: 1,
                                per_page: 10,
                                total: 0,
                                last_page: 1
                            }
                        }
                    };
                }
                
                // 在返回前确保交易记录的格式标准化
                if (formattedData.data && formattedData.data.transactions) {
                    formattedData.data.transactions = formattedData.data.transactions.map(normalizeTransaction);
                    logger.debug('[BankService] 标准化后的交易记录样例:', 
                        formattedData.data.transactions.length > 0 ? 
                        JSON.stringify(formattedData.data.transactions[0]) : '无记录');
                }
            } else {
                // 无效响应
                logger.error('[BankService] 无效交易历史响应格式:', res);
                formattedData = {
                    success: false,
                    data: {
                        transactions: [],
                        pagination: {
                            current_page: 1,
                            per_page: 10,
                            total: 0,
                            last_page: 1
                        }
                    }
                };
            }
            
            // 缓存结果
            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, formattedData.data, 60 * 1000); // 缓存1分钟
            return formattedData;
        }).catch(error => {
            logger.error('[BankService] 获取交易历史记录失败:', error);
            logger.error('[BankService] 请求URL:', fullUrl, '参数:', params);
            
            if (error.response) {
                logger.error('[BankService] 响应状态:', error.response.status);
                logger.error('[BankService] 响应数据:', JSON.stringify(error.response.data, null, 2));
            }
            
            // 返回错误信息和默认空数据
            throw {
                success: false,
                message: error.message || '获取交易历史记录失败',
                data: {
                    transactions: [],
                    pagination: {
                        current_page: 1,
                        per_page: 10,
                        total: 0,
                        last_page: 1
                    }
                },
                original_error: error
            };
        });
    }
};

/**
 * 标准化交易记录格式
 * 确保所有必须字段都存在，类型正确
 * @param {Object} transaction - 交易记录对象
 * @returns {Object} - 标准化后的交易记录对象
 */
function normalizeTransaction(transaction) {
    if (!transaction) return {
        id: 'unknown-' + Math.random().toString(36).substr(2, 9),
        character_id: 0,
        type: 'deposit',
        currency: 'silver',
        amount: 0,
        balance: 0,
        description: '未知交易',
        created_at: new Date().toISOString()
    };
    
    // 返回标准化的交易记录
    return {
        id: transaction.id || 'tx-' + Math.random().toString(36).substr(2, 9),
        character_id: transaction.character_id || 0,
        type: ['deposit', 'withdraw'].includes(transaction.type) ? transaction.type : 'deposit',
        currency: ['silver', 'gold_ingot'].includes(transaction.currency) ? transaction.currency : 'silver',
        amount: parseInt(transaction.amount) || 0,
        balance: parseInt(transaction.balance) || 0,
        description: transaction.description || generateTransactionDescription(transaction),
        created_at: transaction.created_at || new Date().toISOString()
    };
}

/**
 * 为交易记录生成描述
 * @param {Object} transaction - 交易记录对象
 * @returns {string} - 生成的描述
 */
function generateTransactionDescription(transaction) {
    const type = transaction.type === 'withdraw' ? '取出' : '存入';
    const amount = parseInt(transaction.amount) || 0;
    const currency = transaction.currency === 'gold_ingot' ? '金砖' : '银两';
    return `${type} ${amount} ${currency}`;
}

export default bankService; 