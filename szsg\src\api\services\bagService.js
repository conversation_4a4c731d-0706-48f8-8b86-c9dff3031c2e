/**
 * 背包系统API服务
 * 提供背包相关的接口调用
 */
import { get, post, del } from '../request.js';
import logger from '../../utils/logger.js';

/**
 * 背包服务
 */
const bagService = {
    /**
     * 获取角色背包数据
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 背包数据
     */
    getBagData(characterId) {
        logger.debug('[BagService] 获取背包数据, characterId:', characterId);
        
        return get(`/characters/${characterId}/bag`, {}, {
            loading: true,
            loadingText: '加载背包数据...'
        }).then(res => {
            logger.debug('[BagService] 背包数据响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 获取背包数据失败:', error);
            throw error;
        });
    },

    /**
     * 使用物品
     * @param {string} characterId - 角色ID
     * @param {string} itemId - 物品ID
     * @param {number} quantity - 使用数量
     * @returns {Promise<Object>} - 使用结果
     */
    useItem(characterId, itemId, quantity = 1) {
        logger.debug('[BagService] 使用物品, characterId:', characterId, 'itemId:', itemId, 'quantity:', quantity);
        
        return post(`/characters/${characterId}/bag/items/${itemId}/use`, {
            quantity
        }, {
            loading: true,
            loadingText: '使用物品中...'
        }).then(res => {
            logger.debug('[BagService] 使用物品响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 使用物品失败:', error);
            throw error;
        });
    },

    /**
     * 装备物品
     * @param {string} characterId - 角色ID
     * @param {string} itemId - 物品ID
     * @returns {Promise<Object>} - 装备结果
     */
    equipItem(characterId, itemId) {
        logger.debug('[BagService] 装备物品, characterId:', characterId, 'itemId:', itemId);
        
        return post(`/characters/${characterId}/bag/items/${itemId}/equip`, {}, {
            loading: true,
            loadingText: '装备中...'
        }).then(res => {
            logger.debug('[BagService] 装备物品响应:', res);
            // 清除背包缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 装备物品失败:', error);
            throw error;
        });
    },

    /**
     * 卸下装备
     * @param {string} characterId - 角色ID
     * @param {string} itemId - 物品ID
     * @returns {Promise<Object>} - 卸下结果
     */
    unequipItem(characterId, itemId) {
        logger.debug('[BagService] 卸下装备, characterId:', characterId, 'itemId:', itemId);
        
        return post(`/characters/${characterId}/bag/items/${itemId}/unequip`, {}, {
            loading: true,
            loadingText: '卸下装备中...'
        }).then(res => {
            logger.debug('[BagService] 卸下装备响应:', res);
            // 清除背包缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 卸下装备失败:', error);
            throw error;
        });
    },

    /**
     * 丢弃物品
     * @param {string} characterId - 角色ID
     * @param {string} itemId - 物品ID
     * @param {number} quantity - 丢弃数量
     * @returns {Promise<Object>} - 丢弃结果
     */
    discardItem(characterId, itemId, quantity = 1) {
        logger.debug('[BagService] 丢弃物品, characterId:', characterId, 'itemId:', itemId, 'quantity:', quantity);

        return del(`/characters/${characterId}/bag/items/${itemId}`, {
            quantity
        }, {
            loading: true,
            loadingText: '丢弃物品中...'
        }).then(res => {
            logger.debug('[BagService] 丢弃物品响应:', res);
            // 清除背包缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 丢弃物品失败:', error);
            throw error;
        });
    },

    /**
     * 整理背包
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 整理结果
     */
    sortBag(characterId) {
        logger.debug('[BagService] 整理背包, characterId:', characterId);
        
        return post(`/characters/${characterId}/bag/sort`, {}, {
            loading: true,
            loadingText: '整理背包中...'
        }).then(res => {
            logger.debug('[BagService] 整理背包响应:', res);
            // 清除背包缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 整理背包失败:', error);
            throw error;
        });
    },

    /**
     * 扩展背包容量
     * @param {string} characterId - 角色ID
     * @param {number} slots - 扩展的格子数
     * @returns {Promise<Object>} - 扩展结果
     */
    expandBag(characterId, slots) {
        logger.debug('[BagService] 扩展背包, characterId:', characterId, 'slots:', slots);
        
        return post(`/characters/${characterId}/bag/expand`, {
            slots
        }, {
            loading: true,
            loadingText: '扩展背包中...'
        }).then(res => {
            logger.debug('[BagService] 扩展背包响应:', res);
            // 清除背包缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 扩展背包失败:', error);
            throw error;
        });
    },

    /**
     * 获取物品详情
     * @param {string} itemId - 物品ID
     * @returns {Promise<Object>} - 物品详情
     */
    getItemDetails(itemId) {
        logger.debug('[BagService] 获取物品详情, itemId:', itemId);
        
        return get(`/items/${itemId}`, {}, {
            loading: true,
            loadingText: '加载物品详情...'
        }).then(res => {
            logger.debug('[BagService] 物品详情响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[BagService] 获取物品详情失败:', error);
            throw error;
        });
    },


};

export default bagService;
