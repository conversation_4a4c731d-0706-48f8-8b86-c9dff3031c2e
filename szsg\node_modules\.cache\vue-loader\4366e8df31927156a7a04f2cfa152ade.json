{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CreateCharacter.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CreateCharacter.vue", "mtime": 1749698812008}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CreateCharacter.vue"], "names": [], "mappings": ";AA0GA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CreateCharacter.vue", "sourceRoot": "src/views/setup", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"create-character-container\">\n      <!-- 创建角色主界面 -->\n      <div class=\"character-creation-panel\">\n        <form @submit.prevent=\"submitCreateCharacter\" class=\"character-form\">\n          <!-- 名称输入区 -->\n          <div class=\"name-section\">\n            <span class=\"label-text\">名称:</span>\n            <input \n              v-model=\"characterData.name\"\n              type=\"text\" \n              class=\"name-input\"\n              maxlength=\"10\"\n              required\n            />\n          </div>\n\n          <!-- 性别选择区 -->\n          <div class=\"gender-section\">\n            <span class=\"label-text\">性别:</span>\n            <div class=\"gender-buttons\">\n              <button \n                type=\"button\"\n                class=\"gender-btn\"\n                :class=\"{ 'selected': characterData.gender === 'male' }\"\n                @click=\"characterData.gender = 'male'\"\n              >\n                男\n              </button>\n              <button \n                type=\"button\"\n                class=\"gender-btn\"\n                :class=\"{ 'selected': characterData.gender === 'female' }\"\n                @click=\"characterData.gender = 'female'\"\n              >\n                女\n              </button>\n            </div>\n          </div>\n\n          <!-- 职业选择区 -->\n          <div class=\"profession-section\">\n            <span class=\"label-text\">职业:</span>\n            <div class=\"profession-buttons\">\n              <button \n                v-for=\"profession in professions\" \n                :key=\"profession.id\"\n                type=\"button\"\n                class=\"profession-btn\"\n                :class=\"{ 'selected': characterData.profession === profession.id }\"\n                @click=\"selectProfession(profession)\"\n              >\n                {{ profession.name }}\n              </button>\n            </div>\n          </div>\n\n          <!-- 头像选择区 -->\n          <div class=\"avatar-section\">\n            <span class=\"label-text\">头像:</span>\n            <div class=\"avatar-grid\">\n              <div \n                v-for=\"(avatar, index) in getAvailableAvatars()\" \n                :key=\"index\"\n                class=\"avatar-option\"\n                :class=\"{ 'selected': selectedAvatar === index }\"\n                @click=\"selectedAvatar = index\"\n              >\n                <img :src=\"avatar\" :alt=\"`头像${index + 1}`\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- 提示文字 -->\n          <div class=\"hint-text\">\n            请选择在游戏中角色的性别\n          </div>\n\n        </form>\n\n        <!-- 操作按钮区域 -->\n        <div class=\"action-buttons\">\n          <button\n            type=\"button\"\n            @click=\"goBack\"\n            class=\"game-btn return-btn\"\n            :disabled=\"isCreating\"\n          >\n            返回\n          </button>\n          <button\n            type=\"button\"\n            @click=\"submitCreateCharacter\"\n            class=\"game-btn create-btn\"\n            :disabled=\"!isFormValid || isCreating\"\n          >\n            创建角色\n          </button>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapActions } from 'vuex'\nimport { regionService } from '@/api'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'CreateCharacter',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      currentRegion: null,\n      isCreating: false,\n      selectedAvatar: 0,\n      characterData: {\n        name: '',\n        gender: 'male',\n        profession: ''\n      },\n      professions: [\n        {\n          id: 'warrior',\n          name: '武士',\n          description: '武艺高强的战士，擅长近身搏斗，刀剑精通'\n        },\n        {\n          id: 'scholar',\n          name: '文人',\n          description: '博学多才的学者，精通诗词歌赋，法术造诣深厚'\n        },\n        {\n          id: 'mystic',\n          name: '异人',\n          description: '神秘莫测的修行者，身怀奇术，能力均衡'\n        }\n      ]\n    }\n  },\n  computed: {\n    isFormValid() {\n      return this.characterData.name.trim().length >= 2 &&\n             this.characterData.gender &&\n             this.characterData.profession\n    }\n  },\n  created() {\n    logger.debug('[CreateCharacter] 页面初始化')\n    \n    // 获取当前选择的大区\n    this.currentRegion = regionService.getCurrentRegion()\n    if (!this.currentRegion) {\n      this.showToast('请先选择大区')\n      this.$router.push('/setup/region-select')\n      return\n    }\n  },\n  methods: {\n    ...mapActions('character', ['createCharacter']),\n\n    selectProfession(profession) {\n      this.characterData.profession = profession.id\n      logger.debug('[CreateCharacter] 选择职业:', profession.name)\n    },\n\n    getAvailableAvatars() {\n      const gender = this.characterData.gender || 'male'\n      const profession = this.characterData.profession || 'warrior'\n      return [\n        `/static/game/avatars/${gender}_${profession}_1.png`,\n        `/static/game/avatars/${gender}_${profession}_2.png`,\n        `/static/game/avatars/${gender}_${profession}_3.png`\n      ]\n    },\n\n    async submitCreateCharacter() {\n      if (!this.isFormValid) {\n        this.showToast('请完善角色信息')\n        return\n      }\n\n      this.isCreating = true\n\n      try {\n        const characterData = {\n          name: this.characterData.name.trim(),\n          gender: this.characterData.gender,\n          profession: this.characterData.profession, // 直接使用profession字段\n          region_id: this.currentRegion?.id || 1 // 提供默认值\n        }\n\n        logger.debug('[CreateCharacter] 提交数据:', characterData)\n        logger.debug('[CreateCharacter] 当前区域:', this.currentRegion)\n\n        const result = await this.createCharacter(characterData)\n        \n        if (result && result.success) {\n          this.showToast('角色创建成功！')\n          setTimeout(() => {\n            this.$router.push('/setup/character-select')\n          }, 1500)\n        } else {\n          this.showToast(result?.error?.message || '创建角色失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[CreateCharacter] 创建角色失败:', error)\n        this.showToast(error.message || '创建角色失败，请重试')\n      } finally {\n        this.isCreating = false\n      }\n    },\n\n    goBack() {\n      this.$router.push('/setup/character-select')\n    },\n\n    showToast(message) {\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 全局重置和基础样式 */\n* {\n  box-sizing: border-box;\n}\n\nhtml, body {\n  margin: 0;\n  padding: 0;\n  overflow-x: hidden;\n}\n.create-character-container {\n  padding: 5px 15px;\n  max-width: 480px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  align-items: flex-start;\n  justify-content: center;\n  box-sizing: border-box;\n  padding-top: 10px;\n}\n\n.character-creation-panel {\n  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);\n  border: 3px solid #8b7355;\n  border-radius: 12px;\n  padding: 20px;\n  width: 100%;\n  max-width: 420px;\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);\n  box-sizing: border-box;\n  position: relative;\n  margin-top: -25px;\n}\n\n.character-form {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n/* 名称输入区 */\n.name-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.label-text {\n  color: #ffd700;\n  font-size: 16px;\n  font-weight: bold;\n  min-width: 55px;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.name-input {\n  flex: 1;\n  background: #ffff99;\n  border: 2px solid #8b7355;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #000;\n  outline: none;\n  transition: border-color 0.3s ease;\n}\n\n.name-input:focus {\n  border-color: #ffd700;\n  box-shadow: 0 0 3px rgba(255, 215, 0, 0.5);\n}\n\n/* 性别选择区 */\n.gender-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.gender-buttons {\n  display: flex;\n  gap: 20px;\n}\n\n.gender-btn {\n  background: transparent;\n  border: none;\n  color: #ffd700;\n  font-size: 18px;\n  font-weight: bold;\n  cursor: pointer;\n  padding: 8px 16px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.gender-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.05);\n}\n\n.gender-btn.selected {\n  background: rgba(255, 215, 0, 0.3);\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n/* 职业选择区 */\n.profession-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.profession-buttons {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.profession-btn {\n  background: transparent;\n  border: none;\n  color: #ffd700;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  padding: 8px 14px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  letter-spacing: 0.5px;\n}\n\n.profession-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.05);\n}\n\n.profession-btn.selected {\n  background: rgba(255, 215, 0, 0.3);\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n/* 头像选择区 */\n.avatar-section {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  padding: 12px 0;\n  border-bottom: 1px solid #8b7355;\n}\n\n.avatar-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 10px;\n}\n\n.avatar-option {\n  width: 65px;\n  height: 65px;\n  border: 2px solid #8b7355;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.avatar-option:hover {\n  border-color: #ffd700;\n  transform: scale(1.05);\n}\n\n.avatar-option.selected {\n  border-color: #ffd700;\n  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);\n}\n\n.avatar-option img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 提示文字 */\n.hint-text {\n  background: rgba(0, 0, 0, 0.8);\n  color: #ffd700;\n  text-align: center;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 13px;\n  margin: 10px 0 5px 0;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 25px;\n  margin-top: 5px;\n  padding: 10px 0;\n  border-top: 1px solid #8b7355;\n}\n\n.game-btn {\n  background: linear-gradient(135deg, #8b7355 0%, #a0845c 50%, #8b7355 100%);\n  border: 2px solid #ffd700;\n  border-radius: 8px;\n  color: #ffd700;\n  font-size: 14px;\n  font-weight: bold;\n  padding: 10px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  min-width: 80px;\n}\n\n.game-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #a0845c 0%, #b8956b 50%, #a0845c 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);\n}\n\n.game-btn:active:not(:disabled) {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.game-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n  background: #666;\n  border-color: #999;\n  color: #ccc;\n}\n\n.return-btn {\n  background: linear-gradient(135deg, #6b4423 0%, #8b5a2b 50%, #6b4423 100%);\n}\n\n.return-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #8b5a2b 0%, #a0673a 50%, #8b5a2b 100%);\n}\n\n.create-btn {\n  background: linear-gradient(135deg, #2d5016 0%, #4a7c23 50%, #2d5016 100%);\n}\n\n.create-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #4a7c23 0%, #5e9c2e 50%, #4a7c23 100%);\n}\n\n@media (max-width: 768px) {\n  .create-character-container {\n    padding: 8px;\n    min-height: 100vh;\n  }\n\n  .character-creation-panel {\n    margin: 0;\n    padding: 18px;\n    max-width: 100%;\n    border-radius: 8px;\n  }\n\n  .character-form {\n    gap: 15px;\n  }\n\n  .name-section,\n  .gender-section,\n  .profession-section,\n  .avatar-section {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n    padding: 10px 0;\n  }\n\n  .label-text {\n    min-width: auto;\n    font-size: 15px;\n  }\n\n  .gender-buttons,\n  .profession-buttons {\n    gap: 15px;\n    flex-wrap: wrap;\n  }\n\n  .avatar-grid {\n    gap: 8px;\n  }\n\n  .avatar-option {\n    width: 55px;\n    height: 55px;\n  }\n\n  .action-buttons {\n    gap: 18px;\n    margin-top: 15px;\n    padding: 12px 0;\n  }\n\n  .game-btn {\n    font-size: 13px;\n    padding: 8px 16px;\n    min-width: 70px;\n  }\n}\n\n/* 针对小屏幕设备的额外优化 */\n@media (max-width: 480px) {\n  .create-character-container {\n    padding: 5px;\n    min-height: 100vh;\n  }\n\n  .character-creation-panel {\n    padding: 12px;\n    border-radius: 6px;\n  }\n\n  .character-form {\n    gap: 12px;\n  }\n\n  .name-section,\n  .gender-section,\n  .profession-section,\n  .avatar-section {\n    padding: 8px 0;\n  }\n\n  .label-text {\n    font-size: 14px;\n  }\n\n  .gender-btn {\n    font-size: 16px;\n    padding: 6px 12px;\n  }\n\n  .profession-btn {\n    font-size: 14px;\n    padding: 6px 10px;\n  }\n\n  .gender-buttons,\n  .profession-buttons {\n    gap: 10px;\n  }\n\n  .avatar-grid {\n    gap: 6px;\n  }\n\n  .avatar-option {\n    width: 48px;\n    height: 48px;\n  }\n\n  .action-buttons {\n    gap: 15px;\n    margin-top: 12px;\n    padding: 10px 0;\n  }\n\n  .game-btn {\n    font-size: 12px;\n    padding: 6px 12px;\n    min-width: 60px;\n  }\n}\n</style>\n"]}]}