<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamMember extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'character_id',
        'role',
        'joined_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'joined_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取成员所属的队伍
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取成员角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class);
    }
}
