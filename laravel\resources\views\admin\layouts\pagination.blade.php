@if ($paginator->hasPages())
<div class="layui-box layui-laypage layui-laypage-default">
    {{-- Previous Page Link --}}
    @if ($paginator->onFirstPage())
        <a href="javascript:;" class="layui-laypage-prev layui-disabled" aria-disabled="true" aria-label="@lang('pagination.previous')">
            <i class="layui-icon">&lt;</i>
        </a>
    @else
        <a href="{{ $paginator->previousPageUrl() }}" class="layui-laypage-prev" rel="prev" aria-label="@lang('pagination.previous')">
            <i class="layui-icon">&lt;</i>
        </a>
    @endif

    {{-- Pagination Elements --}}
    @foreach ($elements as $element)
        {{-- "Three Dots" Separator --}}
        @if (is_string($element))
            <span class="layui-laypage-spr">{{ $element }}</span>
        @endif

        {{-- Array Of Links --}}
        @if (is_array($element))
            @foreach ($element as $page => $url)
                @if ($page == $paginator->currentPage())
                    <span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>{{ $page }}</em></span>
                @else
                    <a href="{{ $url }}">{{ $page }}</a>
                @endif
            @endforeach
        @endif
    @endforeach

    {{-- Next Page Link --}}
    @if ($paginator->hasMorePages())
        <a href="{{ $paginator->nextPageUrl() }}" class="layui-laypage-next" rel="next" aria-label="@lang('pagination.next')">
            <i class="layui-icon">&gt;</i>
        </a>
    @else
        <a href="javascript:;" class="layui-laypage-next layui-disabled" aria-disabled="true" aria-label="@lang('pagination.next')">
            <i class="layui-icon">&gt;</i>
        </a>
    @endif
</div>
@endif
