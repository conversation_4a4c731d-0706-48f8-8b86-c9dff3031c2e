{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\socketManager.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\socketManager.js", "mtime": 1749619212791}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["io", "API_URL", "WS_URL", "authService", "SocketManager", "constructor", "socket", "isConnected", "isConnecting", "listeners", "channels", "messageQueue", "characterId", "authToken", "lastMessageId", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "maxReconnectDelay", "pingInterval", "reconnectTimeout", "connectionTimeout", "window", "addEventListener", "handleNetworkStatusChange", "bind", "document", "handleVisibilityChange", "navigator", "onLine", "console", "log", "reconnect", "stopHeartbeat", "_emitEvent", "reason", "visibilityState", "init", "Promise", "resolve", "reject", "checkInterval", "setInterval", "clearInterval", "Error", "setTimeout", "token", "getSocketToken", "characterInfo", "getCharacterInfo", "id", "socketUrl", "path", "reconnection", "reconnectionAttempts", "reconnectionDelay", "reconnectionDelayMax", "timeout", "autoConnect", "transports", "withCredentials", "query", "character_id", "last_message_id", "auth", "_registerBaseListeners", "connect", "on", "clearTimeout", "startHeartbeat", "flushMessageQueue", "err", "error", "message", "type", "attempt", "opts", "Math", "min", "pow", "attemptNumber", "attempts", "data", "channel", "emit", "delay", "disconnect", "Object", "keys", "for<PERSON>ach", "joinChannel", "event", "callbacks", "length", "callback", "subscribe", "push", "unsubscribe", "filter", "cb", "warn", "leaveChannel", "queue", "item", "isSocketConnected", "connected", "sendChatMessage", "options", "team_id", "target_id", "socketManager"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/utils/socketManager.js"], "sourcesContent": ["import io from 'socket.io-client';\r\nimport { API_URL, WS_URL } from '../api/config';\r\nimport authService from '../api/services/authService';\r\n\r\n/**\r\n * Socket.IO连接管理类\r\n * 处理WebSocket连接、重连、消息订阅等功能\r\n * 基于原有WebSocket实现进行重构\r\n */\r\nclass SocketManager {\r\n    constructor() {\r\n        this.socket = null;\r\n        this.isConnected = false;\r\n        this.isConnecting = false;\r\n        this.listeners = {};\r\n        this.channels = {};\r\n        this.messageQueue = [];\r\n        this.characterId = null;\r\n        this.authToken = null;\r\n        this.lastMessageId = 0;\r\n        this.reconnectAttempts = 0;\r\n        this.maxReconnectAttempts = 10;\r\n        this.reconnectDelay = 2000;\r\n        this.maxReconnectDelay = 30000;\r\n        this.pingInterval = null;\r\n        this.reconnectTimeout = null;\r\n        this.connectionTimeout = null;\r\n        \r\n        // 监听网络状态变化\r\n        window.addEventListener('online', this.handleNetworkStatusChange.bind(this));\r\n        window.addEventListener('offline', this.handleNetworkStatusChange.bind(this));\r\n        \r\n        // 监听页面可见性变化\r\n        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));\r\n    }\r\n\r\n    /**\r\n     * 处理网络状态变化\r\n     */\r\n    handleNetworkStatusChange() {\r\n        if (navigator.onLine) {\r\n            console.log('[Socket] 网络已恢复，尝试重新连接');\r\n            // 当网络恢复时，如果当前未连接，尝试重新连接\r\n            if (!this.isConnected && !this.isConnecting) {\r\n                this.reconnect();\r\n            }\r\n        } else {\r\n            console.log('[Socket] 网络已断开');\r\n            // 网络断开时，标记为断开连接，停止心跳\r\n            if (this.isConnected) {\r\n                this.isConnected = false;\r\n                this.stopHeartbeat();\r\n                this._emitEvent('disconnect', { reason: 'network_offline' });\r\n            }\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * 处理页面可见性变化\r\n     */\r\n    handleVisibilityChange() {\r\n        if (document.visibilityState === 'visible') {\r\n            console.log('[Socket] 页面变为可见，检查连接状态');\r\n            // 页面重新变为可见时，如果当前未连接，尝试重新连接\r\n            if (!this.isConnected && !this.isConnecting) {\r\n                this.reconnect();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化Socket.IO连接\r\n     * @returns {Promise} 连接成功或失败的Promise\r\n     */\r\n    async init() {\r\n        // 如果已经连接，直接返回\r\n        if (this.isConnected && this.socket) {\r\n            return Promise.resolve(this.socket);\r\n        }\r\n        \r\n        if (this.isConnecting) {\r\n            // 如果正在连接中，返回一个Promise，等待连接完成或失败\r\n            return new Promise((resolve, reject) => {\r\n                const checkInterval = setInterval(() => {\r\n                    if (this.isConnected) {\r\n                        clearInterval(checkInterval);\r\n                        resolve(this.socket);\r\n                    } else if (!this.isConnecting) {\r\n                        clearInterval(checkInterval);\r\n                        reject(new Error('连接失败'));\r\n                    }\r\n                }, 100);\r\n                \r\n                // 10秒后如果仍未连接，超时\r\n                setTimeout(() => {\r\n                    if (!this.isConnected) {\r\n                        clearInterval(checkInterval);\r\n                        reject(new Error('连接超时'));\r\n                    }\r\n                }, 10000);\r\n            });\r\n        }\r\n\r\n        this.isConnecting = true;\r\n\r\n        try {\r\n            // 获取认证令牌和角色ID\r\n            const token = await authService.getSocketToken();\r\n            const characterInfo = await authService.getCharacterInfo();\r\n            \r\n            this.authToken = token;\r\n            this.characterId = characterInfo?.id;\r\n            \r\n            if (!this.authToken || !this.characterId) {\r\n                throw new Error('缺少认证信息或角色ID');\r\n            }\r\n            \r\n            // 创建socket连接\r\n            const socketUrl = WS_URL || API_URL;\r\n            \r\n            this.socket = io(socketUrl, {\r\n                path: '/socket.io',\r\n                reconnection: true,\r\n                reconnectionAttempts: this.maxReconnectAttempts,\r\n                reconnectionDelay: this.reconnectDelay,\r\n                reconnectionDelayMax: this.maxReconnectDelay,\r\n                timeout: 20000,\r\n                autoConnect: false,\r\n                transports: ['websocket', 'polling'],\r\n                withCredentials: true,\r\n                query: {\r\n                    character_id: this.characterId,\r\n                    last_message_id: this.lastMessageId\r\n                },\r\n                auth: {\r\n                    token: this.authToken\r\n                },\r\n            });\r\n\r\n            // 注册基本事件监听器\r\n            this._registerBaseListeners();\r\n            \r\n            // 手动连接\r\n            this.socket.connect();\r\n\r\n            // 返回Promise\r\n            return new Promise((resolve, reject) => {\r\n                // 设置连接超时\r\n                this.connectionTimeout = setTimeout(() => {\r\n                    if (!this.isConnected) {\r\n                        this.isConnecting = false;\r\n                        console.log('[Socket] 连接超时');\r\n                        reject(new Error('连接超时'));\r\n                    }\r\n                }, 10000);\r\n                \r\n                // 连接成功\r\n                this.socket.on('connect', () => {\r\n                    console.log('[Socket] 连接成功:', this.socket.id);\r\n                    clearTimeout(this.connectionTimeout);\r\n                    this.connectionTimeout = null;\r\n                    \r\n                    this.isConnected = true;\r\n                    this.isConnecting = false;\r\n                    this.reconnectAttempts = 0;\r\n                    \r\n                    // 开始心跳\r\n                    this.startHeartbeat();\r\n                    \r\n                    // 刷新消息队列\r\n                    this.flushMessageQueue();\r\n                    \r\n                    // 触发连接事件\r\n                    this._emitEvent('connect');\r\n                    \r\n                    resolve(this.socket);\r\n                });\r\n                \r\n                // 连接错误\r\n                this.socket.on('connect_error', (err) => {\r\n                    console.error('[Socket] 连接错误:', err.message);\r\n                    \r\n                    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n                        clearTimeout(this.connectionTimeout);\r\n                        this.connectionTimeout = null;\r\n                        this.isConnecting = false;\r\n                        \r\n                        this._emitEvent('error', {\r\n                            type: 'connection',\r\n                            message: '连接错误',\r\n                            error: err\r\n                        });\r\n                        \r\n                        reject(err);\r\n                    }\r\n                });\r\n            });\r\n        } catch (error) {\r\n            console.error('[Socket] 初始化失败:', error);\r\n            this.isConnecting = false;\r\n            return Promise.reject(error);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * 注册基本事件监听器\r\n     * @private\r\n     */\r\n    _registerBaseListeners() {\r\n        if (!this.socket) return;\r\n        \r\n        // 断开连接事件\r\n        this.socket.on('disconnect', (reason) => {\r\n            console.log('[Socket] 断开连接:', reason);\r\n            this.isConnected = false;\r\n            this.stopHeartbeat();\r\n            \r\n            this._emitEvent('disconnect', { reason });\r\n            \r\n            // 如果是服务器主动断开，可能需要重新认证\r\n            if (reason === 'io server disconnect') {\r\n                this.reconnect();\r\n            }\r\n        });\r\n        \r\n        // 重连尝试事件\r\n        this.socket.on('reconnect_attempt', (attempt) => {\r\n            console.log(`[Socket] 尝试重新连接(${attempt}/${this.maxReconnectAttempts})...`);\r\n            this.reconnectAttempts = attempt;\r\n            \r\n            // 增加重连延迟，实现指数退避\r\n            this.socket.io.opts.reconnectionDelay = Math.min(\r\n                this.maxReconnectDelay,\r\n                this.reconnectDelay * Math.pow(1.5, attempt - 1)\r\n            );\r\n            \r\n            this._emitEvent('reconnect_attempt', { attempt });\r\n        });\r\n        \r\n        // 重连成功事件\r\n        this.socket.on('reconnect', (attemptNumber) => {\r\n            console.log('[Socket] 重连成功，尝试次数:', attemptNumber);\r\n            this.isConnected = true;\r\n            this.reconnectAttempts = 0;\r\n            \r\n            // 重新启动心跳\r\n            this.startHeartbeat();\r\n            \r\n            this._emitEvent('reconnect', { attempts: attemptNumber });\r\n        });\r\n        \r\n        // 重连失败事件\r\n        this.socket.on('reconnect_failed', () => {\r\n            console.error('[Socket] 重连失败，已达到最大尝试次数');\r\n            this.isConnected = false;\r\n            \r\n            this._emitEvent('reconnect_failed');\r\n        });\r\n        \r\n        // 错误事件\r\n        this.socket.on('error', (err) => {\r\n            console.error('[Socket] 错误:', err);\r\n            this._emitEvent('error', { error: err });\r\n        });\r\n        \r\n        // 聊天消息事件\r\n        this.socket.on('chat_message', (data) => {\r\n            // 更新最后消息ID\r\n            if (data.id && data.id > this.lastMessageId) {\r\n                this.lastMessageId = data.id;\r\n            }\r\n            \r\n            // 触发对应类型的事件\r\n            switch(data.channel) {\r\n                case 'world':\r\n                    this._emitEvent('world_message', data);\r\n                    break;\r\n                case 'team':\r\n                    this._emitEvent('team_message', data);\r\n                    break;\r\n                case 'private':\r\n                    this._emitEvent('private_message', data);\r\n                    break;\r\n                default:\r\n                    this._emitEvent('chat_message', data);\r\n            }\r\n        });\r\n        \r\n        // 系统消息事件\r\n        this.socket.on('system_message', (data) => {\r\n            this._emitEvent('system_message', data);\r\n        });\r\n        \r\n        // 心跳响应事件\r\n        this.socket.on('pong', () => {\r\n            this._emitEvent('heartbeat');\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * 开始心跳检测\r\n     */\r\n    startHeartbeat() {\r\n        this.stopHeartbeat(); // 先停止可能存在的心跳\r\n        \r\n        // 每30秒发送一次心跳\r\n        this.pingInterval = setInterval(() => {\r\n            if (this.isConnected && this.socket) {\r\n                this.socket.emit('ping');\r\n            }\r\n        }, 30000);\r\n    }\r\n    \r\n    /**\r\n     * 停止心跳检测\r\n     */\r\n    stopHeartbeat() {\r\n        if (this.pingInterval) {\r\n            clearInterval(this.pingInterval);\r\n            this.pingInterval = null;\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * 重新连接\r\n     */\r\n    async reconnect() {\r\n        if (this.reconnectTimeout) {\r\n            clearTimeout(this.reconnectTimeout);\r\n        }\r\n        \r\n        if (this.isConnecting) return;\r\n        \r\n        // 计算重连延迟\r\n        const delay = Math.min(\r\n            this.maxReconnectDelay,\r\n            this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts)\r\n        );\r\n        \r\n        console.log(`[Socket] 将在 ${delay}ms 后尝试重连...`);\r\n        \r\n        // 增加重连尝试次数\r\n        this.reconnectAttempts++;\r\n        \r\n        // 如果超过最大重试次数，不再尝试\r\n        if (this.reconnectAttempts > this.maxReconnectAttempts) {\r\n            console.error('[Socket] 已达到最大重连尝试次数，停止重连');\r\n            this._emitEvent('reconnect_failed');\r\n            return;\r\n        }\r\n        \r\n        this.reconnectTimeout = setTimeout(async () => {\r\n            if (this.socket) {\r\n                // 断开现有连接\r\n                this.socket.disconnect();\r\n            }\r\n            \r\n            try {\r\n                // 重新初始化连接\r\n                await this.init();\r\n                \r\n                // 重新加入之前的频道\r\n                Object.keys(this.channels).forEach(channel => {\r\n                    this.joinChannel(channel);\r\n                });\r\n                \r\n                console.log('[Socket] 重连成功');\r\n            } catch (error) {\r\n                console.error('[Socket] 重连失败:', error);\r\n                \r\n                // 递归调用重连\r\n                this.reconnect();\r\n            }\r\n        }, delay);\r\n    }\r\n    \r\n    /**\r\n     * 发送事件到所有监听器\r\n     * @param {string} event 事件名称\r\n     * @param {any} data 事件数据\r\n     * @private\r\n     */\r\n    _emitEvent(event, data = null) {\r\n        // 获取所有该事件的监听器\r\n        const callbacks = this.listeners[event];\r\n        \r\n        if (callbacks && callbacks.length > 0) {\r\n            callbacks.forEach(callback => {\r\n                try {\r\n                    callback(data);\r\n                } catch (error) {\r\n                    console.error(`[Socket] 事件 ${event} 的回调处理出错:`, error);\r\n                }\r\n            });\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * 订阅事件\r\n     * @param {string} event 事件名称\r\n     * @param {Function} callback 回调函数\r\n     * @returns {Function} 取消订阅的函数\r\n     */\r\n    subscribe(event, callback) {\r\n        if (!this.listeners[event]) {\r\n            this.listeners[event] = [];\r\n        }\r\n        \r\n        this.listeners[event].push(callback);\r\n        \r\n        // 返回取消订阅的函数\r\n        return () => {\r\n            this.unsubscribe(event, callback);\r\n        };\r\n    }\r\n    \r\n    /**\r\n     * 取消订阅\r\n     * @param {string} event 事件名称\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    unsubscribe(event, callback) {\r\n        if (!this.listeners[event]) return;\r\n        \r\n        this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);\r\n    }\r\n    \r\n    /**\r\n     * 加入频道\r\n     * @param {string} channel 频道名称\r\n     */\r\n    joinChannel(channel) {\r\n        if (!this.socket || !this.isConnected) {\r\n            // 保存到待加入列表\r\n            this.channels[channel] = true;\r\n            console.warn('[Socket] Socket未连接，频道将在连接后加入:', channel);\r\n            return;\r\n        }\r\n        \r\n        this.socket.emit('join_channel', { channel });\r\n        this.channels[channel] = true;\r\n        console.log('[Socket] 已加入频道:', channel);\r\n    }\r\n    \r\n    /**\r\n     * 离开频道\r\n     * @param {string} channel 频道名称\r\n     */\r\n    leaveChannel(channel) {\r\n        if (!this.socket || !this.isConnected) {\r\n            delete this.channels[channel];\r\n            return;\r\n        }\r\n        \r\n        this.socket.emit('leave_channel', { channel });\r\n        delete this.channels[channel];\r\n        console.log('[Socket] 已离开频道:', channel);\r\n    }\r\n    \r\n    /**\r\n     * 发送消息\r\n     * @param {string} event 事件名称\r\n     * @param {any} data 消息数据\r\n     */\r\n    emit(event, data) {\r\n        if (!this.isConnected || !this.socket) {\r\n            // 如果未连接，将消息放入队列\r\n            this.messageQueue.push({ event, data });\r\n            console.warn('[Socket] Socket未连接，消息已加入队列:', event);\r\n            return;\r\n        }\r\n        \r\n        this.socket.emit(event, data);\r\n    }\r\n    \r\n    /**\r\n     * 处理消息队列\r\n     */\r\n    flushMessageQueue() {\r\n        if (this.messageQueue.length === 0) return;\r\n        \r\n        console.log(`[Socket] 处理队列中的 ${this.messageQueue.length} 条消息`);\r\n        \r\n        const queue = [...this.messageQueue];\r\n        this.messageQueue = [];\r\n        \r\n        queue.forEach(item => {\r\n            this.emit(item.event, item.data);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * 检查连接状态\r\n     * @returns {boolean} 是否已连接\r\n     */\r\n    isSocketConnected() {\r\n        return this.isConnected && this.socket && this.socket.connected;\r\n    }\r\n    \r\n    /**\r\n     * 断开连接\r\n     */\r\n    disconnect() {\r\n        // 清除所有定时器\r\n        if (this.pingInterval) {\r\n            clearInterval(this.pingInterval);\r\n            this.pingInterval = null;\r\n        }\r\n        \r\n        if (this.reconnectTimeout) {\r\n            clearTimeout(this.reconnectTimeout);\r\n            this.reconnectTimeout = null;\r\n        }\r\n        \r\n        if (this.connectionTimeout) {\r\n            clearTimeout(this.connectionTimeout);\r\n            this.connectionTimeout = null;\r\n        }\r\n        \r\n        if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n        }\r\n        \r\n        // 重置状态\r\n        this.isConnected = false;\r\n        this.isConnecting = false;\r\n    }\r\n    \r\n    /**\r\n     * 发送聊天消息\r\n     * @param {Object} options 消息选项\r\n     */\r\n    sendChatMessage(options) {\r\n        if (!options.channel || !options.message) {\r\n            console.error('[Socket] 发送消息失败：缺少必要参数');\r\n            return;\r\n        }\r\n        \r\n        const data = {\r\n            channel: options.channel,\r\n            message: options.message,\r\n            character_id: options.character_id || this.characterId,\r\n            team_id: options.team_id,\r\n            target_id: options.target_id\r\n        };\r\n        \r\n        this.emit('send_chat', data);\r\n    }\r\n}\r\n\r\n// 创建单例实例\r\nconst socketManager = new SocketManager();\r\n\r\nexport default socketManager; "], "mappings": ";;;;;AAAA,OAAOA,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,EAAEC,MAAM,QAAQ,eAAe;AAC/C,OAAOC,WAAW,MAAM,6BAA6B;;AAErD;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,iBAAiB,GAAG,IAAI;;IAE7B;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,yBAAyB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5EH,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACC,yBAAyB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;IAE7E;IACAC,QAAQ,CAACH,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAACI,sBAAsB,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;EACzF;;EAEA;AACJ;AACA;EACID,yBAAyBA,CAAA,EAAG;IACxB,IAAII,SAAS,CAACC,MAAM,EAAE;MAClBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAACxB,WAAW,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACzC,IAAI,CAACwB,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC,MAAM;MACHF,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B;MACA,IAAI,IAAI,CAACxB,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0B,aAAa,CAAC,CAAC;QACpB,IAAI,CAACC,UAAU,CAAC,YAAY,EAAE;UAAEC,MAAM,EAAE;QAAkB,CAAC,CAAC;MAChE;IACJ;EACJ;;EAEA;AACJ;AACA;EACIR,sBAAsBA,CAAA,EAAG;IACrB,IAAID,QAAQ,CAACU,eAAe,KAAK,SAAS,EAAE;MACxCN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACA,IAAI,CAAC,IAAI,CAACxB,WAAW,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACzC,IAAI,CAACwB,SAAS,CAAC,CAAC;MACpB;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;EACI,MAAMK,IAAIA,CAAA,EAAG;IACT;IACA,IAAI,IAAI,CAAC9B,WAAW,IAAI,IAAI,CAACD,MAAM,EAAE;MACjC,OAAOgC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACjC,MAAM,CAAC;IACvC;IAEA,IAAI,IAAI,CAACE,YAAY,EAAE;MACnB;MACA,OAAO,IAAI8B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpC,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;UACpC,IAAI,IAAI,CAACnC,WAAW,EAAE;YAClBoC,aAAa,CAACF,aAAa,CAAC;YAC5BF,OAAO,CAAC,IAAI,CAACjC,MAAM,CAAC;UACxB,CAAC,MAAM,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE;YAC3BmC,aAAa,CAACF,aAAa,CAAC;YAC5BD,MAAM,CAAC,IAAII,KAAK,CAAC,MAAM,CAAC,CAAC;UAC7B;QACJ,CAAC,EAAE,GAAG,CAAC;;QAEP;QACAC,UAAU,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAACtC,WAAW,EAAE;YACnBoC,aAAa,CAACF,aAAa,CAAC;YAC5BD,MAAM,CAAC,IAAII,KAAK,CAAC,MAAM,CAAC,CAAC;UAC7B;QACJ,CAAC,EAAE,KAAK,CAAC;MACb,CAAC,CAAC;IACN;IAEA,IAAI,CAACpC,YAAY,GAAG,IAAI;IAExB,IAAI;MACA;MACA,MAAMsC,KAAK,GAAG,MAAM3C,WAAW,CAAC4C,cAAc,CAAC,CAAC;MAChD,MAAMC,aAAa,GAAG,MAAM7C,WAAW,CAAC8C,gBAAgB,CAAC,CAAC;MAE1D,IAAI,CAACpC,SAAS,GAAGiC,KAAK;MACtB,IAAI,CAAClC,WAAW,GAAGoC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,EAAE;MAEpC,IAAI,CAAC,IAAI,CAACrC,SAAS,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE;QACtC,MAAM,IAAIgC,KAAK,CAAC,aAAa,CAAC;MAClC;;MAEA;MACA,MAAMO,SAAS,GAAGjD,MAAM,IAAID,OAAO;MAEnC,IAAI,CAACK,MAAM,GAAGN,EAAE,CAACmD,SAAS,EAAE;QACxBC,IAAI,EAAE,YAAY;QAClBC,YAAY,EAAE,IAAI;QAClBC,oBAAoB,EAAE,IAAI,CAACtC,oBAAoB;QAC/CuC,iBAAiB,EAAE,IAAI,CAACtC,cAAc;QACtCuC,oBAAoB,EAAE,IAAI,CAACtC,iBAAiB;QAC5CuC,OAAO,EAAE,KAAK;QACdC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,eAAe,EAAE,IAAI;QACrBC,KAAK,EAAE;UACHC,YAAY,EAAE,IAAI,CAAClD,WAAW;UAC9BmD,eAAe,EAAE,IAAI,CAACjD;QAC1B,CAAC;QACDkD,IAAI,EAAE;UACFlB,KAAK,EAAE,IAAI,CAACjC;QAChB;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI,CAACoD,sBAAsB,CAAC,CAAC;;MAE7B;MACA,IAAI,CAAC3D,MAAM,CAAC4D,OAAO,CAAC,CAAC;;MAErB;MACA,OAAO,IAAI5B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpC;QACA,IAAI,CAACnB,iBAAiB,GAAGwB,UAAU,CAAC,MAAM;UACtC,IAAI,CAAC,IAAI,CAACtC,WAAW,EAAE;YACnB,IAAI,CAACC,YAAY,GAAG,KAAK;YACzBsB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;YAC5BS,MAAM,CAAC,IAAII,KAAK,CAAC,MAAM,CAAC,CAAC;UAC7B;QACJ,CAAC,EAAE,KAAK,CAAC;;QAET;QACA,IAAI,CAACtC,MAAM,CAAC6D,EAAE,CAAC,SAAS,EAAE,MAAM;UAC5BrC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACzB,MAAM,CAAC4C,EAAE,CAAC;UAC7CkB,YAAY,CAAC,IAAI,CAAC/C,iBAAiB,CAAC;UACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;UAE7B,IAAI,CAACd,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,YAAY,GAAG,KAAK;UACzB,IAAI,CAACO,iBAAiB,GAAG,CAAC;;UAE1B;UACA,IAAI,CAACsD,cAAc,CAAC,CAAC;;UAErB;UACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;;UAExB;UACA,IAAI,CAACpC,UAAU,CAAC,SAAS,CAAC;UAE1BK,OAAO,CAAC,IAAI,CAACjC,MAAM,CAAC;QACxB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACA,MAAM,CAAC6D,EAAE,CAAC,eAAe,EAAGI,GAAG,IAAK;UACrCzC,OAAO,CAAC0C,KAAK,CAAC,gBAAgB,EAAED,GAAG,CAACE,OAAO,CAAC;UAE5C,IAAI,IAAI,CAAC1D,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;YACrDoD,YAAY,CAAC,IAAI,CAAC/C,iBAAiB,CAAC;YACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAACb,YAAY,GAAG,KAAK;YAEzB,IAAI,CAAC0B,UAAU,CAAC,OAAO,EAAE;cACrBwC,IAAI,EAAE,YAAY;cAClBD,OAAO,EAAE,MAAM;cACfD,KAAK,EAAED;YACX,CAAC,CAAC;YAEF/B,MAAM,CAAC+B,GAAG,CAAC;UACf;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZ1C,OAAO,CAAC0C,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAAChE,YAAY,GAAG,KAAK;MACzB,OAAO8B,OAAO,CAACE,MAAM,CAACgC,KAAK,CAAC;IAChC;EACJ;;EAEA;AACJ;AACA;AACA;EACIP,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAAC3D,MAAM,EAAE;;IAElB;IACA,IAAI,CAACA,MAAM,CAAC6D,EAAE,CAAC,YAAY,EAAGhC,MAAM,IAAK;MACrCL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,MAAM,CAAC;MACrC,IAAI,CAAC5B,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC0B,aAAa,CAAC,CAAC;MAEpB,IAAI,CAACC,UAAU,CAAC,YAAY,EAAE;QAAEC;MAAO,CAAC,CAAC;;MAEzC;MACA,IAAIA,MAAM,KAAK,sBAAsB,EAAE;QACnC,IAAI,CAACH,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC1B,MAAM,CAAC6D,EAAE,CAAC,mBAAmB,EAAGQ,OAAO,IAAK;MAC7C7C,OAAO,CAACC,GAAG,CAAC,mBAAmB4C,OAAO,IAAI,IAAI,CAAC3D,oBAAoB,MAAM,CAAC;MAC1E,IAAI,CAACD,iBAAiB,GAAG4D,OAAO;;MAEhC;MACA,IAAI,CAACrE,MAAM,CAACN,EAAE,CAAC4E,IAAI,CAACrB,iBAAiB,GAAGsB,IAAI,CAACC,GAAG,CAC5C,IAAI,CAAC5D,iBAAiB,EACtB,IAAI,CAACD,cAAc,GAAG4D,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEJ,OAAO,GAAG,CAAC,CACnD,CAAC;MAED,IAAI,CAACzC,UAAU,CAAC,mBAAmB,EAAE;QAAEyC;MAAQ,CAAC,CAAC;IACrD,CAAC,CAAC;;IAEF;IACA,IAAI,CAACrE,MAAM,CAAC6D,EAAE,CAAC,WAAW,EAAGa,aAAa,IAAK;MAC3ClD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiD,aAAa,CAAC;MACjD,IAAI,CAACzE,WAAW,GAAG,IAAI;MACvB,IAAI,CAACQ,iBAAiB,GAAG,CAAC;;MAE1B;MACA,IAAI,CAACsD,cAAc,CAAC,CAAC;MAErB,IAAI,CAACnC,UAAU,CAAC,WAAW,EAAE;QAAE+C,QAAQ,EAAED;MAAc,CAAC,CAAC;IAC7D,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC1E,MAAM,CAAC6D,EAAE,CAAC,kBAAkB,EAAE,MAAM;MACrCrC,OAAO,CAAC0C,KAAK,CAAC,yBAAyB,CAAC;MACxC,IAAI,CAACjE,WAAW,GAAG,KAAK;MAExB,IAAI,CAAC2B,UAAU,CAAC,kBAAkB,CAAC;IACvC,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC5B,MAAM,CAAC6D,EAAE,CAAC,OAAO,EAAGI,GAAG,IAAK;MAC7BzC,OAAO,CAAC0C,KAAK,CAAC,cAAc,EAAED,GAAG,CAAC;MAClC,IAAI,CAACrC,UAAU,CAAC,OAAO,EAAE;QAAEsC,KAAK,EAAED;MAAI,CAAC,CAAC;IAC5C,CAAC,CAAC;;IAEF;IACA,IAAI,CAACjE,MAAM,CAAC6D,EAAE,CAAC,cAAc,EAAGe,IAAI,IAAK;MACrC;MACA,IAAIA,IAAI,CAAChC,EAAE,IAAIgC,IAAI,CAAChC,EAAE,GAAG,IAAI,CAACpC,aAAa,EAAE;QACzC,IAAI,CAACA,aAAa,GAAGoE,IAAI,CAAChC,EAAE;MAChC;;MAEA;MACA,QAAOgC,IAAI,CAACC,OAAO;QACf,KAAK,OAAO;UACR,IAAI,CAACjD,UAAU,CAAC,eAAe,EAAEgD,IAAI,CAAC;UACtC;QACJ,KAAK,MAAM;UACP,IAAI,CAAChD,UAAU,CAAC,cAAc,EAAEgD,IAAI,CAAC;UACrC;QACJ,KAAK,SAAS;UACV,IAAI,CAAChD,UAAU,CAAC,iBAAiB,EAAEgD,IAAI,CAAC;UACxC;QACJ;UACI,IAAI,CAAChD,UAAU,CAAC,cAAc,EAAEgD,IAAI,CAAC;MAC7C;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC5E,MAAM,CAAC6D,EAAE,CAAC,gBAAgB,EAAGe,IAAI,IAAK;MACvC,IAAI,CAAChD,UAAU,CAAC,gBAAgB,EAAEgD,IAAI,CAAC;IAC3C,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC5E,MAAM,CAAC6D,EAAE,CAAC,MAAM,EAAE,MAAM;MACzB,IAAI,CAACjC,UAAU,CAAC,WAAW,CAAC;IAChC,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;EACImC,cAAcA,CAAA,EAAG;IACb,IAAI,CAACpC,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACd,YAAY,GAAGuB,WAAW,CAAC,MAAM;MAClC,IAAI,IAAI,CAACnC,WAAW,IAAI,IAAI,CAACD,MAAM,EAAE;QACjC,IAAI,CAACA,MAAM,CAAC8E,IAAI,CAAC,MAAM,CAAC;MAC5B;IACJ,CAAC,EAAE,KAAK,CAAC;EACb;;EAEA;AACJ;AACA;EACInD,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACd,YAAY,EAAE;MACnBwB,aAAa,CAAC,IAAI,CAACxB,YAAY,CAAC;MAChC,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;EACJ;;EAEA;AACJ;AACA;EACI,MAAMa,SAASA,CAAA,EAAG;IACd,IAAI,IAAI,CAACZ,gBAAgB,EAAE;MACvBgD,YAAY,CAAC,IAAI,CAAChD,gBAAgB,CAAC;IACvC;IAEA,IAAI,IAAI,CAACZ,YAAY,EAAE;;IAEvB;IACA,MAAM6E,KAAK,GAAGR,IAAI,CAACC,GAAG,CAClB,IAAI,CAAC5D,iBAAiB,EACtB,IAAI,CAACD,cAAc,GAAG4D,IAAI,CAACE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAChE,iBAAiB,CAC9D,CAAC;IAEDe,OAAO,CAACC,GAAG,CAAC,eAAesD,KAAK,aAAa,CAAC;;IAE9C;IACA,IAAI,CAACtE,iBAAiB,EAAE;;IAExB;IACA,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;MACpDc,OAAO,CAAC0C,KAAK,CAAC,2BAA2B,CAAC;MAC1C,IAAI,CAACtC,UAAU,CAAC,kBAAkB,CAAC;MACnC;IACJ;IAEA,IAAI,CAACd,gBAAgB,GAAGyB,UAAU,CAAC,YAAY;MAC3C,IAAI,IAAI,CAACvC,MAAM,EAAE;QACb;QACA,IAAI,CAACA,MAAM,CAACgF,UAAU,CAAC,CAAC;MAC5B;MAEA,IAAI;QACA;QACA,MAAM,IAAI,CAACjD,IAAI,CAAC,CAAC;;QAEjB;QACAkD,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9E,QAAQ,CAAC,CAAC+E,OAAO,CAACN,OAAO,IAAI;UAC1C,IAAI,CAACO,WAAW,CAACP,OAAO,CAAC;QAC7B,CAAC,CAAC;QAEFrD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAChC,CAAC,CAAC,OAAOyC,KAAK,EAAE;QACZ1C,OAAO,CAAC0C,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;;QAEtC;QACA,IAAI,CAACxC,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC,EAAEqD,KAAK,CAAC;EACb;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACInD,UAAUA,CAACyD,KAAK,EAAET,IAAI,GAAG,IAAI,EAAE;IAC3B;IACA,MAAMU,SAAS,GAAG,IAAI,CAACnF,SAAS,CAACkF,KAAK,CAAC;IAEvC,IAAIC,SAAS,IAAIA,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACnCD,SAAS,CAACH,OAAO,CAACK,QAAQ,IAAI;QAC1B,IAAI;UACAA,QAAQ,CAACZ,IAAI,CAAC;QAClB,CAAC,CAAC,OAAOV,KAAK,EAAE;UACZ1C,OAAO,CAAC0C,KAAK,CAAC,eAAemB,KAAK,WAAW,EAAEnB,KAAK,CAAC;QACzD;MACJ,CAAC,CAAC;IACN;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIuB,SAASA,CAACJ,KAAK,EAAEG,QAAQ,EAAE;IACvB,IAAI,CAAC,IAAI,CAACrF,SAAS,CAACkF,KAAK,CAAC,EAAE;MACxB,IAAI,CAAClF,SAAS,CAACkF,KAAK,CAAC,GAAG,EAAE;IAC9B;IAEA,IAAI,CAAClF,SAAS,CAACkF,KAAK,CAAC,CAACK,IAAI,CAACF,QAAQ,CAAC;;IAEpC;IACA,OAAO,MAAM;MACT,IAAI,CAACG,WAAW,CAACN,KAAK,EAAEG,QAAQ,CAAC;IACrC,CAAC;EACL;;EAEA;AACJ;AACA;AACA;AACA;EACIG,WAAWA,CAACN,KAAK,EAAEG,QAAQ,EAAE;IACzB,IAAI,CAAC,IAAI,CAACrF,SAAS,CAACkF,KAAK,CAAC,EAAE;IAE5B,IAAI,CAAClF,SAAS,CAACkF,KAAK,CAAC,GAAG,IAAI,CAAClF,SAAS,CAACkF,KAAK,CAAC,CAACO,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKL,QAAQ,CAAC;EAC/E;;EAEA;AACJ;AACA;AACA;EACIJ,WAAWA,CAACP,OAAO,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC7E,MAAM,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MACnC;MACA,IAAI,CAACG,QAAQ,CAACyE,OAAO,CAAC,GAAG,IAAI;MAC7BrD,OAAO,CAACsE,IAAI,CAAC,+BAA+B,EAAEjB,OAAO,CAAC;MACtD;IACJ;IAEA,IAAI,CAAC7E,MAAM,CAAC8E,IAAI,CAAC,cAAc,EAAE;MAAED;IAAQ,CAAC,CAAC;IAC7C,IAAI,CAACzE,QAAQ,CAACyE,OAAO,CAAC,GAAG,IAAI;IAC7BrD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoD,OAAO,CAAC;EAC3C;;EAEA;AACJ;AACA;AACA;EACIkB,YAAYA,CAAClB,OAAO,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC7E,MAAM,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MACnC,OAAO,IAAI,CAACG,QAAQ,CAACyE,OAAO,CAAC;MAC7B;IACJ;IAEA,IAAI,CAAC7E,MAAM,CAAC8E,IAAI,CAAC,eAAe,EAAE;MAAED;IAAQ,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACzE,QAAQ,CAACyE,OAAO,CAAC;IAC7BrD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoD,OAAO,CAAC;EAC3C;;EAEA;AACJ;AACA;AACA;AACA;EACIC,IAAIA,CAACO,KAAK,EAAET,IAAI,EAAE;IACd,IAAI,CAAC,IAAI,CAAC3E,WAAW,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;MACnC;MACA,IAAI,CAACK,YAAY,CAACqF,IAAI,CAAC;QAAEL,KAAK;QAAET;MAAK,CAAC,CAAC;MACvCpD,OAAO,CAACsE,IAAI,CAAC,6BAA6B,EAAET,KAAK,CAAC;MAClD;IACJ;IAEA,IAAI,CAACrF,MAAM,CAAC8E,IAAI,CAACO,KAAK,EAAET,IAAI,CAAC;EACjC;;EAEA;AACJ;AACA;EACIZ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC3D,YAAY,CAACkF,MAAM,KAAK,CAAC,EAAE;IAEpC/D,OAAO,CAACC,GAAG,CAAC,mBAAmB,IAAI,CAACpB,YAAY,CAACkF,MAAM,MAAM,CAAC;IAE9D,MAAMS,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC3F,YAAY,CAAC;IACpC,IAAI,CAACA,YAAY,GAAG,EAAE;IAEtB2F,KAAK,CAACb,OAAO,CAACc,IAAI,IAAI;MAClB,IAAI,CAACnB,IAAI,CAACmB,IAAI,CAACZ,KAAK,EAAEY,IAAI,CAACrB,IAAI,CAAC;IACpC,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIsB,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACjG,WAAW,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmG,SAAS;EACnE;;EAEA;AACJ;AACA;EACInB,UAAUA,CAAA,EAAG;IACT;IACA,IAAI,IAAI,CAACnE,YAAY,EAAE;MACnBwB,aAAa,CAAC,IAAI,CAACxB,YAAY,CAAC;MAChC,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;IAEA,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACvBgD,YAAY,CAAC,IAAI,CAAChD,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;IAEA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB+C,YAAY,CAAC,IAAI,CAAC/C,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IACjC;IAEA,IAAI,IAAI,CAACf,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACgF,UAAU,CAAC,CAAC;MACxB,IAAI,CAAChF,MAAM,GAAG,IAAI;IACtB;;IAEA;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;;EAEA;AACJ;AACA;AACA;EACIkG,eAAeA,CAACC,OAAO,EAAE;IACrB,IAAI,CAACA,OAAO,CAACxB,OAAO,IAAI,CAACwB,OAAO,CAAClC,OAAO,EAAE;MACtC3C,OAAO,CAAC0C,KAAK,CAAC,wBAAwB,CAAC;MACvC;IACJ;IAEA,MAAMU,IAAI,GAAG;MACTC,OAAO,EAAEwB,OAAO,CAACxB,OAAO;MACxBV,OAAO,EAAEkC,OAAO,CAAClC,OAAO;MACxBX,YAAY,EAAE6C,OAAO,CAAC7C,YAAY,IAAI,IAAI,CAAClD,WAAW;MACtDgG,OAAO,EAAED,OAAO,CAACC,OAAO;MACxBC,SAAS,EAAEF,OAAO,CAACE;IACvB,CAAC;IAED,IAAI,CAACzB,IAAI,CAAC,WAAW,EAAEF,IAAI,CAAC;EAChC;AACJ;;AAEA;AACA,MAAM4B,aAAa,GAAG,IAAI1G,aAAa,CAAC,CAAC;AAEzC,eAAe0G,aAAa", "ignoreList": []}]}