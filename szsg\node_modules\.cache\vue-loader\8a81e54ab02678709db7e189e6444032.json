{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\CharacterStatus.vue?vue&type=style&index=0&id=5beee323&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\CharacterStatus.vue", "mtime": 1749781138894}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CharacterStatus.vue"], "names": [], "mappings": ";AA0mBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "CharacterStatus.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\n  <GameLayout>\n  <div class=\"status-page\">\n      <div class=\"loading-overlay\" v-if=\"isLoading\">\n        <div class=\"loading-spinner\"></div>\n      </div>\n      <div class=\"character-info-container\">\n        <!-- 基本信息区域 -->\n        <div class=\"basic-info\">\n          <div class=\"server-name\">{{ characterInfo.serverName }}（{{ characterInfo.id }}）</div>\n          \n          <div class=\"profession-avatar-row\">\n            <div class=\"profession\">职业：（{{ characterInfo.career }}）{{ characterInfo.level }}级{{ characterInfo.className }}</div>\n            <!-- 头像区域 -->\n            <div class=\"avatar-container\">\n          <img :src=\"characterInfo.avatarUrl || defaultAvatar\" class=\"character-avatar\" />\n            </div>\n          </div>\n          \n          <div class=\"server-rank\">S{{ characterInfo.serverRank }}：{{ characterInfo.rankName }}</div>\n          <div class=\"title\">称号：{{ characterInfo.title }}</div>\n          \n          <button class=\"change-avatar-btn\">更换头像</button>\n          \n          <div class=\"upgrade-info\">\n            <div>经验：{{ characterInfo.exp }}/{{ characterInfo.expRequired }}</div>\n            <div class=\"exp-progress\">\n              <div class=\"exp-bar\" :style=\"{width: (characterInfo.exp / characterInfo.expRequired * 100) + '%'}\"></div>\n            </div>\n            <div class=\"upgrade-rate\">升级效率：{{ characterInfo.upgradeRate }}%</div>\n            </div>\n          \n          <div class=\"strength-info\">\n            体力值：{{ characterInfo.stamina }}（{{ characterInfo.strengthStatus }}）\n            <button class=\"close-btn\">关闭</button>\n          </div>\n          \n          <div class=\"money-info\">\n            钱：{{ characterInfo.gold }}金 {{ characterInfo.silver }}两\n            <button class=\"test-exp-btn\" @click=\"addExp(10000)\">+10000经验</button>\n          </div>\n          \n          <div class=\"energy-info\">\n            活力值：{{ characterInfo.energy }}\n      </div>\n      \n          <div class=\"position-info\">\n            职位：{{ characterInfo.position || '无' }}\n          </div>\n          \n          <div class=\"location-info\">\n            居住地：{{ characterInfo.location }}\n          </div>\n          \n          <div class=\"property-info\">\n            房产：{{ characterInfo.property }}\n          </div>\n          \n          <div class=\"faction-info\">\n            帮派：{{ characterInfo.faction }}\n          </div>\n          \n          <div class=\"spouse-info\">\n            击败：{{ characterInfo.defeatedCount }}人\n          </div>\n          \n          <div class=\"partner-info\">\n            配偶：{{ characterInfo.partner || '无' }}\n          </div>\n        </div>\n      </div>\n      \n      <!-- 属性区域 -->\n      <div class=\"attributes-container\">\n        <button class=\"skill-btn\">查看技能</button>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">属性点分配：</span>\n          <span class=\"attribute-value\">{{ characterInfo.attributePoints }}点未分配</span>\n          <button class=\"distribute-btn\" @click=\"showAttributeDistribution = true\">查看</button>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">体质点：</span>\n          <span class=\"attribute-value\">{{ characterInfo.constitution }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">智力点：</span>\n          <span class=\"attribute-value\">{{ characterInfo.intelligence }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">力量点：</span>\n          <span class=\"attribute-value\">{{ characterInfo.strength }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">敏捷点：</span>\n          <span class=\"attribute-value\">{{ characterInfo.agility }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">副将：</span>\n          <span class=\"attribute-value colored-text\">{{ characterInfo.deputies.join('，') }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">坐骑：</span>\n          <span class=\"attribute-value colored-text\">{{ characterInfo.mount }}</span>\n          <button class=\"mount-btn\">坐骑配置</button>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">气血：</span>\n          <span class=\"attribute-value\">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">精力：</span>\n          <span class=\"attribute-value\">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">攻击：</span>\n          <span class=\"attribute-value\">{{ characterInfo.attack }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">速度：</span>\n          <span class=\"attribute-value\">{{ characterInfo.speed }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">防御：</span>\n          <span class=\"attribute-value\">{{ characterInfo.defense }}</span>\n        </div>\n        \n        <div class=\"attribute-row\">\n          <span class=\"attribute-label\">负重：</span>\n          <span class=\"attribute-value\">{{ characterInfo.weight }}/{{ characterInfo.maxWeight }}</span>\n        </div>\n        \n        <div class=\"personal-info-row\">\n          <span class=\"personal-info-label\">个人信息：</span>\n          <span class=\"personal-info-status\">（{{ characterInfo.infoStatus }}）</span>\n          <button class=\"toggle-info-btn\">{{ characterInfo.infoStatus === '关闭中' ? '开启' : '关闭' }}</button>\n        </div>\n        \n        <!-- 底部按钮区域 -->\n        <div class=\"bottom-buttons\">\n          <button class=\"function-btn\">战斗能力</button>\n          <button class=\"function-btn\">生平</button>\n          <button class=\"function-btn\">性格</button>\n        </div>\n        \n        <!-- 天赋区域 -->\n        <div class=\"talent-container\">\n          <div class=\"talent-label\">天赋：</div>\n          <div class=\"talent-list\">{{ characterInfo.talents.join('，') }}</div>\n        </div>\n        \n        <!-- 最底部按钮 -->\n        <div class=\"bottom-buttons\">\n          <button class=\"function-btn\">战斗录像</button>\n          <button class=\"function-btn\">开启</button>\n        </div>\n      </div>\n      \n      <!-- 返回按钮 -->\n      <div class=\"back-button-container\">\n        <div class=\"back-button\" @click=\"goBack\">\n          <img src=\"/static/game/UI/anniu/fhui_.png\" alt=\"返回\" />\n        </div>\n      </div>\n      \n      <!-- 属性点分配对话框 -->\n      <div v-if=\"showAttributeDistribution\" class=\"attribute-distribution-modal\">\n        <div class=\"modal-content\">\n          <h2>属性点分配 (剩余:{{ tempAttributePoints }})</h2>\n          <p class=\"level-bonus\">等级奖励: {{ characterInfo.level > 0 ? characterInfo.level : 0 }}级</p>\n          <p class=\"level-info\">基础属性不能低于当前等级({{ characterInfo.level }})</p>\n          \n          <div class=\"attribute-adjust-row\">\n            <span>体质点:</span>\n            <button class=\"adjust-btn decrease-btn\" @click=\"decreaseAttribute('constitution')\" :disabled=\"tempConstitution <= characterInfo.level\">-</button>\n            <span>{{ tempConstitution }}</span>\n            <button class=\"adjust-btn increase-btn\" @click=\"increaseAttribute('constitution')\" :disabled=\"tempAttributePoints <= 0\">+</button>\n          </div>\n          \n          <div class=\"attribute-adjust-row\">\n            <span>智力点:</span>\n            <button class=\"adjust-btn decrease-btn\" @click=\"decreaseAttribute('intelligence')\" :disabled=\"tempIntelligence <= characterInfo.level\">-</button>\n            <span>{{ tempIntelligence }}</span>\n            <button class=\"adjust-btn increase-btn\" @click=\"increaseAttribute('intelligence')\" :disabled=\"tempAttributePoints <= 0\">+</button>\n          </div>\n          \n          <div class=\"attribute-adjust-row\">\n            <span>力量点:</span>\n            <button class=\"adjust-btn decrease-btn\" @click=\"decreaseAttribute('strength')\" :disabled=\"tempStrength <= characterInfo.level\">-</button>\n            <span>{{ tempStrength }}</span>\n            <button class=\"adjust-btn increase-btn\" @click=\"increaseAttribute('strength')\" :disabled=\"tempAttributePoints <= 0\">+</button>\n          </div>\n          \n          <div class=\"attribute-adjust-row\">\n            <span>敏捷点:</span>\n            <button class=\"adjust-btn decrease-btn\" @click=\"decreaseAttribute('agility')\" :disabled=\"tempAgility <= characterInfo.level\">-</button>\n            <span>{{ tempAgility }}</span>\n            <button class=\"adjust-btn increase-btn\" @click=\"increaseAttribute('agility')\" :disabled=\"tempAttributePoints <= 0\">+</button>\n          </div>\n          \n          <div class=\"attribute-preview\">\n            <h3>属性预览</h3>\n            <div class=\"preview-row\">\n              <span>气血: {{ previewValues.hp }}</span>\n              <span>精力: {{ previewValues.mp }}</span>\n            </div>\n            <div class=\"preview-row\">\n              <span>攻击: {{ previewValues.attack }}</span>\n              <span>速度: {{ previewValues.speed }}</span>\n            </div>\n          </div>\n          \n          <div class=\"modal-buttons\">\n            <button @click=\"confirmAttributeDistribution\">确认</button>\n            <button @click=\"cancelAttributeDistribution\">取消</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue';\nimport { showMessage } from '@/utils/message';\nimport { mapState, mapGetters, mapActions } from 'vuex';\nimport logger from '@/utils/logger';\n\nexport default {\n  components: { GameLayout },\n  data() {\n    return {\n      defaultAvatar: '/static/game/UI/tx/male/tx1.png',\n      characterId: null,\n      showAttributeDistribution: false,\n      tempAttributePoints: 0,\n      tempConstitution: 0,\n      tempIntelligence: 0,\n      tempStrength: 0,\n      tempAgility: 0,\n      attributeBonus: {\n        constitution: { hp: 22 },\n        intelligence: { mp: 12 },\n        strength: { attack: 5 },\n        agility: { speed: 1 }\n      },\n      previewValues: {\n        hp: 0,\n        mp: 0,\n        attack: 0,\n        speed: 0\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      characterInfo: state => state.character.characterStatus || {},\n    }),\n    ...mapGetters('character', [\n      'isLoading',\n      'characterLevel',\n      'characterExp',\n      'expRequired',\n      'expPercentage',\n      'attributePoints',\n      'characterAttributes'\n    ])\n  },\n  created() {\n    this.initCharacterStatus();\n  },\n  mounted() {\n    // 确保在组件完全挂载后初始化\n    this.$nextTick(() => {\n      logger.debug('[角色状态] 组件已挂载, 属性点:', this.attributePoints);\n    });\n  },\n  methods: {\n    ...mapActions('character', [\n      'initCurrentCharacter',\n      'loadCharacterStatus',\n      'addCharacterExperience',\n      'updateCharacterAttributes'\n    ]),\n    \n    addExp(amount) {\n      return this.addCharacterExperience(amount);\n    },\n    \n    async initCharacterStatus() {\n      try {\n        // 初始化当前角色\n        const character = await this.initCurrentCharacter();\n        if (character) {\n          this.characterId = character.id;\n          \n          try {\n            // 加载角色状态\n            const status = await this.loadCharacterStatus();\n            logger.debug('[角色状态] 加载成功, 状态数据:', status);\n            \n            // 设置职业属性加成\n            this.setAttributeBonus();\n            \n            // 确保数据已正确加载\n            this.$nextTick(() => {\n              if (this.characterInfo) {\n                logger.debug('[角色状态] 角色信息已更新:', this.characterInfo);\n                // 计算属性加成\n                this.calculateAttributeBonuses();\n              }\n            });\n          } catch (statusError) {\n            logger.error('[角色状态] 加载角色状态详情失败:', statusError);\n            \n            // 创建默认状态数据，保证UI可以显示\n            this.$store.commit('character/SET_CHARACTER_STATUS', {\n              level: 1,\n              exp: 0,\n              expRequired: 1000,\n              attributePoints: 4,\n              constitution: 1,\n              intelligence: 1,\n              strength: 1,\n              agility: 1,\n              serverName: character.server_name || '未知',\n              id: character.id,\n              career: character.career || '未知',\n              className: character.class_name || '未知',\n              serverRank: '0',\n              rankName: '无',\n              title: '无',\n              avatarUrl: this.defaultAvatar,\n              stamina: 100,\n              strengthStatus: '正常',\n              gold: 0,\n              silver: 0,\n              energy: 100,\n              position: '无',\n              location: '无',\n              property: '无',\n              faction: '无',\n              defeatedCount: 0,\n              partner: '无',\n              deputies: [],\n              mount: '无',\n              hp: 1000,\n              maxHp: 1000,\n              mp: 500,\n              maxMp: 500,\n              attack: 100,\n              speed: 100,\n              defense: 10,\n              weight: 0,\n              maxWeight: 100,\n              infoStatus: '关闭中',\n              talents: []\n            });\n            \n            // 设置职业属性加成\n            this.setAttributeBonus();\n            \n            // 显示错误提示，但不中断界面显示\n            showMessage('加载角色详细数据失败，使用默认数据显示', 'warning');\n          }\n        } else {\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n        }\n      } catch (error) {\n        logger.error('[角色状态] 初始化失败:', error);\n        showMessage('加载角色数据失败', 'error');\n        this.$router.push('/setup/character-select');\n      }\n    },\n    \n    goBack() {\n      this.$router.push('/game/main');\n    },\n    \n    setAttributeBonus() {\n      const className = this.characterInfo.className;\n      \n      switch(className) {\n        case '武士':\n          this.attributeBonus = {\n            constitution: { hp: 22 },\n            intelligence: { mp: 12 },\n            strength: { attack: 5 },\n            agility: { speed: 1 }\n          };\n          break;\n        case '文人':\n          this.attributeBonus = {\n            constitution: { hp: 22 },\n            intelligence: { mp: 20 },\n            strength: { attack: 4 },\n            agility: { speed: 1 }\n          };\n          break;\n        case '异人':\n          this.attributeBonus = {\n            constitution: { hp: 22 },\n            intelligence: { mp: 20 },\n            strength: { attack: 4 },\n            agility: { speed: 1 }\n          };\n          break;\n        default:\n          this.attributeBonus = {\n            constitution: { hp: 22 },\n            intelligence: { mp: 12 },\n            strength: { attack: 5 },\n            agility: { speed: 1 }\n          };\n      }\n    },\n    \n    calculateAttributeBonuses() {\n      // 确保characterInfo存在\n      if (!this.characterInfo) {\n        logger.error('[角色状态] 无法计算属性加成，characterInfo不存在');\n        return;\n      }\n      \n      const baseHp = 1000;\n      const baseMp = 500;\n      const baseAttack = 100;\n      const baseSpeed = 100;\n      \n      // 使用parseInt确保属性值为整数\n      const constitution = parseInt(this.characterInfo.constitution || 0);\n      const intelligence = parseInt(this.characterInfo.intelligence || 0);\n      const strength = parseInt(this.characterInfo.strength || 0);\n      const agility = parseInt(this.characterInfo.agility || 0);\n      \n      // 计算属性加成\n      const hpBonus = constitution * this.attributeBonus.constitution.hp;\n      const mpBonus = intelligence * this.attributeBonus.intelligence.mp;\n      const attackBonus = strength * this.attributeBonus.strength.attack;\n      const speedBonus = agility * this.attributeBonus.agility.speed;\n      \n      // 更新状态\n      this.characterInfo.maxHp = baseHp + hpBonus;\n      this.characterInfo.hp = this.characterInfo.maxHp;\n      \n      this.characterInfo.maxMp = baseMp + mpBonus;\n      this.characterInfo.mp = this.characterInfo.maxMp;\n      \n      this.characterInfo.attack = Math.floor(baseAttack + attackBonus);\n      \n      this.characterInfo.speed = Math.floor(baseSpeed + speedBonus);\n      \n      logger.debug('[角色状态] 属性计算完成:', {\n        hp: this.characterInfo.hp,\n        mp: this.characterInfo.mp,\n        attack: this.characterInfo.attack,\n        speed: this.characterInfo.speed\n      });\n    },\n      \n    calculateExpRequired(level) {\n      // 经验计算公式：基础经验 * 等级 * 1.5\n      const baseExp = 2000;\n      return Math.floor(baseExp * level * 1.5);\n    },\n    \n    increaseAttribute(attribute) {\n      // 确保还有未分配的属性点\n      if (this.tempAttributePoints <= 0) {\n        logger.debug('[属性分配] 没有可用属性点');\n        return;\n      }\n      \n      // 增加对应属性\n      this.tempAttributePoints--;\n      const capitalizedAttr = attribute.charAt(0).toUpperCase() + attribute.slice(1);\n      const propName = 'temp' + capitalizedAttr;\n      this[propName]++;\n      \n      logger.debug(`[属性分配] ${attribute}属性增加到${this[propName]}，剩余点数:${this.tempAttributePoints}`);\n      \n      // 更新预览\n      this.previewAttributeBonuses();\n      this.$forceUpdate();\n    },\n    \n    decreaseAttribute(attribute) {\n      const capitalizedAttr = attribute.charAt(0).toUpperCase() + attribute.slice(1);\n      const propName = 'temp' + capitalizedAttr;\n      const tempValue = this[propName];\n      \n      // 获取当前等级，作为最小属性值\n      const minAttributeValue = this.characterInfo.level || 1;\n      \n      // 确保当前值大于等级（最小属性值）\n      if (tempValue <= minAttributeValue) {\n        logger.debug(`[属性分配] ${attribute}属性已经是最小值(${minAttributeValue})，无法减少`);\n        return;\n      }\n      \n      // 减少对应属性\n      this.tempAttributePoints++;\n      this[propName]--;\n      \n      logger.debug(`[属性分配] ${attribute}属性减少到${this[propName]}，剩余点数:${this.tempAttributePoints}`);\n      \n      // 更新预览\n      this.previewAttributeBonuses();\n      this.$forceUpdate();\n    },\n    \n    previewAttributeBonuses() {\n      // 计算预览效果，使用与calculateAttributeBonuses相同的基础值\n      const baseHp = 1000;\n      const baseMp = 500;\n      const baseAttack = 100;\n      const baseSpeed = 100;\n      \n      // 使用临时属性值计算预览效果并存储到data中\n      this.previewValues.hp = baseHp + (this.tempConstitution * this.attributeBonus.constitution.hp);\n      this.previewValues.mp = baseMp + (this.tempIntelligence * this.attributeBonus.intelligence.mp);\n      this.previewValues.attack = baseAttack + (this.tempStrength * this.attributeBonus.strength.attack);\n      this.previewValues.speed = baseSpeed + (this.tempAgility * this.attributeBonus.agility.speed);\n      \n      logger.debug('[属性预览] 计算结果:', this.previewValues);\n    },\n    \n    async confirmAttributeDistribution() {\n      // 准备要发送的属性数据，确保都是整数\n      const attributes = {\n        constitution: parseInt(this.tempConstitution) || 0,\n        intelligence: parseInt(this.tempIntelligence) || 0,\n        strength: parseInt(this.tempStrength) || 0,\n        agility: parseInt(this.tempAgility) || 0\n      };\n      \n      logger.debug('[属性分配] 确认属性分配:', attributes);\n      logger.debug('[属性分配] 数据类型检查:', {\n        constitution: typeof attributes.constitution,\n        intelligence: typeof attributes.intelligence,\n        strength: typeof attributes.strength,\n        agility: typeof attributes.agility\n      });\n\n      try {\n        // 调用Vuex action保存属性点分配\n        const success = await this.updateCharacterAttributes(attributes);\n        \n        if (success) {\n          // 更新衍生属性\n          this.calculateAttributeBonuses();\n          showMessage('属性点分配成功！', 'success');\n        } else {\n          showMessage('属性点分配失败，请重试', 'error');\n        }\n      } catch (error) {\n        logger.error('[属性分配] 属性分配出错:', error);\n        showMessage('属性点分配出错：' + (error.message || '未知错误'), 'error');\n      }\n      \n      this.showAttributeDistribution = false;\n    },\n    \n    cancelAttributeDistribution() {\n      this.showAttributeDistribution = false;\n    },\n  },\n  watch: {\n    showAttributeDistribution(newValue) {\n      if (newValue) {\n        // 确保从store获取正确的属性点\n        // 如果系统没有返回属性点，默认为4点（与后端保持一致）\n        logger.debug('[属性分配] 打开属性分配窗口, 当前属性点:', this.attributePoints);\n        this.tempAttributePoints = this.attributePoints || 4;\n        \n        // 获取当前等级，作为最小属性值\n        const level = this.characterInfo.level || 1;\n        \n        // 如果系统没有返回属性值，默认为当前等级（最小值）\n        this.tempConstitution = Math.max(this.characterAttributes?.constitution || 1, level);\n        this.tempIntelligence = Math.max(this.characterAttributes?.intelligence || 1, level);\n        this.tempStrength = Math.max(this.characterAttributes?.strength || 1, level);\n        this.tempAgility = Math.max(this.characterAttributes?.agility || 1, level);\n        \n        // 初始化预览值\n        this.$nextTick(() => {\n          this.previewAttributeBonuses();\n          logger.debug('[属性分配] 初始化完成', {\n            points: this.tempAttributePoints,\n            constitution: this.tempConstitution,\n            intelligence: this.tempIntelligence,\n            strength: this.tempStrength,\n            agility: this.tempAgility\n          });\n        });\n      }\n    },\n    'characterInfo.className': function() {\n      this.setAttributeBonus();\n      this.calculateAttributeBonuses();\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.status-page {\n  width: 100%;\n  min-height: 100vh;\n  background-color: #000033;\n  color: #ffffff;\n  padding: 5px;\n  position: relative;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  box-sizing: border-box;\n  font-size: 14px;\n}\n\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 51, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 100;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 5px solid #5555ff;\n  border-top: 5px solid #ff0000;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.character-info-container {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 2px;\n}\n\n.basic-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 1px;\n}\n\n.server-name {\n  color: #ff0000;\n  font-size: 14px;\n  font-weight: bold;\n  line-height: 1;\n}\n\n.profession-avatar-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 1px 0;\n}\n\n.profession {\n  color: #ffffff;\n  line-height: 1;\n}\n\n.server-rank {\n  color: #ff0000;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n}\n\n.title {\n  color: #ffffff;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n}\n\n.position-info, .location-info, .property-info, \n.faction-info, .spouse-info, .partner-info {\n  color: #ffffff;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n}\n\n.money-info {\n  color: #ffff00;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n}\n\n.energy-info {\n  color: #ffffff;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n}\n\n.upgrade-info {\n  color: #ffffff;\n  line-height: 1;\n  margin: 1px 0;\n}\n\n.exp-progress {\n  height: 6px;\n  background-color: #333366;\n  border: 1px solid #5555ff;\n  margin: 2px 0;\n  position: relative;\n  border-radius: 3px;\n  overflow: hidden;\n}\n\n.exp-bar {\n  height: 100%;\n  background-color: #00ff00;\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n\n.test-exp-btn {\n  background-color: #3333AA;\n  color: #ffffff;\n  border: none;\n  padding: 0 3px;\n  cursor: pointer;\n  margin-left: 10px;\n  font-size: 12px;\n  height: 18px;\n  line-height: 1;\n}\n\n.upgrade-rate {\n  color: #ff0000;\n  line-height: 1;\n}\n\n.strength-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: #ff0000;\n  margin: 1px 0;\n  padding: 0;\n  line-height: 1;\n}\n\n.close-btn {\n  background-color: #ff0000;\n  color: #ffffff;\n  border: none;\n  padding: 0 3px;\n  cursor: pointer;\n  font-size: 14px;\n  height: 18px;\n  line-height: 1;\n}\n\n.change-avatar-btn {\n  width: 80px;\n  height: 20px;\n  background-color: #3333AA;\n  color: #ffffff;\n  border: none;\n  margin-top: 1px;\n  margin-bottom: 1px;\n  cursor: pointer;\n  font-size: 14px;\n  line-height: 1;\n  padding: 0;\n}\n\n.avatar-container {\n  width: 80px;\n  height: 80px;\n  overflow: hidden;\n  border: 2px solid #5555ff;\n  border-radius: 5px;\n}\n\n.character-avatar {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.attributes-container {\n  margin-top: 0;\n}\n\n.skill-btn {\n  width: 80px;\n  height: 20px;\n  background-color: #3333AA;\n  color: #ffffff;\n  border: none;\n  margin-bottom: 2px;\n  cursor: pointer;\n  font-size: 14px;\n  line-height: 1;\n  padding: 0;\n}\n\n.attribute-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0;\n  line-height: 1;\n}\n\n.attribute-label {\n  min-width: 40px;\n  color: #ffffff;\n  margin-right: 0;\n  padding-right: 0;\n  font-size: 14px;\n  text-align: right;\n}\n\n.attribute-value {\n  color: #ffffff;\n  flex: 1;\n  font-size: 14px;\n  padding-left: 10px;\n}\n\n.colored-text {\n  color: #ff0000;\n}\n\n.distribute-btn,\n.mount-btn {\n  background-color: #3333AA;\n  color: #ffffff;\n  border: none;\n  padding: 0 3px;\n  cursor: pointer;\n  margin-left: 2px;\n  font-size: 14px;\n  height: 18px;\n  line-height: 1;\n}\n\n.personal-info-row {\n  display: flex;\n  align-items: center;\n  margin: 1px 0;\n}\n\n.personal-info-label {\n  color: #ffffff;\n  font-size: 14px;\n  min-width: 70px;\n}\n\n.personal-info-status {\n  color: #ffffff;\n  margin: 0 2px;\n  font-size: 14px;\n}\n\n.toggle-info-btn {\n  background-color: #ff0000;\n  color: #ffffff;\n  border: none;\n  padding: 0 3px;\n  cursor: pointer;\n  font-size: 14px;\n  height: 18px;\n  line-height: 1;\n}\n\n.bottom-buttons {\n  display: flex;\n  gap: 1px;\n  margin: 1px 0;\n}\n\n.function-btn {\n  flex: 1;\n  background-color: #3333AA;\n  color: #ffffff;\n  border: none;\n  padding: 2px 0;\n  cursor: pointer;\n  font-size: 14px;\n  height: 20px;\n  line-height: 1;\n}\n\n.talent-container {\n  background-color: #000066;\n  padding: 0;\n  margin: 1px 0;\n  line-height: 1;\n}\n\n.talent-label {\n  color: #ffffff;\n  display: inline;\n  font-size: 14px;\n}\n\n.talent-list {\n  color: #ff0000;\n  display: inline;\n  font-size: 14px;\n}\n\n.back-button-container {\n  position: fixed;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  z-index: 10;\n}\n\n.back-button {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  width: 140px;\n  height: 50px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n  }\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.attribute-distribution-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 100;\n  \n  .modal-content {\n    background-color: #000066;\n    padding: 15px;\n    border: 2px solid #3333AA;\n    width: 80%;\n    max-width: 350px;\n    \n    h2 {\n      color: #ff0000;\n      font-size: 16px;\n      margin-bottom: 10px;\n      text-align: center;\n    }\n    \n    p {\n      color: #ffff00;\n      font-size: 14px;\n      margin-bottom: 15px;\n      text-align: center;\n      \n      &.level-bonus {\n        color: #00ff00;\n        margin-bottom: 10px;\n        font-size: 13px;\n      }\n      \n      &.level-info {\n        color: #00ffff;\n        margin-bottom: 10px;\n        font-size: 12px;\n      }\n    }\n    \n    .attribute-adjust-row {\n      display: flex;\n      align-items: center;\n      margin-bottom: 8px;\n      \n      span {\n        flex: 1;\n        color: #ffffff;\n        font-size: 14px;\n        &:nth-child(3) {\n          text-align: center;\n          width: 30px;\n          color: #ffff00;\n        }\n        \n        &.bonus-description {\n          flex: 2;\n          font-size: 12px;\n          color: #00ffff;\n          text-align: right;\n          padding-left: 5px;\n        }\n      }\n      \n      button {\n        background-color: #3333AA;\n        color: #ffffff;\n        border: 1px solid #5555ff;\n        padding: 0 8px;\n        cursor: pointer;\n        margin: 0 5px;\n        font-size: 14px;\n        height: 20px;\n        line-height: 1;\n        \n        &:disabled {\n          background-color: #222244;\n          color: #666666;\n          border-color: #444466;\n          cursor: not-allowed;\n        }\n      }\n\n      .adjust-btn {\n        background-color: #550000;\n        color: #ffffff;\n        border: 2px solid #ff0000;\n        font-weight: bold;\n        font-size: 16px;\n        width: 30px;\n        height: 30px;\n        border-radius: 5px;\n        cursor: pointer;\n        \n        &:hover:not(:disabled) {\n          background-color: #770000;\n        }\n        \n        &:active:not(:disabled) {\n          background-color: #990000;\n        }\n        \n        &:disabled {\n          background-color: #332222;\n          color: #777777;\n          border-color: #664444;\n          cursor: not-allowed;\n        }\n        \n        &.decrease-btn {\n          background-color: #550000;\n        }\n        \n        &.increase-btn {\n          background-color: #550000;\n        }\n      }\n    }\n    \n    .attribute-preview {\n      margin-top: 15px;\n      margin-bottom: 15px;\n      border: 1px solid #3333AA;\n      padding: 10px;\n      background-color: #000044;\n      \n      h3 {\n        color: #ff0000;\n        font-size: 16px;\n        margin-bottom: 8px;\n        text-align: center;\n      }\n      \n      .preview-row {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n  margin-bottom: 5px;\n        \n        span {\n          color: #00ffff;\n          font-size: 14px;\n        }\n      }\n    }\n    \n    .modal-buttons {\n      display: flex;\n      justify-content: center;\n      margin-top: 15px;\n      gap: 20px;\n      \n      button {\n        background-color: #3333AA;\n        color: #ffffff;\n        border: 1px solid #5555ff;\n        padding: 8px 20px;\n        cursor: pointer;\n        font-size: 16px;\n        border-radius: 5px;\n        font-weight: bold;\n        min-width: 80px;\n        \n        &:first-child {\n          background-color: #550000;\n          border-color: #ff0000;\n          border-width: 2px;\n        }\n        \n        &:hover {\n          transform: scale(1.05);\n        }\n        \n        &:active {\n          transform: scale(0.95);\n        }\n      }\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .character-info-container {\n    flex-direction: column;\n  }\n  \n  .avatar-container {\n    align-self: center;\n    margin-bottom: 10px;\n    width: 70px;\n    height: 70px;\n  }\n  \n  .attribute-row {\n    flex-wrap: wrap;\n  }\n  \n  .back-button {\n    width: 120px;\n    height: 45px;\n  }\n  \n  .basic-info {\n    gap: 4px;\n  }\n  \n  .bottom-buttons {\n    gap: 4px;\n  }\n}\n\n@media (max-width: 576px) {\n  .status-page {\n    padding: 8px;\n  }\n  \n  .server-name, .profession, .server-rank, .title {\n    font-size: 14px;\n  }\n  \n  .change-avatar-btn, .skill-btn {\n    width: 100px;\n    height: 28px;\n    font-size: 14px;\n  }\n  \n  .attribute-label {\n    min-width: 90px;\n    font-size: 14px;\n  }\n  \n  .attribute-value {\n    font-size: 14px;\n  }\n  \n  .distribute-btn, .mount-btn, .close-btn, .toggle-info-btn {\n    padding: 4px 10px;\n    font-size: 14px;\n  }\n  \n  .function-btn {\n    padding: 6px;\n    font-size: 14px;\n  }\n  \n  .back-button {\n    width: 100px;\n    height: 40px;\n  }\n  \n  .attributes-container {\n    margin-top: 5px;\n  }\n  \n  .talent-container {\n    padding: 4px;\n    margin: 4px 0;\n  }\n  \n  .bottom-buttons {\n    margin: 8px 0;\n  }\n  \n  .profession-avatar-row {\n    align-items: flex-start;\n  }\n  \n  .avatar-container {\n    width: 60px;\n    height: 60px;\n  }\n}\n\n@media (max-width: 480px) {\n  .status-page {\n    padding: 5px;\n  }\n  \n  .server-name, .profession, .server-rank, .title {\n    font-size: 14px;\n  }\n  \n  .change-avatar-btn, .skill-btn {\n    width: 90px;\n    height: 26px;\n    font-size: 14px;\n  }\n  \n  .attribute-label {\n    min-width: 80px;\n    font-size: 14px;\n  }\n  \n  .attribute-value {\n    font-size: 14px;\n  }\n  \n  .distribute-btn, .mount-btn, .close-btn, .toggle-info-btn {\n    padding: 3px 8px;\n    font-size: 14px;\n  }\n  \n  .function-btn {\n    padding: 5px;\n    font-size: 14px;\n  }\n  \n  .back-button {\n    width: 90px;\n    height: 35px;\n    bottom: 15px;\n  }\n  \n  .profession-avatar-row {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .avatar-container {\n    align-self: flex-end;\n    margin-top: -40px;\n    width: 50px;\n    height: 50px;\n  }\n}\n\n@media (max-width: 375px) {\n  .status-page {\n    padding: 4px;\n  }\n  \n  .server-name, .profession, .server-rank, .title {\n    font-size: 14px;\n  }\n  \n  .change-avatar-btn, .skill-btn {\n    width: 80px;\n    height: 24px;\n    font-size: 14px;\n  }\n  \n  .attribute-label {\n    min-width: 70px;\n    font-size: 14px;\n  }\n  \n  .attribute-value {\n    font-size: 14px;\n  }\n  \n  .distribute-btn, .mount-btn, .close-btn, .toggle-info-btn {\n    padding: 2px 6px;\n    font-size: 14px;\n  }\n  \n  .function-btn {\n    padding: 4px;\n    font-size: 14px;\n  }\n  \n  .back-button {\n    width: 80px;\n    height: 30px;\n    bottom: 10px;\n  }\n  \n  .strength-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .close-btn {\n    margin-top: 2px;\n    align-self: flex-end;\n  }\n  \n  .attribute-row {\n    margin-bottom: 3px;\n  }\n  \n  .personal-info-row {\n    flex-wrap: wrap;\n    margin: 5px 0;\n  }\n  \n  .toggle-info-btn {\n    margin-top: 2px;\n  }\n  \n  .profession-avatar-row {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .avatar-container {\n    align-self: flex-end;\n    margin-top: -35px;\n    width: 45px;\n    height: 45px;\n  }\n}\n\n@media (max-width: 320px) {\n  .server-name, .profession, .server-rank, .title {\n    font-size: 14px;\n  }\n  \n  .attribute-label {\n    min-width: 65px;\n    font-size: 14px;\n  }\n  \n  .attribute-value {\n    font-size: 14px;\n  }\n  \n  .change-avatar-btn, .skill-btn {\n    width: 70px;\n    height: 22px;\n    font-size: 14px;\n  }\n  \n  .distribute-btn, .mount-btn, .close-btn, .toggle-info-btn {\n    padding: 2px 5px;\n    font-size: 14px;\n  }\n  \n  .function-btn {\n    padding: 3px;\n    font-size: 14px;\n  }\n  \n  .back-button {\n    width: 70px;\n    height: 25px;\n  }\n  \n  .avatar-container {\n    width: 40px;\n    height: 40px;\n    margin-top: -30px;\n  }\n}\n\n@media (min-width: 992px) {\n  .status-page {\n    padding: 15px;\n    max-width: 900px;\n    margin: 0 auto;\n  }\n  \n  .avatar-container {\n    width: 100px;\n    height: 100px;\n    border-width: 3px;\n  }\n  \n  .server-name {\n    font-size: 14px;\n  }\n  \n  .profession, .server-rank, .title {\n    font-size: 14px;\n  }\n  \n  .back-button {\n    width: 160px;\n    height: 55px;\n  }\n  \n  .change-avatar-btn, .skill-btn {\n    width: 140px;\n    height: 35px;\n    font-size: 16px;\n  }\n  \n  .attribute-label {\n    min-width: 140px;\n  font-size: 16px;\n}\n\n  .attribute-value {\n    font-size: 16px;\n  }\n  \n  .distribute-btn, .mount-btn, .close-btn, .toggle-info-btn {\n    padding: 6px 18px;\n    font-size: 14px;\n  }\n  \n  .function-btn {\n    padding: 10px;\n    font-size: 16px;\n  }\n  \n  .talent-container {\n    padding: 8px;\n    margin: 10px 0;\n    font-size: 16px;\n  }\n  \n  .bottom-buttons {\n    margin: 15px 0;\n  }\n}\n\n@media (min-width: 1200px) {\n  .status-page {\n    max-width: 1100px;\n    padding: 20px;\n  }\n  \n  .avatar-container {\n    width: 120px;\n    height: 120px;\n  }\n}\n</style> "]}]}