{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Posthouse.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Posthouse.vue", "mtime": 1749719122782}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgR2FtZUxheW91dCBmcm9tICdAL2xheW91dHMvR2FtZUxheW91dC52dWUnOw0KZXhwb3J0IGRlZmF1bHQgeyBuYW1lOiAnUG9zdGhvdXNlJywgY29tcG9uZW50czogeyBHYW1lTGF5b3V0IH0gfTsNCg=="}, {"version": 3, "sources": ["Posthouse.vue"], "names": [], "mappings": ";AAMA;AACA", "file": "Posthouse.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"facility-page\">馆驿</div>\r\n  </GameLayout>\r\n</template>\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nexport default { name: 'Posthouse', components: { GameLayout } };\r\n</script>\r\n<style scoped>.facility-page { font-size: 22px; text-align: center; margin-top: 40px; }</style> "]}]}