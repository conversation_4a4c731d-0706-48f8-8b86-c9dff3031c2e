{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue?vue&type=style&index=0&id=1bd62dc5&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Vip.vue", "mtime": 1749720256889}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudmlwLXBhZ2UgeyBtYXgtd2lkdGg6IDUwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfQ0KLnZpcC1pbmZvIHsgdGV4dC1hbGlnbjogY2VudGVyOyBtYXJnaW4tYm90dG9tOiAyMHB4OyB9DQoudmlwLWljb24geyB3aWR0aDogODBweDsgaGVpZ2h0OiA4MHB4OyB9DQoudmlwLWxldmVsIHsgZm9udC1zaXplOiAyNHB4OyBmb250LXdlaWdodDogYm9sZDsgbWFyZ2luOiAxMHB4IDA7IH0NCi52aXAtZXhwIHsgY29sb3I6ICM4ODg7IG1hcmdpbi1ib3R0b206IDhweDsgfQ0KLnZpcC1uZXh0LWV4cCB7IGNvbG9yOiAjZmY4ODAwOyBmb250LXNpemU6IDE0cHg7IH0NCi52aXAtcHJvZ3Jlc3MtYmFyIHsgd2lkdGg6IDEwMCU7IGhlaWdodDogMTBweDsgYmFja2dyb3VuZDogI2VlZTsgYm9yZGVyLXJhZGl1czogNXB4OyBtYXJnaW4tYm90dG9tOiAxMHB4OyB9DQoudmlwLXByb2dyZXNzIHsgaGVpZ2h0OiAxMDAlOyBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICNmZmQ3MDAsICNmZjg4MDApOyBib3JkZXItcmFkaXVzOiA1cHg7IHRyYW5zaXRpb246IHdpZHRoIDAuNXM7IH0NCi52aXAtaWNvbnMtbGlzdCB7IGRpc3BsYXk6IGZsZXg7IGZsZXgtd3JhcDogd3JhcDsgZ2FwOiA4cHg7IGp1c3RpZnktY29udGVudDogY2VudGVyOyBtYXJnaW4tYm90dG9tOiAyMHB4OyB9DQoudmlwLWljb24taXRlbSB7IHRleHQtYWxpZ246IGNlbnRlcjsgd2lkdGg6IDYwcHg7IH0NCi52aXAtaWNvbi1pdGVtIGltZyB7IHdpZHRoOiA0MHB4OyBoZWlnaHQ6IDQwcHg7IH0NCi52aXAtaWNvbi1pdGVtLmFjdGl2ZSBpbWcgeyBib3JkZXI6IDJweCBzb2xpZCAjZmZkNzAwOyBib3JkZXItcmFkaXVzOiA1MCU7IH0NCi52aXAtcHJpdmlsZWdlcyB7IGJhY2tncm91bmQ6ICMyMjI7IGNvbG9yOiAjZmZkNzAwOyBib3JkZXItcmFkaXVzOiA4cHg7IHBhZGRpbmc6IDEycHg7IG1hcmdpbi1ib3R0b206IDIwcHg7IH0NCi52aXAtcmV3YXJkLWhpc3RvcnkgeyBiYWNrZ3JvdW5kOiAjMzMzOyBjb2xvcjogI2ZmZDcwMDsgYm9yZGVyLXJhZGl1czogOHB4OyBwYWRkaW5nOiAxMnB4OyB9DQo="}, {"version": 3, "sources": ["Vip.vue"], "names": [], "mappings": ";AA+GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Vip.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"vip-page\">\r\n      <h2>VIP特权</h2>\r\n      <div class=\"vip-info\">\r\n        <img :src=\"vipIcon\" class=\"vip-icon\" :alt=\"'VIP' + vipLevel\" />\r\n        <div class=\"vip-level\">VIP{{ vipLevel }}</div>\r\n        <div class=\"vip-exp\">\r\n          当前经验：{{ vipExp }} / {{ vipNextExp }}\r\n          <span v-if=\"vipLevel < 20\" class=\"vip-next-exp\">\r\n            ，还需 {{ vipNextExp - vipExp }} 经验升级到 VIP{{ vipLevel + 1 }}\r\n          </span>\r\n          <span v-else class=\"vip-next-exp\">（已满级）</span>\r\n        </div>\r\n        <div class=\"vip-progress-bar\">\r\n          <div class=\"vip-progress\" :style=\"{width: vipPercent + '%'}\"></div>\r\n        </div>\r\n        <button v-if=\"canClaimReward\" @click=\"claimVipReward\" :disabled=\"isClaiming\">领取VIP奖励</button>\r\n      </div>\r\n      <div class=\"vip-icons-list\">\r\n        <div\r\n          v-for=\"level in 21\"\r\n          :key=\"level-1\"\r\n          class=\"vip-icon-item\"\r\n          :class=\"{active: vipLevel === (level-1)}\"\r\n        >\r\n          <img :src=\"getVipIcon(level-1)\" :alt=\"'VIP' + (level-1)\" />\r\n          <div>VIP{{ level-1 }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"vip-privileges\">\r\n        <h3>VIP特权说明</h3>\r\n        <ul>\r\n          <li v-for=\"(desc, idx) in vipPrivileges\" :key=\"idx\">{{ desc }}</li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"vip-reward-history\" v-if=\"vipRewardHistory.length\">\r\n        <h3>VIP成长奖励历史</h3>\r\n        <ul>\r\n          <li v-for=\"(reward, idx) in vipRewardHistory\" :key=\"idx\">\r\n            <span>VIP{{ reward.level }}（{{ reward.date }}）：</span>{{ reward.desc }}\r\n          </li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'Vip',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      vipLevel: 0,\r\n      vipExp: 0,\r\n      vipNextExp: 100,\r\n      isClaiming: false,\r\n      canClaimReward: false,\r\n      vipPrivileges: [],\r\n      vipRewardHistory: []\r\n    }\r\n  },\r\n  computed: {\r\n    vipIcon() {\r\n      return `/static/game/UI/vip/${this.vipLevel}.png`\r\n    },\r\n    vipPercent() {\r\n      return Math.min(100, Math.round((this.vipExp / this.vipNextExp) * 100))\r\n    }\r\n  },\r\n  methods: {\r\n    getVipIcon(level) {\r\n      return `/static/game/UI/vip/${level}.png`\r\n    },\r\n    async fetchVipData() {\r\n      try {\r\n        const res = await axios.get('/api/vip/info')\r\n        this.vipLevel = res.data.level\r\n        this.vipExp = res.data.exp\r\n        this.vipNextExp = res.data.next_exp\r\n        this.canClaimReward = res.data.can_claim_reward\r\n        this.vipPrivileges = res.data.privileges || []\r\n        this.vipRewardHistory = res.data.reward_history || []\r\n      } catch (e) {\r\n        this.$toast && this.$toast('获取VIP信息失败')\r\n      }\r\n    },\r\n    async claimVipReward() {\r\n      this.isClaiming = true\r\n      try {\r\n        await axios.post('/api/vip/claim-reward')\r\n        this.$toast && this.$toast('领取成功', 'success')\r\n        this.fetchVipData()\r\n      } catch (e) {\r\n        this.$toast && this.$toast('领取失败')\r\n      } finally {\r\n        this.isClaiming = false\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchVipData()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.vip-page { max-width: 500px; margin: 0 auto; padding: 20px; }\r\n.vip-info { text-align: center; margin-bottom: 20px; }\r\n.vip-icon { width: 80px; height: 80px; }\r\n.vip-level { font-size: 24px; font-weight: bold; margin: 10px 0; }\r\n.vip-exp { color: #888; margin-bottom: 8px; }\r\n.vip-next-exp { color: #ff8800; font-size: 14px; }\r\n.vip-progress-bar { width: 100%; height: 10px; background: #eee; border-radius: 5px; margin-bottom: 10px; }\r\n.vip-progress { height: 100%; background: linear-gradient(90deg, #ffd700, #ff8800); border-radius: 5px; transition: width 0.5s; }\r\n.vip-icons-list { display: flex; flex-wrap: wrap; gap: 8px; justify-content: center; margin-bottom: 20px; }\r\n.vip-icon-item { text-align: center; width: 60px; }\r\n.vip-icon-item img { width: 40px; height: 40px; }\r\n.vip-icon-item.active img { border: 2px solid #ffd700; border-radius: 50%; }\r\n.vip-privileges { background: #222; color: #ffd700; border-radius: 8px; padding: 12px; margin-bottom: 20px; }\r\n.vip-reward-history { background: #333; color: #ffd700; border-radius: 8px; padding: 12px; }\r\n</style> "]}]}