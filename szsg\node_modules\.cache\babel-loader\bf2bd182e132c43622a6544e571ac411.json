{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\mapService.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\mapService.js", "mtime": 1749874579101}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["get", "post", "logger", "getCache", "setCache", "removeCache", "CACHE_KEYS", "MAP_DATA", "LOCATION_DETAILS", "AVAILABLE_LOCATIONS", "MOVEMENT_COST", "mapService", "getCurrentMap", "characterId", "debug", "cachedData", "Promise", "resolve", "loading", "loadingText", "then", "res", "data", "catch", "error", "getAvailableLocations", "currentLocation", "cache<PERSON>ey", "current_location", "getLocationDetails", "locationId", "moveToLocation", "targetLocation", "target_location", "clearLocationCache", "getMovementCost", "fromLocation", "toLocation", "from_location", "to_location", "getLocationEntities", "getWorldMap"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/api/services/mapService.js"], "sourcesContent": ["/**\n * 地图系统API服务\n * 提供地图相关的接口调用\n */\nimport { get, post } from '../request.js';\nimport logger from '../../utils/logger.js';\nimport { getCache, setCache, removeCache } from './cacheService.js';\n\n// 缓存键\nconst CACHE_KEYS = {\n    MAP_DATA: 'map_data',\n    LOCATION_DETAILS: 'location_details',\n    AVAILABLE_LOCATIONS: 'available_locations',\n    MOVEMENT_COST: 'movement_cost'\n};\n\n/**\n * 地图服务\n */\nconst mapService = {\n    /**\n     * 获取当前地图数据\n     * @param {string} characterId - 角色ID\n     * @returns {Promise<Object>} - 地图数据\n     */\n    getCurrentMap(characterId) {\n        logger.debug('[MapService] 获取当前地图数据, characterId:', characterId);\n        \n        // 尝试从缓存获取\n        const cachedData = getCache(CACHE_KEYS.MAP_DATA, characterId);\n        if (cachedData) {\n            return Promise.resolve(cachedData);\n        }\n\n        return get(`/characters/${characterId}/map`, {}, {\n            loading: true,\n            loadingText: '加载地图数据...'\n        }).then(res => {\n            logger.debug('[MapService] 地图数据响应:', res);\n            // 缓存结果\n            setCache(CACHE_KEYS.MAP_DATA, characterId, res.data, 300000); // 缓存5分钟\n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 获取地图数据失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取可移动的位置列表\n     * @param {string} characterId - 角色ID\n     * @param {string} currentLocation - 当前位置\n     * @returns {Promise<Array>} - 可移动位置列表\n     */\n    getAvailableLocations(characterId, currentLocation) {\n        logger.debug('[MapService] 获取可移动位置, characterId:', characterId, 'currentLocation:', currentLocation);\n        \n        // 尝试从缓存获取\n        const cacheKey = `${characterId}_${currentLocation}`;\n        const cachedData = getCache(CACHE_KEYS.AVAILABLE_LOCATIONS, cacheKey);\n        if (cachedData) {\n            return Promise.resolve(cachedData);\n        }\n\n        return get(`/characters/${characterId}/map/locations`, { \n            current_location: currentLocation \n        }, {\n            loading: false\n        }).then(res => {\n            logger.debug('[MapService] 可移动位置响应:', res);\n            // 缓存结果\n            setCache(CACHE_KEYS.AVAILABLE_LOCATIONS, cacheKey, res.data, 180000); // 缓存3分钟\n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 获取可移动位置失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取位置详情\n     * @param {string} locationId - 位置ID\n     * @returns {Promise<Object>} - 位置详情\n     */\n    getLocationDetails(locationId) {\n        logger.debug('[MapService] 获取位置详情, locationId:', locationId);\n        \n        // 尝试从缓存获取\n        const cachedData = getCache(CACHE_KEYS.LOCATION_DETAILS, locationId);\n        if (cachedData) {\n            return Promise.resolve(cachedData);\n        }\n\n        return get(`/map/locations/${locationId}`, {}, {\n            loading: true,\n            loadingText: '加载位置信息...'\n        }).then(res => {\n            logger.debug('[MapService] 位置详情响应:', res);\n            // 缓存结果\n            setCache(CACHE_KEYS.LOCATION_DETAILS, locationId, res.data, 600000); // 缓存10分钟\n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 获取位置详情失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 移动到指定位置\n     * @param {string} characterId - 角色ID\n     * @param {string} targetLocation - 目标位置\n     * @returns {Promise<Object>} - 移动结果\n     */\n    moveToLocation(characterId, targetLocation) {\n        logger.debug('[MapService] 移动到位置, characterId:', characterId, 'targetLocation:', targetLocation);\n        \n        return post(`/characters/${characterId}/map/move`, {\n            target_location: targetLocation\n        }, {\n            loading: true,\n            loadingText: '移动中...'\n        }).then(res => {\n            logger.debug('[MapService] 移动响应:', res);\n            \n            // 清除相关缓存\n            this.clearLocationCache(characterId);\n            \n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 移动失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取移动消耗\n     * @param {string} characterId - 角色ID\n     * @param {string} fromLocation - 起始位置\n     * @param {string} toLocation - 目标位置\n     * @returns {Promise<Object>} - 移动消耗信息\n     */\n    getMovementCost(characterId, fromLocation, toLocation) {\n        logger.debug('[MapService] 获取移动消耗, characterId:', characterId, 'from:', fromLocation, 'to:', toLocation);\n        \n        // 尝试从缓存获取\n        const cacheKey = `${fromLocation}_${toLocation}`;\n        const cachedData = getCache(CACHE_KEYS.MOVEMENT_COST, cacheKey);\n        if (cachedData) {\n            return Promise.resolve(cachedData);\n        }\n\n        return get(`/characters/${characterId}/map/movement-cost`, {\n            from_location: fromLocation,\n            to_location: toLocation\n        }, {\n            loading: false\n        }).then(res => {\n            logger.debug('[MapService] 移动消耗响应:', res);\n            // 缓存结果\n            setCache(CACHE_KEYS.MOVEMENT_COST, cacheKey, res.data, 300000); // 缓存5分钟\n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 获取移动消耗失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取位置的NPC和怪物\n     * @param {string} characterId - 角色ID\n     * @param {string} locationId - 位置ID\n     * @returns {Promise<Object>} - NPC和怪物数据\n     */\n    getLocationEntities(characterId, locationId) {\n        logger.debug('[MapService] 获取位置实体, characterId:', characterId, 'locationId:', locationId);\n        \n        return get(`/characters/${characterId}/map/locations/${locationId}/entities`, {}, {\n            loading: false\n        }).then(res => {\n            logger.debug('[MapService] 位置实体响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 获取位置实体失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 清除位置相关缓存\n     * @param {string} characterId - 角色ID\n     */\n    clearLocationCache(characterId) {\n        try {\n            // 清除地图数据缓存\n            removeCache(CACHE_KEYS.MAP_DATA, characterId);\n\n            // 清除可移动位置缓存\n            // 注意：这里简化处理，实际应用中可能需要更复杂的缓存清理逻辑\n            logger.debug('[MapService] 位置缓存已清除');\n        } catch (error) {\n            logger.error('[MapService] 清除缓存失败:', error);\n        }\n    },\n\n    /**\n     * 获取世界地图数据\n     * @returns {Promise<Object>} - 世界地图数据\n     */\n    getWorldMap() {\n        logger.debug('[MapService] 获取世界地图数据');\n        \n        // 尝试从缓存获取\n        const cachedData = getCache('world_map', 'data');\n        if (cachedData) {\n            return Promise.resolve(cachedData);\n        }\n\n        return get('/map/world', {}, {\n            loading: true,\n            loadingText: '加载世界地图...'\n        }).then(res => {\n            logger.debug('[MapService] 世界地图响应:', res);\n            // 缓存结果\n            setCache('world_map', 'data', res.data, 3600000); // 缓存1小时\n            return res.data;\n        }).catch(error => {\n            logger.error('[MapService] 获取世界地图失败:', error);\n            throw error;\n        });\n    }\n};\n\nexport default mapService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,GAAG,EAAEC,IAAI,QAAQ,eAAe;AACzC,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,mBAAmB;;AAEnE;AACA,MAAMC,UAAU,GAAG;EACfC,QAAQ,EAAE,UAAU;EACpBC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE,qBAAqB;EAC1CC,aAAa,EAAE;AACnB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,UAAU,GAAG;EACf;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,WAAW,EAAE;IACvBX,MAAM,CAACY,KAAK,CAAC,qCAAqC,EAAED,WAAW,CAAC;;IAEhE;IACA,MAAME,UAAU,GAAGZ,QAAQ,CAACG,UAAU,CAACC,QAAQ,EAAEM,WAAW,CAAC;IAC7D,IAAIE,UAAU,EAAE;MACZ,OAAOC,OAAO,CAACC,OAAO,CAACF,UAAU,CAAC;IACtC;IAEA,OAAOf,GAAG,CAAC,eAAea,WAAW,MAAM,EAAE,CAAC,CAAC,EAAE;MAC7CK,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,sBAAsB,EAAEO,GAAG,CAAC;MACzC;MACAjB,QAAQ,CAACE,UAAU,CAACC,QAAQ,EAAEM,WAAW,EAAEQ,GAAG,CAACC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;MAC9D,OAAOD,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIC,qBAAqBA,CAACZ,WAAW,EAAEa,eAAe,EAAE;IAChDxB,MAAM,CAACY,KAAK,CAAC,oCAAoC,EAAED,WAAW,EAAE,kBAAkB,EAAEa,eAAe,CAAC;;IAEpG;IACA,MAAMC,QAAQ,GAAG,GAAGd,WAAW,IAAIa,eAAe,EAAE;IACpD,MAAMX,UAAU,GAAGZ,QAAQ,CAACG,UAAU,CAACG,mBAAmB,EAAEkB,QAAQ,CAAC;IACrE,IAAIZ,UAAU,EAAE;MACZ,OAAOC,OAAO,CAACC,OAAO,CAACF,UAAU,CAAC;IACtC;IAEA,OAAOf,GAAG,CAAC,eAAea,WAAW,gBAAgB,EAAE;MACnDe,gBAAgB,EAAEF;IACtB,CAAC,EAAE;MACCR,OAAO,EAAE;IACb,CAAC,CAAC,CAACE,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,uBAAuB,EAAEO,GAAG,CAAC;MAC1C;MACAjB,QAAQ,CAACE,UAAU,CAACG,mBAAmB,EAAEkB,QAAQ,EAAEN,GAAG,CAACC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;MACtE,OAAOD,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIK,kBAAkBA,CAACC,UAAU,EAAE;IAC3B5B,MAAM,CAACY,KAAK,CAAC,kCAAkC,EAAEgB,UAAU,CAAC;;IAE5D;IACA,MAAMf,UAAU,GAAGZ,QAAQ,CAACG,UAAU,CAACE,gBAAgB,EAAEsB,UAAU,CAAC;IACpE,IAAIf,UAAU,EAAE;MACZ,OAAOC,OAAO,CAACC,OAAO,CAACF,UAAU,CAAC;IACtC;IAEA,OAAOf,GAAG,CAAC,kBAAkB8B,UAAU,EAAE,EAAE,CAAC,CAAC,EAAE;MAC3CZ,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,sBAAsB,EAAEO,GAAG,CAAC;MACzC;MACAjB,QAAQ,CAACE,UAAU,CAACE,gBAAgB,EAAEsB,UAAU,EAAET,GAAG,CAACC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;MACrE,OAAOD,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIO,cAAcA,CAAClB,WAAW,EAAEmB,cAAc,EAAE;IACxC9B,MAAM,CAACY,KAAK,CAAC,kCAAkC,EAAED,WAAW,EAAE,iBAAiB,EAAEmB,cAAc,CAAC;IAEhG,OAAO/B,IAAI,CAAC,eAAeY,WAAW,WAAW,EAAE;MAC/CoB,eAAe,EAAED;IACrB,CAAC,EAAE;MACCd,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,oBAAoB,EAAEO,GAAG,CAAC;;MAEvC;MACA,IAAI,CAACa,kBAAkB,CAACrB,WAAW,CAAC;MAEpC,OAAOQ,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIW,eAAeA,CAACtB,WAAW,EAAEuB,YAAY,EAAEC,UAAU,EAAE;IACnDnC,MAAM,CAACY,KAAK,CAAC,mCAAmC,EAAED,WAAW,EAAE,OAAO,EAAEuB,YAAY,EAAE,KAAK,EAAEC,UAAU,CAAC;;IAExG;IACA,MAAMV,QAAQ,GAAG,GAAGS,YAAY,IAAIC,UAAU,EAAE;IAChD,MAAMtB,UAAU,GAAGZ,QAAQ,CAACG,UAAU,CAACI,aAAa,EAAEiB,QAAQ,CAAC;IAC/D,IAAIZ,UAAU,EAAE;MACZ,OAAOC,OAAO,CAACC,OAAO,CAACF,UAAU,CAAC;IACtC;IAEA,OAAOf,GAAG,CAAC,eAAea,WAAW,oBAAoB,EAAE;MACvDyB,aAAa,EAAEF,YAAY;MAC3BG,WAAW,EAAEF;IACjB,CAAC,EAAE;MACCnB,OAAO,EAAE;IACb,CAAC,CAAC,CAACE,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,sBAAsB,EAAEO,GAAG,CAAC;MACzC;MACAjB,QAAQ,CAACE,UAAU,CAACI,aAAa,EAAEiB,QAAQ,EAAEN,GAAG,CAACC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;MAChE,OAAOD,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIgB,mBAAmBA,CAAC3B,WAAW,EAAEiB,UAAU,EAAE;IACzC5B,MAAM,CAACY,KAAK,CAAC,mCAAmC,EAAED,WAAW,EAAE,aAAa,EAAEiB,UAAU,CAAC;IAEzF,OAAO9B,GAAG,CAAC,eAAea,WAAW,kBAAkBiB,UAAU,WAAW,EAAE,CAAC,CAAC,EAAE;MAC9EZ,OAAO,EAAE;IACb,CAAC,CAAC,CAACE,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,sBAAsB,EAAEO,GAAG,CAAC;MACzC,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;EACIU,kBAAkBA,CAACrB,WAAW,EAAE;IAC5B,IAAI;MACA;MACAR,WAAW,CAACC,UAAU,CAACC,QAAQ,EAAEM,WAAW,CAAC;;MAE7C;MACA;MACAX,MAAM,CAACY,KAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACZtB,MAAM,CAACsB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC/C;EACJ,CAAC;EAED;AACJ;AACA;AACA;EACIiB,WAAWA,CAAA,EAAG;IACVvC,MAAM,CAACY,KAAK,CAAC,uBAAuB,CAAC;;IAErC;IACA,MAAMC,UAAU,GAAGZ,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IAChD,IAAIY,UAAU,EAAE;MACZ,OAAOC,OAAO,CAACC,OAAO,CAACF,UAAU,CAAC;IACtC;IAEA,OAAOf,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE;MACzBkB,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXnB,MAAM,CAACY,KAAK,CAAC,sBAAsB,EAAEO,GAAG,CAAC;MACzC;MACAjB,QAAQ,CAAC,WAAW,EAAE,MAAM,EAAEiB,GAAG,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;MAClD,OAAOD,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdtB,MAAM,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN;AACJ,CAAC;AAED,eAAeb,UAAU", "ignoreList": []}]}