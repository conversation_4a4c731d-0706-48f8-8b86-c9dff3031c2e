{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\bankService.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\bankService.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["get", "post", "logger", "setCache", "CACHE_TYPE", "API_BASE_URL", "CACHE_KEYS", "ACCOUNT_INFO", "TRANSACTIONS", "bankService", "getAccountInfo", "characterId", "characterIdStr", "String", "debug", "error", "Promise", "reject", "Error", "API", "apiUrl", "fullUrl", "loading", "loadingText", "then", "res", "JSON", "stringify", "formattedData", "success", "data", "account", "character", "silver", "gold_ingot", "warn", "catch", "response", "status", "message", "original_error", "deposit", "currency", "amount", "amountNum", "parseInt", "_res$data", "_res$data2", "_res$data3", "_error$response", "details", "withdraw", "_res$data4", "_res$data5", "_res$data6", "_error$response2", "getTransactionHistory", "params", "_res$data7", "transactions", "length", "Array", "isArray", "map", "normalizeTransaction", "pagination", "current_page", "page", "per_page", "total", "last_page", "_res$data8", "_res$data9", "Math", "ceil", "transaction", "id", "random", "toString", "substr", "character_id", "type", "balance", "description", "created_at", "Date", "toISOString", "includes", "generateTransactionDescription"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/api/services/bankService.js"], "sourcesContent": ["/**\r\n * 钱庄系统API服务\r\n * 提供钱庄相关的接口调用\r\n */\r\nimport { get, post } from '../request.js';\r\nimport logger from '../../utils/logger.js';\r\nimport { setCache, CACHE_TYPE } from './cacheService.js';\r\nimport { API_BASE_URL } from '../config.js';\r\n\r\n// 缓存键\r\nconst CACHE_KEYS = {\r\n    ACCOUNT_INFO: 'bank_account_info',\r\n    TRANSACTIONS: 'bank_transactions'\r\n};\r\n\r\n/**\r\n * 钱庄服务\r\n */\r\nconst bankService = {\r\n    /**\r\n     * 获取银行账户信息\r\n     * @param {string|number} characterId - 角色ID\r\n     * @returns {Promise<Object>} - 银行账户信息\r\n     */\r\n    getAccountInfo(characterId) {\r\n        // 确保characterId是字符串\r\n        const characterIdStr = String(characterId);\r\n        logger.debug('[BankService] 获取银行账户信息, characterId:', characterIdStr);\r\n        \r\n        // 验证characterId\r\n        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {\r\n            logger.error('[BankService] 无效的角色ID:', characterIdStr);\r\n            return Promise.reject(new Error('无效的角色ID'));\r\n        }\r\n        \r\n        // 清除缓存，确保获取最新数据\r\n        setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);\r\n        \r\n        const apiUrl = `/bank/account/${characterIdStr}`;\r\n        const fullUrl = API_BASE_URL + apiUrl;\r\n        logger.debug('[BankService] 请求 URL:', fullUrl);\r\n        \r\n        // 在请求前记录日志\r\n        logger.debug('[BankService] 开始API请求:', fullUrl);\r\n        \r\n        return get(apiUrl, {}, {\r\n            loading: true,\r\n            loadingText: '获取账户信息...'\r\n        }).then(res => {\r\n            // 增强日志记录 - 成功\r\n            logger.debug('[BankService] 银行账户信息获取成功:', JSON.stringify(res, null, 2));\r\n            logger.debug('[BankService] API请求成功:', fullUrl);\r\n            logger.debug('[BankService] 响应数据:', JSON.stringify(res, null, 2));\r\n            \r\n            // 检查并格式化响应数据\r\n            let formattedData;\r\n            \r\n            // 标准格式：API返回的是包含success和data字段的对象\r\n            if (res && typeof res === 'object') {\r\n                if (res.success === true && res.data) {\r\n                    // 标准格式，直接返回\r\n                    formattedData = res;\r\n                } else if (res.account) {\r\n                    // 只包含account信息，包装成标准格式\r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            account: res.account,\r\n                            character: res.character || {}\r\n                        }\r\n                    };\r\n                } else if ('silver' in res || 'gold_ingot' in res) {\r\n                    // 响应本身就是账户信息\r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            account: {\r\n                                silver: res.silver || 0,\r\n                                gold_ingot: res.gold_ingot || 0\r\n                            }\r\n                        }\r\n                    };\r\n                } else {\r\n                    // 无法识别的格式，但至少是个对象，尝试强制格式化\r\n                    logger.warn('[BankService] 未知的响应格式，尝试格式化:', JSON.stringify(res));\r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            account: {\r\n                                silver: 0,\r\n                                gold_ingot: 0\r\n                            }\r\n                        }\r\n                    };\r\n                }\r\n            } else {\r\n                // 完全无法处理的响应，创建默认响应\r\n                logger.error('[BankService] 无效响应格式:', res);\r\n                formattedData = {\r\n                    success: false,\r\n                    data: {\r\n                        account: {\r\n                            silver: 0,\r\n                            gold_ingot: 0\r\n                        }\r\n                    },\r\n                    error: '无效的响应格式'\r\n                };\r\n            }\r\n            \r\n            // 缓存结果\r\n            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, formattedData.data, 60 * 1000); // 缓存1分钟\r\n            return formattedData;\r\n        }).catch(error => {\r\n            // 增强日志记录 - 失败\r\n            logger.error('[BankService] 获取银行账户信息失败:', error);\r\n            logger.error('[BankService] 请求URL:', fullUrl);\r\n            logger.error('[BankService] API请求失败:', fullUrl);\r\n            logger.error('[BankService] 错误详情:', JSON.stringify(error, null, 2));\r\n            \r\n            if (error.response) {\r\n                logger.error('[BankService] 响应状态:', error.response.status);\r\n                logger.error('[BankService] 响应数据:', JSON.stringify(error.response.data, null, 2));\r\n            }\r\n            \r\n            // 如果后端有详细错误信息，输出它\r\n            if (error.data && error.data.error) {\r\n                logger.error('[BankService] 后端错误详情:', error.data.error);\r\n            }\r\n            \r\n            // 返回错误信息和默认值\r\n            throw {\r\n                success: false,\r\n                message: error.message || '获取银行账户信息失败',\r\n                data: {\r\n                    account: {\r\n                        silver: 0,\r\n                        gold_ingot: 0\r\n                    }\r\n                },\r\n                original_error: error\r\n            };\r\n        });\r\n    },\r\n\r\n    /**\r\n     * 存款\r\n     * @param {string|number} characterId - 角色ID\r\n     * @param {string} currency - 货币类型 (silver/gold_ingot)\r\n     * @param {number} amount - 存款金额\r\n     * @returns {Promise<Object>} - 存款结果\r\n     */\r\n    deposit(characterId, currency, amount) {\r\n        // 确保characterId是字符串\r\n        const characterIdStr = String(characterId);\r\n        // 确保金额是有效数字\r\n        const amountNum = parseInt(amount) || 0;\r\n        // 确保货币类型有效\r\n        if (currency !== 'silver' && currency !== 'gold_ingot') {\r\n            logger.error('[BankService] 无效的货币类型:', currency);\r\n            return Promise.reject(new Error(`无效的货币类型: ${currency}`));\r\n        }\r\n        \r\n        logger.debug('[BankService] 存款操作, characterId:', characterIdStr, 'currency:', currency, 'amount:', amountNum);\r\n        \r\n        // 验证characterId\r\n        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {\r\n            logger.error('[BankService] 无效的角色ID:', characterIdStr);\r\n            return Promise.reject(new Error('无效的角色ID'));\r\n        }\r\n        \r\n        // 验证金额\r\n        if (amountNum <= 0) {\r\n            logger.error('[BankService] 无效的存款金额:', amountNum);\r\n            return Promise.reject(new Error('存款金额必须大于0'));\r\n        }\r\n        \r\n        return post(`/bank/deposit/${characterIdStr}`, {\r\n            currency: currency,\r\n            amount: amountNum\r\n        }, {\r\n            loading: true,\r\n            loadingText: '正在存款...'\r\n        }).then(res => {\r\n            logger.debug('[BankService] 存款成功:', JSON.stringify(res, null, 2));\r\n            \r\n            // 清除缓存，确保下次获取最新数据\r\n            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);\r\n            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);\r\n            \r\n            return {\r\n                success: true,\r\n                message: res.data?.message || '存款成功',\r\n                account: res.data?.account || {},\r\n                character: res.data?.character || {}\r\n            };\r\n        }).catch(error => {\r\n            logger.error('[BankService] 存款失败:', error);\r\n            // 详细记录错误信息\r\n            if (error.response) {\r\n                logger.error('[BankService] 错误状态码:', error.response.status);\r\n                logger.error('[BankService] 错误响应:', JSON.stringify(error.response.data, null, 2));\r\n            }\r\n            \r\n            throw {\r\n                success: false,\r\n                message: error.response?.data?.error?.message || '存款失败，请重试',\r\n                details: error.message || ''\r\n            };\r\n        });\r\n    },\r\n\r\n    /**\r\n     * 取款\r\n     * @param {string|number} characterId - 角色ID\r\n     * @param {string} currency - 货币类型 (silver/gold_ingot)\r\n     * @param {number} amount - 取款金额\r\n     * @returns {Promise<Object>} - 取款结果\r\n     */\r\n    withdraw(characterId, currency, amount) {\r\n        // 确保characterId是字符串\r\n        const characterIdStr = String(characterId);\r\n        // 确保金额是有效数字\r\n        const amountNum = parseInt(amount) || 0;\r\n        // 确保货币类型有效\r\n        if (currency !== 'silver' && currency !== 'gold_ingot') {\r\n            logger.error('[BankService] 无效的货币类型:', currency);\r\n            return Promise.reject(new Error(`无效的货币类型: ${currency}`));\r\n        }\r\n        \r\n        logger.debug('[BankService] 取款操作, characterId:', characterIdStr, 'currency:', currency, 'amount:', amountNum);\r\n        \r\n        // 验证characterId\r\n        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {\r\n            logger.error('[BankService] 无效的角色ID:', characterIdStr);\r\n            return Promise.reject(new Error('无效的角色ID'));\r\n        }\r\n        \r\n        // 验证金额\r\n        if (amountNum <= 0) {\r\n            logger.error('[BankService] 无效的取款金额:', amountNum);\r\n            return Promise.reject(new Error('取款金额必须大于0'));\r\n        }\r\n        \r\n        return post(`/bank/withdraw/${characterIdStr}`, {\r\n            currency: currency,\r\n            amount: amountNum\r\n        }, {\r\n            loading: true,\r\n            loadingText: '正在取款...'\r\n        }).then(res => {\r\n            logger.debug('[BankService] 取款成功:', JSON.stringify(res, null, 2));\r\n            \r\n            // 清除缓存，确保下次获取最新数据\r\n            setCache(CACHE_TYPE.API, CACHE_KEYS.ACCOUNT_INFO, null, 0);\r\n            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);\r\n            \r\n            return {\r\n                success: true,\r\n                message: res.data?.message || '取款成功',\r\n                account: res.data?.account || {},\r\n                character: res.data?.character || {}\r\n            };\r\n        }).catch(error => {\r\n            logger.error('[BankService] 取款失败:', error);\r\n            // 详细记录错误信息\r\n            if (error.response) {\r\n                logger.error('[BankService] 错误状态码:', error.response.status);\r\n                logger.error('[BankService] 错误响应:', JSON.stringify(error.response.data, null, 2));\r\n            }\r\n            \r\n            throw {\r\n                success: false,\r\n                message: error.response?.data?.error?.message || '取款失败，请重试',\r\n                details: error.message || ''\r\n            };\r\n        });\r\n    },\r\n\r\n    /**\r\n     * 获取交易历史记录\r\n     * @param {string|number} characterId - 角色ID\r\n     * @param {Object} params - 查询参数\r\n     * @param {number} [params.page=1] - 页码\r\n     * @param {number} [params.per_page=10] - 每页条数\r\n     * @param {string} [params.currency] - 货币类型筛选 (silver/gold_ingot)\r\n     * @param {string} [params.type] - 交易类型筛选 (deposit/withdraw)\r\n     * @returns {Promise<Object>} - 交易历史记录\r\n     */\r\n    getTransactionHistory(characterId, params = {}) {\r\n        // 确保characterId是字符串\r\n        const characterIdStr = String(characterId);\r\n        logger.debug('[BankService] 获取交易历史记录, characterId:', characterIdStr, 'params:', params);\r\n        \r\n        // 验证characterId\r\n        if (!characterIdStr || characterIdStr === 'undefined' || characterIdStr === 'null') {\r\n            logger.error('[BankService] 无效的角色ID:', characterIdStr);\r\n            return Promise.reject(new Error('无效的角色ID'));\r\n        }\r\n        \r\n        // 清除缓存，确保获取最新数据\r\n        setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, null, 0);\r\n        \r\n        const apiUrl = `/bank/transactions/${characterIdStr}`;\r\n        const fullUrl = API_BASE_URL + apiUrl;\r\n        logger.debug('[BankService] 请求交易历史URL:', fullUrl, '参数:', params);\r\n        \r\n        return get(apiUrl, params, {\r\n            loading: true,\r\n            loadingText: '获取交易记录...'\r\n        }).then(res => {\r\n            logger.debug('[BankService] 交易历史记录获取成功:', JSON.stringify(res, null, 2));\r\n            \r\n            // 格式化响应数据\r\n            let formattedData;\r\n            \r\n            if (res && typeof res === 'object') {\r\n                if (res.success === true && res.data && res.data.transactions) {\r\n                    // 标准格式: { success: true, data: { transactions: [...], pagination: {...} } }\r\n                    formattedData = res;\r\n                    logger.debug('[BankService] 处理标准格式交易记录, 数量:', res.data.transactions.length);\r\n                } else if (Array.isArray(res)) {\r\n                    // 响应直接是交易数组: [...]\r\n                    logger.debug('[BankService] 处理数组格式交易记录, 数量:', res.length);\r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            transactions: res.map(normalizeTransaction),\r\n                            pagination: {\r\n                                current_page: parseInt(params.page) || 1,\r\n                                per_page: parseInt(params.per_page) || 10,\r\n                                total: res.length,\r\n                                last_page: 1\r\n                            }\r\n                        }\r\n                    };\r\n                } else if (res.transactions || res.data?.transactions) {\r\n                    // 包含交易数据的对象: { transactions: [...], pagination: {...} } 或 { data: { transactions: [...] } }\r\n                    const transactions = res.transactions || res.data?.transactions || [];\r\n                    logger.debug('[BankService] 处理嵌套交易记录, 数量:', transactions.length);\r\n                    \r\n                    const pagination = res.pagination || res.data?.pagination || {\r\n                        current_page: parseInt(params.page) || 1,\r\n                        per_page: parseInt(params.per_page) || 10,\r\n                        total: transactions.length,\r\n                        last_page: 1\r\n                    };\r\n                    \r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            transactions: transactions.map(normalizeTransaction),\r\n                            pagination: pagination\r\n                        }\r\n                    };\r\n                } else if (res.data && Array.isArray(res.data)) {\r\n                    // 响应格式为 { data: [...] }\r\n                    logger.debug('[BankService] 处理data数组交易记录, 数量:', res.data.length);\r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            transactions: res.data.map(normalizeTransaction),\r\n                            pagination: {\r\n                                current_page: parseInt(params.page) || 1,\r\n                                per_page: parseInt(params.per_page) || 10,\r\n                                total: res.data.length,\r\n                                last_page: Math.ceil(res.data.length / (parseInt(params.per_page) || 10))\r\n                            }\r\n                        }\r\n                    };\r\n                } else {\r\n                    // 无法识别的格式，创建空数据\r\n                    logger.warn('[BankService] 未知交易历史响应格式:', JSON.stringify(res));\r\n                    formattedData = {\r\n                        success: true,\r\n                        data: {\r\n                            transactions: [],\r\n                            pagination: {\r\n                                current_page: 1,\r\n                                per_page: 10,\r\n                                total: 0,\r\n                                last_page: 1\r\n                            }\r\n                        }\r\n                    };\r\n                }\r\n                \r\n                // 在返回前确保交易记录的格式标准化\r\n                if (formattedData.data && formattedData.data.transactions) {\r\n                    formattedData.data.transactions = formattedData.data.transactions.map(normalizeTransaction);\r\n                    logger.debug('[BankService] 标准化后的交易记录样例:', \r\n                        formattedData.data.transactions.length > 0 ? \r\n                        JSON.stringify(formattedData.data.transactions[0]) : '无记录');\r\n                }\r\n            } else {\r\n                // 无效响应\r\n                logger.error('[BankService] 无效交易历史响应格式:', res);\r\n                formattedData = {\r\n                    success: false,\r\n                    data: {\r\n                        transactions: [],\r\n                        pagination: {\r\n                            current_page: 1,\r\n                            per_page: 10,\r\n                            total: 0,\r\n                            last_page: 1\r\n                        }\r\n                    }\r\n                };\r\n            }\r\n            \r\n            // 缓存结果\r\n            setCache(CACHE_TYPE.API, CACHE_KEYS.TRANSACTIONS, formattedData.data, 60 * 1000); // 缓存1分钟\r\n            return formattedData;\r\n        }).catch(error => {\r\n            logger.error('[BankService] 获取交易历史记录失败:', error);\r\n            logger.error('[BankService] 请求URL:', fullUrl, '参数:', params);\r\n            \r\n            if (error.response) {\r\n                logger.error('[BankService] 响应状态:', error.response.status);\r\n                logger.error('[BankService] 响应数据:', JSON.stringify(error.response.data, null, 2));\r\n            }\r\n            \r\n            // 返回错误信息和默认空数据\r\n            throw {\r\n                success: false,\r\n                message: error.message || '获取交易历史记录失败',\r\n                data: {\r\n                    transactions: [],\r\n                    pagination: {\r\n                        current_page: 1,\r\n                        per_page: 10,\r\n                        total: 0,\r\n                        last_page: 1\r\n                    }\r\n                },\r\n                original_error: error\r\n            };\r\n        });\r\n    }\r\n};\r\n\r\n/**\r\n * 标准化交易记录格式\r\n * 确保所有必须字段都存在，类型正确\r\n * @param {Object} transaction - 交易记录对象\r\n * @returns {Object} - 标准化后的交易记录对象\r\n */\r\nfunction normalizeTransaction(transaction) {\r\n    if (!transaction) return {\r\n        id: 'unknown-' + Math.random().toString(36).substr(2, 9),\r\n        character_id: 0,\r\n        type: 'deposit',\r\n        currency: 'silver',\r\n        amount: 0,\r\n        balance: 0,\r\n        description: '未知交易',\r\n        created_at: new Date().toISOString()\r\n    };\r\n    \r\n    // 返回标准化的交易记录\r\n    return {\r\n        id: transaction.id || 'tx-' + Math.random().toString(36).substr(2, 9),\r\n        character_id: transaction.character_id || 0,\r\n        type: ['deposit', 'withdraw'].includes(transaction.type) ? transaction.type : 'deposit',\r\n        currency: ['silver', 'gold_ingot'].includes(transaction.currency) ? transaction.currency : 'silver',\r\n        amount: parseInt(transaction.amount) || 0,\r\n        balance: parseInt(transaction.balance) || 0,\r\n        description: transaction.description || generateTransactionDescription(transaction),\r\n        created_at: transaction.created_at || new Date().toISOString()\r\n    };\r\n}\r\n\r\n/**\r\n * 为交易记录生成描述\r\n * @param {Object} transaction - 交易记录对象\r\n * @returns {string} - 生成的描述\r\n */\r\nfunction generateTransactionDescription(transaction) {\r\n    const type = transaction.type === 'withdraw' ? '取出' : '存入';\r\n    const amount = parseInt(transaction.amount) || 0;\r\n    const currency = transaction.currency === 'gold_ingot' ? '金砖' : '银两';\r\n    return `${type} ${amount} ${currency}`;\r\n}\r\n\r\nexport default bankService; "], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA,SAASA,GAAG,EAAEC,IAAI,QAAQ,eAAe;AACzC,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,SAASC,QAAQ,EAAEC,UAAU,QAAQ,mBAAmB;AACxD,SAASC,YAAY,QAAQ,cAAc;;AAE3C;AACA,MAAMC,UAAU,GAAG;EACfC,YAAY,EAAE,mBAAmB;EACjCC,YAAY,EAAE;AAClB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAChB;AACJ;AACA;AACA;AACA;EACIC,cAAcA,CAACC,WAAW,EAAE;IACxB;IACA,MAAMC,cAAc,GAAGC,MAAM,CAACF,WAAW,CAAC;IAC1CT,MAAM,CAACY,KAAK,CAAC,sCAAsC,EAAEF,cAAc,CAAC;;IAEpE;IACA,IAAI,CAACA,cAAc,IAAIA,cAAc,KAAK,WAAW,IAAIA,cAAc,KAAK,MAAM,EAAE;MAChFV,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAEH,cAAc,CAAC;MACtD,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC/C;;IAEA;IACAf,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IAE1D,MAAMa,MAAM,GAAG,iBAAiBR,cAAc,EAAE;IAChD,MAAMS,OAAO,GAAGhB,YAAY,GAAGe,MAAM;IACrClB,MAAM,CAACY,KAAK,CAAC,uBAAuB,EAAEO,OAAO,CAAC;;IAE9C;IACAnB,MAAM,CAACY,KAAK,CAAC,wBAAwB,EAAEO,OAAO,CAAC;IAE/C,OAAOrB,GAAG,CAACoB,MAAM,EAAE,CAAC,CAAC,EAAE;MACnBE,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACX;MACAvB,MAAM,CAACY,KAAK,CAAC,2BAA2B,EAAEY,IAAI,CAACC,SAAS,CAACF,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACvEvB,MAAM,CAACY,KAAK,CAAC,wBAAwB,EAAEO,OAAO,CAAC;MAC/CnB,MAAM,CAACY,KAAK,CAAC,qBAAqB,EAAEY,IAAI,CAACC,SAAS,CAACF,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIG,aAAa;;MAEjB;MACA,IAAIH,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAChC,IAAIA,GAAG,CAACI,OAAO,KAAK,IAAI,IAAIJ,GAAG,CAACK,IAAI,EAAE;UAClC;UACAF,aAAa,GAAGH,GAAG;QACvB,CAAC,MAAM,IAAIA,GAAG,CAACM,OAAO,EAAE;UACpB;UACAH,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACFC,OAAO,EAAEN,GAAG,CAACM,OAAO;cACpBC,SAAS,EAAEP,GAAG,CAACO,SAAS,IAAI,CAAC;YACjC;UACJ,CAAC;QACL,CAAC,MAAM,IAAI,QAAQ,IAAIP,GAAG,IAAI,YAAY,IAAIA,GAAG,EAAE;UAC/C;UACAG,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACFC,OAAO,EAAE;gBACLE,MAAM,EAAER,GAAG,CAACQ,MAAM,IAAI,CAAC;gBACvBC,UAAU,EAAET,GAAG,CAACS,UAAU,IAAI;cAClC;YACJ;UACJ,CAAC;QACL,CAAC,MAAM;UACH;UACAhC,MAAM,CAACiC,IAAI,CAAC,8BAA8B,EAAET,IAAI,CAACC,SAAS,CAACF,GAAG,CAAC,CAAC;UAChEG,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACFC,OAAO,EAAE;gBACLE,MAAM,EAAE,CAAC;gBACTC,UAAU,EAAE;cAChB;YACJ;UACJ,CAAC;QACL;MACJ,CAAC,MAAM;QACH;QACAhC,MAAM,CAACa,KAAK,CAAC,uBAAuB,EAAEU,GAAG,CAAC;QAC1CG,aAAa,GAAG;UACZC,OAAO,EAAE,KAAK;UACdC,IAAI,EAAE;YACFC,OAAO,EAAE;cACLE,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE;YAChB;UACJ,CAAC;UACDnB,KAAK,EAAE;QACX,CAAC;MACL;;MAEA;MACAZ,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACC,YAAY,EAAEqB,aAAa,CAACE,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;MAClF,OAAOF,aAAa;IACxB,CAAC,CAAC,CAACQ,KAAK,CAACrB,KAAK,IAAI;MACd;MACAb,MAAM,CAACa,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAChDb,MAAM,CAACa,KAAK,CAAC,sBAAsB,EAAEM,OAAO,CAAC;MAC7CnB,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAEM,OAAO,CAAC;MAC/CnB,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEW,IAAI,CAACC,SAAS,CAACZ,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEnE,IAAIA,KAAK,CAACsB,QAAQ,EAAE;QAChBnC,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAACsB,QAAQ,CAACC,MAAM,CAAC;QAC1DpC,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEW,IAAI,CAACC,SAAS,CAACZ,KAAK,CAACsB,QAAQ,CAACP,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACrF;;MAEA;MACA,IAAIf,KAAK,CAACe,IAAI,IAAIf,KAAK,CAACe,IAAI,CAACf,KAAK,EAAE;QAChCb,MAAM,CAACa,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACe,IAAI,CAACf,KAAK,CAAC;MAC3D;;MAEA;MACA,MAAM;QACFc,OAAO,EAAE,KAAK;QACdU,OAAO,EAAExB,KAAK,CAACwB,OAAO,IAAI,YAAY;QACtCT,IAAI,EAAE;UACFC,OAAO,EAAE;YACLE,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE;UAChB;QACJ,CAAC;QACDM,cAAc,EAAEzB;MACpB,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0B,OAAOA,CAAC9B,WAAW,EAAE+B,QAAQ,EAAEC,MAAM,EAAE;IACnC;IACA,MAAM/B,cAAc,GAAGC,MAAM,CAACF,WAAW,CAAC;IAC1C;IACA,MAAMiC,SAAS,GAAGC,QAAQ,CAACF,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAID,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,YAAY,EAAE;MACpDxC,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAE2B,QAAQ,CAAC;MAChD,OAAO1B,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAYwB,QAAQ,EAAE,CAAC,CAAC;IAC5D;IAEAxC,MAAM,CAACY,KAAK,CAAC,kCAAkC,EAAEF,cAAc,EAAE,WAAW,EAAE8B,QAAQ,EAAE,SAAS,EAAEE,SAAS,CAAC;;IAE7G;IACA,IAAI,CAAChC,cAAc,IAAIA,cAAc,KAAK,WAAW,IAAIA,cAAc,KAAK,MAAM,EAAE;MAChFV,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAEH,cAAc,CAAC;MACtD,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC/C;;IAEA;IACA,IAAI0B,SAAS,IAAI,CAAC,EAAE;MAChB1C,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAE6B,SAAS,CAAC;MACjD,OAAO5B,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC;IACjD;IAEA,OAAOjB,IAAI,CAAC,iBAAiBW,cAAc,EAAE,EAAE;MAC3C8B,QAAQ,EAAEA,QAAQ;MAClBC,MAAM,EAAEC;IACZ,CAAC,EAAE;MACCtB,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MAAA,IAAAqB,SAAA,EAAAC,UAAA,EAAAC,UAAA;MACX9C,MAAM,CAACY,KAAK,CAAC,qBAAqB,EAAEY,IAAI,CAACC,SAAS,CAACF,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEjE;MACAtB,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;MAC1DJ,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;MAE1D,OAAO;QACHqB,OAAO,EAAE,IAAI;QACbU,OAAO,EAAE,EAAAO,SAAA,GAAArB,GAAG,CAACK,IAAI,cAAAgB,SAAA,uBAARA,SAAA,CAAUP,OAAO,KAAI,MAAM;QACpCR,OAAO,EAAE,EAAAgB,UAAA,GAAAtB,GAAG,CAACK,IAAI,cAAAiB,UAAA,uBAARA,UAAA,CAAUhB,OAAO,KAAI,CAAC,CAAC;QAChCC,SAAS,EAAE,EAAAgB,UAAA,GAAAvB,GAAG,CAACK,IAAI,cAAAkB,UAAA,uBAARA,UAAA,CAAUhB,SAAS,KAAI,CAAC;MACvC,CAAC;IACL,CAAC,CAAC,CAACI,KAAK,CAACrB,KAAK,IAAI;MAAA,IAAAkC,eAAA;MACd/C,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC1C;MACA,IAAIA,KAAK,CAACsB,QAAQ,EAAE;QAChBnC,MAAM,CAACa,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACsB,QAAQ,CAACC,MAAM,CAAC;QAC3DpC,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEW,IAAI,CAACC,SAAS,CAACZ,KAAK,CAACsB,QAAQ,CAACP,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACrF;MAEA,MAAM;QACFD,OAAO,EAAE,KAAK;QACdU,OAAO,EAAE,EAAAU,eAAA,GAAAlC,KAAK,CAACsB,QAAQ,cAAAY,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgBnB,IAAI,cAAAmB,eAAA,gBAAAA,eAAA,GAApBA,eAAA,CAAsBlC,KAAK,cAAAkC,eAAA,uBAA3BA,eAAA,CAA6BV,OAAO,KAAI,UAAU;QAC3DW,OAAO,EAAEnC,KAAK,CAACwB,OAAO,IAAI;MAC9B,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIY,QAAQA,CAACxC,WAAW,EAAE+B,QAAQ,EAAEC,MAAM,EAAE;IACpC;IACA,MAAM/B,cAAc,GAAGC,MAAM,CAACF,WAAW,CAAC;IAC1C;IACA,MAAMiC,SAAS,GAAGC,QAAQ,CAACF,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAID,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,YAAY,EAAE;MACpDxC,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAE2B,QAAQ,CAAC;MAChD,OAAO1B,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAYwB,QAAQ,EAAE,CAAC,CAAC;IAC5D;IAEAxC,MAAM,CAACY,KAAK,CAAC,kCAAkC,EAAEF,cAAc,EAAE,WAAW,EAAE8B,QAAQ,EAAE,SAAS,EAAEE,SAAS,CAAC;;IAE7G;IACA,IAAI,CAAChC,cAAc,IAAIA,cAAc,KAAK,WAAW,IAAIA,cAAc,KAAK,MAAM,EAAE;MAChFV,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAEH,cAAc,CAAC;MACtD,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC/C;;IAEA;IACA,IAAI0B,SAAS,IAAI,CAAC,EAAE;MAChB1C,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAE6B,SAAS,CAAC;MACjD,OAAO5B,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC;IACjD;IAEA,OAAOjB,IAAI,CAAC,kBAAkBW,cAAc,EAAE,EAAE;MAC5C8B,QAAQ,EAAEA,QAAQ;MAClBC,MAAM,EAAEC;IACZ,CAAC,EAAE;MACCtB,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MAAA,IAAA2B,UAAA,EAAAC,UAAA,EAAAC,UAAA;MACXpD,MAAM,CAACY,KAAK,CAAC,qBAAqB,EAAEY,IAAI,CAACC,SAAS,CAACF,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEjE;MACAtB,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;MAC1DJ,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;MAE1D,OAAO;QACHqB,OAAO,EAAE,IAAI;QACbU,OAAO,EAAE,EAAAa,UAAA,GAAA3B,GAAG,CAACK,IAAI,cAAAsB,UAAA,uBAARA,UAAA,CAAUb,OAAO,KAAI,MAAM;QACpCR,OAAO,EAAE,EAAAsB,UAAA,GAAA5B,GAAG,CAACK,IAAI,cAAAuB,UAAA,uBAARA,UAAA,CAAUtB,OAAO,KAAI,CAAC,CAAC;QAChCC,SAAS,EAAE,EAAAsB,UAAA,GAAA7B,GAAG,CAACK,IAAI,cAAAwB,UAAA,uBAARA,UAAA,CAAUtB,SAAS,KAAI,CAAC;MACvC,CAAC;IACL,CAAC,CAAC,CAACI,KAAK,CAACrB,KAAK,IAAI;MAAA,IAAAwC,gBAAA;MACdrD,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC1C;MACA,IAAIA,KAAK,CAACsB,QAAQ,EAAE;QAChBnC,MAAM,CAACa,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACsB,QAAQ,CAACC,MAAM,CAAC;QAC3DpC,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEW,IAAI,CAACC,SAAS,CAACZ,KAAK,CAACsB,QAAQ,CAACP,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACrF;MAEA,MAAM;QACFD,OAAO,EAAE,KAAK;QACdU,OAAO,EAAE,EAAAgB,gBAAA,GAAAxC,KAAK,CAACsB,QAAQ,cAAAkB,gBAAA,gBAAAA,gBAAA,GAAdA,gBAAA,CAAgBzB,IAAI,cAAAyB,gBAAA,gBAAAA,gBAAA,GAApBA,gBAAA,CAAsBxC,KAAK,cAAAwC,gBAAA,uBAA3BA,gBAAA,CAA6BhB,OAAO,KAAI,UAAU;QAC3DW,OAAO,EAAEnC,KAAK,CAACwB,OAAO,IAAI;MAC9B,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiB,qBAAqBA,CAAC7C,WAAW,EAAE8C,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5C;IACA,MAAM7C,cAAc,GAAGC,MAAM,CAACF,WAAW,CAAC;IAC1CT,MAAM,CAACY,KAAK,CAAC,sCAAsC,EAAEF,cAAc,EAAE,SAAS,EAAE6C,MAAM,CAAC;;IAEvF;IACA,IAAI,CAAC7C,cAAc,IAAIA,cAAc,KAAK,WAAW,IAAIA,cAAc,KAAK,MAAM,EAAE;MAChFV,MAAM,CAACa,KAAK,CAAC,wBAAwB,EAAEH,cAAc,CAAC;MACtD,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC/C;;IAEA;IACAf,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IAE1D,MAAMY,MAAM,GAAG,sBAAsBR,cAAc,EAAE;IACrD,MAAMS,OAAO,GAAGhB,YAAY,GAAGe,MAAM;IACrClB,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEO,OAAO,EAAE,KAAK,EAAEoC,MAAM,CAAC;IAEhE,OAAOzD,GAAG,CAACoB,MAAM,EAAEqC,MAAM,EAAE;MACvBnC,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXvB,MAAM,CAACY,KAAK,CAAC,2BAA2B,EAAEY,IAAI,CAACC,SAAS,CAACF,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEvE;MACA,IAAIG,aAAa;MAEjB,IAAIH,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAAA,IAAAiC,UAAA;QAChC,IAAIjC,GAAG,CAACI,OAAO,KAAK,IAAI,IAAIJ,GAAG,CAACK,IAAI,IAAIL,GAAG,CAACK,IAAI,CAAC6B,YAAY,EAAE;UAC3D;UACA/B,aAAa,GAAGH,GAAG;UACnBvB,MAAM,CAACY,KAAK,CAAC,+BAA+B,EAAEW,GAAG,CAACK,IAAI,CAAC6B,YAAY,CAACC,MAAM,CAAC;QAC/E,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACrC,GAAG,CAAC,EAAE;UAC3B;UACAvB,MAAM,CAACY,KAAK,CAAC,+BAA+B,EAAEW,GAAG,CAACmC,MAAM,CAAC;UACzDhC,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACF6B,YAAY,EAAElC,GAAG,CAACsC,GAAG,CAACC,oBAAoB,CAAC;cAC3CC,UAAU,EAAE;gBACRC,YAAY,EAAErB,QAAQ,CAACY,MAAM,CAACU,IAAI,CAAC,IAAI,CAAC;gBACxCC,QAAQ,EAAEvB,QAAQ,CAACY,MAAM,CAACW,QAAQ,CAAC,IAAI,EAAE;gBACzCC,KAAK,EAAE5C,GAAG,CAACmC,MAAM;gBACjBU,SAAS,EAAE;cACf;YACJ;UACJ,CAAC;QACL,CAAC,MAAM,IAAI7C,GAAG,CAACkC,YAAY,KAAAD,UAAA,GAAIjC,GAAG,CAACK,IAAI,cAAA4B,UAAA,eAARA,UAAA,CAAUC,YAAY,EAAE;UAAA,IAAAY,UAAA,EAAAC,UAAA;UACnD;UACA,MAAMb,YAAY,GAAGlC,GAAG,CAACkC,YAAY,MAAAY,UAAA,GAAI9C,GAAG,CAACK,IAAI,cAAAyC,UAAA,uBAARA,UAAA,CAAUZ,YAAY,KAAI,EAAE;UACrEzD,MAAM,CAACY,KAAK,CAAC,6BAA6B,EAAE6C,YAAY,CAACC,MAAM,CAAC;UAEhE,MAAMK,UAAU,GAAGxC,GAAG,CAACwC,UAAU,MAAAO,UAAA,GAAI/C,GAAG,CAACK,IAAI,cAAA0C,UAAA,uBAARA,UAAA,CAAUP,UAAU,KAAI;YACzDC,YAAY,EAAErB,QAAQ,CAACY,MAAM,CAACU,IAAI,CAAC,IAAI,CAAC;YACxCC,QAAQ,EAAEvB,QAAQ,CAACY,MAAM,CAACW,QAAQ,CAAC,IAAI,EAAE;YACzCC,KAAK,EAAEV,YAAY,CAACC,MAAM;YAC1BU,SAAS,EAAE;UACf,CAAC;UAED1C,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACF6B,YAAY,EAAEA,YAAY,CAACI,GAAG,CAACC,oBAAoB,CAAC;cACpDC,UAAU,EAAEA;YAChB;UACJ,CAAC;QACL,CAAC,MAAM,IAAIxC,GAAG,CAACK,IAAI,IAAI+B,KAAK,CAACC,OAAO,CAACrC,GAAG,CAACK,IAAI,CAAC,EAAE;UAC5C;UACA5B,MAAM,CAACY,KAAK,CAAC,iCAAiC,EAAEW,GAAG,CAACK,IAAI,CAAC8B,MAAM,CAAC;UAChEhC,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACF6B,YAAY,EAAElC,GAAG,CAACK,IAAI,CAACiC,GAAG,CAACC,oBAAoB,CAAC;cAChDC,UAAU,EAAE;gBACRC,YAAY,EAAErB,QAAQ,CAACY,MAAM,CAACU,IAAI,CAAC,IAAI,CAAC;gBACxCC,QAAQ,EAAEvB,QAAQ,CAACY,MAAM,CAACW,QAAQ,CAAC,IAAI,EAAE;gBACzCC,KAAK,EAAE5C,GAAG,CAACK,IAAI,CAAC8B,MAAM;gBACtBU,SAAS,EAAEG,IAAI,CAACC,IAAI,CAACjD,GAAG,CAACK,IAAI,CAAC8B,MAAM,IAAIf,QAAQ,CAACY,MAAM,CAACW,QAAQ,CAAC,IAAI,EAAE,CAAC;cAC5E;YACJ;UACJ,CAAC;QACL,CAAC,MAAM;UACH;UACAlE,MAAM,CAACiC,IAAI,CAAC,2BAA2B,EAAET,IAAI,CAACC,SAAS,CAACF,GAAG,CAAC,CAAC;UAC7DG,aAAa,GAAG;YACZC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACF6B,YAAY,EAAE,EAAE;cAChBM,UAAU,EAAE;gBACRC,YAAY,EAAE,CAAC;gBACfE,QAAQ,EAAE,EAAE;gBACZC,KAAK,EAAE,CAAC;gBACRC,SAAS,EAAE;cACf;YACJ;UACJ,CAAC;QACL;;QAEA;QACA,IAAI1C,aAAa,CAACE,IAAI,IAAIF,aAAa,CAACE,IAAI,CAAC6B,YAAY,EAAE;UACvD/B,aAAa,CAACE,IAAI,CAAC6B,YAAY,GAAG/B,aAAa,CAACE,IAAI,CAAC6B,YAAY,CAACI,GAAG,CAACC,oBAAoB,CAAC;UAC3F9D,MAAM,CAACY,KAAK,CAAC,4BAA4B,EACrCc,aAAa,CAACE,IAAI,CAAC6B,YAAY,CAACC,MAAM,GAAG,CAAC,GAC1ClC,IAAI,CAACC,SAAS,CAACC,aAAa,CAACE,IAAI,CAAC6B,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACnE;MACJ,CAAC,MAAM;QACH;QACAzD,MAAM,CAACa,KAAK,CAAC,2BAA2B,EAAEU,GAAG,CAAC;QAC9CG,aAAa,GAAG;UACZC,OAAO,EAAE,KAAK;UACdC,IAAI,EAAE;YACF6B,YAAY,EAAE,EAAE;YAChBM,UAAU,EAAE;cACRC,YAAY,EAAE,CAAC;cACfE,QAAQ,EAAE,EAAE;cACZC,KAAK,EAAE,CAAC;cACRC,SAAS,EAAE;YACf;UACJ;QACJ,CAAC;MACL;;MAEA;MACAnE,QAAQ,CAACC,UAAU,CAACe,GAAG,EAAEb,UAAU,CAACE,YAAY,EAAEoB,aAAa,CAACE,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;MAClF,OAAOF,aAAa;IACxB,CAAC,CAAC,CAACQ,KAAK,CAACrB,KAAK,IAAI;MACdb,MAAM,CAACa,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAChDb,MAAM,CAACa,KAAK,CAAC,sBAAsB,EAAEM,OAAO,EAAE,KAAK,EAAEoC,MAAM,CAAC;MAE5D,IAAI1C,KAAK,CAACsB,QAAQ,EAAE;QAChBnC,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAACsB,QAAQ,CAACC,MAAM,CAAC;QAC1DpC,MAAM,CAACa,KAAK,CAAC,qBAAqB,EAAEW,IAAI,CAACC,SAAS,CAACZ,KAAK,CAACsB,QAAQ,CAACP,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACrF;;MAEA;MACA,MAAM;QACFD,OAAO,EAAE,KAAK;QACdU,OAAO,EAAExB,KAAK,CAACwB,OAAO,IAAI,YAAY;QACtCT,IAAI,EAAE;UACF6B,YAAY,EAAE,EAAE;UAChBM,UAAU,EAAE;YACRC,YAAY,EAAE,CAAC;YACfE,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE;UACf;QACJ,CAAC;QACD9B,cAAc,EAAEzB;MACpB,CAAC;IACL,CAAC,CAAC;EACN;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,oBAAoBA,CAACW,WAAW,EAAE;EACvC,IAAI,CAACA,WAAW,EAAE,OAAO;IACrBC,EAAE,EAAE,UAAU,GAAGH,IAAI,CAACI,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACxDC,YAAY,EAAE,CAAC;IACfC,IAAI,EAAE,SAAS;IACfvC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,CAAC;IACTuC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACvC,CAAC;;EAED;EACA,OAAO;IACHV,EAAE,EAAED,WAAW,CAACC,EAAE,IAAI,KAAK,GAAGH,IAAI,CAACI,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACrEC,YAAY,EAAEL,WAAW,CAACK,YAAY,IAAI,CAAC;IAC3CC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAACM,QAAQ,CAACZ,WAAW,CAACM,IAAI,CAAC,GAAGN,WAAW,CAACM,IAAI,GAAG,SAAS;IACvFvC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC6C,QAAQ,CAACZ,WAAW,CAACjC,QAAQ,CAAC,GAAGiC,WAAW,CAACjC,QAAQ,GAAG,QAAQ;IACnGC,MAAM,EAAEE,QAAQ,CAAC8B,WAAW,CAAChC,MAAM,CAAC,IAAI,CAAC;IACzCuC,OAAO,EAAErC,QAAQ,CAAC8B,WAAW,CAACO,OAAO,CAAC,IAAI,CAAC;IAC3CC,WAAW,EAAER,WAAW,CAACQ,WAAW,IAAIK,8BAA8B,CAACb,WAAW,CAAC;IACnFS,UAAU,EAAET,WAAW,CAACS,UAAU,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACjE,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,8BAA8BA,CAACb,WAAW,EAAE;EACjD,MAAMM,IAAI,GAAGN,WAAW,CAACM,IAAI,KAAK,UAAU,GAAG,IAAI,GAAG,IAAI;EAC1D,MAAMtC,MAAM,GAAGE,QAAQ,CAAC8B,WAAW,CAAChC,MAAM,CAAC,IAAI,CAAC;EAChD,MAAMD,QAAQ,GAAGiC,WAAW,CAACjC,QAAQ,KAAK,YAAY,GAAG,IAAI,GAAG,IAAI;EACpE,OAAO,GAAGuC,IAAI,IAAItC,MAAM,IAAID,QAAQ,EAAE;AAC1C;AAEA,eAAejC,WAAW", "ignoreList": []}]}