{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Main.vue", "mtime": 1750348125826}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GameLayout", "GameChat", "logger", "getCharacterDetail", "getCurrentCharacter", "getCharacterStatus", "showMessage", "name", "components", "data", "currentFunction", "characterInfo", "avatar", "profession", "silver", "gold", "expNeeded", "hp", "maxHp", "mp", "maxMp", "level", "exp", "expRequired", "attributePoints", "constitution", "intelligence", "strength", "agility", "attack", "defense", "speed", "npcList", "type", "description", "services", "mainFunctions", "action", "image", "onlinePlayers", "loadingLocations", "showEntityModal", "selected<PERSON><PERSON><PERSON>", "selectedEntityType", "computed", "hpPercent", "mpPercent", "expPercent", "availableLocations", "$store", "state", "map", "isMoving", "loading", "moving", "canMove", "getters", "currentLocationNpcs", "currentLocation", "npcs", "error", "currentLocationMonsters", "monsters", "allEntities", "entities", "Array", "isArray", "for<PERSON>ach", "npc", "push", "monster", "methods", "openCharacterStatus", "info", "dispatch", "then", "character", "debug", "loadCharacterInfo", "$router", "catch", "selectNpc", "handleNpcInteraction", "handleMonsterEncounter", "showToast", "setTimeout", "encounterMessage", "currentCharacter", "id", "path", "query", "characterId", "monsterId", "locationId", "getEntityForRow", "rowIndex", "handleEntityAction", "entity", "getLocationForRow", "locations", "handleFunction", "getCurrentPanelTitle", "selectFacility", "facility", "moveToLocationDirectly", "location", "loadLocationEntities", "message", "_currentLocation$npcs", "_currentLocation$mons", "length", "$forceUpdate", "moveToLocation", "locationObj", "openFunction", "func", "onChatMessageSent", "messageData", "onChatChannelSwitched", "channelData", "showEntityInfo", "closeEntityModal", "getNpcTypeText", "typeMap", "getMonsterTypeText", "getFactionText", "faction", "factionMap", "getElementText", "element", "elementMap", "getSizeText", "size", "sizeMap", "getDefaultAvatar", "current", "detailResponse", "detail", "professionMap", "gender", "detailError", "statusResponse", "status", "parseInt", "max_hp", "max_mp", "exp_required", "attribute_points", "statusError", "Promise", "resolve", "mounted", "isAuthenticated", "auth", "hasCharacter", "warn"], "sources": ["src/views/game/Main.vue"], "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"game-container\">\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <!-- 上半部分：左右分栏 -->\n        <div class=\"top-section\">\n          <!-- 左侧：个人信息 -->\n          <div class=\"left-panel\">\n            <div class=\"pixel-avatar-bar2\" @click=\"openCharacterStatus\">\n              <!-- <img class=\"pixel-bg-img2\" src=\"/static/game/UI/bj/tbu.png\" alt=\"头像背景\" /> -->\n              <img class=\"pixel-avatar-img2\" :src=\"characterInfo.avatar\" :alt=\"characterInfo.name\" />\n              <div class=\"pixel-name2\">{{ characterInfo.name }}</div>\n              <div class=\"pixel-bars2\">\n                <div class=\"pixel-bar2 pixel-hp2\">\n                  <div class=\"pixel-bar-inner2 pixel-hp-inner2\" :style=\"{width: hpPercent + '%'}\"></div>\n                </div>\n                <div class=\"pixel-bar2 pixel-mp2\">\n                  <div class=\"pixel-bar-inner2 pixel-mp-inner2\" :style=\"{width: mpPercent + '%'}\"></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"pixel-info-box\">\n              <div class=\"pixel-row\">职业: {{ characterInfo.profession }}</div>\n              <div class=\"pixel-row\">等级: {{ characterInfo.level }}</div>\n              <div class=\"pixel-row\">银两: {{ characterInfo.silver }}</div>\n              <div class=\"pixel-row\"><span class=\"pixel-label-gold\">金砖:</span><span class=\"pixel-value-gold\">{{ characterInfo.gold }}</span></div>\n              <div class=\"pixel-row pixel-exp-label\">经验: {{ characterInfo.exp }}/{{ characterInfo.expRequired }}</div>\n              <div class=\"pixel-exp-bar\">\n                <div class=\"pixel-exp-inner\" :style=\"{width: expPercent + '%'}\"></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 右侧：动态内容区域 -->\n          <div class=\"right-panel\">\n            <div class=\"panel-header\">{{ getCurrentPanelTitle() }}</div>\n            <div class=\"panel-content\">\n              <!-- 人物功能内容：显示当前地图的NPC和怪物 -->\n              <div v-if=\"!currentFunction || currentFunction === 'character'\" class=\"npc-content\">\n                <div class=\"pixel-border-box\">\n                  <!-- 四行布局容器 -->\n                  <div class=\"four-row-container\">\n                    <!-- 第一行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(0)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(0), getEntityForRow(0).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(0).type === 'monster' }\">\n                            {{ getEntityForRow(0).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(0).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(0))\"\n                        >\n                          {{ getEntityForRow(0).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第二行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(1)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(1), getEntityForRow(1).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(1).type === 'monster' }\">\n                            {{ getEntityForRow(1).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(1).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(1))\"\n                        >\n                          {{ getEntityForRow(1).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第三行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(2)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(2), getEntityForRow(2).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(2).type === 'monster' }\">\n                            {{ getEntityForRow(2).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(2).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(2))\"\n                        >\n                          {{ getEntityForRow(2).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第四行 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getEntityForRow(3)\" class=\"entity-row-single\">\n                        <div class=\"entity-info\" @click=\"showEntityInfo(getEntityForRow(3), getEntityForRow(3).type)\">\n                          <span class=\"entity-name clickable\" :class=\"{ 'monster-name': getEntityForRow(3).type === 'monster' }\">\n                            {{ getEntityForRow(3).name }}\n                          </span>\n                        </div>\n                        <button\n                          class=\"action-btn-compact\"\n                          :class=\"getEntityForRow(3).type === 'npc' ? 'npc-action-btn' : 'battle-btn'\"\n                          @click=\"handleEntityAction(getEntityForRow(3))\"\n                        >\n                          {{ getEntityForRow(3).type === 'npc' ? '对话' : '战斗' }}\n                        </button>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">空位</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 实体信息弹窗 -->\n              <div v-if=\"showEntityModal\" class=\"entity-modal-overlay\" @click=\"closeEntityModal\">\n                <div class=\"entity-modal\" @click.stop>\n                  <div class=\"modal-header\">\n                    <h3>{{ selectedEntity.name }}</h3>\n                    <button class=\"close-btn\" @click=\"closeEntityModal\">×</button>\n                  </div>\n                  <div class=\"modal-content\">\n                    <div v-if=\"selectedEntityType === 'npc'\" class=\"npc-info\">\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">称号：</span>\n                        <span class=\"info-value\">{{ selectedEntity.title || '无' }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">等级：</span>\n                        <span class=\"info-value\">{{ selectedEntity.level }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">类型：</span>\n                        <span class=\"info-value\">{{ getNpcTypeText(selectedEntity.type) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">阵营：</span>\n                        <span class=\"info-value\">{{ getFactionText(selectedEntity.faction) }}</span>\n                      </div>\n                      <div v-if=\"selectedEntity.services && selectedEntity.services.length > 0\" class=\"info-row\">\n                        <span class=\"info-label\">服务：</span>\n                        <span class=\"info-value\">{{ selectedEntity.services.join(', ') }}</span>\n                      </div>\n                      <div class=\"info-row description\">\n                        <span class=\"info-label\">描述：</span>\n                        <span class=\"info-value\">{{ selectedEntity.description || '暂无描述' }}</span>\n                      </div>\n                    </div>\n                    <div v-else-if=\"selectedEntityType === 'monster'\" class=\"monster-info\">\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">称号：</span>\n                        <span class=\"info-value\">{{ selectedEntity.title || '无' }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">等级：</span>\n                        <span class=\"info-value\">{{ selectedEntity.level }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">类型：</span>\n                        <span class=\"info-value\">{{ getMonsterTypeText(selectedEntity.type) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">元素：</span>\n                        <span class=\"info-value\">{{ getElementText(selectedEntity.element) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">体型：</span>\n                        <span class=\"info-value\">{{ getSizeText(selectedEntity.size) }}</span>\n                      </div>\n                      <div class=\"info-row\">\n                        <span class=\"info-label\">威胁等级：</span>\n                        <span class=\"info-value threat-level\">{{ selectedEntity.threat_level || 1 }}</span>\n                      </div>\n                      <div v-if=\"selectedEntity.stats\" class=\"stats-section\">\n                        <div class=\"stats-title\">属性</div>\n                        <div class=\"stats-grid\">\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">生命：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.health }}/{{ selectedEntity.stats.max_health }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">攻击：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.attack }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">防御：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.defense }}</span>\n                          </div>\n                          <div class=\"stat-item\">\n                            <span class=\"stat-label\">速度：</span>\n                            <span class=\"stat-value\">{{ selectedEntity.stats.speed }}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div class=\"info-row description\">\n                        <span class=\"info-label\">描述：</span>\n                        <span class=\"info-value\">{{ selectedEntity.description || '暂无描述' }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 设施功能内容 -->\n              <div v-else-if=\"currentFunction === 'equipment'\" class=\"facilities-content\">\n                <div class=\"facility-list six-grid\">\n                  <div class=\"facility-item\" @click=\"selectFacility('clinic')\">\n                    <span class=\"facility-name\">医馆</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('bank')\">\n                    <span class=\"facility-name\">钱庄</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('posthouse')\">\n                    <span class=\"facility-name\">馆驿</span>\n                    </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('market')\">\n                    <span class=\"facility-name\">市场</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('square')\">\n                    <span class=\"facility-name\">广场</span>\n                      </div>\n                  <div class=\"facility-item\" @click=\"selectFacility('government')\">\n                    <span class=\"facility-name\">官府</span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 移动功能内容 -->\n              <div v-else-if=\"currentFunction === 'move'\" class=\"move-content\">\n                <div class=\"pixel-border-box\">\n                  <!-- 四行位置布局容器 -->\n                  <div class=\"four-row-container\">\n                    <!-- 第一行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(0)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(0))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(0).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第二行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(1)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(1))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(1).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第三行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(2)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(2))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(2).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n\n                    <!-- 第四行位置 -->\n                    <div class=\"entity-row-container\">\n                      <div v-if=\"getLocationForRow(3)\" class=\"location-item-simple\">\n                        <span\n                          class=\"entity-name clickable\"\n                          @click=\"moveToLocationDirectly(getLocationForRow(3))\"\n                          :class=\"{ 'disabled': isMoving }\"\n                        >\n                          {{ getLocationForRow(3).name }}\n                        </span>\n                      </div>\n                      <div v-else class=\"empty-row\">\n                        <span class=\"empty-text\">暂无位置</span>\n                      </div>\n                    </div>\n\n                    <!-- 分割线 -->\n                    <div class=\"divider-line\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 功能菜单内容 -->\n              <div v-else-if=\"currentFunction === 'functions'\" class=\"functions-content\">\n                <div class=\"function-list functions-grid\">\n                  <div class=\"function-item\" @click=\"openFunction('status')\">\n                    <span class=\"function-name\">状态</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('items')\">\n                    <span class=\"function-name\">物品</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('immortal')\">\n                    <span class=\"function-name\">仙将</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('team')\">\n                    <span class=\"function-name\">组队</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('ranking')\">\n                    <span class=\"function-name\">排行</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('friends')\">\n                    <span class=\"function-name\">好友</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('mail')\">\n                    <span class=\"function-name\">邮件</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('quest')\">\n                    <span class=\"function-name\">任务</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('arena')\">\n                    <span class=\"function-name\">擂台</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('guild')\">\n                    <span class=\"function-name\">帮派</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('training')\">\n                    <span class=\"function-name\">训练</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('treasury')\">\n                    <span class=\"function-name\">宝库</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('notice')\">\n                    <span class=\"function-name\">公告</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('vip')\">\n                    <span class=\"function-name\">VIP</span>\n                  </div>\n                  <div class=\"function-item\" @click=\"openFunction('strategy')\">\n                    <span class=\"function-name\">攻略</span>\n                </div>\n                  <div class=\"function-item\" @click=\"openFunction('logout')\">\n                    <span class=\"function-name\">登出</span>\n              </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 下半部分：功能区域 -->\n        <div class=\"bottom-section\">\n          <!-- 中间：功能按钮栏 -->\n          <div class=\"function-bar\">\n            <div\n              v-for=\"(func, index) in mainFunctions\"\n              :key=\"index\"\n              class=\"function-btn\"\n              :class=\"{ 'active': currentFunction === func.action }\"\n              @click=\"handleFunction(func.action)\"\n            >\n              <img :src=\"func.image\" :alt=\"func.name\" class=\"function-btn-image\" />\n            </div>\n          </div>\n\n          <!-- 在线玩家头像区域 -->\n          <div class=\"online-players\">\n            <div class=\"section-title\">在线玩家头像</div>\n            <div class=\"players-avatars\">\n              <div\n                v-for=\"(player, index) in onlinePlayers\"\n                :key=\"index\"\n                class=\"player-avatar\"\n              >\n                <img :src=\"player.avatar\" :alt=\"player.name\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- 底部：聊天组件区域 -->\n          <div class=\"chat-section\">\n            <GameChat\n              :character-info=\"characterInfo\"\n              :auto-connect=\"true\"\n              :initial-minimized=\"false\"\n              @message-sent=\"onChatMessageSent\"\n              @channel-switched=\"onChatChannelSwitched\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport GameChat from '@/components/GameChat.vue'\nimport logger from '@/utils/logger'\nimport { getCharacterDetail, getCurrentCharacter, getCharacterStatus } from '@/api/services/characterService'\nimport { showMessage } from '@/utils/message'\n\nexport default {\n  name: 'Main',\n  components: {\n    GameLayout,\n    GameChat\n  },\n  data() {\n    return {\n      currentFunction: null, // 保持为null，默认显示NPC和怪物\n      characterInfo: {\n        name: '',\n        avatar: '',\n        profession: '',\n        silver: 0,\n        gold: 0,\n        expNeeded: 0,\n        hp: 0,\n        maxHp: 0,\n        mp: 0,\n        maxMp: 0,\n        level: 1,\n        exp: 0,\n        expRequired: 1000,\n        attributePoints: 0,\n        constitution: 0,\n        intelligence: 0,\n        strength: 0,\n        agility: 0,\n        attack: 0,\n        defense: 0,\n        speed: 0\n      },\n      npcList: [\n        {\n          name: '村长',\n          type: 'npc',\n          description: '村庄的领导者，可以接受任务',\n          level: 50,\n          avatar: '/static/game/UI/tx/npc/village_chief.png',\n          services: ['任务', '信息']\n        },\n        {\n          name: '武器商人',\n          type: 'npc',\n          description: '出售各种武器装备',\n          level: 30,\n          avatar: '/static/game/UI/tx/npc/weapon_merchant.png',\n          services: ['武器', '装备']\n        },\n        {\n          name: '药剂师',\n          type: 'npc',\n          description: '出售恢复药剂和魔法药水',\n          level: 25,\n          avatar: '/static/game/UI/tx/npc/alchemist.png',\n          services: ['药剂', '治疗']\n        },\n        {\n          name: '铁匠',\n          type: 'npc',\n          description: '可以强化和修理装备',\n          level: 40,\n          avatar: '/static/game/UI/tx/npc/blacksmith.png',\n          services: ['强化', '修理']\n        }\n      ],\n      mainFunctions: [\n        { name: '人物', action: 'character', image: '/static/game/UI/anniu/sc_gn_1.png' },\n        { name: '设施', action: 'equipment', image: '/static/game/UI/anniu/sc_gn_2.png' },\n        { name: '移动', action: 'move', image: '/static/game/UI/anniu/sc_gn_3.png' },\n        { name: '功能', action: 'functions', image: '/static/game/UI/anniu/sc_gn_4.png' }\n      ],\n      onlinePlayers: [\n        { name: '玩家1', avatar: '/static/game/UI/tx/male/tx2.png' },\n        { name: '玩家2', avatar: '/static/game/UI/tx/male/tx3.png' },\n        { name: '玩家3', avatar: '/static/game/UI/tx/male/tx4.png' },\n        { name: '玩家4', avatar: '/static/game/UI/tx/male/tx5.png' },\n        { name: '玩家5', avatar: '/static/game/UI/tx/male/tx6.png' }\n      ],\n\n      // 地图相关数据\n      loadingLocations: false,\n\n      // 实体信息弹窗\n      showEntityModal: false,\n      selectedEntity: null,\n      selectedEntityType: null\n    }\n  },\n  computed: {\n    hpPercent() {\n      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100\n    },\n    mpPercent() {\n      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100\n    },\n    expPercent() {\n      return (this.characterInfo.exp / this.characterInfo.expRequired) * 100\n    },\n\n    // 地图相关计算属性\n    availableLocations() {\n      return this.$store.state.map.availableLocations || []\n    },\n\n    isMoving() {\n      return this.$store.state.map.loading.moving\n    },\n\n    canMove() {\n      return this.$store.getters['map/canMove']\n    },\n\n    // 当前地图的NPC列表\n    currentLocationNpcs() {\n      try {\n        const currentLocation = this.$store.state.map.currentLocation;\n        return currentLocation.npcs || [];\n      } catch (error) {\n        logger.error('[Main] 获取当前位置NPC失败:', error);\n        return [];\n      }\n    },\n\n    // 当前地图的怪物列表\n    currentLocationMonsters() {\n      try {\n        const currentLocation = this.$store.state.map.currentLocation;\n        return currentLocation.monsters || [];\n      } catch (error) {\n        logger.error('[Main] 获取当前位置怪物失败:', error);\n        return [];\n      }\n    },\n\n    // 合并所有实体（NPC和怪物）\n    allEntities() {\n      const entities = [];\n\n      try {\n        // 添加NPC\n        if (this.currentLocationNpcs && Array.isArray(this.currentLocationNpcs)) {\n          this.currentLocationNpcs.forEach(npc => {\n            entities.push({\n              ...npc,\n              type: 'npc'\n            });\n          });\n        }\n\n        // 添加怪物\n        if (this.currentLocationMonsters && Array.isArray(this.currentLocationMonsters)) {\n          this.currentLocationMonsters.forEach(monster => {\n            entities.push({\n              ...monster,\n              type: 'monster'\n            });\n          });\n        }\n      } catch (error) {\n        logger.error('[Main] 获取实体列表失败:', error);\n      }\n\n      return entities;\n    }\n  },\n  methods: {\n    openCharacterStatus() {\n      logger.info('打开角色状态页面')\n      \n      // 在跳转前初始化角色状态数据\n      this.$store.dispatch('character/initCurrentCharacter').then(character => {\n        if (character) {\n          this.$store.dispatch('character/loadCharacterStatus').then(() => {\n            // 确保状态已加载完成，然后跳转\n            logger.debug('[Main] 角色状态加载成功，准备跳转到角色状态页面');\n            \n            // 强制刷新一次角色信息，确保数据最新\n            this.loadCharacterInfo().then(() => {\n              this.$router.push('/game/character-status');\n            });\n          }).catch(error => {\n            logger.error('[Main] 加载角色状态失败:', error);\n            showMessage('加载角色状态失败', 'error');\n            // 即使加载失败也跳转，CharacterStatus组件会处理错误情况\n            this.$router.push('/game/character-status');\n          });\n        } else {\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n        }\n      });\n    },\n    selectNpc(npc) {\n      logger.info('选择NPC/野怪', npc.name)\n\n      if (npc.type === 'npc') {\n        // NPC交互逻辑\n        this.handleNpcInteraction(npc)\n      } else if (npc.type === 'monster') {\n        // 怪物战斗逻辑\n        this.handleMonsterEncounter(npc)\n      }\n    },\n\n    handleNpcInteraction(npc) {\n      // 西游记主题的NPC对话\n      switch(npc.name) {\n        case '城隍爷':\n          this.showToast(`与${npc.name}对话：施主，此地乃东胜神洲，有何贵干？`)\n          break\n        case '仙界商人':\n          this.showToast(`与${npc.name}对话：仙友，我这里有各种法宝，要不要看看？`)\n          setTimeout(() => {\n            this.$router.push('/game/market')\n          }, 2000)\n          break\n        case '猴族长老':\n          this.showToast(`与${npc.name}对话：小猴子，想学武艺吗？花果山可是修炼的好地方！`)\n          break\n        case '洞府守护':\n          this.showToast(`与${npc.name}对话：水帘洞乃美猴王洞府，此处有上古秘籍！`)\n          break\n        case '得道高僧':\n          this.showToast(`与${npc.name}对话：阿弥陀佛，施主与佛有缘，可愿听贫僧讲经？`)\n          break\n        case '罗汉':\n          this.showToast(`与${npc.name}对话：善哉善哉，施主来到灵山，可是为求真经？`)\n          break\n        case '凡间官员':\n          this.showToast(`与${npc.name}对话：这位侠客，南瞻部洲最近不太平，小心山贼！`)\n          break\n        case '东海龙王':\n          this.showToast(`与${npc.name}对话：何方神圣闯入龙宫？若是有缘人，可赐你神兵利器！`)\n          setTimeout(() => {\n            this.$router.push('/game/market')\n          }, 2000)\n          break\n        case '虾兵':\n          this.showToast(`与${npc.name}对话：龙王有令，闲杂人等不得入内！`)\n          break\n        default:\n          this.showToast(`与${npc.name}对话：施主，贫道这厢有礼了！`)\n      }\n    },\n\n    handleMonsterEncounter(monster) {\n      // 西游记主题的怪物遭遇\n      let encounterMessage = '';\n\n      switch(monster.name) {\n        case '灵猴':\n          encounterMessage = `一只${monster.name}从树上跳下，似乎想要与你切磋武艺！`;\n          break\n        case '山魈':\n          encounterMessage = `${monster.name}从山林中现身，凶神恶煞地盯着你！`;\n          break\n        case '水灵':\n          encounterMessage = `洞中的${monster.name}感受到了你的气息，化作人形挡住去路！`;\n          break\n        case '护法金刚':\n          encounterMessage = `${monster.name}金光闪闪，威严地说：\"欲入灵山，先过我这一关！\"`;\n          break\n        case '山贼':\n          encounterMessage = `一群${monster.name}拦路抢劫：\"此路是我开，此树是我栽！\"`;\n          break\n        case '蟹将':\n          encounterMessage = `龙宫${monster.name}挥舞着巨钳：\"胆敢擅闯龙宫，受死！\"`;\n          break\n        default:\n          encounterMessage = `遭遇了${monster.name}，看起来来者不善！`;\n      }\n\n      this.showToast(`${encounterMessage}（等级${monster.level}）准备战斗！`)\n\n      // 获取当前角色和位置信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      const currentLocation = this.$store.state.map.currentLocation;\n\n      if (!currentCharacter || !currentCharacter.id) {\n        this.showToast('角色信息错误，无法开始战斗', 'error');\n        return;\n      }\n\n      setTimeout(() => {\n        // 传递战斗所需的参数\n        this.$router.push({\n          path: '/game/battle',\n          query: {\n            characterId: currentCharacter.id,\n            monsterId: monster.id,\n            locationId: currentLocation?.id || null\n          }\n        });\n      }, 2000)\n    },\n\n    // 获取指定行的实体（为后台管理预留接口）\n    getEntityForRow(rowIndex) {\n      try {\n        // 目前简单按顺序分配，后续可通过后台配置\n        const entities = this.allEntities || [];\n        return entities[rowIndex] || null;\n      } catch (error) {\n        logger.error('[Main] 获取行实体失败:', error);\n        return null;\n      }\n    },\n\n    // 统一的实体操作方法\n    handleEntityAction(entity) {\n      if (entity.type === 'npc') {\n        this.handleNpcInteraction(entity);\n      } else if (entity.type === 'monster') {\n        this.handleMonsterEncounter(entity);\n      }\n    },\n\n    // 获取指定行的位置（为后台管理预留接口）\n    getLocationForRow(rowIndex) {\n      try {\n        // 目前简单按顺序分配，后续可通过后台配置\n        const locations = this.availableLocations || [];\n        return locations[rowIndex] || null;\n      } catch (error) {\n        logger.error('[Main] 获取行位置失败:', error);\n        return null;\n      }\n    },\n    handleFunction(action) {\n      logger.info('点击功能', action)\n      if (this.currentFunction === action) {\n        // 如果当前功能已经是选中状态，不做任何操作\n        return;\n      } else {\n        // 设置为新的功能\n        this.currentFunction = action\n      }\n    },\n    getCurrentPanelTitle() {\n      switch(this.currentFunction) {\n        case 'character':\n          return '人物信息'\n        case 'equipment':\n          return '设施列表'\n        case 'move':\n          return '移动地点'\n        case 'functions':\n          return '功能菜单'\n        default:\n          return ''\n      }\n    },\n    // 设施相关方法\n    selectFacility(facility) {\n      logger.info('选择设施', facility)\n      switch(facility) {\n        case 'clinic':\n          this.$router.push('/game/clinic');\n          break;\n        case 'bank':\n          this.$router.push('/game/bank');\n          break;\n        case 'posthouse':\n          this.$router.push('/game/posthouse');\n          break;\n        case 'market':\n          this.$router.push('/game/market');\n          break;\n        case 'square':\n          this.$router.push('/game/square');\n          break;\n        case 'government':\n          this.$router.push('/game/government');\n          break;\n      }\n    },\n    // 移动相关方法\n    async moveToLocationDirectly(location) {\n      logger.info('[Main] 直接移动到位置:', location);\n\n      // 检查角色信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      if (!currentCharacter || !currentCharacter.id) {\n        logger.error('[Main] 移动失败: 未找到角色信息');\n        this.showToast('请先选择角色', 'error');\n        return;\n      }\n\n      // 检查是否正在移动\n      if (this.isMoving) {\n        this.showToast('正在移动中，请稍候', 'warning');\n        return;\n      }\n\n      try {\n        await this.$store.dispatch('map/moveToLocation', location.id);\n        this.showToast(`成功移动到${location.name}`, 'success');\n\n        // 重新加载当前位置的NPC和怪物\n        await this.loadLocationEntities();\n      } catch (error) {\n        logger.error('[Main] 移动失败:', error);\n        this.showToast('移动失败: ' + error.message, 'error');\n      }\n    },\n\n\n\n\n\n    async loadLocationEntities() {\n      try {\n        const currentCharacter = this.$store.getters['character/currentCharacter'];\n        const currentLocation = this.$store.state.map.currentLocation;\n\n        if (currentCharacter && currentLocation.id) {\n          logger.debug('[Main] 当前位置实体信息:', {\n            location: currentLocation.name,\n            npcs: currentLocation.npcs?.length || 0,\n            monsters: currentLocation.monsters?.length || 0\n          });\n\n          // 触发Vue的响应式更新\n          this.$forceUpdate();\n        }\n      } catch (error) {\n        logger.error('[Main] 加载位置实体失败:', error);\n      }\n    },\n\n\n\n\n\n    // 兼容旧的移动方法\n    moveToLocation(location) {\n      logger.info('[Main] 兼容性移动方法调用:', location);\n\n      // 如果是字符串，转换为位置对象\n      if (typeof location === 'string') {\n        const locationObj = {\n          id: location,\n          name: location,\n          type: location\n        };\n        this.moveToLocationDirectly(locationObj);\n      } else {\n        this.moveToLocationDirectly(location);\n      }\n    },\n    // 功能菜单相关方法\n    openFunction(func) {\n      logger.info('打开功能', func)\n      // 根据功能类型跳转到对应页面\n      switch(func) {\n        case 'status':\n          this.$router.push('/game/status')\n          break\n        case 'items':\n          this.$router.push('/game/items')\n          break\n        case 'immortal':\n          this.$router.push('/game/immortal')\n          break\n        case 'team':\n          this.$router.push('/game/team')\n          break\n        case 'ranking':\n          this.$router.push('/game/ranking')\n          break\n        case 'friends':\n          this.$router.push('/game/friends')\n          break\n        case 'mail':\n          this.$router.push('/game/mail')\n          break\n        case 'quest':\n          this.$router.push('/game/quest')\n          break\n        case 'arena':\n          this.$router.push('/game/arena')\n          break\n        case 'guild':\n          this.$router.push('/game/guild')\n          break\n        case 'training':\n          this.$router.push('/game/training')\n          break\n        case 'treasury':\n          this.$router.push('/game/treasury')\n          break\n        case 'notice':\n          this.$router.push('/game/notice')\n          break\n        case 'vip':\n          this.$router.push('/game/vip')\n          break\n        case 'strategy':\n          this.$router.push('/game/strategy')\n          break\n        case 'logout':\n          this.$router.push('/game/logout')\n          break\n      }\n    },\n    // 聊天组件事件处理\n    onChatMessageSent(messageData) {\n      logger.info('[Main] 聊天消息已发送:', messageData);\n    },\n\n    onChatChannelSwitched(channelData) {\n      logger.info('[Main] 聊天频道已切换:', channelData);\n    },\n\n    showToast(message, type = 'info') {\n      // 简单的提示实现，使用logger代替alert避免弹出框\n      logger.info(`[Toast ${type}]:`, message)\n    },\n\n    // 实体信息弹窗相关方法\n    showEntityInfo(entity, type) {\n      this.selectedEntity = entity;\n      this.selectedEntityType = type;\n      this.showEntityModal = true;\n      logger.info(`[Main] 显示${type}信息:`, entity.name);\n    },\n\n    closeEntityModal() {\n      this.showEntityModal = false;\n      this.selectedEntity = null;\n      this.selectedEntityType = null;\n    },\n\n    // 获取类型文本的方法\n    getNpcTypeText(type) {\n      const typeMap = {\n        'merchant': '商人',\n        'quest_giver': '任务发布者',\n        'trainer': '训练师',\n        'guard': '守卫',\n        'official': '官员',\n        'immortal': '仙人',\n        'monk': '僧人',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n\n    getMonsterTypeText(type) {\n      const typeMap = {\n        'beast': '野兽',\n        'demon': '妖魔',\n        'spirit': '精灵',\n        'undead': '不死族',\n        'dragon': '龙族',\n        'immortal': '仙族',\n        'elemental': '元素',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n\n    getFactionText(faction) {\n      const factionMap = {\n        'heaven': '天庭',\n        'buddhist': '佛门',\n        'mortal': '凡间',\n        'demon': '妖魔',\n        'dragon': '龙族',\n        'neutral': '中立'\n      };\n      return factionMap[faction] || faction;\n    },\n\n    getElementText(element) {\n      const elementMap = {\n        'none': '无',\n        'fire': '火',\n        'water': '水',\n        'earth': '土',\n        'wind': '风',\n        'thunder': '雷',\n        'ice': '冰',\n        'light': '光',\n        'dark': '暗'\n      };\n      return elementMap[element] || element;\n    },\n\n    getSizeText(size) {\n      const sizeMap = {\n        'tiny': '微小',\n        'small': '小型',\n        'medium': '中型',\n        'large': '大型',\n        'huge': '巨型',\n        'giant': '超巨型'\n      };\n      return sizeMap[size] || size;\n    },\n\n    getDefaultAvatar(type) {\n      if (type === 'npc') {\n        return '/static/game/UI/tx/npc/default.png'\n      } else {\n        return '/static/game/UI/tx/monster/default.png'\n    }\n  },\n    async loadCharacterInfo() {\n      try {\n        // 获取当前角色基本信息\n    const current = getCurrentCharacter();\n        if (!current || !current.id) {\n          logger.error('[Main] 未找到当前角色信息');\n          showMessage('未找到角色信息，请重新登录', 'error');\n          this.$router.push('/setup/character-select');\n          return;\n        }\n\n        // 将角色信息设置到store中\n        await this.$store.dispatch('character/selectCharacter', current);\n        logger.debug('[Main] 角色信息已设置到store:', current);\n\n        // 获取角色详情\n        try {\n          const detailResponse = await getCharacterDetail(current.id);\n          if (detailResponse && detailResponse.data) {\n            const detail = detailResponse.data;\n            \n            // 职业英文到中文的映射\n            const professionMap = {\n              'warrior': '武士',\n              'scholar': '文人',\n              'mystic': '异人'\n            };\n            \n            // 更新角色基本信息\n        this.characterInfo = {\n          ...this.characterInfo,\n              name: detail.name || current.name,\n              avatar: detail.avatar || `/static/game/UI/tx/${detail.gender || 'male'}/tx1.png`,\n              profession: professionMap[detail.profession] || detail.profession || '未知',\n              gold: detail.gold || 0,\n              silver: detail.silver || 0,\n              level: detail.level || 1\n        };\n          }\n        } catch (detailError) {\n          logger.error('[Main] 获取角色详情失败:', detailError);\n    }\n\n        // 获取角色状态信息\n        try {\n          const statusResponse = await getCharacterStatus(current.id);\n          if (statusResponse && statusResponse.data) {\n            const status = statusResponse.data;\n            \n            // 更新角色状态信息\n            this.characterInfo = {\n              ...this.characterInfo,\n              hp: parseInt(status.hp || 0),\n              maxHp: parseInt(status.max_hp || 100),\n              mp: parseInt(status.mp || 0),\n              maxMp: parseInt(status.max_mp || 100),\n              exp: parseInt(status.exp || 0),\n              expRequired: parseInt(status.exp_required || 1000),\n              attributePoints: parseInt(status.attribute_points || 0),\n              constitution: parseInt(status.constitution || 0),\n              intelligence: parseInt(status.intelligence || 0),\n              strength: parseInt(status.strength || 0),\n              agility: parseInt(status.agility || 0),\n              attack: parseInt(status.attack || 0),\n              defense: parseInt(status.defense || 0),\n              speed: parseInt(status.speed || 0)\n            };\n            \n            // 调试输出\n            logger.debug('[Main] 角色状态已更新:', this.characterInfo);\n          }\n        } catch (statusError) {\n          logger.error('[Main] 获取角色状态失败:', statusError);\n        }\n      } catch (error) {\n        logger.error('[Main] 加载角色信息失败:', error);\n        showMessage('加载角色信息失败', 'error');\n      }\n      \n      // 返回Promise以支持链式调用\n      return Promise.resolve();\n    },\n  },\n  async mounted() {\n    try {\n      // 加载角色信息\n      await this.loadCharacterInfo();\n\n      // 检查用户是否已登录且有角色信息\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      const isAuthenticated = this.$store.state.auth.isAuthenticated;\n\n      logger.debug('[Main] 挂载后状态检查:', {\n        isAuthenticated,\n        hasCharacter: !!currentCharacter,\n        characterId: currentCharacter?.id\n      });\n\n      if (isAuthenticated && currentCharacter && currentCharacter.id) {\n        // 初始化地图数据\n        try {\n          await this.$store.dispatch('map/initializeMap');\n          logger.info('[Main] 地图数据初始化完成');\n        } catch (error) {\n          logger.error('[Main] 地图数据初始化失败:', error);\n          // 地图初始化失败不影响主要功能，只记录错误\n        }\n      } else {\n        logger.warn('[Main] 用户未登录或无角色信息，跳过地图初始化');\n      }\n    } catch (error) {\n      logger.error('[Main] 组件挂载过程中发生错误:', error);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.game-container {\n  position: relative;\n  height: 100%;\n  width: 100%;\n  color: #ffd700;\n  overflow: hidden;\n  background: url('/static/game/UI/bg/main_bg.png') center center no-repeat;\n  background-size: cover;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(0, 0, 50, 0.4), rgba(50, 0, 0, 0.4));\n    z-index: 0;\n  }\n}\n\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n  padding: 8px;\n}\n\n.top-section {\n  display: flex;\n  gap: 8px;\n  height: 320px;\n  flex-shrink: 0;\n\n  @media (max-width: 768px) {\n    height: 280px;\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    height: 240px;\n    gap: 4px;\n  }\n}\n\n.bottom-section {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-height: 0;\n\n  @media (max-width: 768px) {\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    gap: 4px;\n  }\n}\n\n.left-panel, .right-panel {\n  background: none;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  border: none;\n}\n\n.left-panel {\n  width: 120px;\n  min-width: 100px;\n  background: none;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  border: none;\n}\n\n.pixel-avatar-bar2 {\n  cursor: pointer;\n  transition: transform 0.2s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.right-panel {\n  flex: 1;\n  min-width: 0;\n}\n\n.panel-header {\n  background: none;\n  color: inherit;\n  padding: 0;\n  font-weight: normal;\n  font-size: inherit;\n  text-align: center;\n  text-shadow: none;\n  border-bottom: none;\n}\n\n.character-info {\n  flex: 1;\n  padding: 6px;\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  background: none;\n}\n\n.info-details {\n  flex: 1;\n}\n\n.character-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 1px;\n  text-shadow: none;\n}\n\n.character-level {\n  font-size: 10px;\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 6px;\n  text-shadow: none;\n}\n\n.stats {\n  display: flex;\n  flex-direction: column;\n  gap: 1px;\n}\n\n.stat-item {\n  font-size: 10px;\n  color: #ffd700;\n  padding: 1px 0;\n  text-shadow: none;\n}\n\n.avatar-section, .status-bars {\n  display: none !important;\n}\n\n.panel-content {\n  flex: 1;\n  padding: 15px;\n  overflow-y: auto;\n  background: none;\n}\n\n.npc-content,\n.npc-content * {\n  background: none !important;\n  box-shadow: none !important;\n  border: none !important;\n}\n\n.npc-text-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  background: none;\n  border: none;\n}\n\n.npc-text-list li {\n  border-bottom: 1px solid #888 !important;\n  margin: 0 !important;\n  padding: 2px 0 !important;\n}\n\n.npc-action-btn {\n  background: none !important;\n  border: none !important;\n  color: inherit !important;\n  font: inherit !important;\n  box-shadow: none !important;\n  border-radius: 0 !important;\n  padding: 0 !important;\n  margin-left: 4px !important;\n  cursor: pointer;\n}\n\n.npc-action-btn:hover,\n.npc-action-btn:active {\n  background: none !important;\n  color: inherit !important;\n  box-shadow: none !important;\n  border: none !important;\n  outline: none !important;\n}\n\n.character-content {\n  height: auto;\n  padding: 0;\n  overflow: visible;\n  background: none;\n  box-shadow: none;\n}\n\n.character-card {\n  background: none;\n  border-radius: 0;\n  padding: 0;\n  box-shadow: none;\n  border: none;\n}\n\n.character-avatar-section,\n.character-basic-info,\n.character-profession,\n.character-bars,\n.character-stats,\n.character-wealth,\n.character-exp {\n  background: none !important;\n  box-shadow: none !important;\n  border: none !important;\n  border-radius: 0 !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n\n.character-avatar-img {\n  width: 60px;\n  height: 60px;\n  border-radius: 10%;\n  border: 2px solid #ffd700;\n  object-fit: cover;\n  box-shadow: none;\n}\n\n.character-name {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-profession {\n  margin: 0;\n  font-size: 14px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-bars {\n  margin-bottom: 16px;\n}\n\n.bar-item {\n  margin-bottom: 8px;\n}\n\n.bar-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n  font-size: 12px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.bar-value {\n  font-weight: bold;\n}\n\n.progress-bar {\n  height: 8px;\n  background: none;\n  border-radius: 0;\n  overflow: hidden;\n  border: 1px solid #088be2;\n}\n\n.progress-fill {\n  height: 100%;\n  transition: width 0.3s ease;\n}\n\n.hp-bar .progress-fill {\n  background: linear-gradient(90deg, #ff0000, #ff3333);\n}\n\n.mp-bar .progress-fill {\n  background: linear-gradient(90deg, #088be2, #4eadf5);\n}\n\n.character-stats {\n  margin-bottom: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.stat-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-wealth {\n  margin-bottom: 16px;\n}\n\n.wealth-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n  margin-bottom: 6px;\n}\n\n.wealth-label {\n  flex: 1;\n  font-size: 13px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.wealth-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.character-exp {\n  padding: 8px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n}\n\n.exp-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 13px;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.exp-value {\n  font-weight: bold;\n  color: #ffd700;\n  text-shadow: none;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: none;\n  border-radius: 4px;\n  border: 1px solid #088be2;\n\n  .label {\n    font-weight: bold;\n    color: #ffd700;\n    text-shadow: none;\n  }\n\n  .value {\n    color: #ffd700;\n    text-shadow: none;\n  }\n}\n\n.facilities-content {\n  height: 100%;\n  background: none !important;\n}\n\n.facility-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.facility-item {\n  padding: 8px 0;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.move-content {\n  height: 100%;\n  background: none !important;\n}\n\n.location-list {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n}\n\n.location-item {\n  padding: 6px;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.functions-content {\n  height: 100%;\n  background: none !important;\n}\n\n.function-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.function-item {\n  padding: 12px;\n  background: none;\n  border: 1px solid #088be2;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: none;\n    border-color: #ffd700;\n    transform: translateY(-1px);\n    box-shadow: none;\n  }\n\n  .function-name {\n    display: block;\n    font-weight: bold;\n    color: #ffd700;\n    margin-bottom: 4px;\n    text-shadow: none;\n  }\n\n  .function-desc {\n    display: block;\n    font-size: 12px;\n    color: #ccc;\n  }\n}\n\n.function-bar {\n  display: flex;\n  gap: 8px;\n  height: 50px;\n  padding: 0;\n  background: none !important;\n  border-radius: 0;\n  border: none;\n  flex-shrink: 0;\n  overflow: visible;\n}\n\n.function-btn {\n  flex: 1;\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2px;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-2px);\n    background: none;\n  }\n\n  &:active {\n    background: none;\n    transform: scale(0.95);\n  }\n\n  &.active {\n    background: none;\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: -3px;\n      left: 25%;\n      right: 25%;\n      height: 3px;\n      background: #ffd700;\n      border-radius: 3px;\n    }\n  }\n}\n\n.function-btn-image {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  max-width: 100%;\n  max-height: 100%;\n}\n\n.online-players {\n  border: none;\n  background: none !important;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  box-shadow: none;\n  flex-shrink: 0;\n}\n\n.section-title {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  color: #ffd700;\n  padding: 6px 12px;\n  font-weight: bold;\n  font-size: 13px;\n  text-align: center;\n  text-shadow: none;\n  border-bottom: none;\n\n  @media (max-width: 768px) {\n    padding: 5px 10px;\n    font-size: 12px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 4px 8px;\n    font-size: 11px;\n  }\n}\n\n.players-avatars {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  gap: 8px;\n  overflow-x: auto;\n  background: none;\n\n  @media (max-width: 768px) {\n    padding: 6px 10px;\n    gap: 6px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 4px 8px;\n    gap: 4px;\n  }\n}\n\n.player-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 10%;\n  border: 2px solid #ffd700;\n  overflow: hidden;\n  flex-shrink: 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: none;\n\n  @media (max-width: 768px) {\n    width: 35px;\n    height: 35px;\n  }\n\n  @media (max-width: 480px) {\n    width: 30px;\n    height: 30px;\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n\n  &:hover {\n    border-color: #088be2;\n    transform: scale(1.1);\n    box-shadow: none;\n  }\n}\n\n.chat-section {\n  border: none;\n  background: none !important;\n  height: 420px;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0;\n  box-shadow: none;\n  flex-shrink: 0;\n  position: relative;\n  overflow: hidden;\n  min-height: 200px;\n  max-height: 50vh;\n}\n\n/* 聊天区域现在使用GameChat组件，移除旧的样式 */\n\n@media (orientation: landscape) and (max-height: 600px) {\n  .top-section {\n    height: 200px;\n  }\n\n  .online-players {\n    height: 60px;\n  }\n\n  .chat-section {\n    height: 280px;\n    max-height: 40vh;\n    min-height: 200px;\n  }\n\n  .function-bar {\n    height: 40px;\n  }\n}\n\n@media (max-width: 320px) {\n  .main-content {\n    padding: 4px;\n    gap: 4px;\n  }\n\n  .left-panel {\n    width: 140px;\n    min-width: 120px;\n  }\n\n  .player-avatar {\n    width: 25px;\n    height: 25px;\n  }\n\n  .function-btn {\n    font-size: 10px;\n  }\n\n  .panel-header {\n    font-size: 10px;\n    padding: 3px 6px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .main-content {\n    max-width: 1000px;\n    margin: 0 auto;\n    padding: 20px;\n  }\n\n  .top-section {\n    height: 350px;\n  }\n\n  .left-panel {\n    width: 320px;\n  }\n\n  .player-avatar {\n    width: 100px;\n    height: 100px;\n  }\n\n  .function-bar {\n    height: 60px;\n  }\n\n  .function-btn {\n    font-size: 16px;\n  }\n\n  .online-players {\n    height: 100px;\n  }\n\n  .chat-section {\n    height: 360px;\n    max-height: 45vh;\n  }\n}\n\n/* 超小屏幕特殊优化 */\n@media (max-width: 480px) {\n  .chat-section {\n    height: 240px !important;\n    max-height: 35vh !important;\n    min-height: 180px !important;\n  }\n\n  .main-content {\n    gap: 4px;\n  }\n\n  .content-section {\n    padding: 4px;\n  }\n}\n\n/* 横屏模式优化 */\n@media (orientation: landscape) and (max-height: 600px) {\n  .chat-section {\n    height: 200px !important;\n    max-height: 30vh !important;\n    min-height: 150px !important;\n  }\n\n  .main-content {\n    gap: 2px;\n  }\n}\n\n/* 高分辨率屏幕优化 */\n@media (min-width: 1440px) {\n  .chat-section {\n    height: 480px;\n    max-height: 55vh;\n  }\n}\n\n@media (hover: none) and (pointer: coarse) {\n  .function-btn, .chat-tab, .npc-item, .player-avatar {\n    min-height: 44px;\n  }\n\n  .function-btn:active {\n    background: #088be2;\n    transform: scale(0.95);\n  }\n\n  .chat-tab:active {\n    background: #088be2;\n  }\n}\n\n.npc-actions {\n  display: flex;\n  justify-content: space-around;\n  margin-top: 10px;\n}\n.npc-action-btn {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  color: #ffd700;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n  text-shadow: none;\n  transition: all 0.3s ease;\n}\n.npc-action-btn:hover {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  transform: none;\n}\n.npc-action-btn:active {\n  background: linear-gradient(90deg, #08407a, #088be2);\n  transform: none;\n}\n\n.pixel-avatar-bar2 {\n  position: relative;\n  width: 180px;\n  height: 60px;\n  margin: 0 auto 8px auto;\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n}\n.pixel-bg-img2 {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 180px;\n  height: 60px;\n  z-index: 0;\n  pointer-events: none;\n}\n.pixel-avatar-img2 {\n  position: absolute;\n  left: 6px;\n  bottom: 6px;\n  width: 38px;\n  height: 38px;\n  border-radius: 6px;\n  border: 2px solid #ffd700;\n  background: #222;\n  z-index: 2;\n  object-fit: cover;\n  box-shadow: none;\n}\n.pixel-name2 {\n  position: absolute;\n  left: 0;\n  top: 4px;\n  width: 180px;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: none;\n  z-index: 3;\n  pointer-events: none;\n}\n.pixel-bars2 {\n  position: absolute;\n  left: 54px;\n  top: 26px;\n  width: 116px;\n  z-index: 2;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.pixel-bar2 {\n  width: 100%;\n  height: 12px;\n  background: #222;\n  border: 1.5px solid #ffd700;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 2px;\n}\n.pixel-bar-inner2 {\n  height: 100%;\n  transition: width 0.3s;\n  border-radius: 4px;\n}\n.pixel-hp-inner2 {\n  background: linear-gradient(90deg, #ff3c3c, #ffb03c);\n}\n.pixel-mp-inner2 {\n  background: linear-gradient(90deg, #3c7cff, #b0e0ff);\n}\n\n* {\n  -webkit-tap-highlight-color: transparent !important;\n}\nbutton, [type=\"button\"], [type=\"submit\"], [type=\"reset\"], .function-btn, .npc-action-btn, .chat-tab, .player-avatar, .location-item, .facility-item, .function-item, .panel-header, .section-title, .stat-item, .character-card, .character-content, .panel-content, .right-panel, .left-panel {\n  outline: none !important;\n  box-shadow: none !important;\n  background: none !important;\n}\nbutton:active, button:focus, .function-btn:active, .function-btn:focus, .npc-action-btn:active, .npc-action-btn:focus, .chat-tab:active, .chat-tab:focus, .player-avatar:active, .player-avatar:focus, .location-item:active, .location-item:focus, .facility-item:active, .facility-item:focus, .function-item:active, .function-item:focus, .location-item-simple:active, .location-item-simple:focus {\n  background: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n  color: inherit !important;\n}\n\n/* 防止虚拟键盘弹出 */\n* {\n  -webkit-user-select: none !important;\n  -moz-user-select: none !important;\n  -ms-user-select: none !important;\n  user-select: none !important;\n  -webkit-touch-callout: none !important;\n  -webkit-tap-highlight-color: transparent !important;\n}\n\n/* 禁用所有元素的焦点 */\n*:focus {\n  outline: none !important;\n  -webkit-tap-highlight-color: transparent !important;\n}\n\n/* 添加六宫格布局样式 */\n.six-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  grid-template-rows: repeat(2, 1fr);\n  gap: 4px;\n  justify-items: center;\n  align-items: center;\n}\n\n.six-grid .facility-item {\n  width: 100%;\n  text-align: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: #ffd700;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  padding: 8px 0;\n  transition: background 0.2s;\n}\n\n.six-grid .facility-item:active {\n  background: none;\n}\n\n/* 添加功能宫格布局样式 */\n.functions-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  grid-template-rows: repeat(4, 1fr);\n  gap: 4px;\n  justify-items: center;\n  align-items: center;\n}\n\n.functions-grid .function-item {\n  width: 100%;\n  text-align: center;\n  font-size: 15px;\n  font-weight: bold;\n  color: #ffd700;\n  background: none;\n  border: none;\n  border-radius: 0;\n  cursor: pointer;\n  padding: 8px 0;\n  transition: background 0.2s;\n}\n\n.functions-grid .function-item:active {\n  background: none;\n}\n\n.pixel-info-box {\n  background: rgba(0, 0, 50, 0.7);\n  border: 1px solid #5555ff;\n  border-radius: 4px;\n  padding: 8px;\n  margin-top: 8px;\n  font-size: 12px;\n  color: #ffffff;\n}\n\n.pixel-row {\n  margin-bottom: 4px;\n  line-height: 1.2;\n}\n\n.pixel-label-gold {\n  color: #ffcc00;\n}\n\n.pixel-value-gold {\n  color: #ffcc00;\n  font-weight: bold;\n}\n\n.pixel-exp-label {\n  margin-top: 6px;\n  color: #00ffff;\n}\n\n.pixel-exp-bar {\n  height: 6px;\n  background: rgba(0, 0, 50, 0.5);\n  border: 1px solid #5555ff;\n  border-radius: 3px;\n  margin-top: 2px;\n  margin-bottom: 6px;\n  position: relative;\n  overflow: hidden;\n}\n\n.pixel-exp-inner {\n  position: absolute;\n  height: 100%;\n  background: linear-gradient(to right, #00ffff, #00aaff);\n  left: 0;\n  top: 0;\n}\n\n/* NPC和怪物显示样式 */\n.entity-section {\n  margin-bottom: 20px;\n}\n\n.section-header {\n  font-size: 16px;\n  font-weight: bold;\n  color: #d4af37;\n  margin-bottom: 10px;\n  padding: 5px 10px;\n  background: rgba(212, 175, 55, 0.1);\n  border-left: 3px solid #d4af37;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.entity-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.entity-row {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 8px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.entity-row:hover {\n  background: rgba(0, 0, 0, 0.5);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n\n.npc-row:hover {\n  border-color: rgba(135, 206, 235, 0.5);\n}\n\n.monster-row:hover {\n  border-color: rgba(255, 107, 107, 0.5);\n}\n\n.entity-left {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  cursor: pointer;\n}\n\n.entity-right {\n  flex-shrink: 0;\n}\n\n.entity-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #fff;\n  transition: color 0.3s ease;\n}\n\n.entity-name.clickable:hover {\n  color: #87ceeb;\n}\n\n.monster-name {\n  color: #ff6b6b;\n}\n\n.monster-name.clickable:hover {\n  color: #ff8a8a;\n}\n\n.entity-level {\n  font-size: 12px;\n  color: #ffd700;\n  background: rgba(255, 215, 0, 0.2);\n  padding: 2px 6px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  display: inline-block;\n  width: fit-content;\n}\n\n.entity-services {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 2px;\n}\n\n.service-tag {\n  font-size: 10px;\n  color: #87ceeb;\n  background: rgba(135, 206, 235, 0.2);\n  padding: 1px 4px;\n  border-radius: 8px;\n  border: 1px solid rgba(135, 206, 235, 0.3);\n}\n\n.entity-type {\n  font-size: 11px;\n  color: #ccc;\n  font-style: italic;\n}\n\n.action-btn {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 60px;\n}\n\n.npc-action-btn {\n  background: linear-gradient(135deg, #87ceeb, #5f9ea0);\n  color: white;\n  border: 1px solid #87ceeb;\n}\n\n.npc-action-btn:hover {\n  background: linear-gradient(135deg, #5f9ea0, #4682b4);\n  box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);\n}\n\n.battle-btn {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: white;\n  border: 1px solid #ff6b6b;\n}\n\n.battle-btn:hover {\n  background: linear-gradient(135deg, #ff3742, #ff2f3a);\n  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n  color: #666;\n}\n\n.empty-message {\n  font-size: 14px;\n  font-style: italic;\n}\n\n/* 实体信息弹窗样式 */\n.entity-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.entity-modal {\n  background: linear-gradient(135deg, #2c3e50, #34495e);\n  border-radius: 12px;\n  border: 2px solid #d4af37;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n  max-width: 500px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 15px 20px;\n  background: rgba(212, 175, 55, 0.1);\n  border-bottom: 1px solid rgba(212, 175, 55, 0.3);\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #d4af37;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: #ff6b6b;\n}\n\n.modal-content {\n  padding: 20px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 12px;\n  align-items: flex-start;\n}\n\n.info-row.description {\n  flex-direction: column;\n  gap: 5px;\n}\n\n.info-label {\n  font-weight: bold;\n  color: #d4af37;\n  min-width: 80px;\n  margin-right: 10px;\n}\n\n.info-value {\n  color: #fff;\n  flex: 1;\n}\n\n.threat-level {\n  color: #ff6b6b;\n  font-weight: bold;\n}\n\n.stats-section {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.stats-title {\n  font-weight: bold;\n  color: #d4af37;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 4px 8px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n}\n\n.stat-label {\n  color: #ccc;\n  font-size: 12px;\n}\n\n.stat-value {\n  color: #fff;\n  font-weight: bold;\n  font-size: 12px;\n}\n\n/* 移动功能样式 */\n.move-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n\n\n.location-description {\n  font-size: 11px;\n  color: #cccccc;\n  line-height: 1.4;\n}\n\n.movement-options {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.section-title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #88ccff;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n}\n\n.location-list {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.location-item {\n  padding: 12px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.location-item:hover:not(.disabled) {\n  background: none;\n  transform: translateY(-1px);\n}\n\n.location-item.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 简化的地图列表样式 */\n.location-item-simple {\n  padding: 8px 12px;\n  background: none;\n  border: none;\n  border-bottom: 1px solid #444;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  color: #ffd700;\n  font-size: 14px;\n  text-align: left;\n  display: block;\n  width: 100%;\n}\n\n.location-item-simple:hover:not(.disabled) {\n  background: none;\n  color: #ffffff;\n}\n\n.location-item-simple.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.location-item-simple:last-child {\n  border-bottom: none;\n}\n\n\n\n\n\n\n\n.no-locations, .loading-locations {\n  text-align: center;\n  padding: 20px;\n  color: #888888;\n  font-size: 12px;\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n/* 四行布局样式 */\n.four-row-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 0;\n}\n\n.entity-row-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: 2px 8px;\n  min-height: 20px;\n}\n\n.entity-list-horizontal {\n  display: flex;\n  gap: 15px;\n  width: 100%;\n  align-items: center;\n}\n\n.entity-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex: 1;\n}\n\n.entity-item-compact .entity-name {\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  flex: 1;\n  line-height: 1.2;\n}\n\n.entity-item-compact .entity-name:hover {\n  color: #87ceeb;\n}\n\n.entity-item-compact .monster-name {\n  color: #ff6b6b;\n}\n\n.entity-item-compact .monster-name:hover {\n  color: #ff8a8a;\n}\n\n.action-btn-compact {\n  padding: 4px 8px;\n  border: none;\n  border-radius: 3px;\n  font-size: 11px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 40px;\n}\n\n.action-btn-compact.npc-action-btn {\n  background: linear-gradient(135deg, #87ceeb, #5f9ea0);\n  color: white;\n  border: 1px solid #87ceeb;\n}\n\n.action-btn-compact.npc-action-btn:hover {\n  background: linear-gradient(135deg, #5f9ea0, #4682b4);\n  box-shadow: 0 0 8px rgba(135, 206, 235, 0.4);\n}\n\n.action-btn-compact.battle-btn {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: white;\n  border: 1px solid #ff6b6b;\n}\n\n.action-btn-compact.battle-btn:hover {\n  background: linear-gradient(135deg, #ff3742, #ff2f3a);\n  box-shadow: 0 0 8px rgba(255, 71, 87, 0.4);\n}\n\n.empty-row {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.empty-text {\n  color: #666;\n  font-size: 11px;\n  font-style: italic;\n  line-height: 1.1;\n}\n\n.divider-line {\n  height: 1px;\n  background: #666;\n  margin: 0 8px;\n  border: none;\n}\n\n/* 单行实体显示样式 */\n.entity-row-single {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  gap: 12px;\n}\n\n.entity-info {\n  flex: 1;\n  min-width: 0;\n  cursor: pointer;\n}\n\n.entity-info .entity-name {\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 150px;\n  display: block;\n  line-height: 1.2;\n}\n\n.entity-info .entity-name:hover {\n  color: #87ceeb;\n}\n\n.entity-info .monster-name {\n  color: #ff6b6b;\n}\n\n.entity-info .monster-name:hover {\n  color: #ff8a8a;\n}\n\n/* 位置行显示样式 */\n.location-row-container {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  min-height: 40px;\n}\n\n.location-row-single {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  gap: 12px;\n}\n\n.location-info {\n  flex: 1;\n  min-width: 0;\n  cursor: pointer;\n  text-align: center;\n}\n\n.location-info.disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n\n.location-info .location-name {\n  font-size: 14px;\n  font-weight: bold;\n  color: #ffd700;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.location-info .location-name:hover {\n  color: #87ceeb;\n}\n\n.location-info.disabled .location-name {\n  color: #666;\n}\n\n.location-info.disabled .location-name:hover {\n  color: #666;\n}\n\n</style>\n\n"], "mappings": ";;;;AA0bA,OAAAA,UAAA;AACA,OAAAC,QAAA;AACA,OAAAC,MAAA;AACA,SAAAC,kBAAA,EAAAC,mBAAA,EAAAC,kBAAA;AACA,SAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAR,UAAA;IACAC;EACA;EACAQ,KAAA;IACA;MACAC,eAAA;MAAA;MACAC,aAAA;QACAJ,IAAA;QACAK,MAAA;QACAC,UAAA;QACAC,MAAA;QACAC,IAAA;QACAC,SAAA;QACAC,EAAA;QACAC,KAAA;QACAC,EAAA;QACAC,KAAA;QACAC,KAAA;QACAC,GAAA;QACAC,WAAA;QACAC,eAAA;QACAC,YAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACAC,OAAA,GACA;QACAzB,IAAA;QACA0B,IAAA;QACAC,WAAA;QACAb,KAAA;QACAT,MAAA;QACAuB,QAAA;MACA,GACA;QACA5B,IAAA;QACA0B,IAAA;QACAC,WAAA;QACAb,KAAA;QACAT,MAAA;QACAuB,QAAA;MACA,GACA;QACA5B,IAAA;QACA0B,IAAA;QACAC,WAAA;QACAb,KAAA;QACAT,MAAA;QACAuB,QAAA;MACA,GACA;QACA5B,IAAA;QACA0B,IAAA;QACAC,WAAA;QACAb,KAAA;QACAT,MAAA;QACAuB,QAAA;MACA,EACA;MACAC,aAAA,GACA;QAAA7B,IAAA;QAAA8B,MAAA;QAAAC,KAAA;MAAA,GACA;QAAA/B,IAAA;QAAA8B,MAAA;QAAAC,KAAA;MAAA,GACA;QAAA/B,IAAA;QAAA8B,MAAA;QAAAC,KAAA;MAAA,GACA;QAAA/B,IAAA;QAAA8B,MAAA;QAAAC,KAAA;MAAA,EACA;MACAC,aAAA,GACA;QAAAhC,IAAA;QAAAK,MAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,MAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,MAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,MAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,MAAA;MAAA,EACA;MAEA;MACA4B,gBAAA;MAEA;MACAC,eAAA;MACAC,cAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MACA,YAAAlC,aAAA,CAAAM,EAAA,QAAAN,aAAA,CAAAO,KAAA;IACA;IACA4B,UAAA;MACA,YAAAnC,aAAA,CAAAQ,EAAA,QAAAR,aAAA,CAAAS,KAAA;IACA;IACA2B,WAAA;MACA,YAAApC,aAAA,CAAAW,GAAA,QAAAX,aAAA,CAAAY,WAAA;IACA;IAEA;IACAyB,mBAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,kBAAA;IACA;IAEAI,SAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAE,OAAA,CAAAC,MAAA;IACA;IAEAC,QAAA;MACA,YAAAN,MAAA,CAAAO,OAAA;IACA;IAEA;IACAC,oBAAA;MACA;QACA,MAAAC,eAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAO,eAAA;QACA,OAAAA,eAAA,CAAAC,IAAA;MACA,SAAAC,KAAA;QACA1D,MAAA,CAAA0D,KAAA,wBAAAA,KAAA;QACA;MACA;IACA;IAEA;IACAC,wBAAA;MACA;QACA,MAAAH,eAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAO,eAAA;QACA,OAAAA,eAAA,CAAAI,QAAA;MACA,SAAAF,KAAA;QACA1D,MAAA,CAAA0D,KAAA,uBAAAA,KAAA;QACA;MACA;IACA;IAEA;IACAG,YAAA;MACA,MAAAC,QAAA;MAEA;QACA;QACA,SAAAP,mBAAA,IAAAQ,KAAA,CAAAC,OAAA,MAAAT,mBAAA;UACA,KAAAA,mBAAA,CAAAU,OAAA,CAAAC,GAAA;YACAJ,QAAA,CAAAK,IAAA;cACA,GAAAD,GAAA;cACAnC,IAAA;YACA;UACA;QACA;;QAEA;QACA,SAAA4B,uBAAA,IAAAI,KAAA,CAAAC,OAAA,MAAAL,uBAAA;UACA,KAAAA,uBAAA,CAAAM,OAAA,CAAAG,OAAA;YACAN,QAAA,CAAAK,IAAA;cACA,GAAAC,OAAA;cACArC,IAAA;YACA;UACA;QACA;MACA,SAAA2B,KAAA;QACA1D,MAAA,CAAA0D,KAAA,qBAAAA,KAAA;MACA;MAEA,OAAAI,QAAA;IACA;EACA;EACAO,OAAA;IACAC,oBAAA;MACAtE,MAAA,CAAAuE,IAAA;;MAEA;MACA,KAAAxB,MAAA,CAAAyB,QAAA,mCAAAC,IAAA,CAAAC,SAAA;QACA,IAAAA,SAAA;UACA,KAAA3B,MAAA,CAAAyB,QAAA,kCAAAC,IAAA;YACA;YACAzE,MAAA,CAAA2E,KAAA;;YAEA;YACA,KAAAC,iBAAA,GAAAH,IAAA;cACA,KAAAI,OAAA,CAAAV,IAAA;YACA;UACA,GAAAW,KAAA,CAAApB,KAAA;YACA1D,MAAA,CAAA0D,KAAA,qBAAAA,KAAA;YACAtD,WAAA;YACA;YACA,KAAAyE,OAAA,CAAAV,IAAA;UACA;QACA;UACA/D,WAAA;UACA,KAAAyE,OAAA,CAAAV,IAAA;QACA;MACA;IACA;IACAY,UAAAb,GAAA;MACAlE,MAAA,CAAAuE,IAAA,aAAAL,GAAA,CAAA7D,IAAA;MAEA,IAAA6D,GAAA,CAAAnC,IAAA;QACA;QACA,KAAAiD,oBAAA,CAAAd,GAAA;MACA,WAAAA,GAAA,CAAAnC,IAAA;QACA;QACA,KAAAkD,sBAAA,CAAAf,GAAA;MACA;IACA;IAEAc,qBAAAd,GAAA;MACA;MACA,QAAAA,GAAA,CAAA7D,IAAA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA8E,UAAA;YACA,KAAAN,OAAA,CAAAV,IAAA;UACA;UACA;QACA;UACA,KAAAe,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA8E,UAAA;YACA,KAAAN,OAAA,CAAAV,IAAA;UACA;UACA;QACA;UACA,KAAAe,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;UACA;QACA;UACA,KAAA6E,SAAA,KAAAhB,GAAA,CAAA7D,IAAA;MACA;IACA;IAEA4E,uBAAAb,OAAA;MACA;MACA,IAAAgB,gBAAA;MAEA,QAAAhB,OAAA,CAAA/D,IAAA;QACA;UACA+E,gBAAA,QAAAhB,OAAA,CAAA/D,IAAA;UACA;QACA;UACA+E,gBAAA,MAAAhB,OAAA,CAAA/D,IAAA;UACA;QACA;UACA+E,gBAAA,SAAAhB,OAAA,CAAA/D,IAAA;UACA;QACA;UACA+E,gBAAA,MAAAhB,OAAA,CAAA/D,IAAA;UACA;QACA;UACA+E,gBAAA,QAAAhB,OAAA,CAAA/D,IAAA;UACA;QACA;UACA+E,gBAAA,QAAAhB,OAAA,CAAA/D,IAAA;UACA;QACA;UACA+E,gBAAA,SAAAhB,OAAA,CAAA/D,IAAA;MACA;MAEA,KAAA6E,SAAA,IAAAE,gBAAA,MAAAhB,OAAA,CAAAjD,KAAA;;MAEA;MACA,MAAAkE,gBAAA,QAAAtC,MAAA,CAAAO,OAAA;MACA,MAAAE,eAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAO,eAAA;MAEA,KAAA6B,gBAAA,KAAAA,gBAAA,CAAAC,EAAA;QACA,KAAAJ,SAAA;QACA;MACA;MAEAC,UAAA;QACA;QACA,KAAAN,OAAA,CAAAV,IAAA;UACAoB,IAAA;UACAC,KAAA;YACAC,WAAA,EAAAJ,gBAAA,CAAAC,EAAA;YACAI,SAAA,EAAAtB,OAAA,CAAAkB,EAAA;YACAK,UAAA,GAAAnC,eAAA,aAAAA,eAAA,uBAAAA,eAAA,CAAA8B,EAAA;UACA;QACA;MACA;IACA;IAEA;IACAM,gBAAAC,QAAA;MACA;QACA;QACA,MAAA/B,QAAA,QAAAD,WAAA;QACA,OAAAC,QAAA,CAAA+B,QAAA;MACA,SAAAnC,KAAA;QACA1D,MAAA,CAAA0D,KAAA,oBAAAA,KAAA;QACA;MACA;IACA;IAEA;IACAoC,mBAAAC,MAAA;MACA,IAAAA,MAAA,CAAAhE,IAAA;QACA,KAAAiD,oBAAA,CAAAe,MAAA;MACA,WAAAA,MAAA,CAAAhE,IAAA;QACA,KAAAkD,sBAAA,CAAAc,MAAA;MACA;IACA;IAEA;IACAC,kBAAAH,QAAA;MACA;QACA;QACA,MAAAI,SAAA,QAAAnD,kBAAA;QACA,OAAAmD,SAAA,CAAAJ,QAAA;MACA,SAAAnC,KAAA;QACA1D,MAAA,CAAA0D,KAAA,oBAAAA,KAAA;QACA;MACA;IACA;IACAwC,eAAA/D,MAAA;MACAnC,MAAA,CAAAuE,IAAA,SAAApC,MAAA;MACA,SAAA3B,eAAA,KAAA2B,MAAA;QACA;QACA;MACA;QACA;QACA,KAAA3B,eAAA,GAAA2B,MAAA;MACA;IACA;IACAgE,qBAAA;MACA,aAAA3F,eAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA;IACA4F,eAAAC,QAAA;MACArG,MAAA,CAAAuE,IAAA,SAAA8B,QAAA;MACA,QAAAA,QAAA;QACA;UACA,KAAAxB,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;MACA;IACA;IACA;IACA,MAAAmC,uBAAAC,QAAA;MACAvG,MAAA,CAAAuE,IAAA,oBAAAgC,QAAA;;MAEA;MACA,MAAAlB,gBAAA,QAAAtC,MAAA,CAAAO,OAAA;MACA,KAAA+B,gBAAA,KAAAA,gBAAA,CAAAC,EAAA;QACAtF,MAAA,CAAA0D,KAAA;QACA,KAAAwB,SAAA;QACA;MACA;;MAEA;MACA,SAAAhC,QAAA;QACA,KAAAgC,SAAA;QACA;MACA;MAEA;QACA,WAAAnC,MAAA,CAAAyB,QAAA,uBAAA+B,QAAA,CAAAjB,EAAA;QACA,KAAAJ,SAAA,SAAAqB,QAAA,CAAAlG,IAAA;;QAEA;QACA,WAAAmG,oBAAA;MACA,SAAA9C,KAAA;QACA1D,MAAA,CAAA0D,KAAA,iBAAAA,KAAA;QACA,KAAAwB,SAAA,YAAAxB,KAAA,CAAA+C,OAAA;MACA;IACA;IAMA,MAAAD,qBAAA;MACA;QACA,MAAAnB,gBAAA,QAAAtC,MAAA,CAAAO,OAAA;QACA,MAAAE,eAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAO,eAAA;QAEA,IAAA6B,gBAAA,IAAA7B,eAAA,CAAA8B,EAAA;UAAA,IAAAoB,qBAAA,EAAAC,qBAAA;UACA3G,MAAA,CAAA2E,KAAA;YACA4B,QAAA,EAAA/C,eAAA,CAAAnD,IAAA;YACAoD,IAAA,IAAAiD,qBAAA,GAAAlD,eAAA,CAAAC,IAAA,cAAAiD,qBAAA,uBAAAA,qBAAA,CAAAE,MAAA;YACAhD,QAAA,IAAA+C,qBAAA,GAAAnD,eAAA,CAAAI,QAAA,cAAA+C,qBAAA,uBAAAA,qBAAA,CAAAC,MAAA;UACA;;UAEA;UACA,KAAAC,YAAA;QACA;MACA,SAAAnD,KAAA;QACA1D,MAAA,CAAA0D,KAAA,qBAAAA,KAAA;MACA;IACA;IAMA;IACAoD,eAAAP,QAAA;MACAvG,MAAA,CAAAuE,IAAA,sBAAAgC,QAAA;;MAEA;MACA,WAAAA,QAAA;QACA,MAAAQ,WAAA;UACAzB,EAAA,EAAAiB,QAAA;UACAlG,IAAA,EAAAkG,QAAA;UACAxE,IAAA,EAAAwE;QACA;QACA,KAAAD,sBAAA,CAAAS,WAAA;MACA;QACA,KAAAT,sBAAA,CAAAC,QAAA;MACA;IACA;IACA;IACAS,aAAAC,IAAA;MACAjH,MAAA,CAAAuE,IAAA,SAAA0C,IAAA;MACA;MACA,QAAAA,IAAA;QACA;UACA,KAAApC,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;QACA;UACA,KAAAU,OAAA,CAAAV,IAAA;UACA;MACA;IACA;IACA;IACA+C,kBAAAC,WAAA;MACAnH,MAAA,CAAAuE,IAAA,oBAAA4C,WAAA;IACA;IAEAC,sBAAAC,WAAA;MACArH,MAAA,CAAAuE,IAAA,oBAAA8C,WAAA;IACA;IAEAnC,UAAAuB,OAAA,EAAA1E,IAAA;MACA;MACA/B,MAAA,CAAAuE,IAAA,WAAAxC,IAAA,MAAA0E,OAAA;IACA;IAEA;IACAa,eAAAvB,MAAA,EAAAhE,IAAA;MACA,KAAAS,cAAA,GAAAuD,MAAA;MACA,KAAAtD,kBAAA,GAAAV,IAAA;MACA,KAAAQ,eAAA;MACAvC,MAAA,CAAAuE,IAAA,aAAAxC,IAAA,OAAAgE,MAAA,CAAA1F,IAAA;IACA;IAEAkH,iBAAA;MACA,KAAAhF,eAAA;MACA,KAAAC,cAAA;MACA,KAAAC,kBAAA;IACA;IAEA;IACA+E,eAAAzF,IAAA;MACA,MAAA0F,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA1F,IAAA,KAAAA,IAAA;IACA;IAEA2F,mBAAA3F,IAAA;MACA,MAAA0F,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA1F,IAAA,KAAAA,IAAA;IACA;IAEA4F,eAAAC,OAAA;MACA,MAAAC,UAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,UAAA,CAAAD,OAAA,KAAAA,OAAA;IACA;IAEAE,eAAAC,OAAA;MACA,MAAAC,UAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,UAAA,CAAAD,OAAA,KAAAA,OAAA;IACA;IAEAE,YAAAC,IAAA;MACA,MAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA,KAAAA,IAAA;IACA;IAEAE,iBAAArG,IAAA;MACA,IAAAA,IAAA;QACA;MACA;QACA;MACA;IACA;IACA,MAAA6C,kBAAA;MACA;QACA;QACA,MAAAyD,OAAA,GAAAnI,mBAAA;QACA,KAAAmI,OAAA,KAAAA,OAAA,CAAA/C,EAAA;UACAtF,MAAA,CAAA0D,KAAA;UACAtD,WAAA;UACA,KAAAyE,OAAA,CAAAV,IAAA;UACA;QACA;;QAEA;QACA,WAAApB,MAAA,CAAAyB,QAAA,8BAAA6D,OAAA;QACArI,MAAA,CAAA2E,KAAA,0BAAA0D,OAAA;;QAEA;QACA;UACA,MAAAC,cAAA,SAAArI,kBAAA,CAAAoI,OAAA,CAAA/C,EAAA;UACA,IAAAgD,cAAA,IAAAA,cAAA,CAAA/H,IAAA;YACA,MAAAgI,MAAA,GAAAD,cAAA,CAAA/H,IAAA;;YAEA;YACA,MAAAiI,aAAA;cACA;cACA;cACA;YACA;;YAEA;YACA,KAAA/H,aAAA;cACA,QAAAA,aAAA;cACAJ,IAAA,EAAAkI,MAAA,CAAAlI,IAAA,IAAAgI,OAAA,CAAAhI,IAAA;cACAK,MAAA,EAAA6H,MAAA,CAAA7H,MAAA,0BAAA6H,MAAA,CAAAE,MAAA;cACA9H,UAAA,EAAA6H,aAAA,CAAAD,MAAA,CAAA5H,UAAA,KAAA4H,MAAA,CAAA5H,UAAA;cACAE,IAAA,EAAA0H,MAAA,CAAA1H,IAAA;cACAD,MAAA,EAAA2H,MAAA,CAAA3H,MAAA;cACAO,KAAA,EAAAoH,MAAA,CAAApH,KAAA;YACA;UACA;QACA,SAAAuH,WAAA;UACA1I,MAAA,CAAA0D,KAAA,qBAAAgF,WAAA;QACA;;QAEA;QACA;UACA,MAAAC,cAAA,SAAAxI,kBAAA,CAAAkI,OAAA,CAAA/C,EAAA;UACA,IAAAqD,cAAA,IAAAA,cAAA,CAAApI,IAAA;YACA,MAAAqI,MAAA,GAAAD,cAAA,CAAApI,IAAA;;YAEA;YACA,KAAAE,aAAA;cACA,QAAAA,aAAA;cACAM,EAAA,EAAA8H,QAAA,CAAAD,MAAA,CAAA7H,EAAA;cACAC,KAAA,EAAA6H,QAAA,CAAAD,MAAA,CAAAE,MAAA;cACA7H,EAAA,EAAA4H,QAAA,CAAAD,MAAA,CAAA3H,EAAA;cACAC,KAAA,EAAA2H,QAAA,CAAAD,MAAA,CAAAG,MAAA;cACA3H,GAAA,EAAAyH,QAAA,CAAAD,MAAA,CAAAxH,GAAA;cACAC,WAAA,EAAAwH,QAAA,CAAAD,MAAA,CAAAI,YAAA;cACA1H,eAAA,EAAAuH,QAAA,CAAAD,MAAA,CAAAK,gBAAA;cACA1H,YAAA,EAAAsH,QAAA,CAAAD,MAAA,CAAArH,YAAA;cACAC,YAAA,EAAAqH,QAAA,CAAAD,MAAA,CAAApH,YAAA;cACAC,QAAA,EAAAoH,QAAA,CAAAD,MAAA,CAAAnH,QAAA;cACAC,OAAA,EAAAmH,QAAA,CAAAD,MAAA,CAAAlH,OAAA;cACAC,MAAA,EAAAkH,QAAA,CAAAD,MAAA,CAAAjH,MAAA;cACAC,OAAA,EAAAiH,QAAA,CAAAD,MAAA,CAAAhH,OAAA;cACAC,KAAA,EAAAgH,QAAA,CAAAD,MAAA,CAAA/G,KAAA;YACA;;YAEA;YACA7B,MAAA,CAAA2E,KAAA,yBAAAlE,aAAA;UACA;QACA,SAAAyI,WAAA;UACAlJ,MAAA,CAAA0D,KAAA,qBAAAwF,WAAA;QACA;MACA,SAAAxF,KAAA;QACA1D,MAAA,CAAA0D,KAAA,qBAAAA,KAAA;QACAtD,WAAA;MACA;;MAEA;MACA,OAAA+I,OAAA,CAAAC,OAAA;IACA;EACA;EACA,MAAAC,QAAA;IACA;MACA;MACA,WAAAzE,iBAAA;;MAEA;MACA,MAAAS,gBAAA,QAAAtC,MAAA,CAAAO,OAAA;MACA,MAAAgG,eAAA,QAAAvG,MAAA,CAAAC,KAAA,CAAAuG,IAAA,CAAAD,eAAA;MAEAtJ,MAAA,CAAA2E,KAAA;QACA2E,eAAA;QACAE,YAAA,IAAAnE,gBAAA;QACAI,WAAA,EAAAJ,gBAAA,aAAAA,gBAAA,uBAAAA,gBAAA,CAAAC;MACA;MAEA,IAAAgE,eAAA,IAAAjE,gBAAA,IAAAA,gBAAA,CAAAC,EAAA;QACA;QACA;UACA,WAAAvC,MAAA,CAAAyB,QAAA;UACAxE,MAAA,CAAAuE,IAAA;QACA,SAAAb,KAAA;UACA1D,MAAA,CAAA0D,KAAA,sBAAAA,KAAA;UACA;QACA;MACA;QACA1D,MAAA,CAAAyJ,IAAA;MACA;IACA,SAAA/F,KAAA;MACA1D,MAAA,CAAA0D,KAAA,wBAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}