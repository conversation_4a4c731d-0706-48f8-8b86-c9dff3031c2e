{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\questService.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\api\\services\\questService.js", "mtime": 1749707360036}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["get", "post", "logger", "questService", "getQuests", "characterId", "params", "debug", "loading", "loadingText", "then", "res", "data", "catch", "error", "getQuestDetails", "questId", "cacheService", "set", "CACHE_KEYS", "QUEST_DETAILS", "acceptQuest", "clearCache", "completeQuest", "abandonQuest", "submitQuestItems", "items", "getAvailableQuests", "getQuestProgress", "getQuestRewards", "getDailyQuests", "removeByPrefix", "QUEST_LIST"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/api/services/questService.js"], "sourcesContent": ["/**\n * 任务系统API服务\n * 提供任务相关的接口调用\n */\nimport { get, post } from '../request.js';\nimport logger from '../../utils/logger.js';\n\n/**\n * 任务服务\n */\nconst questService = {\n    /**\n     * 获取角色任务列表\n     * @param {string} characterId - 角色ID\n     * @param {Object} params - 查询参数\n     * @param {string} params.status - 任务状态筛选 (available/in_progress/completed/finished)\n     * @param {string} params.type - 任务类型筛选\n     * @param {number} params.page - 页码\n     * @param {number} params.pageSize - 每页数量\n     * @returns {Promise<Object>} - 任务列表\n     */\n    getQuests(characterId, params = {}) {\n        logger.debug('[QuestService] 获取任务列表, characterId:', characterId, 'params:', params);\n        \n        return get(`/characters/${characterId}/quests`, params, {\n            loading: true,\n            loadingText: '加载任务列表...'\n        }).then(res => {\n            logger.debug('[QuestService] 任务列表响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 获取任务列表失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取任务详情\n     * @param {string} questId - 任务ID\n     * @returns {Promise<Object>} - 任务详情\n     */\n    getQuestDetails(questId) {\n        logger.debug('[QuestService] 获取任务详情, questId:', questId);\n        \n        return get(`/quests/${questId}`, {}, {\n            loading: true,\n            loadingText: '加载任务详情...'\n        }).then(res => {\n            logger.debug('[QuestService] 任务详情响应:', res);\n            // 缓存结果\n            cacheService.set(`${CACHE_KEYS.QUEST_DETAILS}_${questId}`, res.data, 600); // 缓存10分钟\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 获取任务详情失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 接受任务\n     * @param {string} characterId - 角色ID\n     * @param {string} questId - 任务ID\n     * @returns {Promise<Object>} - 接受结果\n     */\n    acceptQuest(characterId, questId) {\n        logger.debug('[QuestService] 接受任务, characterId:', characterId, 'questId:', questId);\n        \n        return post(`/characters/${characterId}/quests/${questId}/accept`, {}, {\n            loading: true,\n            loadingText: '接受任务中...'\n        }).then(res => {\n            logger.debug('[QuestService] 接受任务响应:', res);\n            // 清除任务列表缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 接受任务失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 完成任务\n     * @param {string} characterId - 角色ID\n     * @param {string} questId - 任务ID\n     * @returns {Promise<Object>} - 完成结果\n     */\n    completeQuest(characterId, questId) {\n        logger.debug('[QuestService] 完成任务, characterId:', characterId, 'questId:', questId);\n        \n        return post(`/characters/${characterId}/quests/${questId}/complete`, {}, {\n            loading: true,\n            loadingText: '完成任务中...'\n        }).then(res => {\n            logger.debug('[QuestService] 完成任务响应:', res);\n            // 清除任务列表缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 完成任务失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 放弃任务\n     * @param {string} characterId - 角色ID\n     * @param {string} questId - 任务ID\n     * @returns {Promise<Object>} - 放弃结果\n     */\n    abandonQuest(characterId, questId) {\n        logger.debug('[QuestService] 放弃任务, characterId:', characterId, 'questId:', questId);\n        \n        return post(`/characters/${characterId}/quests/${questId}/abandon`, {}, {\n            loading: true,\n            loadingText: '放弃任务中...'\n        }).then(res => {\n            logger.debug('[QuestService] 放弃任务响应:', res);\n            // 清除任务列表缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 放弃任务失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 提交任务物品\n     * @param {string} characterId - 角色ID\n     * @param {string} questId - 任务ID\n     * @param {Array} items - 提交的物品列表\n     * @returns {Promise<Object>} - 提交结果\n     */\n    submitQuestItems(characterId, questId, items) {\n        logger.debug('[QuestService] 提交任务物品, characterId:', characterId, 'questId:', questId, 'items:', items);\n        \n        return post(`/characters/${characterId}/quests/${questId}/submit-items`, {\n            items\n        }, {\n            loading: true,\n            loadingText: '提交物品中...'\n        }).then(res => {\n            logger.debug('[QuestService] 提交任务物品响应:', res);\n            // 清除任务列表缓存\n            this.clearCache(characterId);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 提交任务物品失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取可接受的任务列表\n     * @param {string} characterId - 角色ID\n     * @param {Object} params - 查询参数\n     * @returns {Promise<Object>} - 可接受任务列表\n     */\n    getAvailableQuests(characterId, params = {}) {\n        logger.debug('[QuestService] 获取可接受任务, characterId:', characterId, 'params:', params);\n        \n        return get(`/characters/${characterId}/quests/available`, params, {\n            loading: true,\n            loadingText: '加载可接受任务...'\n        }).then(res => {\n            logger.debug('[QuestService] 可接受任务响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 获取可接受任务失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取任务进度\n     * @param {string} characterId - 角色ID\n     * @param {string} questId - 任务ID\n     * @returns {Promise<Object>} - 任务进度\n     */\n    getQuestProgress(characterId, questId) {\n        logger.debug('[QuestService] 获取任务进度, characterId:', characterId, 'questId:', questId);\n        \n        return get(`/characters/${characterId}/quests/${questId}/progress`, {}, {\n            loading: false\n        }).then(res => {\n            logger.debug('[QuestService] 任务进度响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 获取任务进度失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取任务奖励预览\n     * @param {string} questId - 任务ID\n     * @returns {Promise<Object>} - 任务奖励\n     */\n    getQuestRewards(questId) {\n        logger.debug('[QuestService] 获取任务奖励, questId:', questId);\n        \n        return get(`/quests/${questId}/rewards`, {}, {\n            loading: false\n        }).then(res => {\n            logger.debug('[QuestService] 任务奖励响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 获取任务奖励失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 获取每日任务\n     * @param {string} characterId - 角色ID\n     * @returns {Promise<Object>} - 每日任务列表\n     */\n    getDailyQuests(characterId) {\n        logger.debug('[QuestService] 获取每日任务, characterId:', characterId);\n        \n        return get(`/characters/${characterId}/quests/daily`, {}, {\n            loading: true,\n            loadingText: '加载每日任务...'\n        }).then(res => {\n            logger.debug('[QuestService] 每日任务响应:', res);\n            return res.data;\n        }).catch(error => {\n            logger.error('[QuestService] 获取每日任务失败:', error);\n            throw error;\n        });\n    },\n\n    /**\n     * 清除任务相关的缓存\n     * @param {string} characterId - 角色ID\n     */\n    clearCache(characterId) {\n        if (characterId) {\n            cacheService.removeByPrefix(`${CACHE_KEYS.QUEST_LIST}_${characterId}`);\n        } else {\n            // 清除所有任务缓存\n            cacheService.removeByPrefix(CACHE_KEYS.QUEST_LIST);\n            cacheService.removeByPrefix(CACHE_KEYS.QUEST_DETAILS);\n        }\n    }\n};\n\nexport default questService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,GAAG,EAAEC,IAAI,QAAQ,eAAe;AACzC,OAAOC,MAAM,MAAM,uBAAuB;;AAE1C;AACA;AACA;AACA,MAAMC,YAAY,GAAG;EACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,WAAW,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IAChCJ,MAAM,CAACK,KAAK,CAAC,qCAAqC,EAAEF,WAAW,EAAE,SAAS,EAAEC,MAAM,CAAC;IAEnF,OAAON,GAAG,CAAC,eAAeK,WAAW,SAAS,EAAEC,MAAM,EAAE;MACpDE,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIC,eAAeA,CAACC,OAAO,EAAE;IACrBd,MAAM,CAACK,KAAK,CAAC,iCAAiC,EAAES,OAAO,CAAC;IAExD,OAAOhB,GAAG,CAAC,WAAWgB,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE;MACjCR,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C;MACAM,YAAY,CAACC,GAAG,CAAC,GAAGC,UAAU,CAACC,aAAa,IAAIJ,OAAO,EAAE,EAAEL,GAAG,CAACC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;MAC3E,OAAOD,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIO,WAAWA,CAAChB,WAAW,EAAEW,OAAO,EAAE;IAC9Bd,MAAM,CAACK,KAAK,CAAC,mCAAmC,EAAEF,WAAW,EAAE,UAAU,EAAEW,OAAO,CAAC;IAEnF,OAAOf,IAAI,CAAC,eAAeI,WAAW,WAAWW,OAAO,SAAS,EAAE,CAAC,CAAC,EAAE;MACnER,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C;MACA,IAAI,CAACW,UAAU,CAACjB,WAAW,CAAC;MAC5B,OAAOM,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIS,aAAaA,CAAClB,WAAW,EAAEW,OAAO,EAAE;IAChCd,MAAM,CAACK,KAAK,CAAC,mCAAmC,EAAEF,WAAW,EAAE,UAAU,EAAEW,OAAO,CAAC;IAEnF,OAAOf,IAAI,CAAC,eAAeI,WAAW,WAAWW,OAAO,WAAW,EAAE,CAAC,CAAC,EAAE;MACrER,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C;MACA,IAAI,CAACW,UAAU,CAACjB,WAAW,CAAC;MAC5B,OAAOM,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIU,YAAYA,CAACnB,WAAW,EAAEW,OAAO,EAAE;IAC/Bd,MAAM,CAACK,KAAK,CAAC,mCAAmC,EAAEF,WAAW,EAAE,UAAU,EAAEW,OAAO,CAAC;IAEnF,OAAOf,IAAI,CAAC,eAAeI,WAAW,WAAWW,OAAO,UAAU,EAAE,CAAC,CAAC,EAAE;MACpER,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C;MACA,IAAI,CAACW,UAAU,CAACjB,WAAW,CAAC;MAC5B,OAAOM,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIW,gBAAgBA,CAACpB,WAAW,EAAEW,OAAO,EAAEU,KAAK,EAAE;IAC1CxB,MAAM,CAACK,KAAK,CAAC,qCAAqC,EAAEF,WAAW,EAAE,UAAU,EAAEW,OAAO,EAAE,QAAQ,EAAEU,KAAK,CAAC;IAEtG,OAAOzB,IAAI,CAAC,eAAeI,WAAW,WAAWW,OAAO,eAAe,EAAE;MACrEU;IACJ,CAAC,EAAE;MACClB,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,0BAA0B,EAAEI,GAAG,CAAC;MAC7C;MACA,IAAI,CAACW,UAAU,CAACjB,WAAW,CAAC;MAC5B,OAAOM,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIa,kBAAkBA,CAACtB,WAAW,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IACzCJ,MAAM,CAACK,KAAK,CAAC,sCAAsC,EAAEF,WAAW,EAAE,SAAS,EAAEC,MAAM,CAAC;IAEpF,OAAON,GAAG,CAAC,eAAeK,WAAW,mBAAmB,EAAEC,MAAM,EAAE;MAC9DE,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,yBAAyB,EAAEI,GAAG,CAAC;MAC5C,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIc,gBAAgBA,CAACvB,WAAW,EAAEW,OAAO,EAAE;IACnCd,MAAM,CAACK,KAAK,CAAC,qCAAqC,EAAEF,WAAW,EAAE,UAAU,EAAEW,OAAO,CAAC;IAErF,OAAOhB,GAAG,CAAC,eAAeK,WAAW,WAAWW,OAAO,WAAW,EAAE,CAAC,CAAC,EAAE;MACpER,OAAO,EAAE;IACb,CAAC,CAAC,CAACE,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIe,eAAeA,CAACb,OAAO,EAAE;IACrBd,MAAM,CAACK,KAAK,CAAC,iCAAiC,EAAES,OAAO,CAAC;IAExD,OAAOhB,GAAG,CAAC,WAAWgB,OAAO,UAAU,EAAE,CAAC,CAAC,EAAE;MACzCR,OAAO,EAAE;IACb,CAAC,CAAC,CAACE,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIgB,cAAcA,CAACzB,WAAW,EAAE;IACxBH,MAAM,CAACK,KAAK,CAAC,qCAAqC,EAAEF,WAAW,CAAC;IAEhE,OAAOL,GAAG,CAAC,eAAeK,WAAW,eAAe,EAAE,CAAC,CAAC,EAAE;MACtDG,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACXT,MAAM,CAACK,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;MAC3C,OAAOA,GAAG,CAACC,IAAI;IACnB,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;MACdZ,MAAM,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;EACIQ,UAAUA,CAACjB,WAAW,EAAE;IACpB,IAAIA,WAAW,EAAE;MACbY,YAAY,CAACc,cAAc,CAAC,GAAGZ,UAAU,CAACa,UAAU,IAAI3B,WAAW,EAAE,CAAC;IAC1E,CAAC,MAAM;MACH;MACAY,YAAY,CAACc,cAAc,CAACZ,UAAU,CAACa,UAAU,CAAC;MAClDf,YAAY,CAACc,cAAc,CAACZ,UAAU,CAACC,aAAa,CAAC;IACzD;EACJ;AACJ,CAAC;AAED,eAAejB,YAAY", "ignoreList": []}]}