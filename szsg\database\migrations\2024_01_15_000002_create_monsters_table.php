<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monsters', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50);
            $table->string('title', 100)->nullable();
            $table->text('description')->nullable();
            $table->integer('level')->default(1);
            $table->enum('type', ['beast', 'demon', 'spirit', 'undead', 'dragon', 'immortal', 'elemental', 'other'])->default('other');
            $table->enum('element', ['none', 'fire', 'water', 'earth', 'wind', 'thunder', 'ice', 'light', 'dark'])->default('none');
            $table->enum('size', ['tiny', 'small', 'medium', 'large', 'huge', 'giant'])->default('medium');
            $table->integer('threat_level')->default(1);
            $table->string('avatar', 255)->nullable();
            
            // 基础属性
            $table->integer('max_health')->default(100);
            $table->integer('max_mp')->default(50);
            $table->integer('attack')->default(10);
            $table->integer('defense')->default(5);
            $table->integer('speed')->default(10);
            $table->integer('constitution')->default(10);
            $table->integer('intelligence')->default(10);
            $table->integer('strength')->default(10);
            $table->integer('agility')->default(10);
            
            // 奖励设置
            $table->integer('exp_reward')->default(10);
            $table->integer('gold_reward')->default(5);
            $table->json('item_drops')->nullable(); // 掉落物品配置
            
            $table->timestamps();

            $table->index(['level', 'type']);
            $table->index('threat_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monsters');
    }
};
