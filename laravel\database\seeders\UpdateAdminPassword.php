<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UpdateAdminPassword extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('admins')
            ->where('username', 'admin')
            ->update([
                'password' => Hash::make('123456'),
            ]);

        $this->command->info('管理员密码已重置!');
        $this->command->info('用户名: admin');
        $this->command->info('新密码: 123456');
    }
}
