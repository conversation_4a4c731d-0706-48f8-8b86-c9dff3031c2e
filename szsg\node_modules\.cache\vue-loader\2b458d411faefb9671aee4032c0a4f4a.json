{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue?vue&type=template&id=9f58988a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue", "mtime": 1749890706309}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJnYW1lLWxheW91dC1jb250YWluZXIiCiAgfSwgW19jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImJhY2tncm91bmQtZWZmZWN0cyIKICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiZmxvYXRpbmctcGFydGljbGVzIgogIH0sIF92bS5fbCgyMCwgZnVuY3Rpb24gKGkpIHsKICAgIHJldHVybiBfYygiZGl2IiwgewogICAgICBrZXk6IGksCiAgICAgIHN0YXRpY0NsYXNzOiAicGFydGljbGUiLAogICAgICBzdHlsZTogX3ZtLmdldFBhcnRpY2xlU3R5bGUoaSkKICAgIH0pOwogIH0pLCAwKSwgX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiYmFja2dyb3VuZC1ncmFkaWVudCIKICB9KV0pLCBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJ0b3AtYm9yZGVyLWZyYW1lIiwKICAgIHN0eWxlOiB7CiAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybCgke192bS50b3BCb3JkZXJJbWFnZX0pYAogICAgfQogIH0sIFtfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJ0b3AtYm9yZGVyLW92ZXJsYXkiCiAgfSwgW19jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImJvcmRlci1jb250ZW50IgogIH0sIFtfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJwYWdlLXRpdGxlLWNvbnRhaW5lciIKICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAicGFnZS10aXRsZSIKICB9LCBbX3ZtLl92KF92bS5fcyhfdm0ucGFnZVRpdGxlKSldKSwgX3ZtLnNob3dMb2NhdGlvbk5hbWUgJiYgX3ZtLmN1cnJlbnRMb2NhdGlvbk5hbWUgPyBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJsb2NhdGlvbi1uYW1lIgogIH0sIFtfYygic3BhbiIsIHsKICAgIHN0YXRpY0NsYXNzOiAibG9jYXRpb24tdGV4dCIKICB9LCBbX3ZtLl92KF92bS5fcyhfdm0uY3VycmVudExvY2F0aW9uTmFtZSkpXSldKSA6IF92bS5fZSgpXSldKV0pXSksIF92bS5fbSgwKSwgX3ZtLl9tKDEpLCBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJtYWluLWNvbnRlbnQtYXJlYSIKICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiY29udGVudC13cmFwcGVyIgogIH0sIFtfdm0uX3QoImRlZmF1bHQiKV0sIDIpXSksIF92bS5fbSgyKSwgX3ZtLmlzTG9hZGluZyA/IF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImxvYWRpbmctb3ZlcmxheSIKICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAibG9hZGluZy1jb250ZW50IgogIH0sIFtfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJsb2FkaW5nLXNwaW5uZXIiCiAgfSksIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImxvYWRpbmctdGV4dCIKICB9LCBbX3ZtLl92KF92bS5fcyhfdm0ubG9hZGluZ1RleHQgfHwgIuWKoOi9veS4rS4uLiIpKV0pXSldKSA6IF92bS5fZSgpXSk7Cn07CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbZnVuY3Rpb24gKCkgewogIHZhciBfdm0gPSB0aGlzLAogICAgX2MgPSBfdm0uX3NlbGYuX2M7CiAgcmV0dXJuIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImxlZnQtYm9yZGVyLWZyYW1lIgogIH0sIFtfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJib3JkZXItcGF0dGVybiIKICB9KSwgX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiYm9yZGVyLWdsb3ciCiAgfSldKTsKfSwgZnVuY3Rpb24gKCkgewogIHZhciBfdm0gPSB0aGlzLAogICAgX2MgPSBfdm0uX3NlbGYuX2M7CiAgcmV0dXJuIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogInJpZ2h0LWJvcmRlci1mcmFtZSIKICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiYm9yZGVyLXBhdHRlcm4iCiAgfSksIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImJvcmRlci1nbG93IgogIH0pXSk7Cn0sIGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJib3R0b20tYm9yZGVyLWZyYW1lIgogIH0sIFtfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJmb290ZXItZGVjb3JhdGlvbiIKICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiZm9vdGVyLXBhdHRlcm4iCiAgfSksIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImZvb3Rlci10ZXh0IgogIH0sIFtfYygic3BhbiIsIHsKICAgIHN0YXRpY0NsYXNzOiAidmVyc2lvbi1pbmZvIgogIH0sIFtfdm0uX3YoIlZlcnNpb24gMS4wLjAiKV0pLCBfYygic3BhbiIsIHsKICAgIHN0YXRpY0NsYXNzOiAiY29weXJpZ2h0IgogIH0sIFtfdm0uX3YoIsKpIDIwMjUg56We5LmL6KW/5ri4IildKV0pXSldKTsKfV07CnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZTsKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfTs="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_l", "i", "key", "style", "getParticleStyle", "backgroundImage", "topBorderImage", "_v", "_s", "pageTitle", "showLocationName", "currentLocationName", "_e", "_m", "_t", "isLoading", "loadingText", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/layouts/GameLayout.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"game-layout-container\" }, [\n    _c(\"div\", { staticClass: \"background-effects\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"floating-particles\" },\n        _vm._l(20, function (i) {\n          return _c(\"div\", {\n            key: i,\n            staticClass: \"particle\",\n            style: _vm.getParticleStyle(i),\n          })\n        }),\n        0\n      ),\n      _c(\"div\", { staticClass: \"background-gradient\" }),\n    ]),\n    _c(\n      \"div\",\n      {\n        staticClass: \"top-border-frame\",\n        style: { backgroundImage: `url(${_vm.topBorderImage})` },\n      },\n      [\n        _c(\"div\", { staticClass: \"top-border-overlay\" }, [\n          _c(\"div\", { staticClass: \"border-content\" }, [\n            _c(\"div\", { staticClass: \"page-title-container\" }, [\n              _c(\"div\", { staticClass: \"page-title\" }, [\n                _vm._v(_vm._s(_vm.pageTitle)),\n              ]),\n              _vm.showLocationName && _vm.currentLocationName\n                ? _c(\"div\", { staticClass: \"location-name\" }, [\n                    _c(\"span\", { staticClass: \"location-text\" }, [\n                      _vm._v(_vm._s(_vm.currentLocationName)),\n                    ]),\n                  ])\n                : _vm._e(),\n            ]),\n          ]),\n        ]),\n      ]\n    ),\n    _vm._m(0),\n    _vm._m(1),\n    _c(\"div\", { staticClass: \"main-content-area\" }, [\n      _c(\"div\", { staticClass: \"content-wrapper\" }, [_vm._t(\"default\")], 2),\n    ]),\n    _vm._m(2),\n    _vm.isLoading\n      ? _c(\"div\", { staticClass: \"loading-overlay\" }, [\n          _c(\"div\", { staticClass: \"loading-content\" }, [\n            _c(\"div\", { staticClass: \"loading-spinner\" }),\n            _c(\"div\", { staticClass: \"loading-text\" }, [\n              _vm._v(_vm._s(_vm.loadingText || \"加载中...\")),\n            ]),\n          ]),\n        ])\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"left-border-frame\" }, [\n      _c(\"div\", { staticClass: \"border-pattern\" }),\n      _c(\"div\", { staticClass: \"border-glow\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"right-border-frame\" }, [\n      _c(\"div\", { staticClass: \"border-pattern\" }),\n      _c(\"div\", { staticClass: \"border-glow\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"bottom-border-frame\" }, [\n      _c(\"div\", { staticClass: \"footer-decoration\" }, [\n        _c(\"div\", { staticClass: \"footer-pattern\" }),\n        _c(\"div\", { staticClass: \"footer-text\" }, [\n          _c(\"span\", { staticClass: \"version-info\" }, [\n            _vm._v(\"Version 1.0.0\"),\n          ]),\n          _c(\"span\", { staticClass: \"copyright\" }, [_vm._v(\"© 2025 神之西游\")]),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACzDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrCH,GAAG,CAACI,EAAE,CAAC,EAAE,EAAE,UAAUC,CAAC,EAAE;IACtB,OAAOJ,EAAE,CAAC,KAAK,EAAE;MACfK,GAAG,EAAED,CAAC;MACNF,WAAW,EAAE,UAAU;MACvBI,KAAK,EAAEP,GAAG,CAACQ,gBAAgB,CAACH,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACDJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,CAClD,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,kBAAkB;IAC/BI,KAAK,EAAE;MAAEE,eAAe,EAAE,OAAOT,GAAG,CAACU,cAAc;IAAI;EACzD,CAAC,EACD,CACET,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACa,SAAS,CAAC,CAAC,CAC9B,CAAC,EACFb,GAAG,CAACc,gBAAgB,IAAId,GAAG,CAACe,mBAAmB,GAC3Cd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACe,mBAAmB,CAAC,CAAC,CACxC,CAAC,CACH,CAAC,GACFf,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,EACDhB,GAAG,CAACiB,EAAE,CAAC,CAAC,CAAC,EACTjB,GAAG,CAACiB,EAAE,CAAC,CAAC,CAAC,EACThB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAACH,GAAG,CAACkB,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACtE,CAAC,EACFlB,GAAG,CAACiB,EAAE,CAAC,CAAC,CAAC,EACTjB,GAAG,CAACmB,SAAS,GACTlB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACoB,WAAW,IAAI,QAAQ,CAAC,CAAC,CAC5C,CAAC,CACH,CAAC,CACH,CAAC,GACFpB,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,CAAC;AACJ,CAAC;AACD,IAAIK,eAAe,GAAG,CACpB,YAAY;EACV,IAAIrB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CACrDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,CAC1C,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIH,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CACtDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,CAC1C,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIH,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CACvDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC1CH,GAAG,CAACW,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,EACFV,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAClE,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDZ,MAAM,CAACuB,aAAa,GAAG,IAAI;AAE3B,SAASvB,MAAM,EAAEsB,eAAe", "ignoreList": []}]}