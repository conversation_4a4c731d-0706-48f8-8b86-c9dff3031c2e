/**
 * 地图工具函数
 * 提供地图相关的实用工具方法
 */
import { locations, connections, calculateMovementCost } from '../data/mapData.js';
import logger from './logger.js';

/**
 * 地图工具类
 */
class MapUtils {
  /**
   * 验证位置是否存在
   * @param {string} locationId - 位置ID
   * @returns {boolean} - 是否存在
   */
  static isValidLocation(locationId) {
    return !!locations[locationId];
  }

  /**
   * 验证是否可以从一个位置移动到另一个位置
   * @param {string} fromLocationId - 起始位置ID
   * @param {string} toLocationId - 目标位置ID
   * @returns {boolean} - 是否可以移动
   */
  static canMoveTo(fromLocationId, toLocationId) {
    if (!this.isValidLocation(fromLocationId) || !this.isValidLocation(toLocationId)) {
      return false;
    }
    
    const connectedLocations = connections[fromLocationId] || [];
    return connectedLocations.includes(toLocationId);
  }

  /**
   * 获取位置的详细信息
   * @param {string} locationId - 位置ID
   * @returns {Object|null} - 位置信息
   */
  static getLocationInfo(locationId) {
    return locations[locationId] || null;
  }

  /**
   * 获取位置的NPC列表
   * @param {string} locationId - 位置ID
   * @returns {Array} - NPC列表
   */
  static getLocationNpcs(locationId) {
    const location = locations[locationId];
    return location ? location.npcs || [] : [];
  }

  /**
   * 获取位置的怪物列表
   * @param {string} locationId - 位置ID
   * @returns {Array} - 怪物列表
   */
  static getLocationMonsters(locationId) {
    const location = locations[locationId];
    return location ? location.monsters || [] : [];
  }

  /**
   * 获取位置的设施列表
   * @param {string} locationId - 位置ID
   * @returns {Array} - 设施列表
   */
  static getLocationFacilities(locationId) {
    const location = locations[locationId];
    return location ? location.facilities || [] : [];
  }

  /**
   * 获取可移动的位置列表
   * @param {string} currentLocationId - 当前位置ID
   * @returns {Array} - 可移动位置列表
   */
  static getAvailableLocations(currentLocationId) {
    if (!this.isValidLocation(currentLocationId)) {
      return [];
    }
    
    const connectedIds = connections[currentLocationId] || [];
    return connectedIds.map(id => {
      const location = locations[id];
      if (!location) return null;
      
      // 计算移动消耗
      const cost = calculateMovementCost(currentLocationId, id);
      
      return {
        ...location,
        cost: cost,
        distance: cost ? cost.distance : 0
      };
    }).filter(Boolean);
  }

  /**
   * 计算两个位置之间的距离
   * @param {string} fromLocationId - 起始位置ID
   * @param {string} toLocationId - 目标位置ID
   * @returns {number} - 距离
   */
  static calculateDistance(fromLocationId, toLocationId) {
    const fromLocation = locations[fromLocationId];
    const toLocation = locations[toLocationId];
    
    if (!fromLocation || !toLocation) {
      return Infinity;
    }
    
    const dx = Math.abs(toLocation.coordinates.x - fromLocation.coordinates.x);
    const dy = Math.abs(toLocation.coordinates.y - fromLocation.coordinates.y);
    return Math.max(dx, dy);
  }

  /**
   * 查找从一个位置到另一个位置的最短路径
   * @param {string} fromLocationId - 起始位置ID
   * @param {string} toLocationId - 目标位置ID
   * @returns {Array|null} - 路径数组，如果无法到达则返回null
   */
  static findPath(fromLocationId, toLocationId) {
    if (!this.isValidLocation(fromLocationId) || !this.isValidLocation(toLocationId)) {
      return null;
    }
    
    if (fromLocationId === toLocationId) {
      return [fromLocationId];
    }
    
    // 使用广度优先搜索找到最短路径
    const queue = [[fromLocationId]];
    const visited = new Set([fromLocationId]);
    
    while (queue.length > 0) {
      const path = queue.shift();
      const currentLocation = path[path.length - 1];
      
      const connectedLocations = connections[currentLocation] || [];
      
      for (const nextLocation of connectedLocations) {
        if (nextLocation === toLocationId) {
          return [...path, nextLocation];
        }
        
        if (!visited.has(nextLocation)) {
          visited.add(nextLocation);
          queue.push([...path, nextLocation]);
        }
      }
    }
    
    return null; // 无法到达
  }

  /**
   * 获取位置类型的中文名称
   * @param {string} type - 位置类型
   * @returns {string} - 中文名称
   */
  static getLocationTypeName(type) {
    const typeMap = {
      'town': '城镇',
      'village': '村庄',
      'forest': '森林',
      'mountain': '山脉',
      'dungeon': '地下城',
      'facility': '设施',
      'field': '野外',
      'cave': '洞穴',
      'temple': '神庙',
      'ruins': '遗迹',
      'desert': '沙漠',
      'swamp': '沼泽',
      'coast': '海岸',
      'island': '岛屿'
    };
    return typeMap[type] || type;
  }

  /**
   * 检查角色是否满足移动条件
   * @param {Object} character - 角色信息
   * @param {Object} movementCost - 移动消耗
   * @returns {Object} - 检查结果 { canMove: boolean, reason: string }
   */
  static checkMovementRequirements(character, movementCost) {
    if (!character || !movementCost) {
      return { canMove: false, reason: '缺少必要信息' };
    }
    
    // 体力消耗功能已完全移除
    
    // 检查银两
    if (movementCost.silver && character.silver < movementCost.silver) {
      return { canMove: false, reason: '银两不足' };
    }
    
    // 检查等级要求（如果有）
    if (movementCost.levelRequired && character.level < movementCost.levelRequired) {
      return { canMove: false, reason: `需要等级 ${movementCost.levelRequired}` };
    }
    
    return { canMove: true, reason: '' };
  }

  /**
   * 格式化移动消耗信息
   * @param {Object} cost - 移动消耗
   * @returns {string} - 格式化的消耗信息
   */
  static formatMovementCost(cost) {
    if (!cost) return '';
    
    const parts = [];
    
    if (cost.time) {
      parts.push(`${cost.time}分钟`);
    }
    
    // 体力消耗功能已完全移除
    
    if (cost.silver) {
      parts.push(`${cost.silver}银两`);
    }
    
    return parts.join(', ');
  }

  /**
   * 获取位置的安全等级
   * @param {string} locationId - 位置ID
   * @returns {string} - 安全等级 (safe, normal, dangerous, deadly)
   */
  static getLocationSafetyLevel(locationId) {
    const location = locations[locationId];
    if (!location) return 'unknown';
    
    const monsters = location.monsters || [];
    if (monsters.length === 0) return 'safe';
    
    const maxLevel = Math.max(...monsters.map(m => m.level || 1));
    
    if (maxLevel <= 5) return 'safe';
    if (maxLevel <= 15) return 'normal';
    if (maxLevel <= 25) return 'dangerous';
    return 'deadly';
  }

  /**
   * 获取推荐的移动路线
   * @param {string} currentLocationId - 当前位置
   * @param {number} characterLevel - 角色等级
   * @returns {Array} - 推荐的位置列表
   */
  static getRecommendedLocations(currentLocationId, characterLevel = 1) {
    const availableLocations = this.getAvailableLocations(currentLocationId);
    
    return availableLocations.filter(location => {
      const safetyLevel = this.getLocationSafetyLevel(location.id);
      const monsters = location.monsters || [];
      const maxMonsterLevel = Math.max(...monsters.map(m => m.level || 1), 0);
      
      // 推荐等级相近或稍低的位置
      return maxMonsterLevel <= characterLevel + 5;
    }).sort((a, b) => {
      // 按距离和安全性排序
      const aSafety = this.getLocationSafetyLevel(a.id);
      const bSafety = this.getLocationSafetyLevel(b.id);
      
      const safetyOrder = { safe: 0, normal: 1, dangerous: 2, deadly: 3 };
      
      if (safetyOrder[aSafety] !== safetyOrder[bSafety]) {
        return safetyOrder[aSafety] - safetyOrder[bSafety];
      }
      
      return (a.distance || 0) - (b.distance || 0);
    });
  }
}

export default MapUtils;
