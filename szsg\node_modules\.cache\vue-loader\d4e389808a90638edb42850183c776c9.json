{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Clinic.vue?vue&type=style&index=0&id=47b4dff0&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Clinic.vue", "mtime": 1749792050849}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749535533560}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Clinic.vue"], "names": [], "mappings": ";AA0g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file": "Clinic.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"clinic-page\">\r\n      <div class=\"clinic-container\">\r\n        <!-- 医馆标题 -->\r\n        <div class=\"clinic-header\">\r\n          <h1>医馆</h1>\r\n          <div class=\"character-status\">\r\n            <div class=\"status-box\">\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">气血:</div>\r\n                <div class=\"status-value\">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">精力:</div>\r\n                <div class=\"status-value\">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">银两:</div>\r\n                <div class=\"status-value silver-value\">{{ characterInfo.silver }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 选项卡导航 -->\r\n        <div class=\"tabs-container\">\r\n          <div \r\n            v-for=\"tab in tabs\" \r\n            :key=\"tab.id\" \r\n            class=\"tab\" \r\n            :class=\"{ 'active': activeTab === tab.id }\"\r\n            @click=\"activeTab = tab.id\"\r\n          >\r\n            <span class=\"tab-name\">{{ tab.name }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容区域 -->\r\n        <div class=\"content-area\">\r\n          <!-- 气血药品 -->\r\n          <div v-if=\"activeTab === 'health'\" class=\"tab-content\">\r\n            <div class=\"service-list\">\r\n              <div \r\n                v-for=\"potion in healthPotions\" \r\n                :key=\"potion.id\" \r\n                class=\"service-list-item\" \r\n                :class=\"{ 'disabled': !canUseService(potion) }\"\r\n              >\r\n                <div class=\"service-list-left\">\r\n                  <div class=\"service-list-name\">{{ potion.name }}</div>\r\n                  <div class=\"service-list-info\">\r\n                    <div class=\"service-list-description\">恢复 {{ potion.effect_value }} 气血</div>\r\n                    <div class=\"service-list-price\">价格: {{ potion.price }} 银两</div>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  class=\"service-list-button\"\r\n                  @click=\"purchasePotion(potion.id, 'health')\"\r\n                  :disabled=\"!canUseService(potion)\"\r\n                >\r\n                  购买\r\n                </button>\r\n              </div>\r\n              <div v-if=\"healthPotions.length === 0\" class=\"no-items\">\r\n                暂无可用药品\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 精力药品 -->\r\n          <div v-if=\"activeTab === 'mana'\" class=\"tab-content\">\r\n            <div class=\"service-list\">\r\n              <div \r\n                v-for=\"potion in manaPotions\" \r\n                :key=\"potion.id\" \r\n                class=\"service-list-item\" \r\n                :class=\"{ 'disabled': !canUseService(potion) }\"\r\n              >\r\n                <div class=\"service-list-left\">\r\n                  <div class=\"service-list-name\">{{ potion.name }}</div>\r\n                  <div class=\"service-list-info\">\r\n                    <div class=\"service-list-description\">恢复 {{ potion.effect_value }} 精力</div>\r\n                    <div class=\"service-list-price\">价格: {{ potion.price }} 银两</div>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  class=\"service-list-button\"\r\n                  @click=\"purchasePotion(potion.id, 'mana')\"\r\n                  :disabled=\"!canUseService(potion)\"\r\n                >\r\n                  购买\r\n                </button>\r\n              </div>\r\n              <div v-if=\"manaPotions.length === 0\" class=\"no-items\">\r\n                暂无可用药品\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 全员治疗 -->\r\n          <div v-if=\"activeTab === 'team'\" class=\"tab-content\">\r\n            <div class=\"service-list\">\r\n              <div \r\n                v-for=\"service in teamServices\" \r\n                :key=\"service.id\" \r\n                class=\"service-list-item\" \r\n                :class=\"{ 'disabled': !canUseService(service) }\"\r\n              >\r\n                <div class=\"service-list-left\">\r\n                  <div class=\"service-list-name\">{{ service.name }}</div>\r\n                  <div class=\"service-list-info\">\r\n                    <div class=\"service-list-description\">{{ service.description }}</div>\r\n                    <div class=\"service-list-price\">价格: {{ service.price }} 银两</div>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  class=\"service-list-button\"\r\n                  @click=\"purchaseTeamService(service.id)\"\r\n                  :disabled=\"!canUseService(service)\"\r\n                >\r\n                  购买\r\n                </button>\r\n              </div>\r\n              <div v-if=\"teamServices.length === 0\" class=\"no-items\">\r\n                暂无可用服务\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 返回按钮 -->\r\n        <div class=\"bottom-actions\">\r\n          <button class=\"back-button\" @click=\"$router.push('/game/main')\">返回城镇</button>\r\n        </div>\r\n\r\n        <!-- 购买结果弹窗 -->\r\n        <div v-if=\"showResult\" class=\"result-modal\">\r\n          <div class=\"result-content\" :class=\"{ 'error': resultError }\">\r\n            <h3>{{ resultError ? '购买失败' : '购买成功' }}</h3>\r\n            <p>{{ resultMessage }}</p>\r\n            <div v-if=\"healResult && !resultError\" class=\"heal-result\">\r\n              <div v-if=\"healResult.hpHealed\">恢复气血: +{{ healResult.hpHealed }}</div>\r\n              <div v-if=\"healResult.mpHealed\">恢复精力: +{{ healResult.mpHealed }}</div>\r\n            </div>\r\n            <button @click=\"showResult = false\">确定</button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 加载中和错误提示 -->\r\n        <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n          <div class=\"loading-spinner\"></div>\r\n          <div>加载中...</div>\r\n        </div>\r\n        <div v-if=\"error\" class=\"error-message\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nimport clinicService from '@/api/services/clinicService';\r\nimport { showMessage } from '@/utils/message';\r\nimport logger from '@/utils/logger';\r\nimport { getCurrentCharacter } from '@/api/services/characterService';\r\n\r\nexport default {\r\n  name: 'Clinic',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      isLoading: true,\r\n      error: null,\r\n      activeTab: 'health', // 默认选中气血药品选项卡\r\n      tabs: [\r\n        {\r\n          id: 'health',\r\n          name: '气血药品'\r\n        },\r\n        {\r\n          id: 'mana',\r\n          name: '精力药品'\r\n        },\r\n        {\r\n          id: 'team',\r\n          name: '完全恢复'\r\n        }\r\n      ],\r\n      healthPotions: [],\r\n      manaPotions: [],\r\n      teamServices: [],\r\n      characterInfo: {\r\n        id: null,\r\n        level: 1,\r\n        hp: 100,\r\n        maxHp: 100,\r\n        mp: 50,\r\n        maxMp: 50,\r\n        silver: 0,\r\n        gold: 0\r\n      },\r\n      showResult: false,\r\n      resultMessage: '',\r\n      resultError: false,\r\n      healResult: null,\r\n      touchStartY: 0,\r\n      touchMoveY: 0,\r\n      scrolling: false\r\n    };\r\n  },\r\n  computed: {\r\n    hpPercent() {\r\n      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100;\r\n    },\r\n    mpPercent() {\r\n      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100;\r\n    }\r\n  },\r\n  created() {\r\n    this.initCharacterInfo();\r\n    this.loadAllServices();\r\n  },\r\n  mounted() {\r\n    // 添加触摸事件监听\r\n    this.addTouchListeners();\r\n  },\r\n  beforeDestroy() {\r\n    // 移除触摸事件监听\r\n    this.removeTouchListeners();\r\n  },\r\n  methods: {\r\n    // 添加触摸事件监听\r\n    addTouchListeners() {\r\n      const serviceList = this.$el.querySelector('.service-list');\r\n      const clinicPage = this.$el.querySelector('.clinic-page');\r\n      \r\n      if (serviceList) {\r\n        serviceList.addEventListener('touchstart', this.handleTouchStart, { passive: true });\r\n        serviceList.addEventListener('touchmove', this.handleTouchMove, { passive: false });\r\n        serviceList.addEventListener('touchend', this.handleTouchEnd, { passive: true });\r\n      }\r\n      \r\n      if (clinicPage) {\r\n        clinicPage.addEventListener('touchstart', this.handleTouchStart, { passive: true });\r\n        clinicPage.addEventListener('touchmove', this.handleTouchMove, { passive: false });\r\n        clinicPage.addEventListener('touchend', this.handleTouchEnd, { passive: true });\r\n      }\r\n    },\r\n    // 移除触摸事件监听\r\n    removeTouchListeners() {\r\n      const serviceList = this.$el.querySelector('.service-list');\r\n      const clinicPage = this.$el.querySelector('.clinic-page');\r\n      \r\n      if (serviceList) {\r\n        serviceList.removeEventListener('touchstart', this.handleTouchStart);\r\n        serviceList.removeEventListener('touchmove', this.handleTouchMove);\r\n        serviceList.removeEventListener('touchend', this.handleTouchEnd);\r\n      }\r\n      \r\n      if (clinicPage) {\r\n        clinicPage.removeEventListener('touchstart', this.handleTouchStart);\r\n        clinicPage.removeEventListener('touchmove', this.handleTouchMove);\r\n        clinicPage.removeEventListener('touchend', this.handleTouchEnd);\r\n      }\r\n    },\r\n    // 处理触摸开始事件\r\n    handleTouchStart(event) {\r\n      this.touchStartY = event.touches[0].clientY;\r\n      this.scrolling = false;\r\n    },\r\n    // 处理触摸移动事件\r\n    handleTouchMove(event) {\r\n      this.touchMoveY = event.touches[0].clientY;\r\n      const deltaY = this.touchStartY - this.touchMoveY;\r\n      \r\n      // 判断是否是垂直滚动\r\n      if (Math.abs(deltaY) > 10) {\r\n        this.scrolling = true;\r\n      }\r\n      \r\n      // 如果是垂直滚动，不阻止默认行为\r\n      if (this.scrolling) {\r\n        return;\r\n      }\r\n      \r\n      // 对于非垂直滚动的触摸移动，阻止默认行为以防止页面整体滚动\r\n      event.preventDefault();\r\n    },\r\n    // 处理触摸结束事件\r\n    handleTouchEnd() {\r\n      this.touchStartY = 0;\r\n      this.touchMoveY = 0;\r\n      this.scrolling = false;\r\n    },\r\n    initCharacterInfo() {\r\n      // 获取当前角色信息\r\n      const character = getCurrentCharacter();\r\n      if (character) {\r\n        this.characterInfo.id = character.id;\r\n        \r\n        // 从Vuex获取更详细的角色信息\r\n        const characterStatus = this.$store.state.character.characterStatus;\r\n        if (characterStatus) {\r\n          this.characterInfo = {\r\n            ...this.characterInfo,\r\n            level: characterStatus.level || 1,\r\n            hp: characterStatus.hp || 100,\r\n            maxHp: characterStatus.maxHp || 100,\r\n            mp: characterStatus.mp || 50,\r\n            maxMp: characterStatus.maxMp || 50,\r\n            silver: characterStatus.silver || 0,\r\n            gold: characterStatus.gold || 0\r\n          };\r\n        }\r\n      } else {\r\n        this.error = '未找到角色信息，请先选择角色';\r\n        showMessage('未找到角色信息，请先选择角色', 'error');\r\n        this.$router.push('/setup/character-select');\r\n      }\r\n    },\r\n    async loadAllServices() {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      \r\n      try {\r\n        // 加载服务类型\r\n        await this.loadServiceTypes();\r\n        \r\n        // 加载各类药品和服务\r\n        await Promise.all([\r\n          this.loadHealthPotions(),\r\n          this.loadManaPotions(),\r\n          this.loadTeamServices()\r\n        ]);\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载服务失败:', error);\r\n        this.error = '加载服务失败，请重试';\r\n        this.isLoading = false;\r\n        showMessage('加载服务失败，请重试', 'error');\r\n      }\r\n    },\r\n    async loadServiceTypes() {\r\n      try {\r\n        const serviceTypes = await clinicService.getServiceTypes();\r\n        if (serviceTypes && serviceTypes.length > 0) {\r\n          this.tabs = serviceTypes.map(type => ({\r\n            id: type.id,\r\n            name: type.name\r\n          }));\r\n          logger.debug('[医馆] 服务类型加载成功:', this.tabs);\r\n        }\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载服务类型失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    async loadHealthPotions() {\r\n      try {\r\n        const healthPotions = await clinicService.getHealthPotions();\r\n        this.healthPotions = healthPotions || [];\r\n        logger.debug('[医馆] 气血药品加载成功:', this.healthPotions);\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载气血药品失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    async loadManaPotions() {\r\n      try {\r\n        const manaPotions = await clinicService.getManaPotions();\r\n        this.manaPotions = manaPotions || [];\r\n        logger.debug('[医馆] 精力药品加载成功:', this.manaPotions);\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载精力药品失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    async loadTeamServices() {\r\n      try {\r\n        const teamServices = await clinicService.getTeamServices();\r\n        this.teamServices = teamServices || [];\r\n        logger.debug('[医馆] 全员治疗服务加载成功:', this.teamServices);\r\n      } catch (error) {\r\n        logger.error('[医馆] 加载全员治疗服务失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    canUseService(service) {\r\n      // 检查角色是否有足够的银两\r\n      if (this.characterInfo.silver < service.price) {\r\n        return false;\r\n      }\r\n      \r\n      // 对于气血药品，检查角色是否已经满血\r\n      if (service.type === 'health' && this.characterInfo.hp >= this.characterInfo.maxHp) {\r\n        return false;\r\n      }\r\n      \r\n      // 对于精力药品，检查角色是否已经满精力\r\n      if (service.type === 'mana' && this.characterInfo.mp >= this.characterInfo.maxMp) {\r\n        return false;\r\n      }\r\n      \r\n      // 对于全员治疗，检查角色是否已经满血和满精力\r\n      if (service.type === 'team' && \r\n          this.characterInfo.hp >= this.characterInfo.maxHp && \r\n          this.characterInfo.mp >= this.characterInfo.maxMp) {\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    },\r\n    async purchasePotion(potionId, type) {\r\n      if (!this.characterInfo.id) {\r\n        showMessage('未找到角色信息', 'error');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        const response = await clinicService.purchasePotion(this.characterInfo.id, potionId, type);\r\n        logger.debug('[医馆] 购买药品成功:', response);\r\n        \r\n        // 更新角色状态\r\n        if (response.character) {\r\n          this.characterInfo.hp = response.character.hp;\r\n          this.characterInfo.mp = response.character.mp;\r\n          this.characterInfo.silver = response.character.silver;\r\n          this.characterInfo.gold = response.character.gold;\r\n          \r\n          // 更新Vuex中的角色状态\r\n          this.$store.commit('character/updateCharacterStatus', {\r\n            hp: response.character.hp,\r\n            mp: response.character.mp,\r\n            silver: response.character.silver,\r\n            gold: response.character.gold\r\n          });\r\n        }\r\n        \r\n        // 显示结果\r\n        this.resultMessage = response.message || '购买成功';\r\n        this.resultError = false;\r\n        this.healResult = {\r\n          hpHealed: response.hp_recovered,\r\n          mpHealed: response.mp_recovered\r\n        };\r\n        this.showResult = true;\r\n        \r\n        // 刷新药品列表\r\n        if (type === 'health') {\r\n          this.loadHealthPotions();\r\n        } else if (type === 'mana') {\r\n          this.loadManaPotions();\r\n        }\r\n      } catch (error) {\r\n        logger.error('[医馆] 购买药品失败:', error);\r\n        this.resultMessage = error.message || '购买失败';\r\n        this.resultError = true;\r\n        this.healResult = null;\r\n        this.showResult = true;\r\n      }\r\n    },\r\n    async purchaseTeamService(serviceId) {\r\n      if (!this.characterInfo.id) {\r\n        showMessage('未找到角色信息', 'error');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        const response = await clinicService.useTeamService(this.characterInfo.id, serviceId);\r\n        logger.debug('[医馆] 使用全员治疗成功:', response);\r\n        \r\n        // 更新角色状态\r\n        if (response.character) {\r\n          this.characterInfo.hp = response.character.hp;\r\n          this.characterInfo.mp = response.character.mp;\r\n          this.characterInfo.silver = response.character.silver;\r\n          this.characterInfo.gold = response.character.gold;\r\n          \r\n          // 更新Vuex中的角色状态\r\n          this.$store.commit('character/updateCharacterStatus', {\r\n            hp: response.character.hp,\r\n            mp: response.character.mp,\r\n            silver: response.character.silver,\r\n            gold: response.character.gold\r\n          });\r\n        }\r\n        \r\n        // 显示结果\r\n        this.resultMessage = response.message || '治疗成功';\r\n        this.resultError = false;\r\n        this.healResult = {\r\n          hpHealed: response.hp_recovered,\r\n          mpHealed: response.mp_recovered\r\n        };\r\n        this.showResult = true;\r\n        \r\n        // 刷新全员治疗服务列表\r\n        this.loadTeamServices();\r\n      } catch (error) {\r\n        logger.error('[医馆] 使用全员治疗失败:', error);\r\n        this.resultMessage = error.message || '治疗失败';\r\n        this.resultError = true;\r\n        this.healResult = null;\r\n        this.showResult = true;\r\n      }\r\n    },\r\n    closeResult() {\r\n      this.showResult = false;\r\n      this.healResult = null;\r\n    },\r\n    goBack() {\r\n      this.$router.push('/game/main');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.clinic-page {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background-color: #000033;\r\n  color: #ffffff;\r\n  padding: 8px;\r\n  position: relative;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  max-height: 100vh;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.clinic-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  width: 100%;\r\n  padding-bottom: 65px;\r\n}\r\n\r\n.clinic-header {\r\n  margin-bottom: 15px;\r\n  padding: 12px;\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.clinic-header h1 {\r\n  margin: 0 0 10px 0;\r\n  text-align: center;\r\n  color: #ffcc00;\r\n  font-size: 22px;\r\n  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);\r\n}\r\n\r\n.character-status {\r\n  margin-top: 10px;\r\n}\r\n\r\n.status-box {\r\n  background-color: rgba(153, 0, 0, 0.2);\r\n  border: 1px solid rgba(153, 0, 0, 0.5);\r\n  border-radius: 5px;\r\n  padding: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n  padding: 3px 5px;\r\n}\r\n\r\n.status-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.status-label {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-value {\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.silver-value {\r\n  color: #ffcc00;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.tab {\r\n  flex: 1;\r\n  padding: 10px 8px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  border-bottom: 3px solid transparent;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.tab.active {\r\n  border-bottom-color: #ffcc00;\r\n  background-color: rgba(255, 204, 0, 0.1);\r\n}\r\n\r\n.tab-name {\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n}\r\n\r\n.tab.active .tab-name {\r\n  color: #ffcc00;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.tab-content {\r\n  padding: 5px 0;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  max-height: calc(70vh - 180px);\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n  padding-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.service-list-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 12px;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.service-list-item:hover {\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-color: #3333cc;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.service-list-item.disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.service-list-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 75%;\r\n}\r\n\r\n.service-list-name {\r\n  font-weight: bold;\r\n  color: #ffcc00;\r\n  font-size: 16px;\r\n  text-shadow: 0 0 3px rgba(255, 204, 0, 0.3);\r\n}\r\n\r\n.service-list-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n.service-list-description {\r\n  font-size: 14px;\r\n  color: #aaaaff;\r\n}\r\n\r\n.service-list-price {\r\n  color: #ffcc00;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-left: 10px;\r\n  background-color: rgba(153, 0, 0, 0.3);\r\n  border: 1px solid rgba(153, 0, 0, 0.7);\r\n  border-radius: 4px;\r\n  padding: 2px 6px;\r\n}\r\n\r\n.service-list-right {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 30%;\r\n}\r\n\r\n.service-list-button {\r\n  padding: 6px 15px;\r\n  background-color: #990000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: bold;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  width: 60px;\r\n}\r\n\r\n.service-list-button:hover:not(:disabled) {\r\n  background-color: #cc0000;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.service-list-button:active:not(:disabled) {\r\n  transform: translateY(0);\r\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.service-list-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n  box-shadow: none;\r\n}\r\n\r\n.gold {\r\n  color: #ffcc00;\r\n}\r\n\r\n.silver {\r\n  color: #cccccc;\r\n}\r\n\r\n.service-list-btn {\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  padding: 6px 14px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.service-list-btn:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.service-list-btn:disabled {\r\n  background-color: #222255;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.bottom-actions {\r\n  position: fixed;\r\n  bottom: 15px;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 12px 10px;\r\n  z-index: 100;\r\n  background: linear-gradient(to top, rgba(0, 0, 51, 0.95) 0%, rgba(0, 0, 51, 0.8) 50%, rgba(0, 0, 51, 0) 100%);\r\n  padding-top: 25px;\r\n}\r\n\r\n.back-button {\r\n  padding: 10px 25px;\r\n  background-color: #cc3333;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #dd4444;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.back-button:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.result-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.result-content {\r\n  background-color: #000033;\r\n  border: 2px solid #3333cc;\r\n  border-radius: 6px;\r\n  width: 80%;\r\n  max-width: 400px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #3333cc;\r\n}\r\n\r\n.result-header h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  color: #ffcc00;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #aaaaff;\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n}\r\n\r\n.result-body {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.result-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 30px;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n.result-icon.success {\r\n  background-color: #33cc33;\r\n  color: white;\r\n}\r\n\r\n.result-icon.error {\r\n  background-color: #cc3333;\r\n  color: white;\r\n}\r\n\r\n.result-message {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.result-details {\r\n  text-align: left;\r\n  margin-top: 15px;\r\n  border-top: 1px solid #3333cc;\r\n  padding-top: 15px;\r\n}\r\n\r\n.result-item {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.result-label {\r\n  color: #aaaaff;\r\n  display: inline-block;\r\n  width: 100px;\r\n}\r\n\r\n.result-value {\r\n  font-weight: bold;\r\n}\r\n\r\n.result-footer {\r\n  padding: 10px 15px;\r\n  border-top: 1px solid #3333cc;\r\n  text-align: center;\r\n}\r\n\r\n.confirm-btn {\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.confirm-btn:hover {\r\n  background-color: #4444dd;\r\n}\r\n\r\n@media (max-width: 600px) {\r\n  .service-list-item {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .service-list-left {\r\n    width: 100%;\r\n  }\r\n  \r\n  .service-list-right {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .service-list-details {\r\n    text-align: left;\r\n  }\r\n  \r\n  .status-label {\r\n    width: 50px;\r\n  }\r\n  \r\n  .status-bar {\r\n    width: calc(100% - 60px);\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.service-list::-webkit-scrollbar {\r\n  width: 5px;\r\n}\r\n\r\n.service-list::-webkit-scrollbar-track {\r\n  background: rgba(0, 0, 51, 0.3);\r\n  border-radius: 10px;\r\n}\r\n\r\n.service-list::-webkit-scrollbar-thumb {\r\n  background: #3333cc;\r\n  border-radius: 10px;\r\n}\r\n\r\n.service-list::-webkit-scrollbar-thumb:hover {\r\n  background: #4444dd;\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 480px) {\r\n  .clinic-page {\r\n    padding: 5px;\r\n  }\r\n  \r\n  .clinic-header h1 {\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .tab {\r\n    padding: 8px 5px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .tab-icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n  \r\n  .service-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .service-description, .service-price {\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .buy-button {\r\n    padding: 6px 10px;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .bottom-actions {\r\n    bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 确保内容区域可以滚动 */\r\n.content-area {\r\n  flex: 1;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n</style> "]}]}