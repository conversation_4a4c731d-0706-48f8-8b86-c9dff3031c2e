<!DOCTYPE html>
<html>
<head>
    <title>清除游戏缓存</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: green;
            margin: 10px 0;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        .info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>清除游戏缓存工具</h1>
        
        <div class="info">
            <p>如果游戏显示的地图数据不正确，可以使用此工具清除缓存。</p>
        </div>
        
        <button onclick="clearAllCache()">清除所有游戏缓存</button>
        <button onclick="clearMapCache()">只清除地图缓存</button>
        <button onclick="showCacheInfo()">显示缓存信息</button>
        
        <div id="result"></div>
        
        <div class="info">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"清除所有游戏缓存"按钮</li>
                <li>刷新游戏页面</li>
                <li>重新登录角色</li>
                <li>检查移动功能是否显示正确的地点</li>
            </ol>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function clearAllCache() {
            try {
                // 清除所有以 'szsg_' 开头的缓存
                const keys = Object.keys(localStorage);
                const gameKeys = keys.filter(key => key.startsWith('szsg_'));
                
                gameKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // 也清除sessionStorage
                const sessionKeys = Object.keys(sessionStorage);
                const gameSessionKeys = sessionKeys.filter(key => key.startsWith('szsg_'));
                
                gameSessionKeys.forEach(key => {
                    sessionStorage.removeItem(key);
                });
                
                showResult(`成功清除 ${gameKeys.length} 个localStorage缓存和 ${gameSessionKeys.length} 个sessionStorage缓存`, 'success');
            } catch (error) {
                showResult(`清除缓存失败: ${error.message}`, 'error');
            }
        }

        function clearMapCache() {
            try {
                const keys = Object.keys(localStorage);
                const mapKeys = keys.filter(key => 
                    key.includes('map_data') || 
                    key.includes('available_locations') || 
                    key.includes('location_details') ||
                    key.includes('movement_cost')
                );
                
                mapKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                showResult(`成功清除 ${mapKeys.length} 个地图相关缓存`, 'success');
            } catch (error) {
                showResult(`清除地图缓存失败: ${error.message}`, 'error');
            }
        }

        function showCacheInfo() {
            try {
                const keys = Object.keys(localStorage);
                const gameKeys = keys.filter(key => key.startsWith('szsg_'));
                
                let info = `<h3>当前缓存信息：</h3>`;
                info += `<p>总共 ${gameKeys.length} 个游戏缓存项</p>`;
                
                if (gameKeys.length > 0) {
                    info += `<ul>`;
                    gameKeys.forEach(key => {
                        const value = localStorage.getItem(key);
                        const size = value ? (value.length / 1024).toFixed(2) : 0;
                        info += `<li>${key} (${size} KB)</li>`;
                    });
                    info += `</ul>`;
                }
                
                showResult(info, 'info');
            } catch (error) {
                showResult(`获取缓存信息失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示缓存信息
        window.onload = function() {
            showCacheInfo();
        };
    </script>
</body>
</html>
