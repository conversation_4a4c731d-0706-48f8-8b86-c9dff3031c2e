<template>
  <GameLayout>
    <div class="create-character-container">
      <!-- 创建角色主界面 -->
      <div class="character-creation-panel">
        <form @submit.prevent="submitCreateCharacter" class="character-form">
          <!-- 名称输入区 -->
          <div class="name-section">
            <span class="label-text">名称:</span>
            <input 
              v-model="characterData.name"
              type="text" 
              class="name-input"
              maxlength="10"
              required
            />
          </div>

          <!-- 性别选择区 -->
          <div class="gender-section">
            <span class="label-text">性别:</span>
            <div class="gender-buttons">
              <button 
                type="button"
                class="gender-btn"
                :class="{ 'selected': characterData.gender === 'male' }"
                @click="characterData.gender = 'male'"
              >
                男
              </button>
              <button 
                type="button"
                class="gender-btn"
                :class="{ 'selected': characterData.gender === 'female' }"
                @click="characterData.gender = 'female'"
              >
                女
              </button>
            </div>
          </div>

          <!-- 职业选择区 -->
          <div class="profession-section">
            <span class="label-text">职业:</span>
            <div class="profession-buttons">
              <button 
                v-for="profession in professions" 
                :key="profession.id"
                type="button"
                class="profession-btn"
                :class="{ 'selected': characterData.profession === profession.id }"
                @click="selectProfession(profession)"
              >
                {{ profession.name }}
              </button>
            </div>
          </div>

          <!-- 头像选择区 -->
          <div class="avatar-section">
            <span class="label-text">头像:</span>
            <div class="avatar-grid">
              <div 
                v-for="(avatar, index) in getAvailableAvatars()" 
                :key="index"
                class="avatar-option"
                :class="{ 'selected': selectedAvatar === index }"
                @click="selectedAvatar = index"
              >
                <img :src="avatar" :alt="`头像${index + 1}`" />
              </div>
            </div>
          </div>

          <!-- 提示文字 -->
          <div class="hint-text">
            请选择在游戏中角色的性别
          </div>

        </form>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <button
            type="button"
            @click="goBack"
            class="game-btn return-btn"
            :disabled="isCreating"
          >
            返回
          </button>
          <button
            type="button"
            @click="submitCreateCharacter"
            class="game-btn create-btn"
            :disabled="!isFormValid || isCreating"
          >
            创建角色
          </button>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import { mapActions } from 'vuex'
import { regionService } from '@/api'
import logger from '@/utils/logger'

export default {
  name: 'CreateCharacter',
  components: {
    GameLayout
  },
  data() {
    return {
      currentRegion: null,
      isCreating: false,
      selectedAvatar: 0,
      characterData: {
        name: '',
        gender: 'male',
        profession: ''
      },
      professions: [
        {
          id: 'warrior',
          name: '武士',
          description: '武艺高强的战士，擅长近身搏斗，刀剑精通'
        },
        {
          id: 'scholar',
          name: '文人',
          description: '博学多才的学者，精通诗词歌赋，法术造诣深厚'
        },
        {
          id: 'mystic',
          name: '异人',
          description: '神秘莫测的修行者，身怀奇术，能力均衡'
        }
      ]
    }
  },
  computed: {
    isFormValid() {
      return this.characterData.name.trim().length >= 2 &&
             this.characterData.gender &&
             this.characterData.profession
    }
  },
  created() {
    logger.debug('[CreateCharacter] 页面初始化')
    
    // 获取当前选择的大区
    this.currentRegion = regionService.getCurrentRegion()
    if (!this.currentRegion) {
      this.showToast('请先选择大区')
      this.$router.push('/setup/region-select')
      return
    }
  },
  methods: {
    ...mapActions('character', ['createCharacter']),

    selectProfession(profession) {
      this.characterData.profession = profession.id
      logger.debug('[CreateCharacter] 选择职业:', profession.name)
    },

    getAvailableAvatars() {
      const gender = this.characterData.gender || 'male'
      const profession = this.characterData.profession || 'warrior'
      return [
        `/static/game/avatars/${gender}_${profession}_1.png`,
        `/static/game/avatars/${gender}_${profession}_2.png`,
        `/static/game/avatars/${gender}_${profession}_3.png`
      ]
    },

    async submitCreateCharacter() {
      if (!this.isFormValid) {
        this.showToast('请完善角色信息')
        return
      }

      this.isCreating = true

      try {
        const characterData = {
          name: this.characterData.name.trim(),
          gender: this.characterData.gender,
          profession: this.characterData.profession, // 直接使用profession字段
          region_id: this.currentRegion?.id || 1 // 提供默认值
        }

        logger.debug('[CreateCharacter] 提交数据:', characterData)
        logger.debug('[CreateCharacter] 当前区域:', this.currentRegion)

        const result = await this.createCharacter(characterData)
        
        if (result && result.success) {
          this.showToast('角色创建成功！')
          setTimeout(() => {
            this.$router.push('/setup/character-select')
          }, 1500)
        } else {
          this.showToast(result?.error?.message || '创建角色失败，请重试')
        }
      } catch (error) {
        logger.error('[CreateCharacter] 创建角色失败:', error)
        this.showToast(error.message || '创建角色失败，请重试')
      } finally {
        this.isCreating = false
      }
    },

    goBack() {
      this.$router.push('/setup/character-select')
    },

    showToast(message) {
      const toast = document.createElement('div')
      toast.textContent = message
      toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
      `
      document.body.appendChild(toast)
      setTimeout(() => {
        document.body.removeChild(toast)
      }, 2000)
    }
  }
}
</script>

<style scoped>
/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}
.create-character-container {
  padding: 5px 15px;
  max-width: 480px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  box-sizing: border-box;
  padding-top: 10px;
}

.character-creation-panel {
  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);
  border: 3px solid #8b7355;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);
  box-sizing: border-box;
  position: relative;
  margin-top: -25px;
}

.character-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 名称输入区 */
.name-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #8b7355;
}

.label-text {
  color: #ffd700;
  font-size: 16px;
  font-weight: bold;
  min-width: 55px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

.name-input {
  flex: 1;
  background: #ffff99;
  border: 2px solid #8b7355;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #000;
  outline: none;
  transition: border-color 0.3s ease;
}

.name-input:focus {
  border-color: #ffd700;
  box-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
}

/* 性别选择区 */
.gender-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #8b7355;
}

.gender-buttons {
  display: flex;
  gap: 20px;
}

.gender-btn {
  background: transparent;
  border: none;
  color: #ffd700;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 5px;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

.gender-btn:hover {
  background: rgba(255, 215, 0, 0.2);
  transform: scale(1.05);
}

.gender-btn.selected {
  background: rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

/* 职业选择区 */
.profession-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #8b7355;
}

.profession-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.profession-btn {
  background: transparent;
  border: none;
  color: #ffd700;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 8px 14px;
  border-radius: 5px;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

.profession-btn:hover {
  background: rgba(255, 215, 0, 0.2);
  transform: scale(1.05);
}

.profession-btn.selected {
  background: rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

/* 头像选择区 */
.avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #8b7355;
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.avatar-option {
  width: 65px;
  height: 65px;
  border: 2px solid #8b7355;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.avatar-option:hover {
  border-color: #ffd700;
  transform: scale(1.05);
}

.avatar-option.selected {
  border-color: #ffd700;
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

.avatar-option img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 提示文字 */
.hint-text {
  background: rgba(0, 0, 0, 0.8);
  color: #ffd700;
  text-align: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  margin: 10px 0 5px 0;
  border: 1px solid rgba(255, 215, 0, 0.3);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 25px;
  margin-top: 5px;
  padding: 10px 0;
  border-top: 1px solid #8b7355;
}

.game-btn {
  background: linear-gradient(135deg, #8b7355 0%, #a0845c 50%, #8b7355 100%);
  border: 2px solid #ffd700;
  border-radius: 8px;
  color: #ffd700;
  font-size: 14px;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  min-width: 80px;
}

.game-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #a0845c 0%, #b8956b 50%, #a0845c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.game-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.game-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: #666;
  border-color: #999;
  color: #ccc;
}

.return-btn {
  background: linear-gradient(135deg, #6b4423 0%, #8b5a2b 50%, #6b4423 100%);
}

.return-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #8b5a2b 0%, #a0673a 50%, #8b5a2b 100%);
}

.create-btn {
  background: linear-gradient(135deg, #2d5016 0%, #4a7c23 50%, #2d5016 100%);
}

.create-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #4a7c23 0%, #5e9c2e 50%, #4a7c23 100%);
}

@media (max-width: 768px) {
  .create-character-container {
    padding: 8px;
    min-height: 100vh;
  }

  .character-creation-panel {
    margin: 0;
    padding: 18px;
    max-width: 100%;
    border-radius: 8px;
  }

  .character-form {
    gap: 15px;
  }

  .name-section,
  .gender-section,
  .profession-section,
  .avatar-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 10px 0;
  }

  .label-text {
    min-width: auto;
    font-size: 15px;
  }

  .gender-buttons,
  .profession-buttons {
    gap: 15px;
    flex-wrap: wrap;
  }

  .avatar-grid {
    gap: 8px;
  }

  .avatar-option {
    width: 55px;
    height: 55px;
  }

  .action-buttons {
    gap: 18px;
    margin-top: 15px;
    padding: 12px 0;
  }

  .game-btn {
    font-size: 13px;
    padding: 8px 16px;
    min-width: 70px;
  }
}

/* 针对小屏幕设备的额外优化 */
@media (max-width: 480px) {
  .create-character-container {
    padding: 5px;
    min-height: 100vh;
  }

  .character-creation-panel {
    padding: 12px;
    border-radius: 6px;
  }

  .character-form {
    gap: 12px;
  }

  .name-section,
  .gender-section,
  .profession-section,
  .avatar-section {
    padding: 8px 0;
  }

  .label-text {
    font-size: 14px;
  }

  .gender-btn {
    font-size: 16px;
    padding: 6px 12px;
  }

  .profession-btn {
    font-size: 14px;
    padding: 6px 10px;
  }

  .gender-buttons,
  .profession-buttons {
    gap: 10px;
  }

  .avatar-grid {
    gap: 6px;
  }

  .avatar-option {
    width: 48px;
    height: 48px;
  }

  .action-buttons {
    gap: 15px;
    margin-top: 12px;
    padding: 10px 0;
  }

  .game-btn {
    font-size: 12px;
    padding: 6px 12px;
    min-width: 60px;
  }
}
</style>
