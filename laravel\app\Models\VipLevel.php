<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VipLevel extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'level',
        'exp_required',
        'reward',
        'privileges',
    ];

    /**
     * 需要进行类型转换的属性
     *
     * @var array
     */
    protected $casts = [
        'privileges' => 'array',
    ];

    /**
     * 获取下一个VIP等级
     *
     * @return \App\Models\VipLevel|null
     */
    public function nextLevel()
    {
        return static::where('level', $this->level + 1)->first();
    }
}
