{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\message.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\utils\\message.js", "mtime": 1749735742214}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["logger", "MESSAGE_TYPES", "INFO", "SUCCESS", "WARNING", "ERROR", "DEFAULT_OPTIONS", "duration", "closable", "position", "messageContainer", "createMessageContainer", "document", "createElement", "className", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "createMessageElement", "content", "type", "options", "messageElement", "backgroundColor", "textColor", "borderColor", "textContent", "closeBtn", "innerHTML", "addEventListener", "removeMessage", "opacity", "transform", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "children", "length", "showMessage", "customOptions", "debug", "container", "offsetHeight", "error", "info", "success", "warning"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/utils/message.js"], "sourcesContent": ["/**\r\n * 消息提示工具\r\n * 提供统一的消息提示功能\r\n */\r\nimport logger from './logger';\r\n\r\n// 消息类型\r\nconst MESSAGE_TYPES = {\r\n  INFO: 'info',\r\n  SUCCESS: 'success',\r\n  WARNING: 'warning',\r\n  ERROR: 'error'\r\n};\r\n\r\n// 默认配置\r\nconst DEFAULT_OPTIONS = {\r\n  duration: 3000, // 显示时长，单位毫秒\r\n  closable: true, // 是否可关闭\r\n  position: 'top', // 显示位置\r\n};\r\n\r\n// 消息容器\r\nlet messageContainer = null;\r\n\r\n/**\r\n * 创建消息容器\r\n * @returns {HTMLElement} 消息容器元素\r\n */\r\nfunction createMessageContainer() {\r\n  if (messageContainer) return messageContainer;\r\n  \r\n  messageContainer = document.createElement('div');\r\n  messageContainer.className = 'game-message-container';\r\n  messageContainer.style.cssText = `\r\n    position: fixed;\r\n    top: 20px;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    z-index: 9999;\r\n    width: auto;\r\n    max-width: 80%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    pointer-events: none;\r\n  `;\r\n  \r\n  document.body.appendChild(messageContainer);\r\n  return messageContainer;\r\n}\r\n\r\n/**\r\n * 创建单个消息元素\r\n * @param {string} content - 消息内容\r\n * @param {string} type - 消息类型\r\n * @param {Object} options - 配置选项\r\n * @returns {HTMLElement} 消息元素\r\n */\r\nfunction createMessageElement(content, type, options) {\r\n  const messageElement = document.createElement('div');\r\n  messageElement.className = `game-message game-message-${type}`;\r\n  \r\n  // 样式\r\n  let backgroundColor = '#000033';\r\n  let textColor = '#ffffff';\r\n  let borderColor = '#5555ff';\r\n  \r\n  switch (type) {\r\n    case MESSAGE_TYPES.SUCCESS:\r\n      backgroundColor = '#003300';\r\n      borderColor = '#00ff00';\r\n      textColor = '#00ff00';\r\n      break;\r\n    case MESSAGE_TYPES.WARNING:\r\n      backgroundColor = '#333300';\r\n      borderColor = '#ffff00';\r\n      textColor = '#ffff00';\r\n      break;\r\n    case MESSAGE_TYPES.ERROR:\r\n      backgroundColor = '#330000';\r\n      borderColor = '#ff0000';\r\n      textColor = '#ff0000';\r\n      break;\r\n    default:\r\n      // 默认info样式\r\n      break;\r\n  }\r\n  \r\n  messageElement.style.cssText = `\r\n    padding: 10px 15px;\r\n    margin-bottom: 10px;\r\n    border-radius: 4px;\r\n    background-color: ${backgroundColor};\r\n    color: ${textColor};\r\n    border: 1px solid ${borderColor};\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);\r\n    font-size: 14px;\r\n    opacity: 0;\r\n    transition: opacity 0.3s, transform 0.3s;\r\n    pointer-events: auto;\r\n    max-width: 100%;\r\n    word-break: break-word;\r\n    text-align: center;\r\n  `;\r\n  \r\n  messageElement.textContent = content;\r\n  \r\n  // 添加关闭按钮\r\n  if (options.closable) {\r\n    const closeBtn = document.createElement('span');\r\n    closeBtn.className = 'game-message-close';\r\n    closeBtn.innerHTML = '&times;';\r\n    closeBtn.style.cssText = `\r\n      margin-left: 10px;\r\n      cursor: pointer;\r\n      font-weight: bold;\r\n    `;\r\n    closeBtn.addEventListener('click', () => {\r\n      removeMessage(messageElement);\r\n    });\r\n    messageElement.appendChild(closeBtn);\r\n  }\r\n  \r\n  return messageElement;\r\n}\r\n\r\n/**\r\n * 移除消息\r\n * @param {HTMLElement} messageElement - 消息元素\r\n */\r\nfunction removeMessage(messageElement) {\r\n  messageElement.style.opacity = '0';\r\n  messageElement.style.transform = 'translateY(-10px)';\r\n  \r\n  setTimeout(() => {\r\n    if (messageElement.parentNode) {\r\n      messageElement.parentNode.removeChild(messageElement);\r\n    }\r\n    \r\n    // 如果容器为空，移除容器\r\n    if (messageContainer && messageContainer.children.length === 0) {\r\n      document.body.removeChild(messageContainer);\r\n      messageContainer = null;\r\n    }\r\n  }, 300);\r\n}\r\n\r\n/**\r\n * 显示消息\r\n * @param {string} content - 消息内容\r\n * @param {string} type - 消息类型\r\n * @param {Object} customOptions - 自定义选项\r\n */\r\nfunction showMessage(content, type = MESSAGE_TYPES.INFO, customOptions = {}) {\r\n  try {\r\n    logger.debug(`[Message] ${type}: ${content}`);\r\n    \r\n    const options = { ...DEFAULT_OPTIONS, ...customOptions };\r\n    const container = createMessageContainer();\r\n    const messageElement = createMessageElement(content, type, options);\r\n    \r\n    container.appendChild(messageElement);\r\n    \r\n    // 触发重排以应用过渡效果\r\n    messageElement.offsetHeight;\r\n    messageElement.style.opacity = '1';\r\n    \r\n    // 设置自动关闭\r\n    if (options.duration > 0) {\r\n      setTimeout(() => {\r\n        removeMessage(messageElement);\r\n      }, options.duration);\r\n    }\r\n  } catch (error) {\r\n    logger.error('[Message] 显示消息失败:', error);\r\n  }\r\n}\r\n\r\n/**\r\n * 显示信息消息\r\n * @param {string} content - 消息内容\r\n * @param {Object} options - 配置选项\r\n */\r\nfunction info(content, options = {}) {\r\n  showMessage(content, MESSAGE_TYPES.INFO, options);\r\n}\r\n\r\n/**\r\n * 显示成功消息\r\n * @param {string} content - 消息内容\r\n * @param {Object} options - 配置选项\r\n */\r\nfunction success(content, options = {}) {\r\n  showMessage(content, MESSAGE_TYPES.SUCCESS, options);\r\n}\r\n\r\n/**\r\n * 显示警告消息\r\n * @param {string} content - 消息内容\r\n * @param {Object} options - 配置选项\r\n */\r\nfunction warning(content, options = {}) {\r\n  showMessage(content, MESSAGE_TYPES.WARNING, options);\r\n}\r\n\r\n/**\r\n * 显示错误消息\r\n * @param {string} content - 消息内容\r\n * @param {Object} options - 配置选项\r\n */\r\nfunction error(content, options = {}) {\r\n  showMessage(content, MESSAGE_TYPES.ERROR, options);\r\n}\r\n\r\n// 导出\r\nexport {\r\n  showMessage,\r\n  info,\r\n  success,\r\n  warning,\r\n  error,\r\n  MESSAGE_TYPES\r\n}; "], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,MAAM,MAAM,UAAU;;AAE7B;AACA,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,eAAe,GAAG;EACtBC,QAAQ,EAAE,IAAI;EAAE;EAChBC,QAAQ,EAAE,IAAI;EAAE;EAChBC,QAAQ,EAAE,KAAK,CAAE;AACnB,CAAC;;AAED;AACA,IAAIC,gBAAgB,GAAG,IAAI;;AAE3B;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAChC,IAAID,gBAAgB,EAAE,OAAOA,gBAAgB;EAE7CA,gBAAgB,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAChDH,gBAAgB,CAACI,SAAS,GAAG,wBAAwB;EACrDJ,gBAAgB,CAACK,KAAK,CAACC,OAAO,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAEDJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACR,gBAAgB,CAAC;EAC3C,OAAOA,gBAAgB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,oBAAoBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACpD,MAAMC,cAAc,GAAGX,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACpDU,cAAc,CAACT,SAAS,GAAG,6BAA6BO,IAAI,EAAE;;EAE9D;EACA,IAAIG,eAAe,GAAG,SAAS;EAC/B,IAAIC,SAAS,GAAG,SAAS;EACzB,IAAIC,WAAW,GAAG,SAAS;EAE3B,QAAQL,IAAI;IACV,KAAKpB,aAAa,CAACE,OAAO;MACxBqB,eAAe,GAAG,SAAS;MAC3BE,WAAW,GAAG,SAAS;MACvBD,SAAS,GAAG,SAAS;MACrB;IACF,KAAKxB,aAAa,CAACG,OAAO;MACxBoB,eAAe,GAAG,SAAS;MAC3BE,WAAW,GAAG,SAAS;MACvBD,SAAS,GAAG,SAAS;MACrB;IACF,KAAKxB,aAAa,CAACI,KAAK;MACtBmB,eAAe,GAAG,SAAS;MAC3BE,WAAW,GAAG,SAAS;MACvBD,SAAS,GAAG,SAAS;MACrB;IACF;MACE;MACA;EACJ;EAEAF,cAAc,CAACR,KAAK,CAACC,OAAO,GAAG;AACjC;AACA;AACA;AACA,wBAAwBQ,eAAe;AACvC,aAAaC,SAAS;AACtB,wBAAwBC,WAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAEDH,cAAc,CAACI,WAAW,GAAGP,OAAO;;EAEpC;EACA,IAAIE,OAAO,CAACd,QAAQ,EAAE;IACpB,MAAMoB,QAAQ,GAAGhB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC/Ce,QAAQ,CAACd,SAAS,GAAG,oBAAoB;IACzCc,QAAQ,CAACC,SAAS,GAAG,SAAS;IAC9BD,QAAQ,CAACb,KAAK,CAACC,OAAO,GAAG;AAC7B;AACA;AACA;AACA,KAAK;IACDY,QAAQ,CAACE,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACvCC,aAAa,CAACR,cAAc,CAAC;IAC/B,CAAC,CAAC;IACFA,cAAc,CAACL,WAAW,CAACU,QAAQ,CAAC;EACtC;EAEA,OAAOL,cAAc;AACvB;;AAEA;AACA;AACA;AACA;AACA,SAASQ,aAAaA,CAACR,cAAc,EAAE;EACrCA,cAAc,CAACR,KAAK,CAACiB,OAAO,GAAG,GAAG;EAClCT,cAAc,CAACR,KAAK,CAACkB,SAAS,GAAG,mBAAmB;EAEpDC,UAAU,CAAC,MAAM;IACf,IAAIX,cAAc,CAACY,UAAU,EAAE;MAC7BZ,cAAc,CAACY,UAAU,CAACC,WAAW,CAACb,cAAc,CAAC;IACvD;;IAEA;IACA,IAAIb,gBAAgB,IAAIA,gBAAgB,CAAC2B,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9D1B,QAAQ,CAACK,IAAI,CAACmB,WAAW,CAAC1B,gBAAgB,CAAC;MAC3CA,gBAAgB,GAAG,IAAI;IACzB;EACF,CAAC,EAAE,GAAG,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6B,WAAWA,CAACnB,OAAO,EAAEC,IAAI,GAAGpB,aAAa,CAACC,IAAI,EAAEsC,aAAa,GAAG,CAAC,CAAC,EAAE;EAC3E,IAAI;IACFxC,MAAM,CAACyC,KAAK,CAAC,aAAapB,IAAI,KAAKD,OAAO,EAAE,CAAC;IAE7C,MAAME,OAAO,GAAG;MAAE,GAAGhB,eAAe;MAAE,GAAGkC;IAAc,CAAC;IACxD,MAAME,SAAS,GAAG/B,sBAAsB,CAAC,CAAC;IAC1C,MAAMY,cAAc,GAAGJ,oBAAoB,CAACC,OAAO,EAAEC,IAAI,EAAEC,OAAO,CAAC;IAEnEoB,SAAS,CAACxB,WAAW,CAACK,cAAc,CAAC;;IAErC;IACAA,cAAc,CAACoB,YAAY;IAC3BpB,cAAc,CAACR,KAAK,CAACiB,OAAO,GAAG,GAAG;;IAElC;IACA,IAAIV,OAAO,CAACf,QAAQ,GAAG,CAAC,EAAE;MACxB2B,UAAU,CAAC,MAAM;QACfH,aAAa,CAACR,cAAc,CAAC;MAC/B,CAAC,EAAED,OAAO,CAACf,QAAQ,CAAC;IACtB;EACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACd5C,MAAM,CAAC4C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;EAC1C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACzB,OAAO,EAAEE,OAAO,GAAG,CAAC,CAAC,EAAE;EACnCiB,WAAW,CAACnB,OAAO,EAAEnB,aAAa,CAACC,IAAI,EAAEoB,OAAO,CAAC;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASwB,OAAOA,CAAC1B,OAAO,EAAEE,OAAO,GAAG,CAAC,CAAC,EAAE;EACtCiB,WAAW,CAACnB,OAAO,EAAEnB,aAAa,CAACE,OAAO,EAAEmB,OAAO,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyB,OAAOA,CAAC3B,OAAO,EAAEE,OAAO,GAAG,CAAC,CAAC,EAAE;EACtCiB,WAAW,CAACnB,OAAO,EAAEnB,aAAa,CAACG,OAAO,EAAEkB,OAAO,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASsB,KAAKA,CAACxB,OAAO,EAAEE,OAAO,GAAG,CAAC,CAAC,EAAE;EACpCiB,WAAW,CAACnB,OAAO,EAAEnB,aAAa,CAACI,KAAK,EAAEiB,OAAO,CAAC;AACpD;;AAEA;AACA,SACEiB,WAAW,EACXM,IAAI,EACJC,OAAO,EACPC,OAAO,EACPH,KAAK,EACL3C,aAAa", "ignoreList": []}]}