{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\StorageCleanup.vue?vue&type=template&id=41f838c2&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\StorageCleanup.vue", "mtime": 1749868572976}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "_v", "_s", "formatBytes", "storageInfo", "currentSize", "keys", "length", "_l", "storageItems", "item", "key", "class", "size", "attrs", "disabled", "on", "click", "$event", "removeItem", "clearExpiredCache", "clearAllCache", "clearAllStorage", "refreshInfo", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/views/debug/StorageCleanup.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"storage-cleanup\" }, [\n    _vm._m(0),\n    _c(\"div\", { staticClass: \"storage-info\" }, [\n      _c(\"h3\", [_vm._v(\"当前存储状态\")]),\n      _c(\"div\", { staticClass: \"info-item\" }, [\n        _c(\"span\", [_vm._v(\"总存储大小:\")]),\n        _c(\"span\", [\n          _vm._v(_vm._s(_vm.formatBytes(_vm.storageInfo.currentSize))),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"info-item\" }, [\n        _c(\"span\", [_vm._v(\"存储项目数量:\")]),\n        _c(\"span\", [_vm._v(_vm._s(_vm.storageInfo.keys.length))]),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"storage-items\" }, [\n      _c(\"h3\", [_vm._v(\"存储项目详情\")]),\n      _c(\n        \"div\",\n        { staticClass: \"item-list\" },\n        _vm._l(_vm.storageItems, function (item) {\n          return _c(\n            \"div\",\n            {\n              key: item.key,\n              staticClass: \"storage-item\",\n              class: { \"large-item\": item.size > 50000 },\n            },\n            [\n              _c(\"div\", { staticClass: \"item-info\" }, [\n                _c(\"span\", { staticClass: \"item-key\" }, [\n                  _vm._v(_vm._s(item.key)),\n                ]),\n                _c(\"span\", { staticClass: \"item-size\" }, [\n                  _vm._v(_vm._s(_vm.formatBytes(item.size))),\n                ]),\n              ]),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"remove-btn\",\n                  attrs: { disabled: item.key === \"szxy-game-state\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.removeItem(item.key)\n                    },\n                  },\n                },\n                [_vm._v(\" 删除 \")]\n              ),\n            ]\n          )\n        }),\n        0\n      ),\n    ]),\n    _c(\"div\", { staticClass: \"actions\" }, [\n      _c(\n        \"button\",\n        { staticClass: \"action-btn\", on: { click: _vm.clearExpiredCache } },\n        [_vm._v(\"清理过期缓存\")]\n      ),\n      _c(\n        \"button\",\n        { staticClass: \"action-btn warning\", on: { click: _vm.clearAllCache } },\n        [_vm._v(\"清理所有缓存\")]\n      ),\n      _c(\n        \"button\",\n        {\n          staticClass: \"action-btn danger\",\n          on: { click: _vm.clearAllStorage },\n        },\n        [_vm._v(\"清空所有存储\")]\n      ),\n      _c(\n        \"button\",\n        { staticClass: \"action-btn\", on: { click: _vm.refreshInfo } },\n        [_vm._v(\"刷新信息\")]\n      ),\n    ]),\n    _vm._m(1),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header\" }, [\n      _c(\"h2\", [_vm._v(\"存储空间清理\")]),\n      _c(\"p\", [_vm._v('如果遇到\"请求头过大\"错误，可以使用此工具清理存储空间')]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"tips\" }, [\n      _c(\"h3\", [_vm._v(\"使用提示\")]),\n      _c(\"ul\", [\n        _c(\"li\", [_vm._v('如果遇到HTTP 431错误，建议先点击\"清理过期缓存\"')]),\n        _c(\"li\", [\n          _vm._v(\"红色标记的项目是大型存储项目（>50KB），可能导致请求头过大\"),\n        ]),\n        _c(\"li\", [_vm._v('\"szxy-game-state\"是游戏核心状态，不建议删除')]),\n        _c(\"li\", [_vm._v(\"清理后需要重新登录和选择大区\")]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CACnDH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC9BJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,WAAW,CAACP,GAAG,CAACQ,WAAW,CAACC,WAAW,CAAC,CAAC,CAAC,CAC7D,CAAC,CACH,CAAC,EACFR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC/BJ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACQ,WAAW,CAACE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC1D,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BJ,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5BH,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACa,YAAY,EAAE,UAAUC,IAAI,EAAE;IACvC,OAAOb,EAAE,CACP,KAAK,EACL;MACEc,GAAG,EAAED,IAAI,CAACC,GAAG;MACbZ,WAAW,EAAE,cAAc;MAC3Ba,KAAK,EAAE;QAAE,YAAY,EAAEF,IAAI,CAACG,IAAI,GAAG;MAAM;IAC3C,CAAC,EACD,CACEhB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACtCH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACQ,IAAI,CAACC,GAAG,CAAC,CAAC,CACzB,CAAC,EACFd,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACvCH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,WAAW,CAACO,IAAI,CAACG,IAAI,CAAC,CAAC,CAAC,CAC3C,CAAC,CACH,CAAC,EACFhB,EAAE,CACA,QAAQ,EACR;MACEE,WAAW,EAAE,YAAY;MACzBe,KAAK,EAAE;QAAEC,QAAQ,EAAEL,IAAI,CAACC,GAAG,KAAK;MAAkB,CAAC;MACnDK,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOtB,GAAG,CAACuB,UAAU,CAACT,IAAI,CAACC,GAAG,CAAC;QACjC;MACF;IACF,CAAC,EACD,CAACf,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CAEL,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,YAAY;IAAEiB,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAACwB;IAAkB;EAAE,CAAC,EACnE,CAACxB,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDJ,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,oBAAoB;IAAEiB,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAACyB;IAAc;EAAE,CAAC,EACvE,CAACzB,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,mBAAmB;IAChCiB,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAAC0B;IAAgB;EACnC,CAAC,EACD,CAAC1B,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDJ,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,YAAY;IAAEiB,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAAC2B;IAAY;EAAE,CAAC,EAC7D,CAAC3B,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,EACFL,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,CACV,CAAC;AACJ,CAAC;AACD,IAAIwB,eAAe,GAAG,CACpB,YAAY;EACV,IAAI5B,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,CACjD,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIL,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACxCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAClDJ,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACK,EAAE,CAAC,iCAAiC,CAAC,CAC1C,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,gCAAgC,CAAC,CAAC,CAAC,EACpDJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CACrC,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDN,MAAM,CAAC8B,aAAa,GAAG,IAAI;AAE3B,SAAS9B,MAAM,EAAE6B,eAAe", "ignoreList": []}]}