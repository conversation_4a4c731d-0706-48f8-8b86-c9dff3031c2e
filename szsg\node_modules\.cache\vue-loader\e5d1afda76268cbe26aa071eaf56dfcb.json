{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue?vue&type=style&index=0&id=524b12ae&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\CharacterSelect.vue", "mtime": 1749699239704}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CharacterSelect.vue"], "names": [], "mappings": ";AAkVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "CharacterSelect.vue", "sourceRoot": "src/views/setup", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"character-select-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <p class=\"page-subtitle\">请选择您要使用的角色，或创建新角色</p>\n        <div v-if=\"currentRegion\" class=\"current-region\">\n          当前大区: <span class=\"region-name\">{{ currentRegion.name }}</span>\n        </div>\n\n        <!-- 调试信息 -->\n        <div v-if=\"$route.query.debug\" class=\"debug-info\">\n          <h4>调试信息:</h4>\n          <p>加载状态: {{ isLoading }}</p>\n          <p>错误信息: {{ error || '无' }}</p>\n          <p>角色数量: {{ characters.length }}</p>\n          <p>当前大区ID: {{ currentRegion?.id }}</p>\n          <details>\n            <summary>角色数据</summary>\n            <pre>{{ JSON.stringify(characters, null, 2) }}</pre>\n          </details>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-spinner\"></div>\n        <p class=\"loading-text\">正在加载角色列表...</p>\n      </div>\n\n      <!-- 错误提示 -->\n      <div v-if=\"error && !isLoading\" class=\"error-container\">\n        <div class=\"error-message\">\n          <i class=\"error-icon\">⚠️</i>\n          <p>{{ error }}</p>\n          <button @click=\"loadCharacterList\" class=\"retry-btn\">重试</button>\n        </div>\n      </div>\n\n      <!-- 角色列表 -->\n      <div v-if=\"!isLoading && !error\" class=\"characters-container\">\n        <!-- 空状态提示 -->\n        <div v-if=\"characters.length === 0\" class=\"empty-state\">\n          <div class=\"empty-icon\">👤</div>\n          <h3>还没有角色</h3>\n          <p>在当前大区 \"{{ currentRegion?.name }}\" 中还没有创建角色</p>\n        </div>\n\n        <!-- 角色卡片网格 -->\n        <div v-else class=\"characters-grid\">\n          <!-- 现有角色 -->\n          <div \n            v-for=\"character in characters\" \n            :key=\"character.id\"\n            class=\"character-card\"\n            :class=\"{ 'selected': selectedCharacter?.id === character.id }\"\n            @click=\"selectCharacterLocal(character)\"\n          >\n            <div class=\"character-avatar\">\n              <img :src=\"character.avatar || '/static/game/avatars/default.png'\" :alt=\"character.name\" />\n              <div class=\"character-level\">Lv.{{ character.level }}</div>\n            </div>\n            <div class=\"character-info\">\n              <h3 class=\"character-name\">{{ character.name }}</h3>\n              <p class=\"character-class\">{{ getClassName(character.profession || character.class) }}</p>\n              <div class=\"character-stats\">\n                <span class=\"stat\">经验: {{ character.experience || character.exp || 0 }}</span>\n                <span class=\"stat\">创建时间: {{ formatDate(character.created_at || character.createdAt) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 创建新角色按钮 -->\n          <div \n            v-if=\"characters.length < 3\"\n            class=\"character-card create-new\"\n            @click=\"createNewCharacter\"\n          >\n            <div class=\"create-icon\">\n              <i class=\"plus-icon\">+</i>\n            </div>\n            <div class=\"create-text\">\n              <h3>创建新角色</h3>\n              <p>还可以创建 {{ 3 - characters.length }} 个角色</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <!-- 返回大区选择图片按钮 -->\n          <div\n            @click=\"goBack\"\n            @mousedown=\"handleBackMouseDown\"\n            @mouseup=\"handleBackMouseUp\"\n            @mouseleave=\"handleBackMouseUp\"\n            class=\"return-btn\"\n            :class=\"{ 'pressed': isBackPressed }\"\n          >\n            <img\n              :src=\"getBackButtonImage()\"\n              alt=\"返回大区选择\"\n              class=\"return-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n\n          <!-- 如果没有角色，显示创建角色图片按钮 -->\n          <div\n            v-if=\"characters.length === 0\"\n            @click=\"createNewCharacter\"\n            @mousedown=\"handleCreateMouseDown\"\n            @mouseup=\"handleCreateMouseUp\"\n            @mouseleave=\"handleCreateMouseUp\"\n            class=\"create-character-btn\"\n            :class=\"{ 'pressed': isCreatePressed }\"\n          >\n            <img\n              :src=\"getCreateButtonImage()\"\n              alt=\"创建第一个角色\"\n              class=\"create-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n\n          <!-- 如果有角色，显示进入游戏图片按钮 -->\n          <div\n            v-else\n            @click=\"confirmSelection\"\n            @mousedown=\"handleEnterMouseDown\"\n            @mouseup=\"handleEnterMouseUp\"\n            @mouseleave=\"handleEnterMouseUp\"\n            class=\"enter-game-btn\"\n            :class=\"{ 'disabled': !selectedCharacter, 'pressed': isEnterPressed }\"\n          >\n            <img\n              :src=\"getEnterButtonImage()\"\n              alt=\"进入游戏\"\n              class=\"enter-btn-img\"\n              draggable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport { mapState, mapActions } from 'vuex'\nimport { regionService } from '@/api'\nimport logger from '@/utils/logger'\n\nexport default {\n  name: 'CharacterSelect',\n  components: {\n    GameLayout\n  },\n  data() {\n    return {\n      selectedCharacter: null,\n      currentRegion: null,\n      // 按钮状态\n      isBackPressed: false,\n      isCreatePressed: false,\n      isEnterPressed: false\n    }\n  },\n  computed: {\n    ...mapState('character', ['isLoading', 'error']),\n\n    // 获取当前大区的角色列表\n    characters() {\n      if (!this.currentRegion) return [];\n      return this.$store.getters['character/charactersByRegion'](this.currentRegion.id);\n    }\n  },\n  async created() {\n    logger.debug('[CharacterSelect] 页面初始化')\n\n    // 获取当前选择的大区\n    this.currentRegion = regionService.getCurrentRegion()\n    logger.debug('[CharacterSelect] 当前大区:', this.currentRegion)\n\n    if (!this.currentRegion) {\n      this.showToast('请先选择大区')\n      this.$router.push('/setup/region-select')\n      return\n    }\n\n    await this.loadCharacterList()\n  },\n  methods: {\n    ...mapActions('character', ['loadCharacters', 'selectCharacter']),\n\n    async loadCharacterList() {\n      if (!this.currentRegion) return\n\n      try {\n        logger.debug('[CharacterSelect] 开始加载角色列表，大区ID:', this.currentRegion.id)\n        await this.loadCharacters(this.currentRegion.id)\n        logger.debug('[CharacterSelect] 角色列表加载完成，角色数量:', this.characters.length)\n\n        // 如果没有角色，显示提示\n        if (this.characters.length === 0) {\n          logger.info('[CharacterSelect] 当前大区没有角色')\n        }\n      } catch (error) {\n        logger.error('[CharacterSelect] 加载角色列表失败:', error)\n        this.showToast('加载角色列表失败: ' + (error.message || '未知错误'))\n      }\n    },\n\n    selectCharacterLocal(character) {\n      this.selectedCharacter = character\n      logger.debug('[CharacterSelect] 选择角色:', character.name)\n    },\n\n    async confirmSelection() {\n      if (!this.selectedCharacter) {\n        this.showToast('请先选择一个角色')\n        return\n      }\n\n      try {\n        const success = await this.selectCharacter(this.selectedCharacter)\n        if (success) {\n          this.showToast('角色选择成功，正在进入游戏...')\n          // 跳转到游戏主界面\n          setTimeout(() => {\n            this.$router.push('/game/main')\n          }, 1000)\n        } else {\n          this.showToast('选择角色失败，请重试')\n        }\n      } catch (error) {\n        logger.error('[CharacterSelect] 确认选择失败:', error)\n        this.showToast('选择角色失败，请重试')\n      }\n    },\n\n    createNewCharacter() {\n      this.$router.push('/setup/create-character')\n    },\n\n    goBack() {\n      this.$router.push('/setup/region-select')\n    },\n\n    // 按钮状态处理方法\n    handleBackMouseDown() {\n      this.isBackPressed = true\n    },\n\n    handleBackMouseUp() {\n      this.isBackPressed = false\n    },\n\n    handleCreateMouseDown() {\n      this.isCreatePressed = true\n    },\n\n    handleCreateMouseUp() {\n      this.isCreatePressed = false\n    },\n\n    handleEnterMouseDown() {\n      if (!this.selectedCharacter) return\n      this.isEnterPressed = true\n    },\n\n    handleEnterMouseUp() {\n      this.isEnterPressed = false\n    },\n\n    // 按钮图片获取方法\n    getBackButtonImage() {\n      return this.isBackPressed\n        ? '/static/game/UI/anniu/fhui_.png'\n        : '/static/game/UI/anniu/fhui_2.png'\n    },\n\n    getCreateButtonImage() {\n      return this.isCreatePressed\n        ? '/static/game/UI/anniu/cj_1.png'\n        : '/static/game/UI/anniu/cj.png'\n    },\n\n    getEnterButtonImage() {\n      if (!this.selectedCharacter) {\n        return '/static/game/UI/anniu/jr_3.png' // 禁用状态也使用默认图片\n      }\n      return this.isEnterPressed\n        ? '/static/game/UI/anniu/jr_4.png'\n        : '/static/game/UI/anniu/jr_3.png'\n    },\n\n    getClassName(profession) {\n      const classMap = {\n        'warrior': '武士',\n        'scholar': '文人',\n        'mystic': '异人'\n      }\n      return classMap[profession] || profession\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '未知'\n      const date = new Date(dateString)\n      return date.toLocaleDateString('zh-CN')\n    },\n\n    showToast(message) {\n      const toast = document.createElement('div')\n      toast.textContent = message\n      toast.style.cssText = `\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 6px;\n        z-index: 10000;\n        font-size: 14px;\n      `\n      document.body.appendChild(toast)\n      setTimeout(() => {\n        document.body.removeChild(toast)\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.character-select-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.page-title {\n  font-size: 32px;\n  color: #d4af37;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.page-subtitle {\n  font-size: 16px;\n  color: #ccc;\n  margin: 0 0 10px 0;\n}\n\n.current-region {\n  font-size: 14px;\n  color: #999;\n}\n\n.region-name {\n  color: #d4af37;\n  font-weight: bold;\n}\n\n.debug-info {\n  margin-top: 20px;\n  padding: 15px;\n  background: rgba(0, 0, 0, 0.3);\n  border: 1px solid #444;\n  border-radius: 5px;\n  font-size: 12px;\n  text-align: left;\n}\n\n.debug-info h4 {\n  color: #d4af37;\n  margin: 0 0 10px 0;\n}\n\n.debug-info p {\n  margin: 5px 0;\n  color: #ccc;\n}\n\n.debug-info pre {\n  background: rgba(0, 0, 0, 0.5);\n  padding: 10px;\n  border-radius: 3px;\n  overflow-x: auto;\n  font-size: 11px;\n  color: #fff;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #333;\n  border-top: 4px solid #d4af37;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  color: #ccc;\n  font-size: 16px;\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex: 1;\n  min-height: 300px;\n}\n\n.error-message {\n  text-align: center;\n  color: #ff6b6b;\n  background: rgba(255, 107, 107, 0.1);\n  padding: 30px;\n  border-radius: 10px;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.retry-btn {\n  margin-top: 15px;\n  padding: 8px 16px;\n  background: #d4af37;\n  color: #000;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.retry-btn:hover {\n  background: #b8941f;\n}\n\n.characters-container {\n  flex: 1;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #ccc;\n}\n\n.empty-icon {\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.5;\n}\n\n.empty-state h3 {\n  color: #d4af37;\n  margin: 0 0 10px 0;\n  font-size: 24px;\n}\n\n.empty-state p {\n  margin: 0 0 30px 0;\n  font-size: 16px;\n}\n\n.characters-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  margin-bottom: 30px;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.character-card {\n  background: linear-gradient(135deg, rgba(30, 30, 80, 0.9), rgba(50, 50, 120, 0.8));\n  border: 2px solid #4a5568;\n  border-radius: 8px;\n  padding: 15px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  min-height: 100px;\n  position: relative;\n  overflow: hidden;\n}\n\n.character-card:hover {\n  border-color: #d4af37;\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(255, 215, 0, 0.1));\n  transform: translateY(-2px);\n}\n\n.character-card.selected {\n  border-color: #d4af37;\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(255, 215, 0, 0.2));\n  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);\n  transform: translateY(-2px);\n}\n\n.character-card.selected::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);\n  pointer-events: none;\n}\n\n.character-card.create-new {\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  border-style: dashed;\n  border-color: #666;\n  background: linear-gradient(135deg, rgba(60, 60, 60, 0.3), rgba(80, 80, 80, 0.2));\n}\n\n.character-card.create-new:hover {\n  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(255, 215, 0, 0.05));\n  border-color: #d4af37;\n}\n\n.character-avatar {\n  position: relative;\n  margin-right: 20px;\n  flex-shrink: 0;\n}\n\n.character-avatar img {\n  width: 70px;\n  height: 70px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 3px solid #4a5568;\n}\n\n.character-level {\n  position: absolute;\n  bottom: -5px;\n  right: -5px;\n  background: linear-gradient(135deg, #d4af37, #ffd700);\n  color: #000;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n  border: 1px solid #b8941f;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.character-info {\n  text-align: center;\n}\n\n.character-name {\n  font-size: 18px;\n  color: #d4af37;\n  margin: 0 0 5px 0;\n}\n\n.character-class {\n  font-size: 14px;\n  color: #ccc;\n  margin: 0 0 10px 0;\n}\n\n.character-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.stat {\n  font-size: 12px;\n  color: #999;\n}\n\n.create-icon {\n  margin-bottom: 15px;\n}\n\n.plus-icon {\n  font-size: 48px;\n  color: #666;\n  font-style: normal;\n}\n\n.create-text h3 {\n  color: #d4af37;\n  margin: 0 0 8px 0;\n  font-size: 18px;\n}\n\n.create-text p {\n  color: #999;\n  margin: 0;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 30px;\n  padding: 20px 0;\n  flex-wrap: nowrap; /* 确保按钮不换行 */\n}\n\n\n\n/* 图片按钮通用样式 */\n.create-character-btn,\n.return-btn,\n.enter-game-btn {\n  cursor: pointer;\n  display: inline-block;\n  transition: all 0.2s ease;\n  flex-shrink: 0; /* 防止按钮被压缩 */\n}\n\n.enter-game-btn.disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n/* 创建角色图片按钮样式 */\n.create-character-btn.pressed .create-btn-img {\n  transform: scale(0.95);\n}\n\n.create-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 返回按钮图片样式 */\n.return-btn.pressed .return-btn-img {\n  transform: scale(0.95);\n}\n\n.return-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 进入游戏按钮图片样式 */\n.enter-game-btn.pressed .enter-btn-img {\n  transform: scale(0.95);\n}\n\n.enter-btn-img {\n  display: block;\n  max-width: 200px;\n  height: auto;\n  transition: transform 0.1s ease;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .character-select-container {\n    padding: 15px;\n  }\n\n  .page-title {\n    font-size: 24px;\n  }\n\n  .characters-grid {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n\n  .action-buttons {\n    flex-direction: row;\n    flex-wrap: nowrap; /* 保持按钮在同一行 */\n    justify-content: center;\n    gap: 15px;\n    padding: 15px 0;\n  }\n\n  .create-character-btn,\n  .return-btn,\n  .enter-game-btn {\n    display: flex;\n    justify-content: center;\n    flex-shrink: 0; /* 防止按钮被压缩 */\n  }\n\n  .create-btn-img,\n  .return-btn-img,\n  .enter-btn-img {\n    max-width: 120px; /* 在移动设备上稍微缩小按钮 */\n  }\n\n  .empty-state {\n    padding: 40px 15px;\n  }\n\n  .empty-state h3 {\n    font-size: 20px;\n  }\n\n  .empty-state p {\n    font-size: 14px;\n  }\n}\n</style>\n"]}]}