<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>{{ config('admin.name', '神之西游管理系统') }} - @yield('title', '后台管理')</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <!-- LayUI CSS -->
  <link rel="stylesheet" href="https://www.layuicdn.com/layui-v2.5.6/css/layui.css" media="all">
  <!-- 自定义CSS -->
  <style>
    .layui-layout-admin .layui-header {
      background-color: #23262E;
    }
    .layui-layout-admin .layui-logo {
      color: #fff;
      font-size: 18px;
    }
    .main-content {
      padding: 15px;
    }
    .layui-card-header {
      font-size: 16px;
      font-weight: 600;
    }
    .layui-table-cell {
      height: auto;
      line-height: 28px;
    }
    .layui-card {
      margin-bottom: 15px;
    }
    .layui-breadcrumb {
      margin-bottom: 10px;
    }
  </style>
  @yield('css')
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
  <!-- 顶部导航 -->
  <div class="layui-header">
    <div class="layui-logo">{{ config('admin.name', '神之西游管理系统') }}</div>
    <ul class="layui-nav layui-layout-right">
      <li class="layui-nav-item">
        <a href="javascript:;">
          <i class="layui-icon layui-icon-username"></i>
          {{ Auth::guard('admin')->user()->name ?? '管理员' }}
        </a>
        <dl class="layui-nav-child">
          <dd><a href="{{ route('admin.change-password') }}">修改密码</a></dd>
        </dl>
      </li>
      <li class="layui-nav-item">
        <a href="javascript:;" onclick="document.getElementById('logout-form').submit();">
          <i class="layui-icon layui-icon-logout"></i> 退出
        </a>
        <form id="logout-form" action="{{ route('admin.logout') }}" method="POST" style="display: none;">
            @csrf
        </form>
      </li>
    </ul>
  </div>

  <!-- 左侧菜单 -->
  <div class="layui-side layui-bg-black">
    <div class="layui-side-scroll">
      <ul class="layui-nav layui-nav-tree" lay-filter="test">
        @foreach(config('admin.menu', []) as $menu)
          @if(isset($menu['children']))
            <li class="layui-nav-item {{ request()->is(str_replace('/szsg-admin/', 'szsg-admin/', $menu['url'] ?? '')) ? 'layui-nav-itemed' : '' }}">
              <a href="javascript:;">{{ $menu['title'] }}</a>
              <dl class="layui-nav-child">
                @foreach($menu['children'] as $subMenu)
                  <dd class="{{ request()->is(str_replace('/szsg-admin/', 'szsg-admin/', $subMenu['url'] ?? '')) ? 'layui-this' : '' }}">
                    <a href="{{ $subMenu['url'] }}">{{ $subMenu['title'] }}</a>
                  </dd>
                @endforeach
              </dl>
            </li>
          @else
            <li class="layui-nav-item {{ request()->is(str_replace('/szsg-admin/', 'szsg-admin/', $menu['url'] ?? '')) ? 'layui-this' : '' }}">
              <a href="{{ $menu['url'] }}">{{ $menu['title'] }}</a>
            </li>
          @endif
        @endforeach
      </ul>
    </div>
  </div>

  <!-- 内容主体 -->
  <div class="layui-body">
    <div class="main-content">
      <!-- 面包屑导航 -->
      <div class="layui-breadcrumb">
        <a href="{{ route('admin.admin') }}">首页</a>
        @yield('breadcrumb')
      </div>

      <!-- 页面标题 -->
      <div class="layui-card">
        <div class="layui-card-header">@yield('page-title', '后台管理')</div>
        <div class="layui-card-body">
          <!-- 错误提示 -->
          @if($errors->any())
            <div class="layui-bg-red" style="padding: 10px; margin-bottom: 15px;">
              <ul style="margin: 0; padding-left: 20px;">
                @foreach($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
            </div>
          @endif

          <!-- 成功提示 -->
          @if(session('success'))
            <div class="layui-bg-green" style="padding: 10px; margin-bottom: 15px;">
              <i class="layui-icon layui-icon-ok"></i> {{ session('success') }}
            </div>
          @endif

          <!-- 主内容 -->
          @yield('content')
        </div>
      </div>
    </div>
  </div>

  <!-- 底部 -->
  <div class="layui-footer">
    © {{ date('Y') }} {{ config('admin.name', '神之西游管理系统') }}
  </div>
</div>

<!-- LayUI JS -->
<script src="https://www.layuicdn.com/layui-v2.5.6/layui.js"></script>
<script>
  //JavaScript代码区域
  layui.use(['element', 'layer', 'form', 'table'], function(){
    var element = layui.element;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;

    // CSRF设置
    var token = document.head.querySelector('meta[name="csrf-token"]');
    if (token) {
      layui.$.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': token.content
        }
      });
    }
  });
</script>
@yield('js')
</body>
</html>
