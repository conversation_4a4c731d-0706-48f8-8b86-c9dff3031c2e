{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\RegionSelect.vue?vue&type=template&id=f8b01bc8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\setup\\RegionSelect.vue", "mtime": 1750329555400}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}