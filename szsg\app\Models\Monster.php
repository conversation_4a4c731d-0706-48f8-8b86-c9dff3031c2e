<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Monster extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'title',
        'description',
        'level',
        'type',
        'element',
        'size',
        'threat_level',
        'avatar',
        'max_health',
        'max_mp',
        'attack',
        'defense',
        'speed',
        'constitution',
        'intelligence',
        'strength',
        'agility',
        'exp_reward',
        'gold_reward',
        'item_drops'
    ];

    protected $casts = [
        'item_drops' => 'array'
    ];

    /**
     * 关联战斗记录
     */
    public function battles(): HasMany
    {
        return $this->hasMany(Battle::class);
    }

    /**
     * 获取当前生命值（用于战斗中）
     */
    public function getCurrentHealth(): int
    {
        return $this->max_health;
    }

    /**
     * 获取当前魔法值（用于战斗中）
     */
    public function getCurrentMp(): int
    {
        return $this->max_mp;
    }

    /**
     * 计算基础攻击伤害
     */
    public function calculateBaseDamage(): int
    {
        // 基础伤害 = 攻击力 + 力量/2 + 随机波动(±20%)
        $baseDamage = $this->attack + intval($this->strength / 2);
        $variation = $baseDamage * 0.2;
        return intval($baseDamage + rand(-$variation, $variation));
    }

    /**
     * 计算防御减免
     */
    public function calculateDefenseReduction(int $incomingDamage): int
    {
        // 防御减免 = 防御力 + 体质/3
        $defenseValue = $this->defense + intval($this->constitution / 3);
        $reduction = intval($incomingDamage * ($defenseValue / ($defenseValue + 100)));
        return max(1, $incomingDamage - $reduction); // 至少造成1点伤害
    }

    /**
     * 获取掉落物品
     */
    public function getDroppedItems(): array
    {
        $drops = [];
        $itemDrops = $this->item_drops ?? [];

        foreach ($itemDrops as $drop) {
            $chance = $drop['chance'] ?? 0;
            if (rand(1, 100) <= $chance) {
                $drops[] = [
                    'item_id' => $drop['item_id'],
                    'quantity' => $drop['quantity'] ?? 1,
                    'name' => $drop['name'] ?? '未知物品'
                ];
            }
        }

        return $drops;
    }

    /**
     * 获取经验奖励（基于等级调整）
     */
    public function getExpReward(int $characterLevel): int
    {
        $baseExp = $this->exp_reward;
        $levelDiff = $this->level - $characterLevel;
        
        // 等级差异调整：怪物等级高给更多经验，低则减少
        if ($levelDiff > 0) {
            $baseExp = intval($baseExp * (1 + $levelDiff * 0.1));
        } elseif ($levelDiff < 0) {
            $baseExp = intval($baseExp * max(0.1, 1 + $levelDiff * 0.1));
        }
        
        return max(1, $baseExp);
    }

    /**
     * 获取货币奖励（基于等级调整）
     */
    public function getGoldReward(int $characterLevel): int
    {
        $baseGold = $this->gold_reward;
        $levelDiff = $this->level - $characterLevel;
        
        if ($levelDiff > 0) {
            $baseGold = intval($baseGold * (1 + $levelDiff * 0.05));
        } elseif ($levelDiff < 0) {
            $baseGold = intval($baseGold * max(0.1, 1 + $levelDiff * 0.05));
        }
        
        return max(1, $baseGold);
    }
}
