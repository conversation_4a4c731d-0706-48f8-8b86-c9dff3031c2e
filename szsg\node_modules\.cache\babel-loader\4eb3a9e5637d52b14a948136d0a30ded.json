{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\debug\\BattleTest.vue", "mtime": 1749890251457}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["BattleAnimation", "battleService", "logger", "name", "components", "data", "testMonsters", "id", "level", "type", "max_health", "attack", "avatar", "isTestingAPI", "apiTestResult", "methods", "startTestBattle", "monster", "currentCharacter", "$store", "getters", "testCharacterId", "storedCharacter", "localStorage", "getItem", "character", "JSON", "parse", "error", "$toast", "$router", "push", "path", "query", "characterId", "monsterId", "locationId", "testAttackAnimation", "$refs", "animationTest", "playAttackAnimation", "testDamageAnimation", "playDamageAnimation", "testCriticalAnimation", "testHealAnimation", "playHealAnimation", "testSkillAnimation", "playSkillEffect", "testParticleAnimation", "playParticleEffect", "testBattleAPI", "info", "testData", "character_id", "monster_id", "location_id", "result", "startBattle", "success", "timestamp", "Date", "toISOString", "_error$response", "_error$response2", "message", "details", "response", "status", "config", "mounted", "window", "particlesJS", "warn"], "sources": ["src/views/debug/BattleTest.vue"], "sourcesContent": ["<template>\n  <div class=\"battle-test\">\n    <h1>战斗系统测试</h1>\n    \n    <div class=\"test-section\">\n      <h2>测试怪物列表</h2>\n      <div class=\"monster-list\">\n        <div \n          v-for=\"monster in testMonsters\" \n          :key=\"monster.id\"\n          class=\"monster-card\"\n          @click=\"startTestBattle(monster)\"\n        >\n          <img :src=\"monster.avatar\" :alt=\"monster.name\" class=\"monster-avatar\" />\n          <div class=\"monster-info\">\n            <h3>{{ monster.name }}</h3>\n            <p>等级: {{ monster.level }}</p>\n            <p>类型: {{ monster.type }}</p>\n            <p>生命: {{ monster.max_health }}</p>\n            <p>攻击: {{ monster.attack }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h2>动画测试</h2>\n      <div class=\"animation-test\">\n        <BattleAnimation ref=\"animationTest\" />\n        <div class=\"animation-controls\">\n          <button @click=\"testAttackAnimation\">测试攻击动画</button>\n          <button @click=\"testDamageAnimation\">测试伤害动画</button>\n          <button @click=\"testCriticalAnimation\">测试暴击动画</button>\n          <button @click=\"testHealAnimation\">测试治疗动画</button>\n          <button @click=\"testSkillAnimation\">测试技能特效</button>\n          <button @click=\"testParticleAnimation\">测试粒子效果</button>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h2>API测试</h2>\n      <div class=\"api-test\">\n        <button @click=\"testBattleAPI\" :disabled=\"isTestingAPI\">\n          {{ isTestingAPI ? '测试中...' : '测试战斗API' }}\n        </button>\n        <div v-if=\"apiTestResult\" class=\"api-result\">\n          <h4>API测试结果:</h4>\n          <pre>{{ JSON.stringify(apiTestResult, null, 2) }}</pre>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport BattleAnimation from '@/components/game/BattleAnimation.vue'\nimport battleService from '@/api/services/battleService.js'\nimport logger from '@/utils/logger.js'\n\nexport default {\n  name: 'BattleTest',\n  components: {\n    BattleAnimation\n  },\n  \n  data() {\n    return {\n      testMonsters: [\n        {\n          id: 1,\n          name: '灵猴',\n          level: 5,\n          type: 'beast',\n          max_health: 80,\n          attack: 15,\n          avatar: '/static/game/UI/tx/monster/monkey.png'\n        },\n        {\n          id: 2,\n          name: '山魈',\n          level: 8,\n          type: 'demon',\n          max_health: 120,\n          attack: 22,\n          avatar: '/static/game/UI/tx/monster/demon.png'\n        },\n        {\n          id: 3,\n          name: '水灵',\n          level: 6,\n          type: 'elemental',\n          max_health: 90,\n          attack: 18,\n          avatar: '/static/game/UI/tx/monster/water_spirit.png'\n        }\n      ],\n      isTestingAPI: false,\n      apiTestResult: null\n    }\n  },\n  \n  methods: {\n    // 开始测试战斗\n    startTestBattle(monster) {\n      // 获取当前用户的角色ID\n      const currentCharacter = this.$store.getters['character/currentCharacter'];\n      let testCharacterId = 1; // 默认值\n\n      if (currentCharacter && currentCharacter.id) {\n        testCharacterId = currentCharacter.id;\n      } else {\n        // 尝试从localStorage获取\n        try {\n          const storedCharacter = localStorage.getItem('selectedCharacter');\n          if (storedCharacter) {\n            const character = JSON.parse(storedCharacter);\n            testCharacterId = character.id || 1;\n          }\n        } catch (error) {\n          // 无法获取角色信息，使用默认ID\n        }\n      }\n\n      // 显示将要使用的参数\n      this.$toast(`开始战斗测试 - 角色ID: ${testCharacterId}, 怪物ID: ${monster.id}`)\n\n      this.$router.push({\n        path: '/game/battle',\n        query: {\n          characterId: testCharacterId,\n          monsterId: monster.id,\n          locationId: 'test_location'\n        }\n      });\n    },\n\n    // 测试攻击动画\n    testAttackAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playAttackAnimation();\n      }\n    },\n\n    // 测试伤害动画\n    testDamageAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playDamageAnimation(25, false);\n      }\n    },\n\n    // 测试暴击动画\n    testCriticalAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playDamageAnimation(45, true);\n      }\n    },\n\n    // 测试治疗动画\n    testHealAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playHealAnimation(30);\n      }\n    },\n\n    // 测试技能特效\n    testSkillAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playSkillEffect('fire');\n      }\n    },\n\n    // 测试粒子效果\n    testParticleAnimation() {\n      if (this.$refs.animationTest) {\n        this.$refs.animationTest.playParticleEffect('explosion');\n      }\n    },\n\n    // 测试战斗API\n    async testBattleAPI() {\n      this.isTestingAPI = true;\n      this.apiTestResult = null;\n\n      try {\n        logger.info('[BattleTest] 开始测试战斗API...');\n\n        // 首先测试简单的API调用\n        const testData = {\n          character_id: 1,\n          monster_id: 1,\n          location_id: 'test_location'\n        };\n\n        logger.info('[BattleTest] 测试数据:', testData);\n\n        // 测试开始战斗API\n        const result = await battleService.startBattle(1, 1, 'test_location');\n        this.apiTestResult = {\n          success: true,\n          data: result,\n          timestamp: new Date().toISOString()\n        };\n        logger.info('[BattleTest] API测试成功:', result);\n      } catch (error) {\n        this.apiTestResult = {\n          success: false,\n          error: error.message,\n          details: {\n            name: error.name,\n            message: error.message,\n            response: error.response?.data,\n            status: error.response?.status,\n            config: error.config\n          },\n          timestamp: new Date().toISOString()\n        };\n        logger.error('[BattleTest] API测试失败:', error);\n      } finally {\n        this.isTestingAPI = false;\n      }\n    }\n  },\n\n  mounted() {\n    logger.info('[BattleTest] 战斗测试页面已加载');\n    \n    // 检查动画库是否加载\n    if (window.particlesJS) {\n      logger.info('[BattleTest] Particles.js 已加载');\n    } else {\n      logger.warn('[BattleTest] Particles.js 未加载');\n    }\n  }\n}\n</script>\n\n<style scoped>\n.battle-test {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.battle-test h1 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 30px;\n}\n\n.test-section {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.test-section h2 {\n  color: #555;\n  margin-bottom: 15px;\n  border-bottom: 2px solid #eee;\n  padding-bottom: 10px;\n}\n\n/* 怪物列表样式 */\n.monster-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 15px;\n}\n\n.monster-card {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.monster-card:hover {\n  border-color: #007bff;\n  background: #f8f9fa;\n  transform: translateY(-2px);\n}\n\n.monster-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid #ccc;\n}\n\n.monster-info h3 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.monster-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n/* 动画测试样式 */\n.animation-test {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.animation-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.animation-controls button {\n  padding: 10px 15px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.animation-controls button:hover {\n  background: #0056b3;\n}\n\n/* API测试样式 */\n.api-test button {\n  padding: 12px 24px;\n  background: #28a745;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 16px;\n  transition: background 0.3s ease;\n}\n\n.api-test button:hover:not(:disabled) {\n  background: #218838;\n}\n\n.api-test button:disabled {\n  background: #6c757d;\n  cursor: not-allowed;\n}\n\n.api-result {\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 5px;\n}\n\n.api-result h4 {\n  margin: 0 0 10px 0;\n  color: #495057;\n}\n\n.api-result pre {\n  background: #e9ecef;\n  padding: 10px;\n  border-radius: 3px;\n  overflow-x: auto;\n  font-size: 12px;\n  color: #495057;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .battle-test {\n    padding: 10px;\n  }\n  \n  .monster-list {\n    grid-template-columns: 1fr;\n  }\n  \n  .monster-card {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .animation-controls {\n    flex-direction: column;\n  }\n}\n</style>\n"], "mappings": ";AAwDA,OAAAA,eAAA;AACA,OAAAC,aAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ;EACA;EAEAK,KAAA;IACA;MACAC,YAAA,GACA;QACAC,EAAA;QACAJ,IAAA;QACAK,KAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAJ,IAAA;QACAK,KAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAJ,IAAA;QACAK,KAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;MACA,EACA;MACAC,YAAA;MACAC,aAAA;IACA;EACA;EAEAC,OAAA;IACA;IACAC,gBAAAC,OAAA;MACA;MACA,MAAAC,gBAAA,QAAAC,MAAA,CAAAC,OAAA;MACA,IAAAC,eAAA;;MAEA,IAAAH,gBAAA,IAAAA,gBAAA,CAAAX,EAAA;QACAc,eAAA,GAAAH,gBAAA,CAAAX,EAAA;MACA;QACA;QACA;UACA,MAAAe,eAAA,GAAAC,YAAA,CAAAC,OAAA;UACA,IAAAF,eAAA;YACA,MAAAG,SAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,eAAA;YACAD,eAAA,GAAAI,SAAA,CAAAlB,EAAA;UACA;QACA,SAAAqB,KAAA;UACA;QAAA;MAEA;;MAEA;MACA,KAAAC,MAAA,mBAAAR,eAAA,WAAAJ,OAAA,CAAAV,EAAA;MAEA,KAAAuB,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAC,WAAA,EAAAb,eAAA;UACAc,SAAA,EAAAlB,OAAA,CAAAV,EAAA;UACA6B,UAAA;QACA;MACA;IACA;IAEA;IACAC,oBAAA;MACA,SAAAC,KAAA,CAAAC,aAAA;QACA,KAAAD,KAAA,CAAAC,aAAA,CAAAC,mBAAA;MACA;IACA;IAEA;IACAC,oBAAA;MACA,SAAAH,KAAA,CAAAC,aAAA;QACA,KAAAD,KAAA,CAAAC,aAAA,CAAAG,mBAAA;MACA;IACA;IAEA;IACAC,sBAAA;MACA,SAAAL,KAAA,CAAAC,aAAA;QACA,KAAAD,KAAA,CAAAC,aAAA,CAAAG,mBAAA;MACA;IACA;IAEA;IACAE,kBAAA;MACA,SAAAN,KAAA,CAAAC,aAAA;QACA,KAAAD,KAAA,CAAAC,aAAA,CAAAM,iBAAA;MACA;IACA;IAEA;IACAC,mBAAA;MACA,SAAAR,KAAA,CAAAC,aAAA;QACA,KAAAD,KAAA,CAAAC,aAAA,CAAAQ,eAAA;MACA;IACA;IAEA;IACAC,sBAAA;MACA,SAAAV,KAAA,CAAAC,aAAA;QACA,KAAAD,KAAA,CAAAC,aAAA,CAAAU,kBAAA;MACA;IACA;IAEA;IACA,MAAAC,cAAA;MACA,KAAArC,YAAA;MACA,KAAAC,aAAA;MAEA;QACAZ,MAAA,CAAAiD,IAAA;;QAEA;QACA,MAAAC,QAAA;UACAC,YAAA;UACAC,UAAA;UACAC,WAAA;QACA;QAEArD,MAAA,CAAAiD,IAAA,uBAAAC,QAAA;;QAEA;QACA,MAAAI,MAAA,SAAAvD,aAAA,CAAAwD,WAAA;QACA,KAAA3C,aAAA;UACA4C,OAAA;UACArD,IAAA,EAAAmD,MAAA;UACAG,SAAA,MAAAC,IAAA,GAAAC,WAAA;QACA;QACA3D,MAAA,CAAAiD,IAAA,0BAAAK,MAAA;MACA,SAAA5B,KAAA;QAAA,IAAAkC,eAAA,EAAAC,gBAAA;QACA,KAAAjD,aAAA;UACA4C,OAAA;UACA9B,KAAA,EAAAA,KAAA,CAAAoC,OAAA;UACAC,OAAA;YACA9D,IAAA,EAAAyB,KAAA,CAAAzB,IAAA;YACA6D,OAAA,EAAApC,KAAA,CAAAoC,OAAA;YACAE,QAAA,GAAAJ,eAAA,GAAAlC,KAAA,CAAAsC,QAAA,cAAAJ,eAAA,uBAAAA,eAAA,CAAAzD,IAAA;YACA8D,MAAA,GAAAJ,gBAAA,GAAAnC,KAAA,CAAAsC,QAAA,cAAAH,gBAAA,uBAAAA,gBAAA,CAAAI,MAAA;YACAC,MAAA,EAAAxC,KAAA,CAAAwC;UACA;UACAT,SAAA,MAAAC,IAAA,GAAAC,WAAA;QACA;QACA3D,MAAA,CAAA0B,KAAA,0BAAAA,KAAA;MACA;QACA,KAAAf,YAAA;MACA;IACA;EACA;EAEAwD,QAAA;IACAnE,MAAA,CAAAiD,IAAA;;IAEA;IACA,IAAAmB,MAAA,CAAAC,WAAA;MACArE,MAAA,CAAAiD,IAAA;IACA;MACAjD,MAAA,CAAAsE,IAAA;IACA;EACA;AACA", "ignoreList": []}]}