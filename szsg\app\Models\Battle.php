<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Battle extends Model
{
    use HasFactory;

    protected $fillable = [
        'character_id',
        'monster_id',
        'location_id',
        'battle_type',
        'status',
        'rounds',
        'start_time',
        'end_time',
        'exp_gained',
        'gold_gained',
        'items_gained',
        'battle_log'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'items_gained' => 'array',
        'battle_log' => 'array'
    ];

    /**
     * 关联角色
     */
    public function character(): BelongsTo
    {
        return $this->belongsTo(Character::class);
    }

    /**
     * 关联怪物
     */
    public function monster(): BelongsTo
    {
        return $this->belongsTo(Monster::class);
    }

    /**
     * 检查战斗是否进行中
     */
    public function isOngoing(): bool
    {
        return $this->status === 'ongoing';
    }

    /**
     * 检查战斗是否已结束
     */
    public function isFinished(): bool
    {
        return in_array($this->status, ['victory', 'defeat', 'fled']);
    }

    /**
     * 添加战斗日志
     */
    public function addLog(string $action, array $data = []): void
    {
        $logs = $this->battle_log ?? [];
        $logs[] = [
            'round' => $this->rounds,
            'action' => $action,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ];
        $this->battle_log = $logs;
    }

    /**
     * 结束战斗
     */
    public function finish(string $status, int $expGained = 0, int $goldGained = 0, array $itemsGained = []): void
    {
        $this->status = $status;
        $this->end_time = now();
        $this->exp_gained = $expGained;
        $this->gold_gained = $goldGained;
        $this->items_gained = $itemsGained;
        $this->save();
    }

    /**
     * 获取战斗持续时间（秒）
     */
    public function getDurationAttribute(): int
    {
        if (!$this->end_time) {
            return now()->diffInSeconds($this->start_time);
        }
        return $this->end_time->diffInSeconds($this->start_time);
    }
}
