{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\data\\mapData.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\data\\mapData.js", "mtime": 1749874156504}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\eslint-loader\\index.js", "mtime": 1749535518090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["locations", "town_center", "id", "name", "type", "description", "coordinates", "x", "y", "npcs", "level", "services", "monsters", "facilities", "forest_entrance", "forest_deep", "mountain_foot", "mountain_peak", "dungeon_entrance", "dungeon_deep", "connections", "movementCosts", "base", "time", "energy", "terrainMultipliers", "town", "forest", "mountain", "dungeon", "field", "distanceMultipliers", "bank", "market", "inn", "clinic", "camp", "temple", "getLocationData", "locationId", "getAvailableLocations", "currentLocationId", "connectedIds", "map", "filter", "Boolean", "calculateMovementCost", "fromLocationId", "toLocationId", "fromLocation", "toLocation", "dx", "Math", "abs", "dy", "distance", "max", "terrainMultiplier", "distanceMultiplier", "baseCost", "ceil"], "sources": ["C:/Users/<USER>/Desktop/szxy/szsg/src/data/mapData.js"], "sourcesContent": ["/**\n * 地图数据配置\n * 用于开发和测试的模拟地图数据\n */\n\nexport const locations = {\n  // 城镇区域\n  town_center: {\n    id: 'town_center',\n    name: '城镇中心',\n    type: 'town',\n    description: '繁华的城镇中心，这里有各种商店和设施',\n    coordinates: { x: 0, y: 0 },\n    npcs: [\n      {\n        id: 'town_guard',\n        name: '城镇守卫',\n        type: 'npc',\n        level: 20,\n        services: ['信息', '任务']\n      },\n      {\n        id: 'merchant',\n        name: '商人',\n        type: 'npc',\n        level: 15,\n        services: ['交易', '物品']\n      }\n    ],\n    monsters: [],\n    facilities: ['bank', 'market', 'inn', 'clinic']\n  },\n\n  // 森林区域\n  forest_entrance: {\n    id: 'forest_entrance',\n    name: '森林入口',\n    type: 'forest',\n    description: '古老森林的入口，空气中弥漫着神秘的气息',\n    coordinates: { x: -1, y: 1 },\n    npcs: [\n      {\n        id: 'forest_ranger',\n        name: '森林守护者',\n        type: 'npc',\n        level: 25,\n        services: ['指引', '任务']\n      }\n    ],\n    monsters: [\n      {\n        id: 'forest_wolf',\n        name: '森林狼',\n        type: 'monster',\n        level: 8,\n        description: '栖息在森林中的野狼，具有一定的攻击性'\n      },\n      {\n        id: 'tree_spirit',\n        name: '树精',\n        type: 'monster',\n        level: 12,\n        description: '森林中的精灵，拥有自然魔法'\n      }\n    ],\n    facilities: []\n  },\n\n  forest_deep: {\n    id: 'forest_deep',\n    name: '森林深处',\n    type: 'forest',\n    description: '森林的深处，危险与机遇并存',\n    coordinates: { x: -2, y: 2 },\n    npcs: [],\n    monsters: [\n      {\n        id: 'forest_bear',\n        name: '森林熊',\n        type: 'monster',\n        level: 15,\n        description: '强大的森林霸主，拥有惊人的力量'\n      },\n      {\n        id: 'ancient_ent',\n        name: '远古树人',\n        type: 'monster',\n        level: 20,\n        description: '古老的树人，守护着森林的秘密'\n      }\n    ],\n    facilities: []\n  },\n\n  // 山脉区域\n  mountain_foot: {\n    id: 'mountain_foot',\n    name: '山脚',\n    type: 'mountain',\n    description: '高山的脚下，可以看到通往山顶的崎岖小径',\n    coordinates: { x: 1, y: 1 },\n    npcs: [\n      {\n        id: 'mountain_guide',\n        name: '登山向导',\n        type: 'npc',\n        level: 30,\n        services: ['指引', '装备']\n      }\n    ],\n    monsters: [\n      {\n        id: 'mountain_goat',\n        name: '山羊',\n        type: 'monster',\n        level: 5,\n        description: '生活在山区的野生山羊'\n      }\n    ],\n    facilities: ['camp']\n  },\n\n  mountain_peak: {\n    id: 'mountain_peak',\n    name: '山顶',\n    type: 'mountain',\n    description: '高山之巅，可以俯瞰整个大陆的壮丽景色',\n    coordinates: { x: 2, y: 2 },\n    npcs: [\n      {\n        id: 'hermit',\n        name: '隐士',\n        type: 'npc',\n        level: 50,\n        services: ['修炼', '秘籍']\n      }\n    ],\n    monsters: [\n      {\n        id: 'mountain_eagle',\n        name: '山鹰',\n        type: 'monster',\n        level: 18,\n        description: '翱翔在山顶的巨鹰，拥有敏锐的视力'\n      }\n    ],\n    facilities: ['temple']\n  },\n\n  // 地下城区域\n  dungeon_entrance: {\n    id: 'dungeon_entrance',\n    name: '地下城入口',\n    type: 'dungeon',\n    description: '通往地下迷宫的入口，散发着阴森的气息',\n    coordinates: { x: 0, y: -1 },\n    npcs: [\n      {\n        id: 'dungeon_keeper',\n        name: '地下城守护者',\n        type: 'npc',\n        level: 35,\n        services: ['警告', '信息']\n      }\n    ],\n    monsters: [\n      {\n        id: 'skeleton_warrior',\n        name: '骷髅战士',\n        type: 'monster',\n        level: 12,\n        description: '不死的骷髅战士，手持锈蚀的武器'\n      }\n    ],\n    facilities: []\n  },\n\n  dungeon_deep: {\n    id: 'dungeon_deep',\n    name: '地下城深层',\n    type: 'dungeon',\n    description: '地下城的深层，充满了未知的危险',\n    coordinates: { x: 0, y: -2 },\n    npcs: [],\n    monsters: [\n      {\n        id: 'shadow_demon',\n        name: '暗影恶魔',\n        type: 'monster',\n        level: 25,\n        description: '来自暗影位面的恶魔，拥有强大的暗黑魔法'\n      },\n      {\n        id: 'dungeon_boss',\n        name: '地下城领主',\n        type: 'monster',\n        level: 30,\n        description: '地下城的统治者，拥有恐怖的力量'\n      }\n    ],\n    facilities: []\n  }\n};\n\n// 位置之间的连接关系\nexport const connections = {\n  town_center: ['forest_entrance', 'mountain_foot', 'dungeon_entrance'],\n  forest_entrance: ['town_center', 'forest_deep'],\n  forest_deep: ['forest_entrance'],\n  mountain_foot: ['town_center', 'mountain_peak'],\n  mountain_peak: ['mountain_foot'],\n  dungeon_entrance: ['town_center', 'dungeon_deep'],\n  dungeon_deep: ['dungeon_entrance']\n};\n\n// 移动消耗配置\nexport const movementCosts = {\n  // 基础消耗\n  base: {\n    time: 5, // 分钟\n    energy: 10 // 体力\n  },\n  \n  // 不同地形的消耗倍数\n  terrainMultipliers: {\n    town: 1.0,\n    forest: 1.5,\n    mountain: 2.0,\n    dungeon: 1.8,\n    field: 1.2\n  },\n  \n  // 距离消耗倍数\n  distanceMultipliers: {\n    1: 1.0,\n    2: 1.5,\n    3: 2.0,\n    4: 2.5\n  }\n};\n\n// 设施配置\nexport const facilities = {\n  bank: {\n    id: 'bank',\n    name: '钱庄',\n    type: 'facility',\n    description: '可以存取银两和金币的地方',\n    services: ['存款', '取款', '兑换']\n  },\n  market: {\n    id: 'market',\n    name: '集市',\n    type: 'facility',\n    description: '买卖物品的地方',\n    services: ['购买', '出售', '拍卖']\n  },\n  inn: {\n    id: 'inn',\n    name: '客栈',\n    type: 'facility',\n    description: '休息和恢复的地方',\n    services: ['休息', '恢复', '住宿']\n  },\n  clinic: {\n    id: 'clinic',\n    name: '医馆',\n    type: 'facility',\n    description: '治疗伤病的地方',\n    services: ['治疗', '解毒', '恢复']\n  },\n  camp: {\n    id: 'camp',\n    name: '营地',\n    type: 'facility',\n    description: '临时休息的营地',\n    services: ['休息', '补给']\n  },\n  temple: {\n    id: 'temple',\n    name: '神庙',\n    type: 'facility',\n    description: '神圣的祈祷场所',\n    services: ['祈祷', '祝福', '净化']\n  }\n};\n\n/**\n * 获取位置数据\n */\nexport function getLocationData(locationId) {\n  return locations[locationId] || null;\n}\n\n/**\n * 获取可移动的位置列表\n */\nexport function getAvailableLocations(currentLocationId) {\n  const connectedIds = connections[currentLocationId] || [];\n  return connectedIds.map(id => locations[id]).filter(Boolean);\n}\n\n/**\n * 计算移动消耗\n */\nexport function calculateMovementCost(fromLocationId, toLocationId) {\n  const fromLocation = locations[fromLocationId];\n  const toLocation = locations[toLocationId];\n  \n  if (!fromLocation || !toLocation) {\n    return null;\n  }\n  \n  // 计算距离\n  const dx = Math.abs(toLocation.coordinates.x - fromLocation.coordinates.x);\n  const dy = Math.abs(toLocation.coordinates.y - fromLocation.coordinates.y);\n  const distance = Math.max(dx, dy);\n  \n  // 获取地形倍数\n  const terrainMultiplier = movementCosts.terrainMultipliers[toLocation.type] || 1.0;\n  const distanceMultiplier = movementCosts.distanceMultipliers[distance] || 1.0;\n  \n  // 计算最终消耗\n  const baseCost = movementCosts.base;\n  return {\n    time: Math.ceil(baseCost.time * terrainMultiplier * distanceMultiplier),\n    energy: Math.ceil(baseCost.energy * terrainMultiplier * distanceMultiplier),\n    distance: distance\n  };\n}\n\nexport default {\n  locations,\n  connections,\n  movementCosts,\n  facilities,\n  getLocationData,\n  getAvailableLocations,\n  calculateMovementCost\n};\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,SAAS,GAAG;EACvB;EACAC,WAAW,EAAE;IACXC,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,oBAAoB;IACjCC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC3BC,IAAI,EAAE,CACJ;MACEP,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;IACvB,CAAC,EACD;MACET,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;IACvB,CAAC,CACF;IACDC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;EAChD,CAAC;EAED;EACAC,eAAe,EAAE;IACfZ,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,qBAAqB;IAClCC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC5BC,IAAI,EAAE,CACJ;MACEP,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;IACvB,CAAC,CACF;IACDC,QAAQ,EAAE,CACR;MACEV,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,CAAC;MACRL,WAAW,EAAE;IACf,CAAC,EACD;MACEH,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,CACF;IACDQ,UAAU,EAAE;EACd,CAAC;EAEDE,WAAW,EAAE;IACXb,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC5BC,IAAI,EAAE,EAAE;IACRG,QAAQ,EAAE,CACR;MACEV,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,EACD;MACEH,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,CACF;IACDQ,UAAU,EAAE;EACd,CAAC;EAED;EACAG,aAAa,EAAE;IACbd,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC3BC,IAAI,EAAE,CACJ;MACEP,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;IACvB,CAAC,CACF;IACDC,QAAQ,EAAE,CACR;MACEV,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,CAAC;MACRL,WAAW,EAAE;IACf,CAAC,CACF;IACDQ,UAAU,EAAE,CAAC,MAAM;EACrB,CAAC;EAEDI,aAAa,EAAE;IACbf,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,oBAAoB;IACjCC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC3BC,IAAI,EAAE,CACJ;MACEP,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;IACvB,CAAC,CACF;IACDC,QAAQ,EAAE,CACR;MACEV,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,CACF;IACDQ,UAAU,EAAE,CAAC,QAAQ;EACvB,CAAC;EAED;EACAK,gBAAgB,EAAE;IAChBhB,EAAE,EAAE,kBAAkB;IACtBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,oBAAoB;IACjCC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAE,CAAC;IAC5BC,IAAI,EAAE,CACJ;MACEP,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;IACvB,CAAC,CACF;IACDC,QAAQ,EAAE,CACR;MACEV,EAAE,EAAE,kBAAkB;MACtBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,CACF;IACDQ,UAAU,EAAE;EACd,CAAC;EAEDM,YAAY,EAAE;IACZjB,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAE,CAAC;IAC5BC,IAAI,EAAE,EAAE;IACRG,QAAQ,EAAE,CACR;MACEV,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,EACD;MACEH,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,SAAS;MACfM,KAAK,EAAE,EAAE;MACTL,WAAW,EAAE;IACf,CAAC,CACF;IACDQ,UAAU,EAAE;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,WAAW,GAAG;EACzBnB,WAAW,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,CAAC;EACrEa,eAAe,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EAC/CC,WAAW,EAAE,CAAC,iBAAiB,CAAC;EAChCC,aAAa,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;EAC/CC,aAAa,EAAE,CAAC,eAAe,CAAC;EAChCC,gBAAgB,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC;EACjDC,YAAY,EAAE,CAAC,kBAAkB;AACnC,CAAC;;AAED;AACA,OAAO,MAAME,aAAa,GAAG;EAC3B;EACAC,IAAI,EAAE;IACJC,IAAI,EAAE,CAAC;IAAE;IACTC,MAAM,EAAE,EAAE,CAAC;EACb,CAAC;EAED;EACAC,kBAAkB,EAAE;IAClBC,IAAI,EAAE,GAAG;IACTC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,GAAG;IACbC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,mBAAmB,EAAE;IACnB,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE;EACL;AACF,CAAC;;AAED;AACA,OAAO,MAAMlB,UAAU,GAAG;EACxBmB,IAAI,EAAE;IACJ9B,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,cAAc;IAC3BM,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;EAC7B,CAAC;EACDsB,MAAM,EAAE;IACN/B,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,SAAS;IACtBM,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;EAC7B,CAAC;EACDuB,GAAG,EAAE;IACHhC,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,UAAU;IACvBM,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;EAC7B,CAAC;EACDwB,MAAM,EAAE;IACNjC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,SAAS;IACtBM,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;EAC7B,CAAC;EACDyB,IAAI,EAAE;IACJlC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,SAAS;IACtBM,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI;EACvB,CAAC;EACD0B,MAAM,EAAE;IACNnC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,SAAS;IACtBM,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;EAC7B;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,SAAS2B,eAAeA,CAACC,UAAU,EAAE;EAC1C,OAAOvC,SAAS,CAACuC,UAAU,CAAC,IAAI,IAAI;AACtC;;AAEA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,iBAAiB,EAAE;EACvD,MAAMC,YAAY,GAAGtB,WAAW,CAACqB,iBAAiB,CAAC,IAAI,EAAE;EACzD,OAAOC,YAAY,CAACC,GAAG,CAACzC,EAAE,IAAIF,SAAS,CAACE,EAAE,CAAC,CAAC,CAAC0C,MAAM,CAACC,OAAO,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,cAAc,EAAEC,YAAY,EAAE;EAClE,MAAMC,YAAY,GAAGjD,SAAS,CAAC+C,cAAc,CAAC;EAC9C,MAAMG,UAAU,GAAGlD,SAAS,CAACgD,YAAY,CAAC;EAE1C,IAAI,CAACC,YAAY,IAAI,CAACC,UAAU,EAAE;IAChC,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC5C,WAAW,CAACC,CAAC,GAAG0C,YAAY,CAAC3C,WAAW,CAACC,CAAC,CAAC;EAC1E,MAAM+C,EAAE,GAAGF,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC5C,WAAW,CAACE,CAAC,GAAGyC,YAAY,CAAC3C,WAAW,CAACE,CAAC,CAAC;EAC1E,MAAM+C,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAACL,EAAE,EAAEG,EAAE,CAAC;;EAEjC;EACA,MAAMG,iBAAiB,GAAGpC,aAAa,CAACI,kBAAkB,CAACyB,UAAU,CAAC9C,IAAI,CAAC,IAAI,GAAG;EAClF,MAAMsD,kBAAkB,GAAGrC,aAAa,CAACU,mBAAmB,CAACwB,QAAQ,CAAC,IAAI,GAAG;;EAE7E;EACA,MAAMI,QAAQ,GAAGtC,aAAa,CAACC,IAAI;EACnC,OAAO;IACLC,IAAI,EAAE6B,IAAI,CAACQ,IAAI,CAACD,QAAQ,CAACpC,IAAI,GAAGkC,iBAAiB,GAAGC,kBAAkB,CAAC;IACvElC,MAAM,EAAE4B,IAAI,CAACQ,IAAI,CAACD,QAAQ,CAACnC,MAAM,GAAGiC,iBAAiB,GAAGC,kBAAkB,CAAC;IAC3EH,QAAQ,EAAEA;EACZ,CAAC;AACH;AAEA,eAAe;EACbvD,SAAS;EACToB,WAAW;EACXC,aAAa;EACbR,UAAU;EACVyB,eAAe;EACfE,qBAAqB;EACrBM;AACF,CAAC", "ignoreList": []}]}