{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Bank.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Bank.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Bank.vue"], "names": [], "mappings": ";AAk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file": "Bank.vue", "sourceRoot": "src/views/game/subpages", "sourcesContent": ["<template>\r\n  <GameLayout>\r\n    <div class=\"bank-page\">\r\n      <div class=\"bank-container\">\r\n        <!-- 钱庄标题 -->\r\n        <div class=\"bank-header\">\r\n          <h1>钱庄</h1>\r\n          <div class=\"character-status\">\r\n            <div class=\"status-box\">\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">银两:</div>\r\n                <div class=\"status-value silver-value\">{{ characterInfo.silver }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">金砖:</div>\r\n                <div class=\"status-value gold-value\">{{ characterInfo.gold }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">存款银两:</div>\r\n                <div class=\"status-value silver-value\">{{ accountInfo.silver }}</div>\r\n              </div>\r\n              <div class=\"status-item\">\r\n                <div class=\"status-label\">存款金砖:</div>\r\n                <div class=\"status-value gold-value\">{{ accountInfo.gold_ingot }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 选项卡导航 -->\r\n        <div class=\"tabs-container\">\r\n          <div \r\n            v-for=\"tab in tabs\" \r\n            :key=\"tab.id\" \r\n            class=\"tab\" \r\n            :class=\"{ 'active': activeTab === tab.id }\"\r\n            @click=\"activeTab = tab.id\"\r\n          >\r\n            <span class=\"tab-name\">{{ tab.name }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容区域 -->\r\n        <div class=\"content-area\">\r\n          <!-- 存款 -->\r\n          <div v-if=\"activeTab === 'deposit'\" class=\"tab-content\">\r\n            <div class=\"operation-form\">\r\n              <div class=\"form-group\">\r\n                <label for=\"deposit-currency\">货币类型:</label>\r\n                <select id=\"deposit-currency\" v-model=\"depositForm.currency\" class=\"form-control\">\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"form-group\">\r\n                <label for=\"deposit-amount\">存款金额:</label>\r\n                <input \r\n                  id=\"deposit-amount\" \r\n                  type=\"number\" \r\n                  v-model=\"depositForm.amount\" \r\n                  class=\"form-control\" \r\n                  min=\"1\"\r\n                  :max=\"getMaxDepositAmount()\"\r\n                />\r\n              </div>\r\n              <div class=\"form-actions\">\r\n                <button \r\n                  @click=\"deposit\" \r\n                  class=\"action-button\" \r\n                  :disabled=\"!canDeposit()\"\r\n                >\r\n                  存款\r\n                </button>\r\n                <button @click=\"setMaxDepositAmount\" class=\"max-button\">最大</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 取款 -->\r\n          <div v-if=\"activeTab === 'withdraw'\" class=\"tab-content\">\r\n            <div class=\"operation-form\">\r\n              <div class=\"form-group\">\r\n                <label for=\"withdraw-currency\">货币类型:</label>\r\n                <select id=\"withdraw-currency\" v-model=\"withdrawForm.currency\" class=\"form-control\">\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"form-group\">\r\n                <label for=\"withdraw-amount\">取款金额:</label>\r\n                <input \r\n                  id=\"withdraw-amount\" \r\n                  type=\"number\" \r\n                  v-model=\"withdrawForm.amount\" \r\n                  class=\"form-control\" \r\n                  min=\"1\"\r\n                  :max=\"getMaxWithdrawAmount()\"\r\n                />\r\n              </div>\r\n              <div class=\"form-actions\">\r\n                <button \r\n                  @click=\"withdraw\" \r\n                  class=\"action-button\" \r\n                  :disabled=\"!canWithdraw()\"\r\n                >\r\n                  取款\r\n                </button>\r\n                <button @click=\"setMaxWithdrawAmount\" class=\"max-button\">最大</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 交易记录 -->\r\n          <div v-if=\"activeTab === 'transactions'\" class=\"tab-content\">\r\n            <div class=\"transaction-filters\">\r\n              <div class=\"filter-group\">\r\n                <label for=\"filter-currency\">货币类型:</label>\r\n                <select id=\"filter-currency\" v-model=\"transactionFilters.currency\" class=\"form-control\" @change=\"loadTransactions\">\r\n                  <option value=\"\">全部</option>\r\n                  <option value=\"silver\">银两</option>\r\n                  <option value=\"gold_ingot\">金砖</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"filter-group\">\r\n                <label for=\"filter-type\">交易类型:</label>\r\n                <select id=\"filter-type\" v-model=\"transactionFilters.type\" class=\"form-control\" @change=\"loadTransactions\">\r\n                  <option value=\"\">全部</option>\r\n                  <option value=\"deposit\">存款</option>\r\n                  <option value=\"withdraw\">取款</option>\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"transaction-list\">\r\n              <table v-if=\"formattedTransactions.length > 0\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>时间</th>\r\n                    <th>类型</th>\r\n                    <th>货币</th>\r\n                    <th>金额</th>\r\n                    <th>余额</th>\r\n                    <th>说明</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr v-for=\"transaction in formattedTransactions\" :key=\"transaction.id\">\r\n                    <td>{{ formatDate(transaction.created_at) }}</td>\r\n                    <td>{{ transaction.type === 'deposit' ? '存款' : '取款' }}</td>\r\n                    <td>{{ transaction.currency === 'silver' ? '银两' : '金砖' }}</td>\r\n                    <td>{{ transaction.amount }}</td>\r\n                    <td>{{ transaction.balance }}</td>\r\n                    <td>{{ transaction.description }}</td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n              <div v-else class=\"no-transactions\">\r\n                暂无交易记录\r\n              </div>\r\n              \r\n              <!-- 分页 -->\r\n              <div v-if=\"pagination && pagination.total > 0\" class=\"pagination\">\r\n                <button \r\n                  @click=\"changePage(pagination.current_page - 1)\" \r\n                  :disabled=\"pagination.current_page <= 1\"\r\n                  class=\"pagination-button\"\r\n                >\r\n                  上一页\r\n                </button>\r\n                <span class=\"page-info\">{{ pagination.current_page }} / {{ pagination.last_page }}</span>\r\n                <button \r\n                  @click=\"changePage(pagination.current_page + 1)\" \r\n                  :disabled=\"pagination.current_page >= pagination.last_page\"\r\n                  class=\"pagination-button\"\r\n                >\r\n                  下一页\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 返回按钮 -->\r\n        <div class=\"bottom-actions\">\r\n          <button class=\"back-button\" @click=\"$router.push('/game/main')\">返回城镇</button>\r\n        </div>\r\n\r\n        <!-- 操作结果弹窗 -->\r\n        <div v-if=\"showResult\" class=\"result-modal\">\r\n          <div class=\"result-content\" :class=\"{ 'error': resultError }\">\r\n            <h3>{{ resultError ? '操作失败' : '操作成功' }}</h3>\r\n            <p>{{ resultMessage }}</p>\r\n            <button @click=\"showResult = false\">确定</button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 加载中和错误提示 -->\r\n        <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n          <div class=\"loading-spinner\"></div>\r\n          <div>加载中...</div>\r\n        </div>\r\n        <div v-if=\"error\" class=\"error-message\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </GameLayout>\r\n</template>\r\n\r\n<script>\r\nimport GameLayout from '@/layouts/GameLayout.vue';\r\nimport bankService from '@/api/services/bankService';\r\nimport { showMessage } from '@/utils/message';\r\nimport logger from '@/utils/logger';\r\nimport { getCurrentCharacter } from '@/api/services/characterService';\r\n\r\nexport default {\r\n  name: 'Bank',\r\n  components: { GameLayout },\r\n  data() {\r\n    return {\r\n      isLoading: true,\r\n      error: null,\r\n      activeTab: 'deposit', // 默认选中存款选项卡\r\n      tabs: [\r\n        {\r\n          id: 'deposit',\r\n          name: '存款'\r\n        },\r\n        {\r\n          id: 'withdraw',\r\n          name: '取款'\r\n        },\r\n        {\r\n          id: 'transactions',\r\n          name: '交易记录'\r\n        }\r\n      ],\r\n      characterInfo: {\r\n        id: null,\r\n        name: '',\r\n        silver: 0,\r\n        gold: 0\r\n      },\r\n      accountInfo: {\r\n        silver: 0,\r\n        gold_ingot: 0\r\n      },\r\n      depositForm: {\r\n        currency: 'silver',\r\n        amount: 100\r\n      },\r\n      withdrawForm: {\r\n        currency: 'silver',\r\n        amount: 100\r\n      },\r\n      transactions: [],\r\n      pagination: null,\r\n      transactionFilters: {\r\n        currency: '',\r\n        type: '',\r\n        page: 1,\r\n        per_page: 10\r\n      },\r\n      showResult: false,\r\n      resultError: false,\r\n      resultMessage: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.loadData();\r\n  },\r\n  watch: {\r\n    activeTab(newTab) {\r\n      if (newTab === 'transactions') {\r\n        this.loadTransactions();\r\n      }\r\n    },\r\n    'depositForm.currency'() {\r\n      // 切换货币类型时，重置金额\r\n      this.depositForm.amount = 100;\r\n    },\r\n    'withdrawForm.currency'() {\r\n      // 切换货币类型时，重置金额\r\n      this.withdrawForm.amount = 100;\r\n    }\r\n  },\r\n  computed: {\r\n    /**\r\n     * 格式化交易记录，确保数据格式一致\r\n     * 处理可能的数据格式不一致问题\r\n     */\r\n    formattedTransactions() {\r\n      return this.transactions.map(transaction => {\r\n        // 确保所有必要字段都存在，使用默认值代替可能缺失的字段\r\n        return {\r\n          id: transaction.id || Math.random().toString(36).substr(2, 9), // 生成随机ID如果缺失\r\n          created_at: transaction.created_at || new Date().toISOString(),\r\n          type: transaction.type || 'deposit',\r\n          currency: transaction.currency || 'silver',\r\n          amount: parseInt(transaction.amount) || 0,\r\n          balance: parseInt(transaction.balance) || 0,\r\n          description: transaction.description || this.generateDescription(transaction)\r\n        };\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 加载初始数据\r\n     */\r\n    async loadData() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        // 获取当前角色信息\r\n        const character = getCurrentCharacter();\r\n        if (!character) {\r\n          logger.error('[Bank] 未找到角色信息，尝试从LocalStorage获取');\r\n          \r\n          // 尝试直接从LocalStorage获取\r\n          try {\r\n            const characterId = localStorage.getItem('selectedCharacterId');\r\n            const characterJson = localStorage.getItem('selectedCharacter');\r\n            \r\n            if (characterId && characterJson) {\r\n              const savedCharacter = JSON.parse(characterJson);\r\n              logger.debug('[Bank] 从LocalStorage获取到角色:', savedCharacter);\r\n              \r\n              this.characterInfo = {\r\n                id: savedCharacter.id,\r\n                name: savedCharacter.name,\r\n                silver: savedCharacter.silver || 0,\r\n                gold: savedCharacter.gold || 0\r\n              };\r\n            } else {\r\n              throw new Error('未找到角色信息，请先选择角色');\r\n            }\r\n          } catch (e) {\r\n            logger.error('[Bank] 从LocalStorage获取角色失败:', e);\r\n            throw new Error('未找到角色信息，请先选择角色');\r\n          }\r\n        } else {\r\n          // 正常获取到角色信息\r\n          this.characterInfo = {\r\n            id: character.id,\r\n            name: character.name,\r\n            silver: character.silver || 0,\r\n            gold: character.gold || 0\r\n          };\r\n          \r\n          logger.debug('[Bank] 获取到角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        // 从Vuex获取更详细的角色信息\r\n        try {\r\n          const characterStatus = this.$store?.state?.character?.characterStatus;\r\n          if (characterStatus) {\r\n            this.characterInfo.silver = characterStatus.silver || this.characterInfo.silver;\r\n            this.characterInfo.gold = characterStatus.gold || this.characterInfo.gold;\r\n            logger.debug('[Bank] 从Vuex更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n          } else {\r\n            logger.warn('[Bank] 未从Vuex获取到角色状态，使用默认值');\r\n          }\r\n        } catch (stateError) {\r\n          logger.error('[Bank] 从Vuex获取角色状态失败:', stateError);\r\n        }\r\n        \r\n        // 银行账户信息初始化为默认值，防止API调用失败时没有数据\r\n        this.accountInfo = {\r\n          silver: 0,\r\n          gold_ingot: 0\r\n        };\r\n        \r\n        try {\r\n          // 获取银行账户信息\r\n          logger.debug('[Bank] 开始获取银行账户信息, ID:', this.characterInfo.id);\r\n          const accountResponse = await bankService.getAccountInfo(this.characterInfo.id);\r\n          logger.debug('[Bank] 银行账户响应:', JSON.stringify(accountResponse));\r\n          \r\n          // 处理可能的不同响应格式\r\n          if (accountResponse) {\r\n            // 检查不同的响应数据结构\r\n            if (accountResponse.data && accountResponse.data.account) {\r\n              // 标准格式: { data: { account: {...} } }\r\n              this.accountInfo = accountResponse.data.account;\r\n              logger.debug('[Bank] 获取到账户信息(标准格式):', JSON.stringify(this.accountInfo));\r\n              \r\n              // 更新角色信息\r\n              if (accountResponse.data.character) {\r\n                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.data.character));\r\n                this.characterInfo.name = accountResponse.data.character.name || this.characterInfo.name;\r\n                this.characterInfo.silver = accountResponse.data.character.silver || this.characterInfo.silver;\r\n                this.characterInfo.gold = accountResponse.data.character.gold || this.characterInfo.gold;\r\n                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n              }\r\n            } else if (accountResponse.account) {\r\n              // 替代格式1: { account: {...} }\r\n              this.accountInfo = accountResponse.account;\r\n              logger.debug('[Bank] 获取到账户信息(替代格式1):', JSON.stringify(this.accountInfo));\r\n              \r\n              // 更新角色信息\r\n              if (accountResponse.character) {\r\n                logger.debug('[Bank] 从API获取到角色信息:', JSON.stringify(accountResponse.character));\r\n                this.characterInfo.name = accountResponse.character.name || this.characterInfo.name;\r\n                this.characterInfo.silver = accountResponse.character.silver || this.characterInfo.silver;\r\n                this.characterInfo.gold = accountResponse.character.gold || this.characterInfo.gold;\r\n                logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n              }\r\n            } else if (typeof accountResponse === 'object' && accountResponse !== null) {\r\n              // 替代格式2: 响应本身就是账户对象\r\n              // 检查是否至少有银两或金砖字段\r\n              if ('silver' in accountResponse || 'gold_ingot' in accountResponse) {\r\n                this.accountInfo = {\r\n                  silver: accountResponse.silver || 0,\r\n                  gold_ingot: accountResponse.gold_ingot || 0\r\n                };\r\n                logger.debug('[Bank] 获取到账户信息(替代格式2):', JSON.stringify(this.accountInfo));\r\n                \r\n                // 检查是否包含角色信息\r\n                if ('name' in accountResponse || 'silver' in accountResponse || 'gold' in accountResponse) {\r\n                  logger.debug('[Bank] 从API响应对象中获取角色信息');\r\n                  if ('name' in accountResponse) this.characterInfo.name = accountResponse.name;\r\n                  if ('silver' in accountResponse) this.characterInfo.silver = accountResponse.silver;\r\n                  if ('gold' in accountResponse) this.characterInfo.gold = accountResponse.gold;\r\n                  logger.debug('[Bank] 更新后的角色信息:', JSON.stringify(this.characterInfo));\r\n                }\r\n              } else {\r\n                throw new Error('获取银行账户信息失败: 响应数据格式无效');\r\n              }\r\n            } else {\r\n              throw new Error('获取银行账户信息失败: 响应数据格式无效');\r\n            }\r\n          } else {\r\n            throw new Error('获取银行账户信息失败: 无响应数据');\r\n          }\r\n        } catch (apiError) {\r\n          // 记录API错误但不阻止页面加载，使用默认账户信息\r\n          logger.error('[Bank] API调用失败:', apiError);\r\n          logger.error('[Bank] API调用失败详情:', JSON.stringify(apiError));\r\n          \r\n          // 显示警告消息\r\n          showMessage('获取银行账户信息失败，将使用临时数据显示', 'warning');\r\n          \r\n          // 默认值已初始化，无需重复设置\r\n        } finally {\r\n          // 无论API成功与否，都初始化表单\r\n          this.depositForm.amount = Math.min(100, this.characterInfo.silver || 0);\r\n          this.withdrawForm.amount = Math.min(100, this.accountInfo.silver || 0);\r\n        }\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        this.error = error.message || '加载数据失败，请刷新重试';\r\n        this.isLoading = false;\r\n        logger.error('[Bank] 加载数据失败:', error);\r\n        \r\n        // 如果是未找到角色信息，则跳转到角色选择页面\r\n        if (error.message && error.message.includes('未找到角色信息')) {\r\n          showMessage('未找到角色信息，请先选择角色', 'error');\r\n          this.$router.push('/setup/character-select');\r\n        }\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 加载交易记录\r\n     */\r\n    async loadTransactions() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        // 如果角色信息不存在，不尝试加载交易记录\r\n        if (!this.characterInfo || !this.characterInfo.id) {\r\n          logger.warn('[Bank] 无法加载交易记录: 角色信息不存在');\r\n          this.transactions = [];\r\n          this.isLoading = false;\r\n          return;\r\n        }\r\n        \r\n        // 构建请求参数，只有当筛选值不为空字符串时才添加到请求参数中\r\n        const params = {};\r\n        if (this.transactionFilters.page) {\r\n          params.page = this.transactionFilters.page;\r\n        }\r\n        if (this.transactionFilters.per_page) {\r\n          params.per_page = this.transactionFilters.per_page;\r\n        }\r\n        // 只有当值不是空字符串时才添加筛选条件\r\n        if (this.transactionFilters.currency && this.transactionFilters.currency !== '') {\r\n          params.currency = this.transactionFilters.currency;\r\n        }\r\n        if (this.transactionFilters.type && this.transactionFilters.type !== '') {\r\n          params.type = this.transactionFilters.type;\r\n        }\r\n        \r\n        logger.debug('[Bank] 加载交易记录, 参数:', params);\r\n        \r\n        try {\r\n          const response = await bankService.getTransactionHistory(this.characterInfo.id, params);\r\n          logger.debug('[Bank] 交易记录响应:', JSON.stringify(response));\r\n          \r\n          // 处理响应结构\r\n          if (response && response.data) {\r\n            // 标准响应结构\r\n            this.transactions = response.data.transactions || [];\r\n            this.pagination = response.data.pagination || null;\r\n            logger.debug('[Bank] 解析到交易记录:', this.transactions.length);\r\n          } else if (response && Array.isArray(response)) {\r\n            // 响应直接是交易数组\r\n            this.transactions = response;\r\n            logger.debug('[Bank] 解析到交易记录(数组格式):', this.transactions.length);\r\n          } else if (response && Array.isArray(response.transactions)) {\r\n            // 响应中有transactions数组\r\n            this.transactions = response.transactions;\r\n            this.pagination = response.pagination || null;\r\n            logger.debug('[Bank] 解析到交易记录(嵌套数组):', this.transactions.length);\r\n          } else if (response && response.success && Array.isArray(response.data)) {\r\n            // success包装的数组\r\n            this.transactions = response.data;\r\n            logger.debug('[Bank] 解析到交易记录(success包装数组):', this.transactions.length);\r\n          } else {\r\n            // 无效响应，使用空数组\r\n            logger.warn('[Bank] 无效的交易记录响应格式，使用空数组:', JSON.stringify(response));\r\n            this.transactions = [];\r\n            this.pagination = null;\r\n          }\r\n          \r\n          // 检查交易记录是否有效\r\n          if (this.transactions.length > 0) {\r\n            logger.debug('[Bank] 交易记录首条样例:', JSON.stringify(this.transactions[0]));\r\n          } else {\r\n            logger.warn('[Bank] 未获取到交易记录，筛选条件:', this.transactionFilters);\r\n          }\r\n        } catch (apiError) {\r\n          // API调用失败，使用空数组\r\n          logger.error('[Bank] 交易记录API调用失败:', apiError);\r\n          this.transactions = [];\r\n          this.pagination = null;\r\n          \r\n          // 显示警告消息，但不中断加载过程\r\n          showMessage('获取交易记录失败，将显示空记录', 'warning');\r\n        }\r\n        \r\n        this.isLoading = false;\r\n      } catch (error) {\r\n        this.error = error.message || '加载交易记录失败，请刷新重试';\r\n        this.isLoading = false;\r\n        logger.error('[Bank] 加载交易记录失败:', error);\r\n        \r\n        // 确保有默认值\r\n        this.transactions = this.transactions || [];\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 存款操作\r\n     */\r\n    async deposit() {\r\n      try {\r\n        if (!this.canDeposit()) {\r\n          return this.showResultMessage('无法进行存款操作', true);\r\n        }\r\n        \r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        const response = await bankService.deposit(\r\n          this.characterInfo.id,\r\n          this.depositForm.currency,\r\n          parseInt(this.depositForm.amount) || 0\r\n        );\r\n        \r\n        // 更新本地数据\r\n        if (response.account) {\r\n          this.accountInfo = response.account;\r\n          logger.debug('[Bank] 存款后更新账户信息:', JSON.stringify(this.accountInfo));\r\n        }\r\n        \r\n        if (response.character) {\r\n          // 确保数据类型正确转换\r\n          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;\r\n          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;\r\n          logger.debug('[Bank] 存款后更新角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        this.isLoading = false;\r\n        this.showResultMessage(response.message || '存款成功', false);\r\n      } catch (error) {\r\n        this.isLoading = false;\r\n        this.showResultMessage(error.message || '存款失败，请重试', true);\r\n        logger.error('[Bank] 存款操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 取款操作\r\n     */\r\n    async withdraw() {\r\n      try {\r\n        if (!this.canWithdraw()) {\r\n          return this.showResultMessage('无法进行取款操作', true);\r\n        }\r\n        \r\n        this.isLoading = true;\r\n        this.error = null;\r\n        \r\n        const response = await bankService.withdraw(\r\n          this.characterInfo.id,\r\n          this.withdrawForm.currency,\r\n          parseInt(this.withdrawForm.amount) || 0\r\n        );\r\n        \r\n        // 更新本地数据\r\n        if (response.account) {\r\n          this.accountInfo = response.account;\r\n          logger.debug('[Bank] 取款后更新账户信息:', JSON.stringify(this.accountInfo));\r\n        }\r\n        \r\n        if (response.character) {\r\n          // 确保数据类型正确转换\r\n          this.characterInfo.silver = parseInt(response.character.silver) || this.characterInfo.silver;\r\n          this.characterInfo.gold = parseInt(response.character.gold) || this.characterInfo.gold;\r\n          logger.debug('[Bank] 取款后更新角色信息:', JSON.stringify(this.characterInfo));\r\n        }\r\n        \r\n        this.isLoading = false;\r\n        this.showResultMessage(response.message || '取款成功', false);\r\n      } catch (error) {\r\n        this.isLoading = false;\r\n        this.showResultMessage(error.message || '取款失败，请重试', true);\r\n        logger.error('[Bank] 取款操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 显示操作结果消息\r\n     */\r\n    showResultMessage(message, isError = false) {\r\n      this.resultMessage = message;\r\n      this.resultError = isError;\r\n      this.showResult = true;\r\n    },\r\n    \r\n    /**\r\n     * 为缺失描述的交易记录生成默认描述\r\n     */\r\n    generateDescription(transaction) {\r\n      const type = transaction.type === 'deposit' ? '存入' : '取出';\r\n      const currency = transaction.currency === 'silver' ? '银两' : '金砖';\r\n      const amount = transaction.amount || 0;\r\n      return `${type} ${amount} ${currency}`;\r\n    },\r\n    \r\n    /**\r\n     * 格式化日期\r\n     */\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未知时间';\r\n      \r\n      try {\r\n        const date = new Date(dateStr);\r\n        if (isNaN(date.getTime())) return '日期格式错误';\r\n        \r\n        return date.toLocaleString('zh-CN', {\r\n          year: 'numeric',\r\n          month: '2-digit',\r\n          day: '2-digit',\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          second: '2-digit'\r\n        });\r\n      } catch (e) {\r\n        logger.error('[Bank] 日期格式化失败:', e);\r\n        return '日期格式错误';\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 切换分页\r\n     */\r\n    changePage(page) {\r\n      if (page < 1 || (this.pagination && page > this.pagination.last_page)) {\r\n        return;\r\n      }\r\n      \r\n      this.transactionFilters.page = page;\r\n      this.loadTransactions();\r\n    },\r\n    \r\n    /**\r\n     * 获取最大可存款金额\r\n     */\r\n    getMaxDepositAmount() {\r\n      if (this.depositForm.currency === 'silver') {\r\n        return this.characterInfo.silver;\r\n      } else {\r\n        return this.characterInfo.gold;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 获取最大可取款金额\r\n     */\r\n    getMaxWithdrawAmount() {\r\n      if (this.withdrawForm.currency === 'silver') {\r\n        return this.accountInfo.silver;\r\n      } else {\r\n        return this.accountInfo.gold_ingot;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 设置最大存款金额\r\n     */\r\n    setMaxDepositAmount() {\r\n      this.depositForm.amount = this.getMaxDepositAmount();\r\n    },\r\n    \r\n    /**\r\n     * 设置最大取款金额\r\n     */\r\n    setMaxWithdrawAmount() {\r\n      this.withdrawForm.amount = this.getMaxWithdrawAmount();\r\n    },\r\n    \r\n    /**\r\n     * 判断是否可以存款\r\n     */\r\n    canDeposit() {\r\n      const amount = parseInt(this.depositForm.amount);\r\n      if (!amount || amount <= 0) {\r\n        return false;\r\n      }\r\n      \r\n      if (this.depositForm.currency === 'silver') {\r\n        return amount <= this.characterInfo.silver;\r\n      } else {\r\n        return amount <= this.characterInfo.gold;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 判断是否可以取款\r\n     */\r\n    canWithdraw() {\r\n      const amount = parseInt(this.withdrawForm.amount);\r\n      if (!amount || amount <= 0) {\r\n        return false;\r\n      }\r\n      \r\n      if (this.withdrawForm.currency === 'silver') {\r\n        return amount <= this.accountInfo.silver;\r\n      } else {\r\n        return amount <= this.accountInfo.gold_ingot;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bank-page {\r\n  padding: 10px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.bank-container {\r\n  background-color: rgba(0, 0, 20, 0.7);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  box-shadow: 0 0 20px rgba(0, 0, 50, 0.5);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.bank-header {\r\n  margin-bottom: 15px;\r\n  padding: 12px;\r\n  background-color: rgba(0, 0, 51, 0.5);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.bank-header h1 {\r\n  margin: 0 0 10px 0;\r\n  text-align: center;\r\n  color: #ffcc00;\r\n  font-size: 22px;\r\n  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);\r\n}\r\n\r\n.character-status {\r\n  margin-top: 10px;\r\n}\r\n\r\n.status-box {\r\n  background-color: rgba(153, 0, 0, 0.2);\r\n  border: 1px solid rgba(153, 0, 0, 0.5);\r\n  border-radius: 5px;\r\n  padding: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 8px;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 3px 5px;\r\n}\r\n\r\n.status-label {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-value {\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.silver-value {\r\n  color: #ffcc00;\r\n}\r\n\r\n.gold-value {\r\n  color: #ff9900;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid rgba(51, 51, 204, 0.5);\r\n}\r\n\r\n.tab {\r\n  padding: 10px 15px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  flex: 1;\r\n  border-radius: 5px 5px 0 0;\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n}\r\n\r\n.tab:hover {\r\n  background-color: rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.tab.active {\r\n  background-color: rgba(51, 51, 204, 0.5);\r\n  border: 1px solid rgba(51, 51, 204, 0.8);\r\n  border-bottom: none;\r\n}\r\n\r\n.tab-name {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tab-content {\r\n  padding: 10px;\r\n}\r\n\r\n.operation-form {\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-control {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border-radius: 5px;\r\n  border: 1px solid rgba(51, 51, 204, 0.5);\r\n  background-color: rgba(0, 0, 20, 0.7);\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.action-button {\r\n  flex: 3;\r\n  padding: 12px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-button:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.action-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.max-button {\r\n  flex: 1;\r\n  padding: 12px;\r\n  background-color: #990000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.max-button:hover {\r\n  background-color: #cc0000;\r\n}\r\n\r\n.transaction-filters {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-group {\r\n  flex: 1;\r\n}\r\n\r\n.transaction-list {\r\n  background-color: rgba(0, 0, 51, 0.3);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(51, 51, 204, 0.3);\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\ntable {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\nthead {\r\n  position: sticky;\r\n  top: 0;\r\n  background-color: rgba(0, 0, 51, 0.8);\r\n  z-index: 1;\r\n}\r\n\r\nth, td {\r\n  padding: 10px;\r\n  text-align: left;\r\n  border-bottom: 1px solid rgba(51, 51, 204, 0.3);\r\n}\r\n\r\nth {\r\n  color: #aaaaff;\r\n  font-weight: bold;\r\n}\r\n\r\ntr:hover {\r\n  background-color: rgba(51, 51, 204, 0.1);\r\n}\r\n\r\n.no-transactions {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #aaaaaa;\r\n}\r\n\r\n.pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n  gap: 10px;\r\n}\r\n\r\n.pagination-button {\r\n  padding: 8px 15px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.pagination-button:hover:not(:disabled) {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.pagination-button:disabled {\r\n  background-color: #666666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.page-info {\r\n  font-weight: bold;\r\n}\r\n\r\n.bottom-actions {\r\n  margin-top: auto;\r\n  text-align: center;\r\n}\r\n\r\n.back-button {\r\n  padding: 10px 20px;\r\n  background-color: #cc0000;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #dd4444;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.back-button:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.result-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.result-content {\r\n  background-color: #000033;\r\n  border: 2px solid #3333cc;\r\n  border-radius: 6px;\r\n  padding: 20px;\r\n  width: 80%;\r\n  max-width: 400px;\r\n  text-align: center;\r\n}\r\n\r\n.result-content.error {\r\n  border-color: #cc0000;\r\n}\r\n\r\n.result-content h3 {\r\n  color: #ffcc00;\r\n  margin-top: 0;\r\n}\r\n\r\n.result-content p {\r\n  margin: 15px 0;\r\n}\r\n\r\n.result-content button {\r\n  padding: 8px 20px;\r\n  background-color: #3333cc;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.result-content button:hover {\r\n  background-color: #4444dd;\r\n}\r\n\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n  color: white;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 4px solid rgba(51, 51, 204, 0.3);\r\n  border-top: 4px solid #3333cc;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.error-message {\r\n  background-color: rgba(153, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 10px;\r\n  border-radius: 5px;\r\n  margin: 10px 0;\r\n  text-align: center;\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 480px) {\r\n  .status-box {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .transaction-filters {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  th, td {\r\n    padding: 8px 5px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .action-button, .max-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style> "]}]}