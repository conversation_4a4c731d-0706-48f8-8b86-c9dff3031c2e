<?php $__env->startSection('title', '编辑地图'); ?>

<?php $__env->startSection('content'); ?>
<div class="layui-card">
    <div class="layui-card-header">
        <a href="<?php echo e(route('admin.maps.index')); ?>" class="layui-btn layui-btn-sm layui-btn-primary">
            <i class="layui-icon layui-icon-left"></i> 返回列表
        </a>
        编辑地图 - <?php echo e($map->name); ?>

    </div>
    <div class="layui-card-body">
        <?php if(session('success')): ?>
        <div class="layui-alert layui-alert-success">
            <?php echo e(session('success')); ?>

        </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
        <div class="layui-alert layui-alert-danger">
            <?php echo e(session('error')); ?>

        </div>
        <?php endif; ?>

        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">基本信息</li>
                <li>位置管理</li>
                <li>NPC管理</li>
                <li>地图预览</li>
            </ul>
            <div class="layui-tab-content">
                <!-- 基本信息 -->
                <div class="layui-tab-item layui-show">
                    <form class="layui-form" method="POST" action="<?php echo e(route('admin.maps.update', $map->id)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="layui-form-item">
                            <label class="layui-form-label">名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" value="<?php echo e($map->name); ?>" required lay-verify="required" placeholder="请输入地图名称" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">代码</label>
                            <div class="layui-input-block">
                                <input type="text" name="code" value="<?php echo e($map->code); ?>" required lay-verify="required" placeholder="请输入地图代码" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">描述</label>
                            <div class="layui-input-block">
                                <textarea name="description" placeholder="请输入地图描述" class="layui-textarea"><?php echo e($map->description); ?></textarea>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">地图类型</label>
                            <div class="layui-input-block">
                                <select name="type" lay-verify="required">
                                    <option value="city" <?php echo e($map->type == 'city' ? 'selected' : ''); ?>>城市</option>
                                    <option value="wilderness" <?php echo e($map->type == 'wilderness' ? 'selected' : ''); ?>>野外</option>
                                    <option value="dungeon" <?php echo e($map->type == 'dungeon' ? 'selected' : ''); ?>>副本</option>
                                    <option value="instance" <?php echo e($map->type == 'instance' ? 'selected' : ''); ?>>实例</option>
                                    <option value="special" <?php echo e($map->type == 'special' ? 'selected' : ''); ?>>特殊</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">等级范围</label>
                            <div class="layui-input-inline" style="width: 80px;">
                                <input type="number" name="level_range_min" value="<?php echo e($map->level_range_min); ?>" required lay-verify="required|number" placeholder="最低" class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 80px;">
                                <input type="number" name="level_range_max" value="<?php echo e($map->level_range_max); ?>" required lay-verify="required|number" placeholder="最高" class="layui-input">
                            </div>
                        </div>

                        <input type="hidden" name="danger_level" value="1">

                        <div class="layui-form-item">
                            <label class="layui-form-label">排序权重</label>
                            <div class="layui-input-inline">
                                <input type="number" name="sort_order" value="<?php echo e($map->sort_order); ?>" required lay-verify="required|number" placeholder="请输入排序权重" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-text-em">数值越大排序越靠前</div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="checkbox" name="is_pvp" value="1" title="允许PVP" <?php echo e($map->is_pvp ? 'checked' : ''); ?>>
                            </div>
                        </div>

                        <input type="hidden" name="weather_enabled" value="0">

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="checkbox" name="is_active" value="1" title="启用" <?php echo e($map->is_active ? 'checked' : ''); ?>>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit>保存</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 位置管理 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            位置列表
                            <button class="layui-btn layui-btn-xs layui-btn-normal" id="addLocationBtn" style="float: right;">添加位置</button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>坐标</th>
                                        <th>等级要求</th>
                                        <th>安全区</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($location->id); ?></td>
                                        <td><?php echo e($location->name); ?></td>
                                        <td><?php echo e($location->type); ?></td>
                                        <td>(<?php echo e($location->x); ?>, <?php echo e($location->y); ?>)</td>
                                        <td><?php echo e($location->level_requirement); ?></td>
                                        <td>
                                            <?php if($location->is_safe): ?>
                                            <span class="layui-badge layui-bg-green">安全</span>
                                            <?php else: ?>
                                            <span class="layui-badge layui-bg-red">危险</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="layui-btn-group">
                                                <button class="layui-btn layui-btn-xs" onclick="editLocation(<?php echo e($location->id); ?>)">编辑</button>
                                                <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteLocation(<?php echo e($location->id); ?>, '<?php echo e($location->name); ?>')">删除</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="layui-center">暂无位置数据</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- NPC管理 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            NPC列表
                            <button class="layui-btn layui-btn-xs layui-btn-normal" id="addNpcBtn" style="float: right;">添加NPC</button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>位置</th>
                                        <th>等级</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="npcList">
                                    <tr>
                                        <td colspan="7" class="layui-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 地图预览 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-header">地图预览</div>
                        <div class="layui-card-body">
                            <div id="mapContainer" style="width: 100%; height: 600px; background-color: #f0f0f0; position: relative;">
                                <!-- 地图将在这里显示 -->
                                <?php if($map->map_image): ?>
                                <img src="<?php echo e(asset('storage/' . $map->map_image)); ?>" alt="地图" style="max-width: 100%; max-height: 100%;">
                                <?php else: ?>
                                <div class="layui-center" style="padding-top: 250px;">
                                    <p>暂无地图图片</p>
                                    <button type="button" class="layui-btn" id="uploadMapBtn">
                                        <i class="layui-icon">&#xe67c;</i>上传地图图片
                                    </button>
                                </div>
                                <?php endif; ?>

                                <!-- 地图上的位置标记 -->
                                <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="map-marker" data-id="<?php echo e($location->id); ?>" style="position: absolute; left: <?php echo e($location->x); ?>px; top: <?php echo e($location->y); ?>px; width: 10px; height: 10px; background-color: <?php echo e($location->is_safe ? 'green' : 'red'); ?>; border-radius: 50%; cursor: pointer;" title="<?php echo e($location->name); ?>"></div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
layui.use(['form', 'element', 'layer', 'jquery', 'upload'], function(){
    var form = layui.form;
    var element = layui.element;
    var layer = layui.layer;
    var $ = layui.jquery;
    var upload = layui.upload;

    // 加载NPC列表
    function loadNpcs() {
        $.ajax({
            url: '<?php echo e(route("admin.api.map.characters", $map->id)); ?>',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if(res.success) {
                    var html = '';
                    if(res.data.length > 0) {
                        $.each(res.data, function(i, item) {
                            if(item.is_npc) {
                                html += '<tr>';
                                html += '<td>' + item.id + '</td>';
                                html += '<td>' + item.name + '</td>';
                                html += '<td>' + item.npc_type + '</td>';
                                html += '<td>' + item.location_name + '</td>';
                                html += '<td>' + item.level + '</td>';
                                html += '<td>' + (item.status == 'active' ? '<span class="layui-badge layui-bg-green">活跃</span>' : '<span class="layui-badge">非活跃</span>') + '</td>';
                                html += '<td><a href="/admin/npcs/' + item.id + '" class="layui-btn layui-btn-xs">查看</a></td>';
                                html += '</tr>';
                            }
                        });
                    }
                    if(html === '') {
                        html = '<tr><td colspan="7" class="layui-center">暂无NPC数据</td></tr>';
                    }
                    $('#npcList').html(html);
                } else {
                    layer.msg('获取NPC列表失败：' + res.message);
                }
            },
            error: function() {
                layer.msg('获取NPC列表失败，请稍后再试');
            }
        });
    }

    // 添加位置
    $('#addLocationBtn').on('click', function() {
        layer.open({
            type: 2,
            title: '添加位置',
            area: ['800px', '600px'],
            content: '/admin/maps/<?php echo e($map->id); ?>/locations/create'
        });
    });

    // 添加NPC
    $('#addNpcBtn').on('click', function() {
        layer.open({
            type: 2,
            title: '添加NPC',
            area: ['800px', '600px'],
            content: '/admin/npcs/create?region_id=<?php echo e($map->id); ?>'
        });
    });

    // 上传地图图片
    upload.render({
        elem: '#uploadMapBtn',
        url: '<?php echo e(route("admin.maps.upload", $map->id)); ?>',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res){
            if(res.code == 0){
                layer.msg('上传成功');
                setTimeout(function(){
                    window.location.reload();
                }, 1000);
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 地图标记点击事件
    $('.map-marker').on('click', function() {
        var locationId = $(this).data('id');
        editLocation(locationId);
    });

    // 初始化
    loadNpcs();
});

// 编辑位置
function editLocation(id) {
    layer.open({
        type: 2,
        title: '编辑位置',
        area: ['800px', '600px'],
        content: '/admin/locations/' + id + '/edit'
    });
}

// 删除位置
function deleteLocation(id, name) {
    layer.confirm('确定要删除位置 "' + name + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        $.ajax({
            url: '/admin/locations/' + id,
            type: 'DELETE',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(res) {
                if(res.success) {
                    layer.msg('删除成功');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    layer.msg('删除失败：' + res.message);
                }
            },
            error: function() {
                layer.msg('删除失败，请稍后再试');
            }
        });
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\szxy\laravel\resources\views/admin/maps/edit.blade.php ENDPATH**/ ?>