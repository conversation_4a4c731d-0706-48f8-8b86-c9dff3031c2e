<template>
  <GameLayout>
    <div class="clinic-page">
      <div class="clinic-container">
        <!-- 医馆标题 -->
        <div class="clinic-header">
          <h1>医馆</h1>
          <div class="character-status">
            <div class="status-box">
              <div class="status-item">
                <div class="status-label">气血:</div>
                <div class="status-value">{{ characterInfo.hp }}/{{ characterInfo.maxHp }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">精力:</div>
                <div class="status-value">{{ characterInfo.mp }}/{{ characterInfo.maxMp }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">银两:</div>
                <div class="status-value silver-value">{{ characterInfo.silver }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 选项卡导航 -->
        <div class="tabs-container">
          <div 
            v-for="tab in tabs" 
            :key="tab.id" 
            class="tab" 
            :class="{ 'active': activeTab === tab.id }"
            @click="activeTab = tab.id"
          >
            <span class="tab-name">{{ tab.name }}</span>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <!-- 气血药品 -->
          <div v-if="activeTab === 'health'" class="tab-content">
            <div class="service-list">
              <div 
                v-for="potion in healthPotions" 
                :key="potion.id" 
                class="service-list-item" 
                :class="{ 'disabled': !canUseService(potion) }"
              >
                <div class="service-list-left">
                  <div class="service-list-name">{{ potion.name }}</div>
                  <div class="service-list-info">
                    <div class="service-list-description">恢复 {{ potion.effect_value }} 气血</div>
                    <div class="service-list-price">价格: {{ potion.price }} 银两</div>
                  </div>
                </div>
                <button 
                  class="service-list-button"
                  @click="purchasePotion(potion.id, 'health')"
                  :disabled="!canUseService(potion)"
                >
                  购买
                </button>
              </div>
              <div v-if="healthPotions.length === 0" class="no-items">
                暂无可用药品
              </div>
            </div>
          </div>

          <!-- 精力药品 -->
          <div v-if="activeTab === 'mana'" class="tab-content">
            <div class="service-list">
              <div 
                v-for="potion in manaPotions" 
                :key="potion.id" 
                class="service-list-item" 
                :class="{ 'disabled': !canUseService(potion) }"
              >
                <div class="service-list-left">
                  <div class="service-list-name">{{ potion.name }}</div>
                  <div class="service-list-info">
                    <div class="service-list-description">恢复 {{ potion.effect_value }} 精力</div>
                    <div class="service-list-price">价格: {{ potion.price }} 银两</div>
                  </div>
                </div>
                <button 
                  class="service-list-button"
                  @click="purchasePotion(potion.id, 'mana')"
                  :disabled="!canUseService(potion)"
                >
                  购买
                </button>
              </div>
              <div v-if="manaPotions.length === 0" class="no-items">
                暂无可用药品
              </div>
            </div>
          </div>

          <!-- 全员治疗 -->
          <div v-if="activeTab === 'team'" class="tab-content">
            <div class="service-list">
              <div 
                v-for="service in teamServices" 
                :key="service.id" 
                class="service-list-item" 
                :class="{ 'disabled': !canUseService(service) }"
              >
                <div class="service-list-left">
                  <div class="service-list-name">{{ service.name }}</div>
                  <div class="service-list-info">
                    <div class="service-list-description">{{ service.description }}</div>
                    <div class="service-list-price">价格: {{ service.price }} 银两</div>
                  </div>
                </div>
                <button 
                  class="service-list-button"
                  @click="purchaseTeamService(service.id)"
                  :disabled="!canUseService(service)"
                >
                  购买
                </button>
              </div>
              <div v-if="teamServices.length === 0" class="no-items">
                暂无可用服务
              </div>
            </div>
          </div>
        </div>

        <!-- 返回按钮 -->
        <div class="bottom-actions">
          <button class="back-button" @click="$router.push('/game/main')">返回城镇</button>
        </div>

        <!-- 购买结果弹窗 -->
        <div v-if="showResult" class="result-modal">
          <div class="result-content" :class="{ 'error': resultError }">
            <h3>{{ resultError ? '购买失败' : '购买成功' }}</h3>
            <p>{{ resultMessage }}</p>
            <div v-if="healResult && !resultError" class="heal-result">
              <div v-if="healResult.hpHealed">恢复气血: +{{ healResult.hpHealed }}</div>
              <div v-if="healResult.mpHealed">恢复精力: +{{ healResult.mpHealed }}</div>
            </div>
            <button @click="showResult = false">确定</button>
          </div>
        </div>

        <!-- 加载中和错误提示 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
          <div>加载中...</div>
        </div>
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue';
import clinicService from '@/api/services/clinicService';
import { showMessage } from '@/utils/message';
import logger from '@/utils/logger';
import { getCurrentCharacter } from '@/api/services/characterService';

export default {
  name: 'Clinic',
  components: { GameLayout },
  data() {
    return {
      isLoading: true,
      error: null,
      activeTab: 'health', // 默认选中气血药品选项卡
      tabs: [
        {
          id: 'health',
          name: '气血药品'
        },
        {
          id: 'mana',
          name: '精力药品'
        },
        {
          id: 'team',
          name: '完全恢复'
        }
      ],
      healthPotions: [],
      manaPotions: [],
      teamServices: [],
      characterInfo: {
        id: null,
        level: 1,
        hp: 100,
        maxHp: 100,
        mp: 50,
        maxMp: 50,
        silver: 0,
        gold: 0
      },
      showResult: false,
      resultMessage: '',
      resultError: false,
      healResult: null,
      touchStartY: 0,
      touchMoveY: 0,
      scrolling: false
    };
  },
  computed: {
    hpPercent() {
      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100;
    },
    mpPercent() {
      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100;
    }
  },
  created() {
    this.initCharacterInfo();
    this.loadAllServices();
  },
  mounted() {
    // 添加触摸事件监听
    this.addTouchListeners();
  },
  beforeDestroy() {
    // 移除触摸事件监听
    this.removeTouchListeners();
  },
  methods: {
    // 添加触摸事件监听
    addTouchListeners() {
      const serviceList = this.$el.querySelector('.service-list');
      const clinicPage = this.$el.querySelector('.clinic-page');
      
      if (serviceList) {
        serviceList.addEventListener('touchstart', this.handleTouchStart, { passive: true });
        serviceList.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        serviceList.addEventListener('touchend', this.handleTouchEnd, { passive: true });
      }
      
      if (clinicPage) {
        clinicPage.addEventListener('touchstart', this.handleTouchStart, { passive: true });
        clinicPage.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        clinicPage.addEventListener('touchend', this.handleTouchEnd, { passive: true });
      }
    },
    // 移除触摸事件监听
    removeTouchListeners() {
      const serviceList = this.$el.querySelector('.service-list');
      const clinicPage = this.$el.querySelector('.clinic-page');
      
      if (serviceList) {
        serviceList.removeEventListener('touchstart', this.handleTouchStart);
        serviceList.removeEventListener('touchmove', this.handleTouchMove);
        serviceList.removeEventListener('touchend', this.handleTouchEnd);
      }
      
      if (clinicPage) {
        clinicPage.removeEventListener('touchstart', this.handleTouchStart);
        clinicPage.removeEventListener('touchmove', this.handleTouchMove);
        clinicPage.removeEventListener('touchend', this.handleTouchEnd);
      }
    },
    // 处理触摸开始事件
    handleTouchStart(event) {
      this.touchStartY = event.touches[0].clientY;
      this.scrolling = false;
    },
    // 处理触摸移动事件
    handleTouchMove(event) {
      this.touchMoveY = event.touches[0].clientY;
      const deltaY = this.touchStartY - this.touchMoveY;
      
      // 判断是否是垂直滚动
      if (Math.abs(deltaY) > 10) {
        this.scrolling = true;
      }
      
      // 如果是垂直滚动，不阻止默认行为
      if (this.scrolling) {
        return;
      }
      
      // 对于非垂直滚动的触摸移动，阻止默认行为以防止页面整体滚动
      event.preventDefault();
    },
    // 处理触摸结束事件
    handleTouchEnd() {
      this.touchStartY = 0;
      this.touchMoveY = 0;
      this.scrolling = false;
    },
    initCharacterInfo() {
      // 获取当前角色信息
      const character = getCurrentCharacter();
      if (character) {
        this.characterInfo.id = character.id;
        
        // 从Vuex获取更详细的角色信息
        const characterStatus = this.$store.state.character.characterStatus;
        if (characterStatus) {
          this.characterInfo = {
            ...this.characterInfo,
            level: characterStatus.level || 1,
            hp: characterStatus.hp || 100,
            maxHp: characterStatus.maxHp || 100,
            mp: characterStatus.mp || 50,
            maxMp: characterStatus.maxMp || 50,
            silver: characterStatus.silver || 0,
            gold: characterStatus.gold || 0
          };
        }
      } else {
        this.error = '未找到角色信息，请先选择角色';
        showMessage('未找到角色信息，请先选择角色', 'error');
        this.$router.push('/setup/character-select');
      }
    },
    async loadAllServices() {
      this.isLoading = true;
      this.error = null;
      
      try {
        // 加载服务类型
        await this.loadServiceTypes();
        
        // 加载各类药品和服务
        await Promise.all([
          this.loadHealthPotions(),
          this.loadManaPotions(),
          this.loadTeamServices()
        ]);
        
        this.isLoading = false;
      } catch (error) {
        logger.error('[医馆] 加载服务失败:', error);
        this.error = '加载服务失败，请重试';
        this.isLoading = false;
        showMessage('加载服务失败，请重试', 'error');
      }
    },
    async loadServiceTypes() {
      try {
        const serviceTypes = await clinicService.getServiceTypes();
        if (serviceTypes && serviceTypes.length > 0) {
          this.tabs = serviceTypes.map(type => ({
            id: type.id,
            name: type.name
          }));
          logger.debug('[医馆] 服务类型加载成功:', this.tabs);
        }
      } catch (error) {
        logger.error('[医馆] 加载服务类型失败:', error);
        throw error;
      }
    },
    async loadHealthPotions() {
      try {
        const healthPotions = await clinicService.getHealthPotions();
        this.healthPotions = healthPotions || [];
        logger.debug('[医馆] 气血药品加载成功:', this.healthPotions);
      } catch (error) {
        logger.error('[医馆] 加载气血药品失败:', error);
        throw error;
      }
    },
    async loadManaPotions() {
      try {
        const manaPotions = await clinicService.getManaPotions();
        this.manaPotions = manaPotions || [];
        logger.debug('[医馆] 精力药品加载成功:', this.manaPotions);
      } catch (error) {
        logger.error('[医馆] 加载精力药品失败:', error);
        throw error;
      }
    },
    async loadTeamServices() {
      try {
        const teamServices = await clinicService.getTeamServices();
        this.teamServices = teamServices || [];
        logger.debug('[医馆] 全员治疗服务加载成功:', this.teamServices);
      } catch (error) {
        logger.error('[医馆] 加载全员治疗服务失败:', error);
        throw error;
      }
    },
    canUseService(service) {
      // 检查角色是否有足够的银两
      if (this.characterInfo.silver < service.price) {
        return false;
      }
      
      // 对于气血药品，检查角色是否已经满血
      if (service.type === 'health' && this.characterInfo.hp >= this.characterInfo.maxHp) {
        return false;
      }
      
      // 对于精力药品，检查角色是否已经满精力
      if (service.type === 'mana' && this.characterInfo.mp >= this.characterInfo.maxMp) {
        return false;
      }
      
      // 对于全员治疗，检查角色是否已经满血和满精力
      if (service.type === 'team' && 
          this.characterInfo.hp >= this.characterInfo.maxHp && 
          this.characterInfo.mp >= this.characterInfo.maxMp) {
        return false;
      }
      
      return true;
    },
    async purchasePotion(potionId, type) {
      if (!this.characterInfo.id) {
        showMessage('未找到角色信息', 'error');
        return;
      }
      
      try {
        const response = await clinicService.purchasePotion(this.characterInfo.id, potionId, type);
        logger.debug('[医馆] 购买药品成功:', response);
        
        // 更新角色状态
        if (response.character) {
          this.characterInfo.hp = response.character.hp;
          this.characterInfo.mp = response.character.mp;
          this.characterInfo.silver = response.character.silver;
          this.characterInfo.gold = response.character.gold;
          
          // 更新Vuex中的角色状态
          this.$store.commit('character/updateCharacterStatus', {
            hp: response.character.hp,
            mp: response.character.mp,
            silver: response.character.silver,
            gold: response.character.gold
          });
        }
        
        // 显示结果
        this.resultMessage = response.message || '购买成功';
        this.resultError = false;
        this.healResult = {
          hpHealed: response.hp_recovered,
          mpHealed: response.mp_recovered
        };
        this.showResult = true;
        
        // 刷新药品列表
        if (type === 'health') {
          this.loadHealthPotions();
        } else if (type === 'mana') {
          this.loadManaPotions();
        }
      } catch (error) {
        logger.error('[医馆] 购买药品失败:', error);
        this.resultMessage = error.message || '购买失败';
        this.resultError = true;
        this.healResult = null;
        this.showResult = true;
      }
    },
    async purchaseTeamService(serviceId) {
      if (!this.characterInfo.id) {
        showMessage('未找到角色信息', 'error');
        return;
      }
      
      try {
        const response = await clinicService.useTeamService(this.characterInfo.id, serviceId);
        logger.debug('[医馆] 使用全员治疗成功:', response);
        
        // 更新角色状态
        if (response.character) {
          this.characterInfo.hp = response.character.hp;
          this.characterInfo.mp = response.character.mp;
          this.characterInfo.silver = response.character.silver;
          this.characterInfo.gold = response.character.gold;
          
          // 更新Vuex中的角色状态
          this.$store.commit('character/updateCharacterStatus', {
            hp: response.character.hp,
            mp: response.character.mp,
            silver: response.character.silver,
            gold: response.character.gold
          });
        }
        
        // 显示结果
        this.resultMessage = response.message || '治疗成功';
        this.resultError = false;
        this.healResult = {
          hpHealed: response.hp_recovered,
          mpHealed: response.mp_recovered
        };
        this.showResult = true;
        
        // 刷新全员治疗服务列表
        this.loadTeamServices();
      } catch (error) {
        logger.error('[医馆] 使用全员治疗失败:', error);
        this.resultMessage = error.message || '治疗失败';
        this.resultError = true;
        this.healResult = null;
        this.showResult = true;
      }
    },
    closeResult() {
      this.showResult = false;
      this.healResult = null;
    },
    goBack() {
      this.$router.push('/game/main');
    }
  }
};
</script>

<style lang="scss" scoped>
.clinic-page {
  width: 100%;
  min-height: 100vh;
  background-color: #000033;
  color: #ffffff;
  padding: 8px;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  max-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.clinic-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  padding-bottom: 65px;
}

.clinic-header {
  margin-bottom: 15px;
  padding: 12px;
  background-color: rgba(0, 0, 51, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(51, 51, 204, 0.3);
}

.clinic-header h1 {
  margin: 0 0 10px 0;
  text-align: center;
  color: #ffcc00;
  font-size: 22px;
  text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);
}

.character-status {
  margin-top: 10px;
}

.status-box {
  background-color: rgba(153, 0, 0, 0.2);
  border: 1px solid rgba(153, 0, 0, 0.5);
  border-radius: 5px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 3px 5px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  color: #aaaaff;
  font-weight: bold;
}

.status-value {
  color: white;
  font-weight: bold;
}

.silver-value {
  color: #ffcc00;
}

.tabs-container {
  display: flex;
  margin-bottom: 15px;
  background-color: rgba(0, 0, 51, 0.3);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(51, 51, 204, 0.3);
}

.tab {
  flex: 1;
  padding: 10px 8px;
  text-align: center;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tab.active {
  border-bottom-color: #ffcc00;
  background-color: rgba(255, 204, 0, 0.1);
}

.tab-name {
  font-weight: bold;
  color: #ffffff;
}

.tab.active .tab-name {
  color: #ffcc00;
}

.content-area {
  flex: 1;
  overflow: visible;
  position: relative;
}

.tab-content {
  padding: 5px 0;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: calc(70vh - 180px);
  overflow-y: auto;
  padding-right: 5px;
  padding-bottom: 20px;
  margin-top: 10px;
}

.service-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: rgba(0, 0, 51, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(51, 51, 204, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.service-list-item:hover {
  background-color: rgba(0, 0, 51, 0.5);
  border-color: #3333cc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.service-list-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.service-list-left {
  display: flex;
  flex-direction: column;
  width: 75%;
}

.service-list-name {
  font-weight: bold;
  color: #ffcc00;
  font-size: 16px;
  text-shadow: 0 0 3px rgba(255, 204, 0, 0.3);
}

.service-list-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.service-list-description {
  font-size: 14px;
  color: #aaaaff;
}

.service-list-price {
  color: #ffcc00;
  font-weight: bold;
  font-size: 14px;
  margin-left: 10px;
  background-color: rgba(153, 0, 0, 0.3);
  border: 1px solid rgba(153, 0, 0, 0.7);
  border-radius: 4px;
  padding: 2px 6px;
}

.service-list-right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30%;
}

.service-list-button {
  padding: 6px 15px;
  background-color: #990000;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  width: 60px;
}

.service-list-button:hover:not(:disabled) {
  background-color: #cc0000;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.service-list-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.service-list-button:disabled {
  background-color: #666666;
  cursor: not-allowed;
  box-shadow: none;
}

.gold {
  color: #ffcc00;
}

.silver {
  color: #cccccc;
}

.service-list-btn {
  background-color: #3333cc;
  color: white;
  border: none;
  padding: 6px 14px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0;
}

.service-list-btn:hover:not(:disabled) {
  background-color: #4444dd;
}

.service-list-btn:disabled {
  background-color: #222255;
  cursor: not-allowed;
}

.bottom-actions {
  position: fixed;
  bottom: 15px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 12px 10px;
  z-index: 100;
  background: linear-gradient(to top, rgba(0, 0, 51, 0.95) 0%, rgba(0, 0, 51, 0.8) 50%, rgba(0, 0, 51, 0) 100%);
  padding-top: 25px;
}

.back-button {
  padding: 10px 25px;
  background-color: #cc3333;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  font-weight: bold;
  font-size: 16px;
  letter-spacing: 1px;
}

.back-button:hover {
  background-color: #dd4444;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
}

.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  background-color: #000033;
  border: 2px solid #3333cc;
  border-radius: 6px;
  width: 80%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #3333cc;
}

.result-header h2 {
  margin: 0;
  font-size: 18px;
  color: #ffcc00;
}

.close-btn {
  background: none;
  border: none;
  color: #aaaaff;
  font-size: 20px;
  cursor: pointer;
}

.result-body {
  padding: 20px;
  text-align: center;
}

.result-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  margin: 0 auto 15px;
}

.result-icon.success {
  background-color: #33cc33;
  color: white;
}

.result-icon.error {
  background-color: #cc3333;
  color: white;
}

.result-message {
  font-size: 16px;
  margin-bottom: 15px;
}

.result-details {
  text-align: left;
  margin-top: 15px;
  border-top: 1px solid #3333cc;
  padding-top: 15px;
}

.result-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.result-label {
  color: #aaaaff;
  display: inline-block;
  width: 100px;
}

.result-value {
  font-weight: bold;
}

.result-footer {
  padding: 10px 15px;
  border-top: 1px solid #3333cc;
  text-align: center;
}

.confirm-btn {
  background-color: #3333cc;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.confirm-btn:hover {
  background-color: #4444dd;
}

@media (max-width: 600px) {
  .service-list-item {
    flex-direction: column;
    gap: 10px;
  }
  
  .service-list-left {
    width: 100%;
  }
  
  .service-list-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .service-list-details {
    text-align: left;
  }
  
  .status-label {
    width: 50px;
  }
  
  .status-bar {
    width: calc(100% - 60px);
  }
}

/* 自定义滚动条样式 */
.service-list::-webkit-scrollbar {
  width: 5px;
}

.service-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 51, 0.3);
  border-radius: 10px;
}

.service-list::-webkit-scrollbar-thumb {
  background: #3333cc;
  border-radius: 10px;
}

.service-list::-webkit-scrollbar-thumb:hover {
  background: #4444dd;
}

/* 移动设备适配 */
@media (max-width: 480px) {
  .clinic-page {
    padding: 5px;
  }
  
  .clinic-header h1 {
    font-size: 20px;
  }
  
  .tab {
    padding: 8px 5px;
    font-size: 14px;
  }
  
  .tab-icon {
    width: 20px;
    height: 20px;
  }
  
  .service-name {
    font-size: 14px;
  }
  
  .service-description, .service-price {
    font-size: 12px;
  }
  
  .buy-button {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .bottom-actions {
    bottom: 10px;
  }
}

/* 确保内容区域可以滚动 */
.content-area {
  flex: 1;
  overflow: visible;
  position: relative;
}
</style> 