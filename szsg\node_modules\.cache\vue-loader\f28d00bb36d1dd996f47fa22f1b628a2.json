{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Battle.vue?vue&type=style&index=0&id=36eb2a2a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\Battle.vue", "mtime": 1749890584410}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Battle.vue"], "names": [], "mappings": ";AA6cA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Battle.vue", "sourceRoot": "src/views/game", "sourcesContent": ["<template>\n  <GameLayout>\n    <div class=\"battle-container\">\n      <!-- 怪物信息区域 -->\n      <div class=\"monster-section\">\n        <div class=\"monster-info\">\n          <img :src=\"monster.avatar || '/static/game/ui/default_monster.png'\" class=\"monster-avatar\" />\n          <div class=\"monster-details\">\n            <h3>{{ monster.name }} Lv.{{ monster.level }}</h3>\n            <div class=\"hp-bar\">\n              <div class=\"bar-fill hp-fill\" :style=\"{ width: monsterHpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ monster.hp }}/{{ monster.max_hp }}</span>\n            </div>\n            <div class=\"mp-bar\">\n              <div class=\"bar-fill mp-fill\" :style=\"{ width: monsterMpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ monster.mp }}/{{ monster.max_mp }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 战斗动画区域 -->\n      <div class=\"battle-animation-area\" ref=\"animationArea\">\n        <BattleAnimation ref=\"battleAnimation\" />\n      </div>\n\n      <!-- 角色信息区域 -->\n      <div class=\"character-section\">\n        <div class=\"character-info\">\n          <img :src=\"character.avatar || '/static/game/ui/default_avatar.png'\" class=\"character-avatar\" />\n          <div class=\"character-details\">\n            <h3>{{ character.name }} Lv.{{ character.level }}</h3>\n            <div class=\"hp-bar\">\n              <div class=\"bar-fill hp-fill\" :style=\"{ width: characterHpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ character.hp }}/{{ character.max_hp }}</span>\n            </div>\n            <div class=\"mp-bar\">\n              <div class=\"bar-fill mp-fill\" :style=\"{ width: characterMpPercent + '%' }\"></div>\n              <span class=\"bar-text\">{{ character.mp }}/{{ character.max_mp }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 操作按钮区域 -->\n      <div class=\"action-buttons\">\n        <button \n          @click=\"attack\" \n          :disabled=\"!canAct\" \n          class=\"action-btn attack-btn\"\n        >\n          攻击\n        </button>\n        <button \n          @click=\"openItems\" \n          :disabled=\"!canAct\" \n          class=\"action-btn item-btn\"\n        >\n          物品\n        </button>\n        <button \n          @click=\"flee\" \n          :disabled=\"!canAct\" \n          class=\"action-btn flee-btn\"\n        >\n          逃跑\n        </button>\n        <button \n          @click=\"returnToMain\" \n          v-if=\"battleFinished\"\n          class=\"action-btn return-btn\"\n        >\n          返回\n        </button>\n      </div>\n\n      <!-- 战斗日志 -->\n      <div class=\"battle-log\" v-if=\"showLog\">\n        <div class=\"log-header\">\n          <h4>战斗记录</h4>\n          <button @click=\"toggleLog\" class=\"close-btn\">×</button>\n        </div>\n        <div class=\"log-content\">\n          <div \n            v-for=\"(log, index) in battleLogs\" \n            :key=\"index\"\n            class=\"log-entry\"\n          >\n            <span class=\"log-time\">第{{ log.round }}回合</span>\n            <span class=\"log-message\">{{ formatLogMessage(log) }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 战斗结果弹窗 -->\n      <div v-if=\"showResult\" class=\"battle-result-modal\">\n        <div class=\"result-content\">\n          <h3 :class=\"resultClass\">{{ resultTitle }}</h3>\n          <div v-if=\"battleResult === 'victory'\" class=\"rewards\">\n            <p>获得经验: {{ rewards.exp_gained }}</p>\n            <p>获得金币: {{ rewards.gold_gained }}</p>\n            <div v-if=\"rewards.items_gained && rewards.items_gained.length > 0\">\n              <p>获得物品:</p>\n              <ul>\n                <li v-for=\"item in rewards.items_gained\" :key=\"item.item_id\">\n                  {{ item.name }} × {{ item.quantity }}\n                </li>\n              </ul>\n            </div>\n          </div>\n          <button @click=\"closeResult\" class=\"confirm-btn\">确定</button>\n        </div>\n      </div>\n    </div>\n  </GameLayout>\n</template>\n\n<script>\nimport GameLayout from '@/layouts/GameLayout.vue'\nimport BattleAnimation from '@/components/game/BattleAnimation.vue'\nimport battleService from '@/api/services/battleService.js'\nimport logger from '@/utils/logger.js'\n\nexport default {\n  name: 'Battle',\n  components: {\n    GameLayout,\n    BattleAnimation\n  },\n  \n  data() {\n    return {\n      battleId: null,\n      character: {\n        id: null,\n        name: '',\n        level: 1,\n        avatar: '',\n        hp: 100,\n        max_hp: 100,\n        mp: 50,\n        max_mp: 50\n      },\n      monster: {\n        id: null,\n        name: '',\n        level: 1,\n        avatar: '',\n        hp: 100,\n        max_hp: 100,\n        mp: 50,\n        max_mp: 50\n      },\n      battleStatus: 'ongoing', // ongoing, victory, defeat, fled\n      canAct: true,\n      isProcessing: false,\n      battleLogs: [],\n      showLog: false,\n      showResult: false,\n      battleResult: null,\n      rewards: {},\n      animationManager: null\n    }\n  },\n\n  computed: {\n    characterHpPercent() {\n      return this.character.max_hp > 0 ? (this.character.hp / this.character.max_hp) * 100 : 0\n    },\n    \n    characterMpPercent() {\n      return this.character.max_mp > 0 ? (this.character.mp / this.character.max_mp) * 100 : 0\n    },\n    \n    monsterHpPercent() {\n      return this.monster.max_hp > 0 ? (this.monster.hp / this.monster.max_hp) * 100 : 0\n    },\n    \n    monsterMpPercent() {\n      return this.monster.max_mp > 0 ? (this.monster.mp / this.monster.max_mp) * 100 : 0\n    },\n    \n    battleFinished() {\n      return ['victory', 'defeat', 'fled'].includes(this.battleStatus)\n    },\n    \n    resultTitle() {\n      switch (this.battleResult) {\n        case 'victory': return '战斗胜利！'\n        case 'defeat': return '战斗失败！'\n        case 'fled': return '成功逃脱！'\n        default: return ''\n      }\n    },\n    \n    resultClass() {\n      switch (this.battleResult) {\n        case 'victory': return 'victory-text'\n        case 'defeat': return 'defeat-text'\n        case 'fled': return 'flee-text'\n        default: return ''\n      }\n    }\n  },\n\n  async mounted() {\n    try {\n      // 从路由参数获取战斗信息\n      const { characterId, monsterId, locationId } = this.$route.query\n      logger.debug('[Battle] 路由参数:', { characterId, monsterId, locationId })\n\n      if (!characterId || !monsterId) {\n        logger.error('[Battle] 缺少战斗参数')\n        this.showToast('缺少战斗参数')\n        this.$router.push('/game/main')\n        return\n      }\n\n      // 验证用户认证状态\n      const isAuthenticated = this.$store.state.auth.isAuthenticated\n      logger.debug('[Battle] 用户认证状态:', isAuthenticated)\n\n      if (!isAuthenticated) {\n        logger.error('[Battle] 用户未认证')\n        this.showToast('请先登录')\n        this.$router.push('/login')\n        return\n      }\n\n      // 开始战斗\n      await this.initBattle(parseInt(characterId), parseInt(monsterId), locationId)\n\n    } catch (error) {\n      logger.error('[Battle] 初始化战斗失败:', error)\n      this.showToast('初始化战斗失败')\n      this.$router.push('/game/main')\n    }\n  },\n\n  methods: {\n    // 初始化战斗\n    async initBattle(characterId, monsterId, locationId) {\n      try {\n        logger.debug('[Battle] 开始初始化战斗:', { characterId, monsterId, locationId })\n\n        // 验证参数\n        if (!characterId || !monsterId) {\n          throw new Error('缺少必要的战斗参数')\n        }\n\n        const result = await battleService.startBattle(characterId, monsterId, locationId)\n        logger.debug('[Battle] 战斗API响应:', result)\n\n        // 检查API响应结构 - battleService返回的是res.data，所以直接检查battle_id\n        if (result && result.battle_id && result.battle) {\n          this.battleId = result.battle_id\n          this.updateBattleData(result.battle)\n          this.showToast('战斗开始！')\n        } else {\n          // 显示更详细的错误信息\n          const errorMessage = result?.message || result?.error || '开始战斗失败'\n          logger.error('[Battle] 战斗失败详情:', result)\n          throw new Error(errorMessage)\n        }\n      } catch (error) {\n        logger.error('[Battle] 开始战斗失败:', error)\n        // 如果是网络错误，显示更友好的错误信息\n        if (error.response) {\n          logger.error('[Battle] HTTP错误:', error.response.status, error.response.data)\n          throw new Error(`服务器错误 (${error.response.status}): ${error.response.data?.message || '未知错误'}`)\n        } else if (error.request) {\n          logger.error('[Battle] 网络错误:', error.request)\n          throw new Error('网络连接失败，请检查网络连接')\n        } else {\n          throw error\n        }\n      }\n    },\n\n    // 更新战斗数据\n    updateBattleData(battleData) {\n      this.character = { ...battleData.character }\n      this.monster = { ...battleData.monster }\n      this.battleStatus = battleData.status\n      this.canAct = battleData.can_act\n      this.rewards = battleData.rewards || {}\n    },\n\n    // 攻击\n    async attack() {\n      if (!this.canAct || this.isProcessing) return\n      \n      try {\n        this.isProcessing = true\n        this.canAct = false\n        \n        const result = await battleService.performAction(this.battleId, this.character.id, 'attack')\n        \n        if (result.success) {\n          // 播放攻击动画\n          if (this.$refs.battleAnimation) {\n            this.$refs.battleAnimation.playAttackAnimation()\n            \n            // 处理角色攻击结果\n            if (result.data.action_results.character_action) {\n              const charAction = result.data.action_results.character_action\n              this.$refs.battleAnimation.playDamageAnimation(charAction.damage, charAction.is_critical)\n            }\n            \n            // 处理怪物反击\n            if (result.data.action_results.monster_action) {\n              const monsterAction = result.data.action_results.monster_action\n              setTimeout(() => {\n                this.$refs.battleAnimation.playDamageAnimation(monsterAction.damage, monsterAction.is_critical)\n              }, 1500)\n            }\n          }\n          \n          // 更新战斗数据\n          this.updateBattleData(result.data.battle)\n          \n          // 检查战斗是否结束\n          if (result.data.action_results.character_action?.battle_end) {\n            this.handleBattleEnd(result.data.action_results.character_action.battle_end)\n          }\n          \n        } else {\n          this.showToast(result.message || '攻击失败')\n        }\n      } catch (error) {\n        logger.error('[Battle] 攻击失败:', error)\n        this.showToast('攻击失败')\n      } finally {\n        this.isProcessing = false\n        if (this.battleStatus === 'ongoing') {\n          this.canAct = true\n        }\n      }\n    },\n\n    // 逃跑\n    async flee() {\n      if (!this.canAct || this.isProcessing) return\n      \n      try {\n        this.isProcessing = true\n        this.canAct = false\n        \n        const result = await battleService.performAction(this.battleId, this.character.id, 'flee')\n        \n        if (result.success) {\n          if (result.data.action_results.flee_result?.success) {\n            this.battleResult = 'fled'\n            this.battleStatus = 'fled'\n            this.showResult = true\n          } else {\n            this.showToast('逃跑失败！')\n            this.canAct = true\n          }\n        } else {\n          this.showToast(result.message || '逃跑失败')\n          this.canAct = true\n        }\n      } catch (error) {\n        logger.error('[Battle] 逃跑失败:', error)\n        this.showToast('逃跑失败')\n        this.canAct = true\n      } finally {\n        this.isProcessing = false\n      }\n    },\n\n    // 处理战斗结束\n    handleBattleEnd(endResult) {\n      this.battleResult = endResult.result\n      this.battleStatus = endResult.result\n      this.canAct = false\n      \n      if (endResult.rewards) {\n        this.rewards = endResult.rewards\n      }\n      \n      // 延迟显示结果，让动画播放完\n      setTimeout(() => {\n        this.showResult = true\n      }, 2000)\n    },\n\n    // 打开物品界面\n    openItems() {\n      // TODO: 实现物品使用界面\n      this.showToast('物品功能开发中...')\n    },\n\n    // 切换日志显示\n    toggleLog() {\n      this.showLog = !this.showLog\n      if (this.showLog) {\n        this.loadBattleLog()\n      }\n    },\n\n    // 加载战斗日志\n    async loadBattleLog() {\n      try {\n        const result = await battleService.getBattleLog(this.battleId)\n        if (result.success) {\n          this.battleLogs = result.data.battle_log || []\n        }\n      } catch (error) {\n        logger.error('[Battle] 加载战斗日志失败:', error)\n      }\n    },\n\n    // 格式化日志消息\n    formatLogMessage(log) {\n      switch (log.action) {\n        case 'battle_start':\n          return '战斗开始！'\n        case 'character_attack': {\n          const damage = log.data.damage || 0\n          const critical = log.data.is_critical ? '(暴击)' : ''\n          return `你对${this.monster.name}造成了${damage}点伤害${critical}`\n        }\n        case 'monster_attack': {\n          const monsterDamage = log.data.damage || 0\n          const monsterCritical = log.data.is_critical ? '(暴击)' : ''\n          return `${this.monster.name}对你造成了${monsterDamage}点伤害${monsterCritical}`\n        }\n        case 'battle_end':\n          return `战斗结束，结果：${log.data.result}`\n        default:\n          return log.action\n      }\n    },\n\n    // 关闭结果弹窗\n    closeResult() {\n      this.showResult = false\n      this.returnToMain()\n    },\n\n    // 返回主界面\n    returnToMain() {\n      this.$router.push('/game/main')\n    },\n\n    // 显示提示\n    showToast(message) {\n      // 使用全局提示组件\n      if (this.$toast) {\n        this.$toast(message)\n      } else {\n        alert(message)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.battle-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);\n  color: #fff;\n  font-family: 'Microsoft YaHei', sans-serif;\n}\n\n/* 怪物信息区域 */\n.monster-section {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(139, 69, 19, 0.3);\n  border-bottom: 2px solid #8B4513;\n}\n\n.monster-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 15px;\n  border-radius: 10px;\n  border: 2px solid #666;\n}\n\n.monster-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid #ff6b6b;\n  object-fit: cover;\n}\n\n.monster-details h3 {\n  margin: 0 0 10px 0;\n  color: #ff6b6b;\n  font-size: 18px;\n}\n\n/* 战斗动画区域 */\n.battle-animation-area {\n  flex: 2;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* 角色信息区域 */\n.character-section {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n  background: rgba(0, 100, 0, 0.2);\n  border-top: 2px solid #228B22;\n}\n\n.character-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 15px;\n  border-radius: 10px;\n  border: 2px solid #666;\n}\n\n.character-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid #87ceeb;\n  object-fit: cover;\n}\n\n.character-details h3 {\n  margin: 0 0 10px 0;\n  color: #87ceeb;\n  font-size: 18px;\n}\n\n/* 血条和蓝条样式 */\n.hp-bar, .mp-bar {\n  position: relative;\n  width: 200px;\n  height: 20px;\n  background: #333;\n  border: 1px solid #666;\n  border-radius: 10px;\n  margin: 5px 0;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  transition: width 0.5s ease;\n  border-radius: 9px;\n}\n\n.hp-fill {\n  background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%);\n}\n\n.mp-fill {\n  background: linear-gradient(90deg, #4444ff 0%, #6666ff 100%);\n}\n\n.bar-text {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  border-top: 2px solid #666;\n}\n\n.action-btn {\n  padding: 12px 24px;\n  font-size: 16px;\n  font-weight: bold;\n  border: 2px solid;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  min-width: 80px;\n}\n\n.attack-btn {\n  border-color: #ff4444;\n  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);\n}\n\n.attack-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ff6666 0%, #ff4444 100%);\n  transform: translateY(-2px);\n}\n\n.item-btn {\n  border-color: #44ff44;\n  background: linear-gradient(135deg, #44ff44 0%, #33cc33 100%);\n}\n\n.item-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #66ff66 0%, #44ff44 100%);\n  transform: translateY(-2px);\n}\n\n.flee-btn {\n  border-color: #ffff44;\n  background: linear-gradient(135deg, #ffff44 0%, #cccc33 100%);\n  color: #333;\n}\n\n.flee-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ffff66 0%, #ffff44 100%);\n  transform: translateY(-2px);\n}\n\n.return-btn {\n  border-color: #888;\n  background: linear-gradient(135deg, #888 0%, #666 100%);\n}\n\n.return-btn:hover {\n  background: linear-gradient(135deg, #aaa 0%, #888 100%);\n  transform: translateY(-2px);\n}\n\n.action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 战斗日志 */\n.battle-log {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 400px;\n  max-height: 300px;\n  background: rgba(0, 0, 0, 0.9);\n  border: 2px solid #666;\n  border-radius: 10px;\n  z-index: 1000;\n}\n\n.log-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border-bottom: 1px solid #666;\n}\n\n.log-header h4 {\n  margin: 0;\n  color: #fff;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 20px;\n  cursor: pointer;\n  padding: 0;\n  width: 25px;\n  height: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.log-content {\n  max-height: 200px;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.log-entry {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.log-time {\n  color: #888;\n  min-width: 60px;\n}\n\n.log-message {\n  color: #fff;\n}\n\n/* 战斗结果弹窗 */\n.battle-result-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 2000;\n}\n\n.result-content {\n  background: linear-gradient(135deg, #2c1810 0%, #1a0f08 100%);\n  border: 3px solid #666;\n  border-radius: 15px;\n  padding: 30px;\n  text-align: center;\n  min-width: 300px;\n}\n\n.result-content h3 {\n  margin: 0 0 20px 0;\n  font-size: 24px;\n}\n\n.victory-text {\n  color: #44ff44;\n  text-shadow: 0 0 10px #44ff44;\n}\n\n.defeat-text {\n  color: #ff4444;\n  text-shadow: 0 0 10px #ff4444;\n}\n\n.flee-text {\n  color: #ffff44;\n  text-shadow: 0 0 10px #ffff44;\n}\n\n.rewards {\n  margin: 20px 0;\n  text-align: left;\n}\n\n.rewards p {\n  margin: 8px 0;\n  color: #fff;\n}\n\n.rewards ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rewards li {\n  color: #87ceeb;\n  margin: 5px 0;\n}\n\n.confirm-btn {\n  padding: 10px 30px;\n  font-size: 16px;\n  font-weight: bold;\n  background: linear-gradient(135deg, #4444ff 0%, #3333cc 100%);\n  color: #fff;\n  border: 2px solid #4444ff;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.confirm-btn:hover {\n  background: linear-gradient(135deg, #6666ff 0%, #4444ff 100%);\n  transform: translateY(-2px);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .monster-info, .character-info {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .monster-avatar, .character-avatar {\n    width: 60px;\n    height: 60px;\n  }\n\n  .hp-bar, .mp-bar {\n    width: 150px;\n  }\n\n  .action-buttons {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .action-btn {\n    padding: 10px 20px;\n    font-size: 14px;\n    min-width: 70px;\n  }\n\n  .battle-log {\n    width: 90%;\n    max-width: 350px;\n  }\n}\n</style>\n"]}]}