@extends('admin.layouts.app')

@section('title', '战斗日志')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">战斗日志列表</div>
    <div class="layui-card-body">
        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <div class="layui-form layui-form-pane">
            <form action="{{ route('admin.battles.index') }}" method="GET">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">角色</label>
                        <div class="layui-input-inline">
                            <select name="character_id">
                                <option value="">全部</option>
                                @foreach($characters ?? [] as $character)
                                <option value="{{ $character->id }}" {{ request('character_id') == $character->id ? 'selected' : '' }}>
                                    {{ $character->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">怪物</label>
                        <div class="layui-input-inline">
                            <select name="monster_id">
                                <option value="">全部</option>
                                @foreach($monsters ?? [] as $monster)
                                <option value="{{ $monster->id }}" {{ request('monster_id') == $monster->id ? 'selected' : '' }}>
                                    {{ $monster->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">战斗结果</label>
                        <div class="layui-input-inline">
                            <select name="result">
                                <option value="">全部</option>
                                <option value="win" {{ request('result') == 'win' ? 'selected' : '' }}>胜利</option>
                                <option value="lose" {{ request('result') == 'lose' ? 'selected' : '' }}>失败</option>
                                <option value="escape" {{ request('result') == 'escape' ? 'selected' : '' }}>逃跑</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">开始日期</label>
                        <div class="layui-input-inline">
                            <input type="date" name="date_from" value="{{ request('date_from') }}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">结束日期</label>
                        <div class="layui-input-inline">
                            <input type="date" name="date_to" value="{{ request('date_to') }}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit>筛选</button>
                        <a href="{{ route('admin.battles.index') }}" class="layui-btn layui-btn-primary">重置</a>
                    </div>
                </div>
            </form>
        </div>

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>角色</th>
                    <th>怪物</th>
                    <th>回合数</th>
                    <th>状态</th>
                    <th>经验获得</th>
                    <th>银两获得</th>
                    <th>时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($battles as $battle)
                <tr>
                    <td>{{ $battle->id }}</td>
                    <td>{{ $battle->character->name ?? ($battle->character_name ?? '未知') }}</td>
                    <td>{{ $battle->monster->name ?? ($battle->monster_name ?? '未知') }}</td>
                    <td>{{ $battle->rounds ?? 0 }}</td>
                    <td>
                        @php
                        $statusMap = [
                            'ongoing' => '<span class="layui-badge layui-bg-blue">进行中</span>',
                            'victory' => '<span class="layui-badge layui-bg-green">胜利</span>',
                            'defeat' => '<span class="layui-badge layui-bg-red">失败</span>',
                            'fled' => '<span class="layui-badge layui-bg-orange">逃跑</span>',
                            // 兼容旧数据
                            'win' => '<span class="layui-badge layui-bg-green">胜利</span>',
                            'lose' => '<span class="layui-badge layui-bg-red">失败</span>',
                            'escape' => '<span class="layui-badge layui-bg-orange">逃跑</span>',
                            'active' => '<span class="layui-badge layui-bg-blue">进行中</span>',
                        ];

                        // 增强错误处理，确保即使对象没有status属性也不会报错
                        $status = 'unknown';

                        // 检查是否为对象，以及对象是否有status或result属性
                        if (is_object($battle)) {
                            if (property_exists($battle, 'status') && isset($battle->status)) {
                                $status = $battle->status;
                            } elseif (property_exists($battle, 'result') && isset($battle->result)) {
                                $status = $battle->result;
                            } elseif (property_exists($battle, 'type') && isset($battle->type)) {
                                // 如果有type属性但没有status，可能是旧数据
                                $status = 'unknown';
                            }
                        } elseif (is_array($battle)) {
                            // 如果是数组，尝试使用数组访问方式
                            if (isset($battle['status'])) {
                                $status = $battle['status'];
                            } elseif (isset($battle['result'])) {
                                $status = $battle['result'];
                            }
                        }

                        $statusDisplay = $statusMap[$status] ?? '<span class="layui-badge">未知</span>';
                        @endphp
                        {!! $statusDisplay !!}
                    </td>
                    <td>{{ $battle->exp_gained ?? 0 }}</td>
                    <td>{{ $battle->gold_gained ?? ($battle->silver_gained ?? 0) }}</td>
                    <td>{{ $battle->created_at }}</td>
                    <td>
                        <a href="{{ route('admin.battles.show', $battle->id) }}" class="layui-btn layui-btn-xs">查看详情</a>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="9" class="layui-center">暂无战斗记录</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $battles->links('admin.layouts.pagination') }}
    </div>
</div>
@endsection

@section('scripts')
<script>
layui.use(['form', 'laydate'], function(){
    var form = layui.form;
    var laydate = layui.laydate;

    // 重新渲染表单
    form.render();
});
</script>
@endsection
