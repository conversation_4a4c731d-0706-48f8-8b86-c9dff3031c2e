<?php $__env->startSection('title', '创建怪物'); ?>

<?php $__env->startSection('content'); ?>
<div class="layui-card">
    <div class="layui-card-header">
        创建新怪物
        <a href="<?php echo e(route('admin.monsters.index')); ?>" class="layui-btn layui-btn-xs layui-btn-primary" style="float: right;">返回列表</a>
    </div>
    <div class="layui-card-body">
        <?php if(session('error')): ?>
        <div class="layui-alert layui-alert-danger">
            <?php echo e(session('error')); ?>

        </div>
        <?php endif; ?>

        <form class="layui-form" action="<?php echo e(route('admin.monsters.store')); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <div class="layui-form-item">
                <label class="layui-form-label required">怪物名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="<?php echo e(old('name')); ?>" class="layui-input" required>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">怪物描述</label>
                <div class="layui-input-block">
                    <textarea name="description" class="layui-textarea"><?php echo e(old('description')); ?></textarea>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">等级</label>
                        <div class="layui-input-block">
                            <input type="number" name="level" value="<?php echo e(old('level', 1)); ?>" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">生命值</label>
                        <div class="layui-input-block">
                            <input type="number" name="hp" value="<?php echo e(old('hp', 100)); ?>" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">魔法值</label>
                        <div class="layui-input-block">
                            <input type="number" name="mp" value="<?php echo e(old('mp', 0)); ?>" class="layui-input" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">攻击力</label>
                        <div class="layui-input-block">
                            <input type="number" name="attack" value="<?php echo e(old('attack', 10)); ?>" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">防御力</label>
                        <div class="layui-input-block">
                            <input type="number" name="defense" value="<?php echo e(old('defense', 5)); ?>" class="layui-input" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">速度</label>
                        <div class="layui-input-block">
                            <input type="number" name="speed" value="<?php echo e(old('speed', 5)); ?>" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">经验奖励</label>
                        <div class="layui-input-block">
                            <input type="number" name="exp_reward" value="<?php echo e(old('exp_reward', 10)); ?>" class="layui-input" min="1" required>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">银两奖励</label>
                        <div class="layui-input-block">
                            <input type="number" name="silver_reward" value="<?php echo e(old('silver_reward', 5)); ?>" class="layui-input" min="0" required>
                        </div>
                    </div>
                </div>
            </div>

            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                <legend>怪物出现位置</legend>
            </fieldset>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <input type="checkbox" name="location_ids[]" value="<?php echo e($location->id); ?>" title="<?php echo e($location->region_name); ?> - <?php echo e($location->name); ?>" lay-skin="primary">
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                <legend>怪物掉落物品</legend>
            </fieldset>

            <div class="layui-form-item">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div id="drop_items">
                            <div class="layui-form-item drop-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">物品</label>
                                    <div class="layui-input-inline">
                                        <select name="drop_items[0][item_id]">
                                            <option value="">请选择</option>
                                            <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">掉落率(%)</label>
                                    <div class="layui-input-inline" style="width: 80px;">
                                        <input type="number" name="drop_items[0][drop_rate]" class="layui-input" value="10" min="0" max="100" step="0.1">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button type="button" class="layui-btn layui-btn-danger" onclick="removeDropItem(this)">删除</button>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <button type="button" class="layui-btn" onclick="addDropItem()">添加掉落物品</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top: 20px;">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit>保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 表单验证
    form.verify({
        name: function(value) {
            if(value.length < 1 || value.length > 50) {
                return '怪物名称必须在1-50个字符之间';
            }
        }
    });
});

// 掉落物品计数器
var dropItemCount = 1;

// 添加掉落物品
function addDropItem() {
    var html = '<div class="layui-form-item drop-item">' +
        '<div class="layui-inline">' +
        '<label class="layui-form-label">物品</label>' +
        '<div class="layui-input-inline">' +
        '<select name="drop_items[' + dropItemCount + '][item_id]">' +
        '<option value="">请选择</option>';

    // 添加所有物品选项
    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    html += '<option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>';
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    html += '</select>' +
        '</div>' +
        '</div>' +
        '<div class="layui-inline">' +
        '<label class="layui-form-label">掉落率(%)</label>' +
        '<div class="layui-input-inline" style="width: 80px;">' +
        '<input type="number" name="drop_items[' + dropItemCount + '][drop_rate]" class="layui-input" value="10" min="0" max="100" step="0.1">' +
        '</div>' +
        '</div>' +
        '<div class="layui-inline">' +
        '<button type="button" class="layui-btn layui-btn-danger" onclick="removeDropItem(this)">删除</button>' +
        '</div>' +
        '</div>';

    $('#drop_items').append(html);
    dropItemCount++;

    // 重新渲染表单
    layui.form.render();
}

// 删除掉落物品
function removeDropItem(btn) {
    $(btn).closest('.drop-item').remove();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\szxy\laravel\resources\views/admin/monsters/create.blade.php ENDPATH**/ ?>