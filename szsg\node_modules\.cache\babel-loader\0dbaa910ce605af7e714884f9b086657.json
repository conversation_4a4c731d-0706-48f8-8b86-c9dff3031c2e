{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\layouts\\GameLayout.vue", "mtime": 1749890706309}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\babel.config.js", "mtime": 1749539051582}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["logger", "name", "props", "pageType", "type", "String", "default", "customTitle", "showDecorations", "Boolean", "data", "isLoading", "loadingText", "computed", "globalLoading", "$store", "state", "topBorderImage", "getImageByPageType", "currentPath", "$route", "path", "includes", "pageTitle", "showLocationName", "currentLocationName", "_this$$store$state$ma", "currentLocation", "map", "error", "watch", "newVal", "created", "debug", "setupPages", "_this$$store$state$au", "authToken", "localStorage", "getItem", "storeAuth", "auth", "isAuthenticated", "isSetupPage", "some", "page", "<PERSON><PERSON><PERSON><PERSON>", "showToast", "$nextTick", "$router", "replace", "methods", "imageMap", "getParticleStyle", "delay", "Math", "random", "duration", "size", "left", "animationDelay", "animationDuration", "width", "height", "message", "toast", "document", "createElement", "innerHTML", "style", "cssText", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "setTimeout", "animation", "contains", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/layouts/GameLayout.vue"], "sourcesContent": ["<template>\r\n  <div class=\"game-layout-container\">\r\n    <!-- 动态背景效果 -->\r\n    <div class=\"background-effects\">\r\n      <div class=\"floating-particles\">\r\n        <div v-for=\"i in 20\" :key=\"i\" class=\"particle\" :style=\"getParticleStyle(i)\"></div>\r\n      </div>\r\n      <div class=\"background-gradient\"></div>\r\n    </div>\r\n\r\n    <!-- 顶部装饰边框 -->\r\n    <div class=\"top-border-frame\" :style=\"{ backgroundImage: `url(${topBorderImage})` }\">\r\n      <div class=\"top-border-overlay\">\r\n        <div class=\"border-content\">\r\n          <!-- 页面标题和地图名 -->\r\n          <div class=\"page-title-container\">\r\n            <div class=\"page-title\">{{ pageTitle }}</div>\r\n            <div v-if=\"showLocationName && currentLocationName\" class=\"location-name\">\r\n              <span class=\"location-text\">{{ currentLocationName }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 左右固定边框 -->\r\n    <div class=\"left-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n    <div class=\"right-border-frame\">\r\n      <div class=\"border-pattern\"></div>\r\n      <div class=\"border-glow\"></div>\r\n    </div>\r\n\r\n    <!-- 页面实际内容插入点 -->\r\n    <div class=\"main-content-area\">\r\n      <div class=\"content-wrapper\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部装饰边框 -->\r\n    <div class=\"bottom-border-frame\">\r\n      <div class=\"footer-decoration\">\r\n        <div class=\"footer-pattern\"></div>\r\n        <div class=\"footer-text\">\r\n          <span class=\"version-info\">Version 1.0.0</span>\r\n          <span class=\"copyright\">© 2025 神之西游</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载遮罩 -->\r\n    <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n      <div class=\"loading-content\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <div class=\"loading-text\">{{ loadingText || '加载中...' }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logger from '@/utils/logger';\r\n\r\nexport default {\r\n  name: 'GameLayout',\r\n  props: {\r\n    // 页面类型：'setup', 'game', 'main', 'battle' 等\r\n    pageType: {\r\n      type: String,\r\n      default: 'main'\r\n    },\r\n    // 自定义页面标题\r\n    customTitle: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 是否显示装饰元素\r\n    showDecorations: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isLoading: false,\r\n      loadingText: ''\r\n    };\r\n  },\r\n  computed: {\r\n    // 从store获取全局加载状态\r\n    globalLoading() {\r\n      return this.$store.state.isLoading;\r\n    },\r\n\r\n    // 根据页面类型或路由获取顶部边框图片\r\n    topBorderImage() {\r\n      // 如果有自定义标题，优先使用pageType\r\n      if (this.pageType && this.pageType !== 'main') {\r\n        return this.getImageByPageType(this.pageType);\r\n      }\r\n\r\n      // 根据当前路由自动判断\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '/static/game/UI/bj/xuanzefenqu.png';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '/static/game/UI/bj/xuanzejuese.png';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '/static/game/UI/bj/chuangjian.png';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '/static/game/UI/bj/top.jpg';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '/static/game/UI/bj/zhandou.png';\r\n      }\r\n\r\n      // 默认主页面\r\n      return '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 页面标题\r\n    pageTitle() {\r\n      if (this.customTitle) {\r\n        return this.customTitle;\r\n      }\r\n\r\n      const currentPath = this.$route.path;\r\n      if (currentPath.includes('/setup/region-select')) {\r\n        return '选择大区';\r\n      } else if (currentPath.includes('/setup/character-select')) {\r\n        return '选择角色';\r\n      } else if (currentPath.includes('/setup/create-character')) {\r\n        return '创建角色';\r\n      } else if (currentPath.includes('/game')) {\r\n        return '';\r\n      } else if (currentPath.includes('/battle')) {\r\n        return '战斗场景';\r\n      }\r\n\r\n      return '神之西游';\r\n    },\r\n\r\n    // 是否显示地图名\r\n    showLocationName() {\r\n      const currentPath = this.$route.path;\r\n      return currentPath.includes('/game');\r\n    },\r\n\r\n    // 当前地图名\r\n    currentLocationName() {\r\n      try {\r\n        // 从store获取当前位置信息\r\n        const currentLocation = this.$store.state.map?.currentLocation;\r\n        if (currentLocation && currentLocation.name) {\r\n          return currentLocation.name;\r\n        }\r\n\r\n        // 如果store中没有数据，返回默认值\r\n        return '未知位置';\r\n      } catch (error) {\r\n        logger.error('[GameLayout] 获取当前位置失败:', error);\r\n        return '未知位置';\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    globalLoading(newVal) {\r\n      this.isLoading = newVal;\r\n    }\r\n  },\r\n  created() {\r\n    logger.debug('[GameLayout] 初始化');\r\n\r\n    // 获取当前页面路径\r\n    const currentPath = this.$route.path;\r\n    logger.debug('[GameLayout] 当前路径:', currentPath);\r\n\r\n    // 设置页面列表（这些页面只需要验证登录，不需要验证角色选择）\r\n    const setupPages = [\r\n      '/setup/region-select',\r\n      '/setup/character-select',\r\n      '/setup/create-character'\r\n    ];\r\n\r\n    // 检查认证状态（路由守卫已经处理了基本的登录检查，这里只做额外检查）\r\n    try {\r\n      const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');\r\n      const storeAuth = this.$store.state.auth?.isAuthenticated;\r\n      const isAuthenticated = storeAuth || !!authToken;\r\n\r\n      // 如果没有认证信息，让路由守卫处理重定向\r\n      if (!isAuthenticated) {\r\n        logger.debug('[GameLayout] 未登录，由路由守卫处理');\r\n        return;\r\n      }\r\n\r\n      logger.debug('[GameLayout] 已登录，继续检查角色状态');\r\n\r\n      // 只有非设置页面才需要检查角色选择状态\r\n      const isSetupPage = setupPages.some(page => currentPath.includes(page));\r\n      if (!isSetupPage) {\r\n        const selectedCharacter = localStorage.getItem('selectedCharacter');\r\n        if (!selectedCharacter) {\r\n          logger.debug('[GameLayout] 未选择角色，重定向到区域选择页');\r\n          this.showToast('请先选择角色');\r\n          // 使用nextTick避免在created中立即重定向\r\n          this.$nextTick(() => {\r\n            this.$router.replace('/setup/region-select');\r\n          });\r\n          return;\r\n        }\r\n      }\r\n\r\n      logger.debug('[GameLayout] 认证检查通过，路径:', currentPath);\r\n    } catch (error) {\r\n      logger.error('[GameLayout] 认证检查失败:', error);\r\n    }\r\n  },\r\n  methods: {\r\n    // 根据页面类型获取图片\r\n    getImageByPageType(pageType) {\r\n      const imageMap = {\r\n        'setup': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'region-select': '/static/game/UI/bj/xuanzefenqu.png',\r\n        'character-select': '/static/game/UI/bj/xuanzejuese.png',\r\n        'create-character': '/static/game/UI/bj/chuangjian.png',\r\n        'game': '/static/game/UI/bj/youxi.png',\r\n        'battle': '/static/game/UI/bj/zhandou.png',\r\n        'main': '/static/game/UI/bj/szxy_1.png'\r\n      };\r\n      return imageMap[pageType] || '/static/game/UI/bj/szxy_1.png';\r\n    },\r\n\r\n    // 获取粒子样式\r\n    getParticleStyle() {\r\n      const delay = Math.random() * 20;\r\n      const duration = 15 + Math.random() * 10;\r\n      const size = 2 + Math.random() * 4;\r\n      const left = Math.random() * 100;\r\n\r\n      return {\r\n        left: `${left}%`,\r\n        animationDelay: `${delay}s`,\r\n        animationDuration: `${duration}s`,\r\n        width: `${size}px`,\r\n        height: `${size}px`\r\n      };\r\n    },\r\n\r\n    // 显示提示框\r\n    showToast(message, duration = 2000) {\r\n      logger.debug('[GameLayout] 显示提示:', message);\r\n      const toast = document.createElement('div');\r\n      toast.innerHTML = message;\r\n      toast.style.cssText = `\r\n        position: fixed;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        background: linear-gradient(135deg, rgba(212, 175, 55, 0.9), rgba(184, 148, 31, 0.9));\r\n        color: #000;\r\n        padding: 15px 25px;\r\n        border-radius: 8px;\r\n        border: 2px solid #d4af37;\r\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.3);\r\n        z-index: 10000;\r\n        font-weight: bold;\r\n        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);\r\n        animation: toastSlideIn 0.3s ease-out;\r\n      `;\r\n\r\n      // 添加动画样式\r\n      const style = document.createElement('style');\r\n      style.textContent = `\r\n        @keyframes toastSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translate(-50%, -50%) scale(0.8);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translate(-50%, -50%) scale(1);\r\n          }\r\n        }\r\n      `;\r\n      document.head.appendChild(style);\r\n\r\n      document.body.appendChild(toast);\r\n      setTimeout(() => {\r\n        toast.style.animation = 'toastSlideIn 0.3s ease-out reverse';\r\n        setTimeout(() => {\r\n          if (document.body.contains(toast)) {\r\n            document.body.removeChild(toast);\r\n          }\r\n          if (document.head.contains(style)) {\r\n            document.head.removeChild(style);\r\n          }\r\n        }, 300);\r\n      }, duration);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 应用全局背景和基本布局 */\r\n.game-layout-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  box-sizing: border-box;\r\n  background:\r\n    linear-gradient(135deg, rgba(10, 10, 10, 0.3) 0%, rgba(26, 26, 46, 0.2) 50%, rgba(22, 33, 62, 0.3) 100%),\r\n    url('/static/game/UI/bj/zise.png');\r\n  background-size: cover, cover;\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-attachment: fixed;\r\n  overflow-x: hidden;\r\n\r\n  /* 为边框留出空间 */\r\n  padding: 70px 40px 50px 30px;\r\n}\r\n\r\n/* 动态背景效果 */\r\n.background-effects {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: -1;\r\n}\r\n\r\n.background-gradient {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background:\r\n    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),\r\n    linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.02) 50%, transparent 70%);\r\n  animation: gradientShift 20s ease-in-out infinite;\r\n}\r\n\r\n@keyframes gradientShift {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n/* 浮动粒子效果 */\r\n.floating-particles {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  background: #d4af37;\r\n  border-radius: 50%;\r\n  opacity: 0.6;\r\n  animation: float-up linear infinite;\r\n}\r\n\r\n@keyframes float-up {\r\n  0% {\r\n    transform: translateY(100vh) rotate(0deg);\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 0.6;\r\n  }\r\n  90% {\r\n    opacity: 0.6;\r\n  }\r\n  100% {\r\n    transform: translateY(-100px) rotate(360deg);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 顶部边框装饰 */\r\n.top-border-frame {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 60px;\r\n  z-index: 1500;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.top-border-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.border-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 页面标题容器 */\r\n.page-title-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 26px;\r\n  font-weight: bold;\r\n  color: #d4af37;\r\n  text-shadow:\r\n    2px 2px 4px rgba(0, 0, 0, 0.8),\r\n    0 0 15px rgba(212, 175, 55, 0.6);\r\n  letter-spacing: 2px;\r\n}\r\n\r\n/* 地图位置显示 */\r\n.location-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  padding: 6px 16px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(212, 175, 55, 0.4);\r\n  backdrop-filter: blur(8px);\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.location-label {\r\n  color: #ccc;\r\n  font-size: 16px;\r\n}\r\n\r\n.location-text {\r\n  color: #ffd700;\r\n  font-weight: bold;\r\n  font-size: 22px;\r\n  text-shadow:\r\n    1px 1px 2px rgba(0, 0, 0, 0.8),\r\n    0 0 8px rgba(255, 215, 0, 0.4);\r\n}\r\n\r\n\r\n\r\n@keyframes sparkle {\r\n  0%, 100% { opacity: 0.7; transform: scale(1); }\r\n  50% { opacity: 1; transform: scale(1.1); }\r\n}\r\n\r\n/* 左右边框样式 */\r\n.left-border-frame,\r\n.right-border-frame {\r\n  position: fixed;\r\n  top: 60px;\r\n  bottom: 40px;\r\n  z-index: 1400;\r\n  pointer-events: none;\r\n}\r\n\r\n.left-border-frame {\r\n  left: 0;\r\n  width: 30px;\r\n  background: url('/static/game/UI/bk/gnl_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.right-border-frame {\r\n  right: 0;\r\n  width: 40px;\r\n  background: url('/static/game/UI/bk/gnl1_bk.png');\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-top: -1px;\r\n}\r\n\r\n.border-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: repeating-linear-gradient(\r\n    0deg,\r\n    transparent,\r\n    transparent 20px,\r\n    rgba(212, 175, 55, 0.1) 21px,\r\n    rgba(212, 175, 55, 0.1) 22px\r\n  );\r\n}\r\n\r\n.border-glow {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 2px;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, transparent, #d4af37, transparent);\r\n  opacity: 0.6;\r\n  animation: borderPulse 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderPulse {\r\n  0%, 100% { opacity: 0.3; }\r\n  50% { opacity: 0.8; }\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 50px 15px 35px 15px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .game-title {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.content-wrapper {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(212, 175, 55, 0.2);\r\n  box-shadow:\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 8px 32px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 底部边框装饰 */\r\n.bottom-border-frame {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.8));\r\n  border-top: 1px solid #d4af37;\r\n  z-index: 1500;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.footer-decoration {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.footer-pattern {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #d4af37, transparent);\r\n}\r\n\r\n.footer-text {\r\n  display: flex;\r\n  gap: 30px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.version-info,\r\n.copyright {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  color: #d4af37;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(212, 175, 55, 0.3);\r\n  border-top: 4px solid #d4af37;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .game-layout-container {\r\n    padding: 60px 25px 45px 20px;\r\n  }\r\n\r\n  .top-border-frame {\r\n    height: 50px;\r\n  }\r\n\r\n  .left-border-frame,\r\n  .right-border-frame {\r\n    top: 50px;\r\n  }\r\n\r\n  .left-border-frame {\r\n    width: 20px;\r\n  }\r\n\r\n  .right-border-frame {\r\n    width: 25px;\r\n  }\r\n\r\n  /* 移动端标题样式调整 */\r\n  .page-title-container {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .location-name {\r\n    font-size: 16px;\r\n    padding: 4px 12px;\r\n  }\r\n\r\n  .location-label {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .location-text {\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .page-title-container {\r\n    gap: 6px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .location-name {\r\n    font-size: 14px;\r\n    padding: 3px 8px;\r\n  }\r\n\r\n  .location-label {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .location-text {\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n/* 深度选择器 */\r\n:deep(.page) {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n</style> "], "mappings": ";;AAgEA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAE,eAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;EACA;EACAI,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,cAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAL,SAAA;IACA;IAEA;IACAM,eAAA;MACA;MACA,SAAAd,QAAA,SAAAA,QAAA;QACA,YAAAe,kBAAA,MAAAf,QAAA;MACA;;MAEA;MACA,MAAAgB,WAAA,QAAAC,MAAA,CAAAC,IAAA;MACA,IAAAF,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC,UAAA;MACA,SAAAhB,WAAA;QACA,YAAAA,WAAA;MACA;MAEA,MAAAY,WAAA,QAAAC,MAAA,CAAAC,IAAA;MACA,IAAAF,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA,WAAAH,WAAA,CAAAG,QAAA;QACA;MACA;MAEA;IACA;IAEA;IACAE,iBAAA;MACA,MAAAL,WAAA,QAAAC,MAAA,CAAAC,IAAA;MACA,OAAAF,WAAA,CAAAG,QAAA;IACA;IAEA;IACAG,oBAAA;MACA;QAAA,IAAAC,qBAAA;QACA;QACA,MAAAC,eAAA,IAAAD,qBAAA,QAAAX,MAAA,CAAAC,KAAA,CAAAY,GAAA,cAAAF,qBAAA,uBAAAA,qBAAA,CAAAC,eAAA;QACA,IAAAA,eAAA,IAAAA,eAAA,CAAA1B,IAAA;UACA,OAAA0B,eAAA,CAAA1B,IAAA;QACA;;QAEA;QACA;MACA,SAAA4B,KAAA;QACA7B,MAAA,CAAA6B,KAAA,2BAAAA,KAAA;QACA;MACA;IACA;EACA;EACAC,KAAA;IACAhB,cAAAiB,MAAA;MACA,KAAApB,SAAA,GAAAoB,MAAA;IACA;EACA;EACAC,QAAA;IACAhC,MAAA,CAAAiC,KAAA;;IAEA;IACA,MAAAd,WAAA,QAAAC,MAAA,CAAAC,IAAA;IACArB,MAAA,CAAAiC,KAAA,uBAAAd,WAAA;;IAEA;IACA,MAAAe,UAAA,IACA,wBACA,2BACA,0BACA;;IAEA;IACA;MAAA,IAAAC,qBAAA;MACA,MAAAC,SAAA,GAAAC,YAAA,CAAAC,OAAA,iBAAAD,YAAA,CAAAC,OAAA;MACA,MAAAC,SAAA,IAAAJ,qBAAA,QAAApB,MAAA,CAAAC,KAAA,CAAAwB,IAAA,cAAAL,qBAAA,uBAAAA,qBAAA,CAAAM,eAAA;MACA,MAAAA,eAAA,GAAAF,SAAA,MAAAH,SAAA;;MAEA;MACA,KAAAK,eAAA;QACAzC,MAAA,CAAAiC,KAAA;QACA;MACA;MAEAjC,MAAA,CAAAiC,KAAA;;MAEA;MACA,MAAAS,WAAA,GAAAR,UAAA,CAAAS,IAAA,CAAAC,IAAA,IAAAzB,WAAA,CAAAG,QAAA,CAAAsB,IAAA;MACA,KAAAF,WAAA;QACA,MAAAG,iBAAA,GAAAR,YAAA,CAAAC,OAAA;QACA,KAAAO,iBAAA;UACA7C,MAAA,CAAAiC,KAAA;UACA,KAAAa,SAAA;UACA;UACA,KAAAC,SAAA;YACA,KAAAC,OAAA,CAAAC,OAAA;UACA;UACA;QACA;MACA;MAEAjD,MAAA,CAAAiC,KAAA,4BAAAd,WAAA;IACA,SAAAU,KAAA;MACA7B,MAAA,CAAA6B,KAAA,yBAAAA,KAAA;IACA;EACA;EACAqB,OAAA;IACA;IACAhC,mBAAAf,QAAA;MACA,MAAAgD,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAhD,QAAA;IACA;IAEA;IACAiD,iBAAA;MACA,MAAAC,KAAA,GAAAC,IAAA,CAAAC,MAAA;MACA,MAAAC,QAAA,QAAAF,IAAA,CAAAC,MAAA;MACA,MAAAE,IAAA,OAAAH,IAAA,CAAAC,MAAA;MACA,MAAAG,IAAA,GAAAJ,IAAA,CAAAC,MAAA;MAEA;QACAG,IAAA,KAAAA,IAAA;QACAC,cAAA,KAAAN,KAAA;QACAO,iBAAA,KAAAJ,QAAA;QACAK,KAAA,KAAAJ,IAAA;QACAK,MAAA,KAAAL,IAAA;MACA;IACA;IAEA;IACAX,UAAAiB,OAAA,EAAAP,QAAA;MACAxD,MAAA,CAAAiC,KAAA,uBAAA8B,OAAA;MACA,MAAAC,KAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,KAAA,CAAAG,SAAA,GAAAJ,OAAA;MACAC,KAAA,CAAAI,KAAA,CAAAC,OAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA;MACA,MAAAD,KAAA,GAAAH,QAAA,CAAAC,aAAA;MACAE,KAAA,CAAAE,WAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACAL,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAJ,KAAA;MAEAH,QAAA,CAAAQ,IAAA,CAAAD,WAAA,CAAAR,KAAA;MACAU,UAAA;QACAV,KAAA,CAAAI,KAAA,CAAAO,SAAA;QACAD,UAAA;UACA,IAAAT,QAAA,CAAAQ,IAAA,CAAAG,QAAA,CAAAZ,KAAA;YACAC,QAAA,CAAAQ,IAAA,CAAAI,WAAA,CAAAb,KAAA;UACA;UACA,IAAAC,QAAA,CAAAM,IAAA,CAAAK,QAAA,CAAAR,KAAA;YACAH,QAAA,CAAAM,IAAA,CAAAM,WAAA,CAAAT,KAAA;UACA;QACA;MACA,GAAAZ,QAAA;IACA;EACA;AACA", "ignoreList": []}]}