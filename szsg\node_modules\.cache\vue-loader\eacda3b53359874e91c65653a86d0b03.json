{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Battle.vue?vue&type=template&id=a4709880&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\views\\game\\subpages\\Battle.vue", "mtime": 1749718635742}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749535537842}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749535541140}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}