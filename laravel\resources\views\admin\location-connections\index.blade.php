@extends('admin.layouts.app')

@section('title', '位置连接管理')

@section('content')
@section('breadcrumb')
<a href="{{ route('admin.location-connections.index') }}">位置连接管理</a>
@endsection

@section('page-title', '位置连接管理')

<!-- 统计卡片 -->
<div class="layui-row layui-col-space15">
    <div class="layui-col-md4">
        <div class="layui-card">
            <div class="layui-card-header">总连接数</div>
            <div class="layui-card-body">
                <h2>{{ $stats['total_connections'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="layui-card">
            <div class="layui-card-header">活跃连接</div>
            <div class="layui-card-body">
                <h2>{{ $stats['active_connections'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="layui-card">
            <div class="layui-card-header">双向连接</div>
            <div class="layui-card-body">
                <h2>{{ $stats['bidirectional_connections'] ?? 0 }}</h2>
            </div>
        </div>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-header">
        位置连接列表
        <div style="float: right;">
            <a href="{{ route('admin.location-connections.create') }}" class="layui-btn layui-btn-sm layui-btn-normal">添加连接</a>
            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="batchCreateBtn">批量创建</button>
        </div>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>起始位置</th>
                    <th>目标位置</th>
                    <th>距离</th>
                    <th>时间消耗</th>
                    <th>银两消耗</th>
                    <th>等级要求</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($connections as $connection)
                <tr>
                    <td>{{ $connection->id }}</td>
                    <td>{{ $connection->fromLocation->name ?? 'N/A' }}</td>
                    <td>{{ $connection->toLocation->name ?? 'N/A' }}</td>
                    <td>{{ $connection->distance }}</td>
                    <td>{{ $connection->time_cost }} 分钟</td>
                    <td>{{ $connection->silver_cost }} 两</td>
                    <td>{{ $connection->level_requirement }}</td>
                    <td>
                        @if($connection->is_active)
                        <span class="layui-badge layui-bg-green">活跃</span>
                        @else
                        <span class="layui-badge layui-bg-gray">关闭</span>
                        @endif
                    </td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.location-connections.edit', $connection->id) }}" class="layui-btn layui-btn-xs">
                                <i class="layui-icon layui-icon-edit"></i> 编辑
                            </a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="confirmDelete({{ $connection->id }}, '{{ $connection->fromLocation->name ?? '' }} -> {{ $connection->toLocation->name ?? '' }}')">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="9" style="text-align: center;">暂无位置连接数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        <!-- 分页 -->
        @if($connections->hasPages())
        <div class="layui-box layui-laypage layui-laypage-default">
            {{ $connections->links() }}
        </div>
        @endif
    </div>
</div>

<!-- 隐藏的删除表单 -->
<form id="deleteForm" action="" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>

<!-- 批量创建弹窗 -->
<div id="batchCreateModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="batchCreateForm">
        @csrf
        <div class="layui-form-item">
            <label class="layui-form-label">选择位置</label>
            <div class="layui-input-block">
                <div id="locationCheckboxes" style="max-height: 200px; overflow-y: auto;">
                    <!-- 位置复选框将通过AJAX加载 -->
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">距离</label>
            <div class="layui-input-block">
                <input type="number" name="distance" value="1" required lay-verify="required|number" placeholder="请输入距离" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">时间消耗</label>
            <div class="layui-input-block">
                <input type="number" name="time_cost" value="5" required lay-verify="required|number" placeholder="请输入时间消耗（分钟）" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">银两消耗</label>
            <div class="layui-input-block">
                <input type="number" name="silver_cost" value="0" required lay-verify="required|number" placeholder="请输入银两消耗" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">等级要求</label>
            <div class="layui-input-block">
                <input type="number" name="level_requirement" value="1" required lay-verify="required|number" placeholder="请输入等级要求" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="batchCreate">创建连接</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
@endsection

@section('js')
<script>
layui.use(['layer', 'form', 'jquery'], function() {
    var layer = layui.layer;
    var form = layui.form;
    var $ = layui.jquery;

    // 批量创建按钮
    $('#batchCreateBtn').on('click', function() {
        // 加载位置列表
        $.get('/admin/api/locations', function(res) {
            if (res.success) {
                var html = '';
                $.each(res.data, function(i, location) {
                    html += '<div style="margin-bottom: 10px;">';
                    html += '<input type="checkbox" name="location_ids[]" value="' + location.id + '" title="' + location.name + '" lay-skin="primary">';
                    html += '</div>';
                });
                $('#locationCheckboxes').html(html);
                form.render('checkbox');

                layer.open({
                    type: 1,
                    title: '批量创建位置连接',
                    area: ['600px', '500px'],
                    content: $('#batchCreateModal')
                });
            } else {
                layer.msg('加载位置列表失败');
            }
        });
    });

    // 批量创建表单提交
    form.on('submit(batchCreate)', function(data) {
        var selectedLocations = $('input[name="location_ids[]"]:checked');
        if (selectedLocations.length < 2) {
            layer.msg('请至少选择2个位置');
            return false;
        }

        var formData = data.field;
        formData.location_ids = [];
        selectedLocations.each(function() {
            formData.location_ids.push($(this).val());
        });

        $.ajax({
            url: '{{ route("admin.location-connections.batch-create") }}',
            type: 'POST',
            data: formData,
            success: function(res) {
                if (res.success) {
                    layer.msg(res.message);
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.message);
                }
            },
            error: function() {
                layer.msg('创建失败，请稍后再试');
            }
        });

        return false;
    });
});

function confirmDelete(id, name) {
    layui.use('layer', function(){
        var layer = layui.layer;

        layer.confirm('确定要删除连接 "' + name + '" 吗？此操作不可逆。', {
            btn: ['确认删除','取消']
        }, function(){
            document.getElementById('deleteForm').action = "{{ route('admin.location-connections.index') }}/" + id;
            document.getElementById('deleteForm').submit();
        });
    });
}
</script>
@endsection
