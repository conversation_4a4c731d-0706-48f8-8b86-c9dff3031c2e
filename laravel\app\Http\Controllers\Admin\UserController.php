<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * 显示用户列表
     */
    public function index()
    {
        try {
            $users = DB::table('users')
                ->orderBy('created_at', 'desc')
                ->paginate(15);

            return view('admin.users.index', compact('users'));
        } catch (\Exception $e) {
            // 创建一个空的分页对象而不是简单的集合
            $users = new \Illuminate\Pagination\LengthAwarePaginator(
                [], // 空数据
                0,  // 总记录数
                15, // 每页显示数
                1   // 当前页码
            );

            return view('admin.users.index', compact('users'));
        }
    }

    /**
     * 显示创建用户表单
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * 保存新用户
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:255|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::table('users')->insert([
                'username' => $request->username,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'status' => $request->has('status') ? 1 : 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return redirect()->route('admin.users.index')
                ->with('success', '用户创建成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '用户创建失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 显示用户详情
     */
    public function show($id)
    {
        try {
            $user = DB::table('users')->where('id', $id)->first();

            if (!$user) {
                return redirect()->route('admin.users.index')
                    ->with('error', '用户不存在');
            }

            // 获取用户的角色列表
            $characters = DB::table('characters')
                ->where('user_id', $id)
                ->orderBy('level', 'desc')
                ->get();

            return view('admin.users.show', compact('user', 'characters'));
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', '无法获取用户信息: ' . $e->getMessage());
        }
    }

    /**
     * 显示编辑用户表单
     */
    public function edit($id)
    {
        try {
            $user = DB::table('users')->where('id', $id)->first();

            if (!$user) {
                return redirect()->route('admin.users.index')
                    ->with('error', '用户不存在');
            }

            return view('admin.users.edit', compact('user'));
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', '无法获取用户信息: ' . $e->getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:255|unique:users,username,' . $id,
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'password' => 'nullable|string|min:6|confirmed',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = [
                'username' => $request->username,
                'email' => $request->email,
                'status' => $request->has('status') ? 1 : 0,
                'updated_at' => now(),
            ];

            // 如果提供了密码，则更新密码
            if ($request->filled('password')) {
                $data['password'] = Hash::make($request->password);
            }

            DB::table('users')
                ->where('id', $id)
                ->update($data);

            return redirect()->route('admin.users.index')
                ->with('success', '用户更新成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', '用户更新失败: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * 删除用户
     */
    public function destroy($id)
    {
        try {
            // 检查是否有关联角色
            $characterCount = DB::table('characters')
                ->where('user_id', $id)
                ->count();

            if ($characterCount > 0) {
                return redirect()->route('admin.users.index')
                    ->with('error', '无法删除用户，请先删除该用户的所有角色');
            }

            DB::table('users')->where('id', $id)->delete();

            return redirect()->route('admin.users.index')
                ->with('success', '用户已删除');
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', '用户删除失败: ' . $e->getMessage());
        }
    }
}
