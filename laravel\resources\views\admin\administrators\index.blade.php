@extends('admin.layouts.app')

@section('title', '管理员列表')

@section('breadcrumb')
<a><cite>管理员列表</cite></a>
@endsection

@section('page-title', '管理员列表')

@section('content')
<div class="layui-card">
  <div class="layui-card-header">
    <div class="layui-btn-group">
      <a href="{{ route('admin.administrators.create') }}" class="layui-btn layui-btn-sm layui-btn-normal">
        <i class="layui-icon layui-icon-add-1"></i> 添加管理员
      </a>
    </div>
  </div>
  <div class="layui-card-body">
    <table class="layui-table">
      <colgroup>
        <col width="80">
        <col width="150">
        <col width="150">
        <col>
        <col width="180">
        <col width="180">
      </colgroup>
      <thead>
        <tr>
          <th>ID</th>
          <th>姓名</th>
          <th>用户名</th>
          <th>角色</th>
          <th>创建时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        @forelse($admins as $admin)
        <tr>
          <td>{{ $admin->id }}</td>
          <td>{{ $admin->name }}</td>
          <td>{{ $admin->username }}</td>
          <td>
            @if($admin->role == 'super_admin')
              <span class="layui-badge layui-bg-blue">超级管理员</span>
            @else
              <span class="layui-badge layui-bg-green">普通管理员</span>
            @endif
          </td>
          <td>{{ $admin->created_at }}</td>
          <td>
            <a href="{{ route('admin.administrators.edit', $admin->id) }}" class="layui-btn layui-btn-xs">
              <i class="layui-icon layui-icon-edit"></i> 编辑
            </a>

            @if(auth('admin')->id() != $admin->id)
            <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteAdmin({{ $admin->id }})">
              <i class="layui-icon layui-icon-delete"></i> 删除
            </button>
            <form id="delete-form-{{ $admin->id }}" action="{{ route('admin.administrators.destroy', $admin->id) }}" method="POST" style="display: none;">
              @csrf
              @method('DELETE')
            </form>
            @endif
          </td>
        </tr>
        @empty
        <tr>
          <td colspan="6" style="text-align: center;">暂无数据</td>
        </tr>
        @endforelse
      </tbody>
    </table>
  </div>
</div>
@endsection

@section('js')
<script>
  function deleteAdmin(id) {
    layui.use('layer', function(){
      var layer = layui.layer;

      layer.confirm('确定要删除这个管理员吗？', {
        btn: ['确定','取消']
      }, function(){
        document.getElementById('delete-form-' + id).submit();
      });
    });
  }
</script>
@endsection
