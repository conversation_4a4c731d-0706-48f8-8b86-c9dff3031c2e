/**
 * 大区状态Vuex模块
 */
import logger from '@/utils/logger';

// 初始状态
const state = {
  regions: [],
  currentRegion: null,
  loading: false,
  error: null
};

// getter
const getters = {
  // 获取所有大区
  allRegions: state => state.regions,
  
  // 获取当前大区
  currentRegion: state => state.currentRegion,
  
  // 获取当前大区ID
  currentRegionId: state => state.currentRegion ? state.currentRegion.id : null,
  
  // 获取当前大区名称
  currentRegionName: state => state.currentRegion ? state.currentRegion.name : '',
  
  // 加载状态
  isLoading: state => state.loading
};

// 变更事件类型
const mutations = {
  // 设置大区列表
  SET_REGIONS(state, regions) {
    state.regions = regions;
  },
  
  // 设置当前大区
  SET_CURRENT_REGION(state, region) {
    state.currentRegion = region;
  },
  
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error;
  }
};

// 动作
const actions = {
  // 初始化大区信息
  initRegion({ commit }) {
    try {
      const savedRegion = localStorage.getItem('selectedRegion');
      if (savedRegion) {
        commit('SET_CURRENT_REGION', JSON.parse(savedRegion));
      }
    } catch (error) {
      logger.error('[Region Store] 初始化大区信息失败:', error);
    }
  },
  
  // 选择大区
  selectRegion({ commit }, region) {
    if (!region || !region.id) {
      commit('SET_ERROR', '无效的大区数据');
      return false;
    }
    
    try {
      // 保存到Vuex状态
      commit('SET_CURRENT_REGION', region);
      
      // 同时保存到本地存储
      localStorage.setItem('selectedRegion', JSON.stringify(region));
      logger.debug('[Region Store] 已选择大区:', region.name);
      
      return true;
    } catch (error) {
      logger.error('[Region Store] 选择大区失败:', error);
      commit('SET_ERROR', '选择大区失败');
      return false;
    }
  },
  
  // 清除当前大区
  clearRegion({ commit }) {
    commit('SET_CURRENT_REGION', null);
    localStorage.removeItem('selectedRegion');
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}; 