<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// 获取locations表的结构
$columns = Schema::getColumnListing('locations');
echo "Locations表的列：\n";
print_r($columns);

// 检查是否有数据
$locations = DB::table('locations')->get();
echo "\nLocations表的数据：\n";
print_r($locations);

// 检查regions表的结构
$regionColumns = Schema::getColumnListing('regions');
echo "\nRegions表的列：\n";
print_r($regionColumns);

// 检查是否有数据
$regions = DB::table('regions')->get();
echo "\nRegions表的数据：\n";
print_r($regions);
