<?php $__env->startSection('title', '管理控制台'); ?>

<?php $__env->startSection('content'); ?>
<div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">系统概况</div>
            <div class="layui-card-body">
                <?php if(session('error')): ?>
                <div class="layui-alert layui-alert-danger">
                    <?php echo e(session('error')); ?>

                </div>
                <?php endif; ?>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-header">用户统计</div>
                            <div class="layui-card-body">
                                <p>总用户数: <?php echo e($userCount ?? 0); ?></p>
                                <p>活跃用户: <?php echo e($activeUserCount ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-header">角色统计</div>
                            <div class="layui-card-body">
                                <p>总角色数: <?php echo e($characterCount ?? 0); ?></p>
                                <p>活跃角色: <?php echo e($activeCharacterCount ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-header">游戏内容</div>
                            <div class="layui-card-body">
                                <p>物品数量: <?php echo e($itemCount ?? 0); ?></p>
                                <p>怪物数量: <?php echo e($monsterCount ?? 0); ?></p>
                                <p>地图数量: <?php echo e($mapCount ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-header">任务统计</div>
                            <div class="layui-card-body">
                                <p>总任务数: <?php echo e($questCount ?? 0); ?></p>
                                <p>激活任务: <?php echo e($activeQuestCount ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">最近注册用户</div>
                            <div class="layui-card-body">
                                <table class="layui-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>用户名</th>
                                            <th>注册时间</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $recentUsers ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($user->id); ?></td>
                                            <td><?php echo e($user->username); ?></td>
                                            <td><?php echo e($user->created_at); ?></td>
                                            <td>
                                                <?php if(isset($user->status) && $user->status == 1): ?>
                                                <span class="layui-badge layui-bg-green">正常</span>
                                                <?php else: ?>
                                                <span class="layui-badge">禁用</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="4" class="layui-center">暂无数据</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">最近战斗记录</div>
                            <div class="layui-card-body">
                                <table class="layui-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>角色</th>
                                            <th>怪物</th>
                                            <th>结果</th>
                                            <th>时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $recentBattles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $battle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($battle->id); ?></td>
                                            <td><?php echo e($battle->character_name ?? '未知'); ?></td>
                                            <td><?php echo e($battle->monster_name ?? '未知'); ?></td>
                                            <td>
                                                <?php
                                                $resultMap = [
                                                    'win' => '<span class="layui-badge layui-bg-green">胜利</span>',
                                                    'lose' => '<span class="layui-badge layui-bg-red">失败</span>',
                                                    'escape' => '<span class="layui-badge layui-bg-orange">逃跑</span>',
                                                ];
                                                ?>
                                                <?php echo $resultMap[$battle->result] ?? $battle->result; ?>

                                            </td>
                                            <td><?php echo e($battle->created_at); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="5" class="layui-center">暂无数据</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
layui.use(['jquery'], function(){
    var $ = layui.jquery;

    // 战斗统计图表
    var battleChart = echarts.init(document.getElementById('battleStats'));
    var battleOption = {
        title: {
            text: '战斗结果统计'
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 10,
            data: ['胜利', '失败', '逃跑']
        },
        series: [
            {
                name: '战斗结果',
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: <?php echo e($winBattleCount ?? 0); ?>, name: '胜利'},
                    {value: <?php echo e($loseBattleCount ?? 0); ?>, name: '失败'},
                    {value: <?php echo e(($battleCount ?? 0) - ($winBattleCount ?? 0) - ($loseBattleCount ?? 0)); ?>, name: '逃跑'}
                ]
            }
        ]
    };
    battleChart.setOption(battleOption);

    // 注册用户趋势图表
    var registrationChart = echarts.init(document.getElementById('registrationStats'));

    // 获取统计数据
    $.ajax({
        url: '<?php echo e(route("admin.stats")); ?>',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.error) {
                console.error('获取统计数据失败:', data.error);
                return;
            }

            var dates = [];
            var registrations = [];
            var battles = [];

            // 处理注册数据
            if (data.daily_registrations && data.daily_registrations.length > 0) {
                data.daily_registrations.forEach(function(item) {
                    dates.push(item.date);
                    registrations.push(item.count);
                });
            }

            // 处理战斗数据
            if (data.daily_battles && data.daily_battles.length > 0) {
                data.daily_battles.forEach(function(item, index) {
                    if (index < dates.length) {
                        battles.push(item.count);
                    }
                });
            }

            // 更新注册趋势图表
            var registrationOption = {
                title: {
                    text: '最近7天用户注册与战斗趋势'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['注册用户', '战斗次数']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '注册用户',
                        type: 'line',
                        stack: 'Total',
                        data: registrations,
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#5FB878'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(95, 184, 120, 0.5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(95, 184, 120, 0.1)'
                                }
                            ])
                        }
                    },
                    {
                        name: '战斗次数',
                        type: 'line',
                        stack: 'Total',
                        data: battles,
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#1E9FFF'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(30, 159, 255, 0.5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(30, 159, 255, 0.1)'
                                }
                            ])
                        }
                    }
                ]
            };
            registrationChart.setOption(registrationOption);
        },
        error: function(xhr, status, error) {
            console.error('获取统计数据失败:', error);
        }
    });

    // 窗口大小变化时，重新调整图表大小
    window.addEventListener('resize', function() {
        battleChart.resize();
        registrationChart.resize();
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\szxy\laravel\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>