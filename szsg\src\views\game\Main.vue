<template>
  <GameLayout>
    <div class="game-container">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 上半部分：左右分栏 -->
        <div class="top-section">
          <!-- 左侧：个人信息 -->
          <div class="left-panel">
            <div class="pixel-avatar-bar2" @click="openCharacterStatus">
              <!-- <img class="pixel-bg-img2" src="/static/game/UI/bj/tbu.png" alt="头像背景" /> -->
              <img class="pixel-avatar-img2" :src="characterInfo.avatar" :alt="characterInfo.name" />
              <div class="pixel-name2">{{ characterInfo.name }}</div>
              <div class="pixel-bars2">
                <div class="pixel-bar2 pixel-hp2">
                  <div class="pixel-bar-inner2 pixel-hp-inner2" :style="{width: hpPercent + '%'}"></div>
                </div>
                <div class="pixel-bar2 pixel-mp2">
                  <div class="pixel-bar-inner2 pixel-mp-inner2" :style="{width: mpPercent + '%'}"></div>
                </div>
              </div>
            </div>
            <div class="pixel-info-box">
              <div class="pixel-row">职业: {{ characterInfo.profession }}</div>
              <div class="pixel-row">等级: {{ characterInfo.level }}</div>
              <div class="pixel-row">银两: {{ characterInfo.silver }}</div>
              <div class="pixel-row"><span class="pixel-label-gold">金砖:</span><span class="pixel-value-gold">{{ characterInfo.gold }}</span></div>
              <div class="pixel-row pixel-exp-label">经验: {{ characterInfo.exp }}/{{ characterInfo.expRequired }}</div>
              <div class="pixel-exp-bar">
                <div class="pixel-exp-inner" :style="{width: expPercent + '%'}"></div>
              </div>
            </div>
          </div>

          <!-- 右侧：动态内容区域 -->
          <div class="right-panel">
            <div class="panel-header">{{ getCurrentPanelTitle() }}</div>
            <div class="panel-content">
              <!-- 人物功能内容：显示当前地图的NPC和怪物 -->
              <div v-if="!currentFunction || currentFunction === 'character'" class="npc-content">
                <div class="pixel-border-box">
                  <!-- 四行布局容器 -->
                  <div class="four-row-container">
                    <!-- 第一行 -->
                    <div class="entity-row-container">
                      <div v-if="getEntityForRow(0)" class="entity-row-single">
                        <div class="entity-info" @click="showEntityInfo(getEntityForRow(0), getEntityForRow(0).type)">
                          <span class="entity-name clickable" :class="{ 'monster-name': getEntityForRow(0).type === 'monster' }">
                            {{ getEntityForRow(0).name }}
                          </span>
                        </div>
                        <button
                          class="action-btn-compact"
                          :class="getEntityForRow(0).type === 'npc' ? 'npc-action-btn' : 'battle-btn'"
                          @click="handleEntityAction(getEntityForRow(0))"
                        >
                          {{ getEntityForRow(0).type === 'npc' ? '对话' : '战斗' }}
                        </button>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">空位</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>

                    <!-- 第二行 -->
                    <div class="entity-row-container">
                      <div v-if="getEntityForRow(1)" class="entity-row-single">
                        <div class="entity-info" @click="showEntityInfo(getEntityForRow(1), getEntityForRow(1).type)">
                          <span class="entity-name clickable" :class="{ 'monster-name': getEntityForRow(1).type === 'monster' }">
                            {{ getEntityForRow(1).name }}
                          </span>
                        </div>
                        <button
                          class="action-btn-compact"
                          :class="getEntityForRow(1).type === 'npc' ? 'npc-action-btn' : 'battle-btn'"
                          @click="handleEntityAction(getEntityForRow(1))"
                        >
                          {{ getEntityForRow(1).type === 'npc' ? '对话' : '战斗' }}
                        </button>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">空位</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>

                    <!-- 第三行 -->
                    <div class="entity-row-container">
                      <div v-if="getEntityForRow(2)" class="entity-row-single">
                        <div class="entity-info" @click="showEntityInfo(getEntityForRow(2), getEntityForRow(2).type)">
                          <span class="entity-name clickable" :class="{ 'monster-name': getEntityForRow(2).type === 'monster' }">
                            {{ getEntityForRow(2).name }}
                          </span>
                        </div>
                        <button
                          class="action-btn-compact"
                          :class="getEntityForRow(2).type === 'npc' ? 'npc-action-btn' : 'battle-btn'"
                          @click="handleEntityAction(getEntityForRow(2))"
                        >
                          {{ getEntityForRow(2).type === 'npc' ? '对话' : '战斗' }}
                        </button>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">空位</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>

                    <!-- 第四行 -->
                    <div class="entity-row-container">
                      <div v-if="getEntityForRow(3)" class="entity-row-single">
                        <div class="entity-info" @click="showEntityInfo(getEntityForRow(3), getEntityForRow(3).type)">
                          <span class="entity-name clickable" :class="{ 'monster-name': getEntityForRow(3).type === 'monster' }">
                            {{ getEntityForRow(3).name }}
                          </span>
                        </div>
                        <button
                          class="action-btn-compact"
                          :class="getEntityForRow(3).type === 'npc' ? 'npc-action-btn' : 'battle-btn'"
                          @click="handleEntityAction(getEntityForRow(3))"
                        >
                          {{ getEntityForRow(3).type === 'npc' ? '对话' : '战斗' }}
                        </button>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">空位</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>
                  </div>
                </div>
              </div>

              <!-- 实体信息弹窗 -->
              <div v-if="showEntityModal" class="entity-modal-overlay" @click="closeEntityModal">
                <div class="entity-modal" @click.stop>
                  <div class="modal-header">
                    <h3>{{ selectedEntity.name }}</h3>
                    <button class="close-btn" @click="closeEntityModal">×</button>
                  </div>
                  <div class="modal-content">
                    <div v-if="selectedEntityType === 'npc'" class="npc-info">
                      <div class="info-row">
                        <span class="info-label">称号：</span>
                        <span class="info-value">{{ selectedEntity.title || '无' }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">等级：</span>
                        <span class="info-value">{{ selectedEntity.level }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">类型：</span>
                        <span class="info-value">{{ getNpcTypeText(selectedEntity.type) }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">阵营：</span>
                        <span class="info-value">{{ getFactionText(selectedEntity.faction) }}</span>
                      </div>
                      <div v-if="selectedEntity.services && selectedEntity.services.length > 0" class="info-row">
                        <span class="info-label">服务：</span>
                        <span class="info-value">{{ selectedEntity.services.join(', ') }}</span>
                      </div>
                      <div class="info-row description">
                        <span class="info-label">描述：</span>
                        <span class="info-value">{{ selectedEntity.description || '暂无描述' }}</span>
                      </div>
                    </div>
                    <div v-else-if="selectedEntityType === 'monster'" class="monster-info">
                      <div class="info-row">
                        <span class="info-label">称号：</span>
                        <span class="info-value">{{ selectedEntity.title || '无' }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">等级：</span>
                        <span class="info-value">{{ selectedEntity.level }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">类型：</span>
                        <span class="info-value">{{ getMonsterTypeText(selectedEntity.type) }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">元素：</span>
                        <span class="info-value">{{ getElementText(selectedEntity.element) }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">体型：</span>
                        <span class="info-value">{{ getSizeText(selectedEntity.size) }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">威胁等级：</span>
                        <span class="info-value threat-level">{{ selectedEntity.threat_level || 1 }}</span>
                      </div>
                      <div v-if="selectedEntity.stats" class="stats-section">
                        <div class="stats-title">属性</div>
                        <div class="stats-grid">
                          <div class="stat-item">
                            <span class="stat-label">生命：</span>
                            <span class="stat-value">{{ selectedEntity.stats.health }}/{{ selectedEntity.stats.max_health }}</span>
                          </div>
                          <div class="stat-item">
                            <span class="stat-label">攻击：</span>
                            <span class="stat-value">{{ selectedEntity.stats.attack }}</span>
                          </div>
                          <div class="stat-item">
                            <span class="stat-label">防御：</span>
                            <span class="stat-value">{{ selectedEntity.stats.defense }}</span>
                          </div>
                          <div class="stat-item">
                            <span class="stat-label">速度：</span>
                            <span class="stat-value">{{ selectedEntity.stats.speed }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="info-row description">
                        <span class="info-label">描述：</span>
                        <span class="info-value">{{ selectedEntity.description || '暂无描述' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 设施功能内容 -->
              <div v-else-if="currentFunction === 'equipment'" class="facilities-content">
                <div class="facility-list six-grid">
                  <div class="facility-item" @click="selectFacility('clinic')">
                    <span class="facility-name">医馆</span>
                      </div>
                  <div class="facility-item" @click="selectFacility('bank')">
                    <span class="facility-name">钱庄</span>
                      </div>
                  <div class="facility-item" @click="selectFacility('posthouse')">
                    <span class="facility-name">馆驿</span>
                    </div>
                  <div class="facility-item" @click="selectFacility('market')">
                    <span class="facility-name">市场</span>
                      </div>
                  <div class="facility-item" @click="selectFacility('square')">
                    <span class="facility-name">广场</span>
                      </div>
                  <div class="facility-item" @click="selectFacility('government')">
                    <span class="facility-name">官府</span>
                  </div>
                </div>
              </div>

              <!-- 移动功能内容 -->
              <div v-else-if="currentFunction === 'move'" class="move-content">
                <div class="pixel-border-box">
                  <!-- 四行位置布局容器 -->
                  <div class="four-row-container">
                    <!-- 第一行位置 -->
                    <div class="entity-row-container">
                      <div v-if="getLocationForRow(0)" class="location-item-simple">
                        <span
                          class="entity-name clickable"
                          @click="moveToLocationDirectly(getLocationForRow(0))"
                          :class="{ 'disabled': isMoving }"
                        >
                          {{ getLocationForRow(0).name }}
                        </span>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">暂无位置</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>

                    <!-- 第二行位置 -->
                    <div class="entity-row-container">
                      <div v-if="getLocationForRow(1)" class="location-item-simple">
                        <span
                          class="entity-name clickable"
                          @click="moveToLocationDirectly(getLocationForRow(1))"
                          :class="{ 'disabled': isMoving }"
                        >
                          {{ getLocationForRow(1).name }}
                        </span>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">暂无位置</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>

                    <!-- 第三行位置 -->
                    <div class="entity-row-container">
                      <div v-if="getLocationForRow(2)" class="location-item-simple">
                        <span
                          class="entity-name clickable"
                          @click="moveToLocationDirectly(getLocationForRow(2))"
                          :class="{ 'disabled': isMoving }"
                        >
                          {{ getLocationForRow(2).name }}
                        </span>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">暂无位置</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>

                    <!-- 第四行位置 -->
                    <div class="entity-row-container">
                      <div v-if="getLocationForRow(3)" class="location-item-simple">
                        <span
                          class="entity-name clickable"
                          @click="moveToLocationDirectly(getLocationForRow(3))"
                          :class="{ 'disabled': isMoving }"
                        >
                          {{ getLocationForRow(3).name }}
                        </span>
                      </div>
                      <div v-else class="empty-row">
                        <span class="empty-text">暂无位置</span>
                      </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider-line"></div>
                  </div>
                </div>
              </div>

              <!-- 功能菜单内容 -->
              <div v-else-if="currentFunction === 'functions'" class="functions-content">
                <div class="function-list functions-grid">
                  <div class="function-item" @click="openFunction('status')">
                    <span class="function-name">状态</span>
                  </div>
                  <div class="function-item" @click="openFunction('items')">
                    <span class="function-name">物品</span>
                  </div>
                  <div class="function-item" @click="openFunction('immortal')">
                    <span class="function-name">仙将</span>
                  </div>
                  <div class="function-item" @click="openFunction('team')">
                    <span class="function-name">组队</span>
                  </div>
                  <div class="function-item" @click="openFunction('ranking')">
                    <span class="function-name">排行</span>
                  </div>
                  <div class="function-item" @click="openFunction('friends')">
                    <span class="function-name">好友</span>
                  </div>
                  <div class="function-item" @click="openFunction('mail')">
                    <span class="function-name">邮件</span>
                  </div>
                  <div class="function-item" @click="openFunction('quest')">
                    <span class="function-name">任务</span>
                  </div>
                  <div class="function-item" @click="openFunction('arena')">
                    <span class="function-name">擂台</span>
                  </div>
                  <div class="function-item" @click="openFunction('guild')">
                    <span class="function-name">帮派</span>
                  </div>
                  <div class="function-item" @click="openFunction('training')">
                    <span class="function-name">训练</span>
                  </div>
                  <div class="function-item" @click="openFunction('treasury')">
                    <span class="function-name">宝库</span>
                  </div>
                  <div class="function-item" @click="openFunction('notice')">
                    <span class="function-name">公告</span>
                  </div>
                  <div class="function-item" @click="openFunction('vip')">
                    <span class="function-name">VIP</span>
                  </div>
                  <div class="function-item" @click="openFunction('strategy')">
                    <span class="function-name">攻略</span>
                </div>
                  <div class="function-item" @click="openFunction('logout')">
                    <span class="function-name">登出</span>
              </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 下半部分：功能区域 -->
        <div class="bottom-section">
          <!-- 中间：功能按钮栏 -->
          <div class="function-bar">
            <div
              v-for="(func, index) in mainFunctions"
              :key="index"
              class="function-btn"
              :class="{ 'active': currentFunction === func.action }"
              @click="handleFunction(func.action)"
            >
              <img :src="func.image" :alt="func.name" class="function-btn-image" />
            </div>
          </div>

          <!-- 在线玩家头像区域 -->
          <div class="online-players">
            <div class="section-title">在线玩家头像</div>
            <div class="players-avatars">
              <div
                v-for="(player, index) in onlinePlayers"
                :key="index"
                class="player-avatar"
              >
                <img :src="player.avatar" :alt="player.name" />
              </div>
            </div>
          </div>

          <!-- 底部：聊天组件区域 -->
          <div class="chat-section">
            <GameChat
              :character-info="characterInfo"
              :auto-connect="true"
              :initial-minimized="false"
              @message-sent="onChatMessageSent"
              @channel-switched="onChatChannelSwitched"
            />
          </div>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import GameChat from '@/components/GameChat.vue'
import logger from '@/utils/logger'
import { getCharacterDetail, getCurrentCharacter, getCharacterStatus } from '@/api/services/characterService'
import { showMessage } from '@/utils/message'

export default {
  name: 'Main',
  components: {
    GameLayout,
    GameChat
  },
  data() {
    return {
      currentFunction: null, // 保持为null，默认显示NPC和怪物
      characterInfo: {
        name: '',
        avatar: '',
        profession: '',
        silver: 0,
        gold: 0,
        expNeeded: 0,
        hp: 0,
        maxHp: 0,
        mp: 0,
        maxMp: 0,
        level: 1,
        exp: 0,
        expRequired: 1000,
        attributePoints: 0,
        constitution: 0,
        intelligence: 0,
        strength: 0,
        agility: 0,
        attack: 0,
        defense: 0,
        speed: 0
      },
      npcList: [
        {
          name: '村长',
          type: 'npc',
          description: '村庄的领导者，可以接受任务',
          level: 50,
          avatar: '/static/game/UI/tx/npc/village_chief.png',
          services: ['任务', '信息']
        },
        {
          name: '武器商人',
          type: 'npc',
          description: '出售各种武器装备',
          level: 30,
          avatar: '/static/game/UI/tx/npc/weapon_merchant.png',
          services: ['武器', '装备']
        },
        {
          name: '药剂师',
          type: 'npc',
          description: '出售恢复药剂和魔法药水',
          level: 25,
          avatar: '/static/game/UI/tx/npc/alchemist.png',
          services: ['药剂', '治疗']
        },
        {
          name: '铁匠',
          type: 'npc',
          description: '可以强化和修理装备',
          level: 40,
          avatar: '/static/game/UI/tx/npc/blacksmith.png',
          services: ['强化', '修理']
        }
      ],
      mainFunctions: [
        { name: '人物', action: 'character', image: '/static/game/UI/anniu/sc_gn_1.png' },
        { name: '设施', action: 'equipment', image: '/static/game/UI/anniu/sc_gn_2.png' },
        { name: '移动', action: 'move', image: '/static/game/UI/anniu/sc_gn_3.png' },
        { name: '功能', action: 'functions', image: '/static/game/UI/anniu/sc_gn_4.png' }
      ],
      onlinePlayers: [
        { name: '玩家1', avatar: '/static/game/UI/tx/male/tx2.png' },
        { name: '玩家2', avatar: '/static/game/UI/tx/male/tx3.png' },
        { name: '玩家3', avatar: '/static/game/UI/tx/male/tx4.png' },
        { name: '玩家4', avatar: '/static/game/UI/tx/male/tx5.png' },
        { name: '玩家5', avatar: '/static/game/UI/tx/male/tx6.png' }
      ],

      // 地图相关数据
      loadingLocations: false,

      // 实体信息弹窗
      showEntityModal: false,
      selectedEntity: null,
      selectedEntityType: null
    }
  },
  computed: {
    hpPercent() {
      return (this.characterInfo.hp / this.characterInfo.maxHp) * 100
    },
    mpPercent() {
      return (this.characterInfo.mp / this.characterInfo.maxMp) * 100
    },
    expPercent() {
      return (this.characterInfo.exp / this.characterInfo.expRequired) * 100
    },

    // 地图相关计算属性
    availableLocations() {
      return this.$store.state.map.availableLocations || []
    },

    isMoving() {
      return this.$store.state.map.loading.moving
    },

    canMove() {
      return this.$store.getters['map/canMove']
    },

    // 当前地图的NPC列表
    currentLocationNpcs() {
      try {
        const currentLocation = this.$store.state.map.currentLocation;
        return currentLocation.npcs || [];
      } catch (error) {
        logger.error('[Main] 获取当前位置NPC失败:', error);
        return [];
      }
    },

    // 当前地图的怪物列表
    currentLocationMonsters() {
      try {
        const currentLocation = this.$store.state.map.currentLocation;
        return currentLocation.monsters || [];
      } catch (error) {
        logger.error('[Main] 获取当前位置怪物失败:', error);
        return [];
      }
    },

    // 合并所有实体（NPC和怪物）
    allEntities() {
      const entities = [];

      try {
        // 添加NPC
        if (this.currentLocationNpcs && Array.isArray(this.currentLocationNpcs)) {
          this.currentLocationNpcs.forEach(npc => {
            entities.push({
              ...npc,
              type: 'npc'
            });
          });
        }

        // 添加怪物
        if (this.currentLocationMonsters && Array.isArray(this.currentLocationMonsters)) {
          this.currentLocationMonsters.forEach(monster => {
            entities.push({
              ...monster,
              type: 'monster'
            });
          });
        }
      } catch (error) {
        logger.error('[Main] 获取实体列表失败:', error);
      }

      return entities;
    }
  },
  methods: {
    openCharacterStatus() {
      logger.info('打开角色状态页面')
      
      // 在跳转前初始化角色状态数据
      this.$store.dispatch('character/initCurrentCharacter').then(character => {
        if (character) {
          this.$store.dispatch('character/loadCharacterStatus').then(() => {
            // 确保状态已加载完成，然后跳转
            logger.debug('[Main] 角色状态加载成功，准备跳转到角色状态页面');
            
            // 强制刷新一次角色信息，确保数据最新
            this.loadCharacterInfo().then(() => {
              this.$router.push('/game/character-status');
            });
          }).catch(error => {
            logger.error('[Main] 加载角色状态失败:', error);
            showMessage('加载角色状态失败', 'error');
            // 即使加载失败也跳转，CharacterStatus组件会处理错误情况
            this.$router.push('/game/character-status');
          });
        } else {
          showMessage('未找到角色信息，请重新登录', 'error');
          this.$router.push('/setup/character-select');
        }
      });
    },
    selectNpc(npc) {
      logger.info('选择NPC/野怪', npc.name)

      if (npc.type === 'npc') {
        // NPC交互逻辑
        this.handleNpcInteraction(npc)
      } else if (npc.type === 'monster') {
        // 怪物战斗逻辑
        this.handleMonsterEncounter(npc)
      }
    },

    handleNpcInteraction(npc) {
      // 西游记主题的NPC对话
      switch(npc.name) {
        case '城隍爷':
          this.showToast(`与${npc.name}对话：施主，此地乃东胜神洲，有何贵干？`)
          break
        case '仙界商人':
          this.showToast(`与${npc.name}对话：仙友，我这里有各种法宝，要不要看看？`)
          setTimeout(() => {
            this.$router.push('/game/market')
          }, 2000)
          break
        case '猴族长老':
          this.showToast(`与${npc.name}对话：小猴子，想学武艺吗？花果山可是修炼的好地方！`)
          break
        case '洞府守护':
          this.showToast(`与${npc.name}对话：水帘洞乃美猴王洞府，此处有上古秘籍！`)
          break
        case '得道高僧':
          this.showToast(`与${npc.name}对话：阿弥陀佛，施主与佛有缘，可愿听贫僧讲经？`)
          break
        case '罗汉':
          this.showToast(`与${npc.name}对话：善哉善哉，施主来到灵山，可是为求真经？`)
          break
        case '凡间官员':
          this.showToast(`与${npc.name}对话：这位侠客，南瞻部洲最近不太平，小心山贼！`)
          break
        case '东海龙王':
          this.showToast(`与${npc.name}对话：何方神圣闯入龙宫？若是有缘人，可赐你神兵利器！`)
          setTimeout(() => {
            this.$router.push('/game/market')
          }, 2000)
          break
        case '虾兵':
          this.showToast(`与${npc.name}对话：龙王有令，闲杂人等不得入内！`)
          break
        default:
          this.showToast(`与${npc.name}对话：施主，贫道这厢有礼了！`)
      }
    },

    handleMonsterEncounter(monster) {
      // 西游记主题的怪物遭遇
      let encounterMessage = '';

      switch(monster.name) {
        case '灵猴':
          encounterMessage = `一只${monster.name}从树上跳下，似乎想要与你切磋武艺！`;
          break
        case '山魈':
          encounterMessage = `${monster.name}从山林中现身，凶神恶煞地盯着你！`;
          break
        case '水灵':
          encounterMessage = `洞中的${monster.name}感受到了你的气息，化作人形挡住去路！`;
          break
        case '护法金刚':
          encounterMessage = `${monster.name}金光闪闪，威严地说："欲入灵山，先过我这一关！"`;
          break
        case '山贼':
          encounterMessage = `一群${monster.name}拦路抢劫："此路是我开，此树是我栽！"`;
          break
        case '蟹将':
          encounterMessage = `龙宫${monster.name}挥舞着巨钳："胆敢擅闯龙宫，受死！"`;
          break
        default:
          encounterMessage = `遭遇了${monster.name}，看起来来者不善！`;
      }

      this.showToast(`${encounterMessage}（等级${monster.level}）准备战斗！`)

      // 获取当前角色和位置信息
      const currentCharacter = this.$store.getters['character/currentCharacter'];
      const currentLocation = this.$store.state.map.currentLocation;

      if (!currentCharacter || !currentCharacter.id) {
        this.showToast('角色信息错误，无法开始战斗', 'error');
        return;
      }

      setTimeout(() => {
        // 传递战斗所需的参数
        this.$router.push({
          path: '/game/battle',
          query: {
            characterId: currentCharacter.id,
            monsterId: monster.id,
            locationId: currentLocation?.id || null
          }
        });
      }, 2000)
    },

    // 获取指定行的实体（为后台管理预留接口）
    getEntityForRow(rowIndex) {
      try {
        // 目前简单按顺序分配，后续可通过后台配置
        const entities = this.allEntities || [];
        return entities[rowIndex] || null;
      } catch (error) {
        logger.error('[Main] 获取行实体失败:', error);
        return null;
      }
    },

    // 统一的实体操作方法
    handleEntityAction(entity) {
      if (entity.type === 'npc') {
        this.handleNpcInteraction(entity);
      } else if (entity.type === 'monster') {
        this.handleMonsterEncounter(entity);
      }
    },

    // 获取指定行的位置（为后台管理预留接口）
    getLocationForRow(rowIndex) {
      try {
        // 目前简单按顺序分配，后续可通过后台配置
        const locations = this.availableLocations || [];
        return locations[rowIndex] || null;
      } catch (error) {
        logger.error('[Main] 获取行位置失败:', error);
        return null;
      }
    },
    handleFunction(action) {
      logger.info('点击功能', action)
      if (this.currentFunction === action) {
        // 如果当前功能已经是选中状态，不做任何操作
        return;
      } else {
        // 设置为新的功能
        this.currentFunction = action
      }
    },
    getCurrentPanelTitle() {
      switch(this.currentFunction) {
        case 'character':
          return '人物信息'
        case 'equipment':
          return '设施列表'
        case 'move':
          return '移动地点'
        case 'functions':
          return '功能菜单'
        default:
          return ''
      }
    },
    // 设施相关方法
    selectFacility(facility) {
      logger.info('选择设施', facility)
      switch(facility) {
        case 'clinic':
          this.$router.push('/game/clinic');
          break;
        case 'bank':
          this.$router.push('/game/bank');
          break;
        case 'posthouse':
          this.$router.push('/game/posthouse');
          break;
        case 'market':
          this.$router.push('/game/market');
          break;
        case 'square':
          this.$router.push('/game/square');
          break;
        case 'government':
          this.$router.push('/game/government');
          break;
      }
    },
    // 移动相关方法
    async moveToLocationDirectly(location) {
      logger.info('[Main] 直接移动到位置:', location);

      // 检查角色信息
      const currentCharacter = this.$store.getters['character/currentCharacter'];
      if (!currentCharacter || !currentCharacter.id) {
        logger.error('[Main] 移动失败: 未找到角色信息');
        this.showToast('请先选择角色', 'error');
        return;
      }

      // 检查是否正在移动
      if (this.isMoving) {
        this.showToast('正在移动中，请稍候', 'warning');
        return;
      }

      try {
        await this.$store.dispatch('map/moveToLocation', location.id);
        this.showToast(`成功移动到${location.name}`, 'success');

        // 重新加载当前位置的NPC和怪物
        await this.loadLocationEntities();
      } catch (error) {
        logger.error('[Main] 移动失败:', error);
        this.showToast('移动失败: ' + error.message, 'error');
      }
    },





    async loadLocationEntities() {
      try {
        const currentCharacter = this.$store.getters['character/currentCharacter'];
        const currentLocation = this.$store.state.map.currentLocation;

        if (currentCharacter && currentLocation.id) {
          logger.debug('[Main] 当前位置实体信息:', {
            location: currentLocation.name,
            npcs: currentLocation.npcs?.length || 0,
            monsters: currentLocation.monsters?.length || 0
          });

          // 触发Vue的响应式更新
          this.$forceUpdate();
        }
      } catch (error) {
        logger.error('[Main] 加载位置实体失败:', error);
      }
    },





    // 兼容旧的移动方法
    moveToLocation(location) {
      logger.info('[Main] 兼容性移动方法调用:', location);

      // 如果是字符串，转换为位置对象
      if (typeof location === 'string') {
        const locationObj = {
          id: location,
          name: location,
          type: location
        };
        this.moveToLocationDirectly(locationObj);
      } else {
        this.moveToLocationDirectly(location);
      }
    },
    // 功能菜单相关方法
    openFunction(func) {
      logger.info('打开功能', func)
      // 根据功能类型跳转到对应页面
      switch(func) {
        case 'status':
          this.$router.push('/game/status')
          break
        case 'items':
          this.$router.push('/game/items')
          break
        case 'immortal':
          this.$router.push('/game/immortal')
          break
        case 'team':
          this.$router.push('/game/team')
          break
        case 'ranking':
          this.$router.push('/game/ranking')
          break
        case 'friends':
          this.$router.push('/game/friends')
          break
        case 'mail':
          this.$router.push('/game/mail')
          break
        case 'quest':
          this.$router.push('/game/quest')
          break
        case 'arena':
          this.$router.push('/game/arena')
          break
        case 'guild':
          this.$router.push('/game/guild')
          break
        case 'training':
          this.$router.push('/game/training')
          break
        case 'treasury':
          this.$router.push('/game/treasury')
          break
        case 'notice':
          this.$router.push('/game/notice')
          break
        case 'vip':
          this.$router.push('/game/vip')
          break
        case 'strategy':
          this.$router.push('/game/strategy')
          break
        case 'logout':
          this.$router.push('/game/logout')
          break
      }
    },
    // 聊天组件事件处理
    onChatMessageSent(messageData) {
      logger.info('[Main] 聊天消息已发送:', messageData);
    },

    onChatChannelSwitched(channelData) {
      logger.info('[Main] 聊天频道已切换:', channelData);
    },

    showToast(message, type = 'info') {
      // 简单的提示实现，使用logger代替alert避免弹出框
      logger.info(`[Toast ${type}]:`, message)
    },

    // 实体信息弹窗相关方法
    showEntityInfo(entity, type) {
      this.selectedEntity = entity;
      this.selectedEntityType = type;
      this.showEntityModal = true;
      logger.info(`[Main] 显示${type}信息:`, entity.name);
    },

    closeEntityModal() {
      this.showEntityModal = false;
      this.selectedEntity = null;
      this.selectedEntityType = null;
    },

    // 获取类型文本的方法
    getNpcTypeText(type) {
      const typeMap = {
        'merchant': '商人',
        'quest_giver': '任务发布者',
        'trainer': '训练师',
        'guard': '守卫',
        'official': '官员',
        'immortal': '仙人',
        'monk': '僧人',
        'other': '其他'
      };
      return typeMap[type] || type;
    },

    getMonsterTypeText(type) {
      const typeMap = {
        'beast': '野兽',
        'demon': '妖魔',
        'spirit': '精灵',
        'undead': '不死族',
        'dragon': '龙族',
        'immortal': '仙族',
        'elemental': '元素',
        'other': '其他'
      };
      return typeMap[type] || type;
    },

    getFactionText(faction) {
      const factionMap = {
        'heaven': '天庭',
        'buddhist': '佛门',
        'mortal': '凡间',
        'demon': '妖魔',
        'dragon': '龙族',
        'neutral': '中立'
      };
      return factionMap[faction] || faction;
    },

    getElementText(element) {
      const elementMap = {
        'none': '无',
        'fire': '火',
        'water': '水',
        'earth': '土',
        'wind': '风',
        'thunder': '雷',
        'ice': '冰',
        'light': '光',
        'dark': '暗'
      };
      return elementMap[element] || element;
    },

    getSizeText(size) {
      const sizeMap = {
        'tiny': '微小',
        'small': '小型',
        'medium': '中型',
        'large': '大型',
        'huge': '巨型',
        'giant': '超巨型'
      };
      return sizeMap[size] || size;
    },

    getDefaultAvatar(type) {
      if (type === 'npc') {
        return '/static/game/UI/tx/npc/default.png'
      } else {
        return '/static/game/UI/tx/monster/default.png'
    }
  },
    async loadCharacterInfo() {
      try {
        // 获取当前角色基本信息
    const current = getCurrentCharacter();
        if (!current || !current.id) {
          logger.error('[Main] 未找到当前角色信息');
          showMessage('未找到角色信息，请重新登录', 'error');
          this.$router.push('/setup/character-select');
          return;
        }

        // 将角色信息设置到store中
        await this.$store.dispatch('character/selectCharacter', current);
        logger.debug('[Main] 角色信息已设置到store:', current);

        // 获取角色详情
        try {
          const detailResponse = await getCharacterDetail(current.id);
          if (detailResponse && detailResponse.data) {
            const detail = detailResponse.data;
            
            // 职业英文到中文的映射
            const professionMap = {
              'warrior': '武士',
              'scholar': '文人',
              'mystic': '异人'
            };
            
            // 更新角色基本信息
        this.characterInfo = {
          ...this.characterInfo,
              name: detail.name || current.name,
              avatar: detail.avatar || `/static/game/UI/tx/${detail.gender || 'male'}/tx1.png`,
              profession: professionMap[detail.profession] || detail.profession || '未知',
              gold: detail.gold || 0,
              silver: detail.silver || 0,
              level: detail.level || 1
        };
          }
        } catch (detailError) {
          logger.error('[Main] 获取角色详情失败:', detailError);
    }

        // 获取角色状态信息
        try {
          const statusResponse = await getCharacterStatus(current.id);
          if (statusResponse && statusResponse.data) {
            const status = statusResponse.data;
            
            // 更新角色状态信息
            this.characterInfo = {
              ...this.characterInfo,
              hp: parseInt(status.hp || 0),
              maxHp: parseInt(status.max_hp || 100),
              mp: parseInt(status.mp || 0),
              maxMp: parseInt(status.max_mp || 100),
              exp: parseInt(status.exp || 0),
              expRequired: parseInt(status.exp_required || 1000),
              attributePoints: parseInt(status.attribute_points || 0),
              constitution: parseInt(status.constitution || 0),
              intelligence: parseInt(status.intelligence || 0),
              strength: parseInt(status.strength || 0),
              agility: parseInt(status.agility || 0),
              attack: parseInt(status.attack || 0),
              defense: parseInt(status.defense || 0),
              speed: parseInt(status.speed || 0)
            };
            
            // 调试输出
            logger.debug('[Main] 角色状态已更新:', this.characterInfo);
          }
        } catch (statusError) {
          logger.error('[Main] 获取角色状态失败:', statusError);
        }
      } catch (error) {
        logger.error('[Main] 加载角色信息失败:', error);
        showMessage('加载角色信息失败', 'error');
      }
      
      // 返回Promise以支持链式调用
      return Promise.resolve();
    },
  },
  async mounted() {
    try {
      // 加载角色信息
      await this.loadCharacterInfo();

      // 检查用户是否已登录且有角色信息
      const currentCharacter = this.$store.getters['character/currentCharacter'];
      const isAuthenticated = this.$store.state.auth.isAuthenticated;

      logger.debug('[Main] 挂载后状态检查:', {
        isAuthenticated,
        hasCharacter: !!currentCharacter,
        characterId: currentCharacter?.id
      });

      if (isAuthenticated && currentCharacter && currentCharacter.id) {
        // 初始化地图数据
        try {
          await this.$store.dispatch('map/initializeMap');
          logger.info('[Main] 地图数据初始化完成');
        } catch (error) {
          logger.error('[Main] 地图数据初始化失败:', error);
          // 地图初始化失败不影响主要功能，只记录错误
        }
      } else {
        logger.warn('[Main] 用户未登录或无角色信息，跳过地图初始化');
      }
    } catch (error) {
      logger.error('[Main] 组件挂载过程中发生错误:', error);
    }
  }
};
</script>

<style lang="scss" scoped>
.game-container {
  position: relative;
  height: 100%;
  width: 100%;
  color: #ffd700;
  overflow: hidden;
  background: url('/static/game/UI/bg/main_bg.png') center center no-repeat;
  background-size: cover;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 50, 0.4), rgba(50, 0, 0, 0.4));
    z-index: 0;
  }
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  position: relative;
  z-index: 1;
  padding: 8px;
}

.top-section {
  display: flex;
  gap: 8px;
  height: 320px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    height: 280px;
    gap: 6px;
  }

  @media (max-width: 480px) {
    height: 240px;
    gap: 4px;
  }
}

.bottom-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-height: 0;

  @media (max-width: 768px) {
    gap: 6px;
  }

  @media (max-width: 480px) {
    gap: 4px;
  }
}

.left-panel, .right-panel {
  background: none;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  border: none;
}

.left-panel {
  width: 120px;
  min-width: 100px;
  background: none;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  border: none;
}

.pixel-avatar-bar2 {
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.panel-header {
  background: none;
  color: inherit;
  padding: 0;
  font-weight: normal;
  font-size: inherit;
  text-align: center;
  text-shadow: none;
  border-bottom: none;
}

.character-info {
  flex: 1;
  padding: 6px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  background: none;
}

.info-details {
  flex: 1;
}

.character-name {
  font-size: 14px;
  font-weight: bold;
  color: #ffd700;
  text-align: center;
  margin-bottom: 1px;
  text-shadow: none;
}

.character-level {
  font-size: 10px;
  color: #ffd700;
  text-align: center;
  margin-bottom: 6px;
  text-shadow: none;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.stat-item {
  font-size: 10px;
  color: #ffd700;
  padding: 1px 0;
  text-shadow: none;
}

.avatar-section, .status-bars {
  display: none !important;
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background: none;
}

.npc-content,
.npc-content * {
  background: none !important;
  box-shadow: none !important;
  border: none !important;
}

.npc-text-list {
  list-style: none;
  padding: 0;
  margin: 0;
  background: none;
  border: none;
}

.npc-text-list li {
  border-bottom: 1px solid #888 !important;
  margin: 0 !important;
  padding: 2px 0 !important;
}

.npc-action-btn {
  background: none !important;
  border: none !important;
  color: inherit !important;
  font: inherit !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin-left: 4px !important;
  cursor: pointer;
}

.npc-action-btn:hover,
.npc-action-btn:active {
  background: none !important;
  color: inherit !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.character-content {
  height: auto;
  padding: 0;
  overflow: visible;
  background: none;
  box-shadow: none;
}

.character-card {
  background: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  border: none;
}

.character-avatar-section,
.character-basic-info,
.character-profession,
.character-bars,
.character-stats,
.character-wealth,
.character-exp {
  background: none !important;
  box-shadow: none !important;
  border: none !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.character-avatar-img {
  width: 60px;
  height: 60px;
  border-radius: 10%;
  border: 2px solid #ffd700;
  object-fit: cover;
  box-shadow: none;
}

.character-name {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: bold;
  color: #ffd700;
  text-shadow: none;
}

.character-profession {
  margin: 0;
  font-size: 14px;
  color: #ffd700;
  text-shadow: none;
}

.character-bars {
  margin-bottom: 16px;
}

.bar-item {
  margin-bottom: 8px;
}

.bar-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #ffd700;
  text-shadow: none;
}

.bar-value {
  font-weight: bold;
}

.progress-bar {
  height: 8px;
  background: none;
  border-radius: 0;
  overflow: hidden;
  border: 1px solid #088be2;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.hp-bar .progress-fill {
  background: linear-gradient(90deg, #ff0000, #ff3333);
}

.mp-bar .progress-fill {
  background: linear-gradient(90deg, #088be2, #4eadf5);
}

.character-stats {
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: none;
  border-radius: 4px;
  border: 1px solid #088be2;
}

.stat-label {
  font-size: 12px;
  color: #ffd700;
  text-shadow: none;
}

.stat-value {
  font-weight: bold;
  color: #ffd700;
  text-shadow: none;
}

.character-wealth {
  margin-bottom: 16px;
}

.wealth-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: none;
  border-radius: 4px;
  border: 1px solid #088be2;
  margin-bottom: 6px;
}

.wealth-label {
  flex: 1;
  font-size: 13px;
  color: #ffd700;
  text-shadow: none;
}

.wealth-value {
  font-weight: bold;
  color: #ffd700;
  text-shadow: none;
}

.character-exp {
  padding: 8px;
  background: none;
  border-radius: 4px;
  border: 1px solid #088be2;
}

.exp-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #ffd700;
  text-shadow: none;
}

.exp-value {
  font-weight: bold;
  color: #ffd700;
  text-shadow: none;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: none;
  border-radius: 4px;
  border: 1px solid #088be2;

  .label {
    font-weight: bold;
    color: #ffd700;
    text-shadow: none;
  }

  .value {
    color: #ffd700;
    text-shadow: none;
  }
}

.facilities-content {
  height: 100%;
  background: none !important;
}

.facility-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.facility-item {
  padding: 8px 0;
  background: none;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.move-content {
  height: 100%;
  background: none !important;
}

.location-list {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.location-item {
  padding: 6px;
  background: none;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.functions-content {
  height: 100%;
  background: none !important;
}

.function-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.function-item {
  padding: 12px;
  background: none;
  border: 1px solid #088be2;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: none;
    border-color: #ffd700;
    transform: translateY(-1px);
    box-shadow: none;
  }

  .function-name {
    display: block;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 4px;
    text-shadow: none;
  }

  .function-desc {
    display: block;
    font-size: 12px;
    color: #ccc;
  }
}

.function-bar {
  display: flex;
  gap: 8px;
  height: 50px;
  padding: 0;
  background: none !important;
  border-radius: 0;
  border: none;
  flex-shrink: 0;
  overflow: visible;
}

.function-btn {
  flex: 1;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    background: none;
  }

  &:active {
    background: none;
    transform: scale(0.95);
  }

  &.active {
    background: none;
    &::after {
      content: '';
      position: absolute;
      bottom: -3px;
      left: 25%;
      right: 25%;
      height: 3px;
      background: #ffd700;
      border-radius: 3px;
    }
  }
}

.function-btn-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

.online-players {
  border: none;
  background: none !important;
  height: 70px;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  box-shadow: none;
  flex-shrink: 0;
}

.section-title {
  background: linear-gradient(90deg, #08407a, #088be2);
  color: #ffd700;
  padding: 6px 12px;
  font-weight: bold;
  font-size: 13px;
  text-align: center;
  text-shadow: none;
  border-bottom: none;

  @media (max-width: 768px) {
    padding: 5px 10px;
    font-size: 12px;
  }

  @media (max-width: 480px) {
    padding: 4px 8px;
    font-size: 11px;
  }
}

.players-avatars {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
  overflow-x: auto;
  background: none;

  @media (max-width: 768px) {
    padding: 6px 10px;
    gap: 6px;
  }

  @media (max-width: 480px) {
    padding: 4px 8px;
    gap: 4px;
  }
}

.player-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10%;
  border: 2px solid #ffd700;
  overflow: hidden;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: none;

  @media (max-width: 768px) {
    width: 35px;
    height: 35px;
  }

  @media (max-width: 480px) {
    width: 30px;
    height: 30px;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &:hover {
    border-color: #088be2;
    transform: scale(1.1);
    box-shadow: none;
  }
}

.chat-section {
  border: none;
  background: none !important;
  height: 420px;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  box-shadow: none;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

/* 聊天区域现在使用GameChat组件，移除旧的样式 */

@media (orientation: landscape) and (max-height: 600px) {
  .top-section {
    height: 200px;
  }

  .online-players {
    height: 60px;
  }

  .chat-section {
    height: 300px;
  }

  .function-bar {
    height: 40px;
  }
}

@media (max-width: 320px) {
  .main-content {
    padding: 4px;
    gap: 4px;
  }

  .left-panel {
    width: 140px;
    min-width: 120px;
  }

  .player-avatar {
    width: 25px;
    height: 25px;
  }

  .function-btn {
    font-size: 10px;
  }

  .panel-header {
    font-size: 10px;
    padding: 3px 6px;
  }
}

@media (min-width: 1200px) {
  .main-content {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
  }

  .top-section {
    height: 350px;
  }

  .left-panel {
    width: 320px;
  }

  .player-avatar {
    width: 100px;
    height: 100px;
  }

  .function-bar {
    height: 60px;
  }

  .function-btn {
    font-size: 16px;
  }

  .online-players {
    height: 100px;
  }

  .chat-section {
    height: 420px;
  }
}

@media (hover: none) and (pointer: coarse) {
  .function-btn, .chat-tab, .npc-item, .player-avatar {
    min-height: 44px;
  }

  .function-btn:active {
    background: #088be2;
    transform: scale(0.95);
  }

  .chat-tab:active {
    background: #088be2;
  }
}

.npc-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 10px;
}
.npc-action-btn {
  background: linear-gradient(90deg, #08407a, #088be2);
  color: #ffd700;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  text-shadow: none;
  transition: all 0.3s ease;
}
.npc-action-btn:hover {
  background: linear-gradient(90deg, #08407a, #088be2);
  transform: none;
}
.npc-action-btn:active {
  background: linear-gradient(90deg, #08407a, #088be2);
  transform: none;
}

.pixel-avatar-bar2 {
  position: relative;
  width: 180px;
  height: 60px;
  margin: 0 auto 8px auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.pixel-bg-img2 {
  position: absolute;
  left: 0;
  top: 0;
  width: 180px;
  height: 60px;
  z-index: 0;
  pointer-events: none;
}
.pixel-avatar-img2 {
  position: absolute;
  left: 6px;
  bottom: 6px;
  width: 38px;
  height: 38px;
  border-radius: 6px;
  border: 2px solid #ffd700;
  background: #222;
  z-index: 2;
  object-fit: cover;
  box-shadow: none;
}
.pixel-name2 {
  position: absolute;
  left: 0;
  top: 4px;
  width: 180px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: none;
  z-index: 3;
  pointer-events: none;
}
.pixel-bars2 {
  position: absolute;
  left: 54px;
  top: 26px;
  width: 116px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.pixel-bar2 {
  width: 100%;
  height: 12px;
  background: #222;
  border: 1.5px solid #ffd700;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 2px;
}
.pixel-bar-inner2 {
  height: 100%;
  transition: width 0.3s;
  border-radius: 4px;
}
.pixel-hp-inner2 {
  background: linear-gradient(90deg, #ff3c3c, #ffb03c);
}
.pixel-mp-inner2 {
  background: linear-gradient(90deg, #3c7cff, #b0e0ff);
}

* {
  -webkit-tap-highlight-color: transparent !important;
}
button, [type="button"], [type="submit"], [type="reset"], .function-btn, .npc-action-btn, .chat-tab, .player-avatar, .location-item, .facility-item, .function-item, .panel-header, .section-title, .stat-item, .character-card, .character-content, .panel-content, .right-panel, .left-panel {
  outline: none !important;
  box-shadow: none !important;
  background: none !important;
}
button:active, button:focus, .function-btn:active, .function-btn:focus, .npc-action-btn:active, .npc-action-btn:focus, .chat-tab:active, .chat-tab:focus, .player-avatar:active, .player-avatar:focus, .location-item:active, .location-item:focus, .facility-item:active, .facility-item:focus, .function-item:active, .function-item:focus, .location-item-simple:active, .location-item-simple:focus {
  background: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: inherit !important;
}

/* 防止虚拟键盘弹出 */
* {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 禁用所有元素的焦点 */
*:focus {
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 添加六宫格布局样式 */
.six-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 4px;
  justify-items: center;
  align-items: center;
}

.six-grid .facility-item {
  width: 100%;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #ffd700;
  background: none;
  border: none;
  border-radius: 0;
  cursor: pointer;
  padding: 8px 0;
  transition: background 0.2s;
}

.six-grid .facility-item:active {
  background: none;
}

/* 添加功能宫格布局样式 */
.functions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 4px;
  justify-items: center;
  align-items: center;
}

.functions-grid .function-item {
  width: 100%;
  text-align: center;
  font-size: 15px;
  font-weight: bold;
  color: #ffd700;
  background: none;
  border: none;
  border-radius: 0;
  cursor: pointer;
  padding: 8px 0;
  transition: background 0.2s;
}

.functions-grid .function-item:active {
  background: none;
}

.pixel-info-box {
  background: rgba(0, 0, 50, 0.7);
  border: 1px solid #5555ff;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  font-size: 12px;
  color: #ffffff;
}

.pixel-row {
  margin-bottom: 4px;
  line-height: 1.2;
}

.pixel-label-gold {
  color: #ffcc00;
}

.pixel-value-gold {
  color: #ffcc00;
  font-weight: bold;
}

.pixel-exp-label {
  margin-top: 6px;
  color: #00ffff;
}

.pixel-exp-bar {
  height: 6px;
  background: rgba(0, 0, 50, 0.5);
  border: 1px solid #5555ff;
  border-radius: 3px;
  margin-top: 2px;
  margin-bottom: 6px;
  position: relative;
  overflow: hidden;
}

.pixel-exp-inner {
  position: absolute;
  height: 100%;
  background: linear-gradient(to right, #00ffff, #00aaff);
  left: 0;
  top: 0;
}

/* NPC和怪物显示样式 */
.entity-section {
  margin-bottom: 20px;
}

.section-header {
  font-size: 16px;
  font-weight: bold;
  color: #d4af37;
  margin-bottom: 10px;
  padding: 5px 10px;
  background: rgba(212, 175, 55, 0.1);
  border-left: 3px solid #d4af37;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.entity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.entity-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.entity-row:hover {
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
}

.npc-row:hover {
  border-color: rgba(135, 206, 235, 0.5);
}

.monster-row:hover {
  border-color: rgba(255, 107, 107, 0.5);
}

.entity-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.entity-right {
  flex-shrink: 0;
}

.entity-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  transition: color 0.3s ease;
}

.entity-name.clickable:hover {
  color: #87ceeb;
}

.monster-name {
  color: #ff6b6b;
}

.monster-name.clickable:hover {
  color: #ff8a8a;
}

.entity-level {
  font-size: 12px;
  color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  display: inline-block;
  width: fit-content;
}

.entity-services {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 2px;
}

.service-tag {
  font-size: 10px;
  color: #87ceeb;
  background: rgba(135, 206, 235, 0.2);
  padding: 1px 4px;
  border-radius: 8px;
  border: 1px solid rgba(135, 206, 235, 0.3);
}

.entity-type {
  font-size: 11px;
  color: #ccc;
  font-style: italic;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.npc-action-btn {
  background: linear-gradient(135deg, #87ceeb, #5f9ea0);
  color: white;
  border: 1px solid #87ceeb;
}

.npc-action-btn:hover {
  background: linear-gradient(135deg, #5f9ea0, #4682b4);
  box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);
}

.battle-btn {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: white;
  border: 1px solid #ff6b6b;
}

.battle-btn:hover {
  background: linear-gradient(135deg, #ff3742, #ff2f3a);
  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-message {
  font-size: 14px;
  font-style: italic;
}

/* 实体信息弹窗样式 */
.entity-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.entity-modal {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  border-radius: 12px;
  border: 2px solid #d4af37;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: rgba(212, 175, 55, 0.1);
  border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.modal-header h3 {
  margin: 0;
  color: #d4af37;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}

.modal-content {
  padding: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-row.description {
  flex-direction: column;
  gap: 5px;
}

.info-label {
  font-weight: bold;
  color: #d4af37;
  min-width: 80px;
  margin-right: 10px;
}

.info-value {
  color: #fff;
  flex: 1;
}

.threat-level {
  color: #ff6b6b;
  font-weight: bold;
}

.stats-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-title {
  font-weight: bold;
  color: #d4af37;
  margin-bottom: 10px;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.stat-label {
  color: #ccc;
  font-size: 12px;
}

.stat-value {
  color: #fff;
  font-weight: bold;
  font-size: 12px;
}

/* 移动功能样式 */
.move-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
  padding: 10px;
}



.location-description {
  font-size: 11px;
  color: #cccccc;
  line-height: 1.4;
}

.movement-options {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #88ccff;
  margin-bottom: 8px;
  padding-bottom: 4px;
}

.location-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.location-item {
  padding: 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-item:hover:not(.disabled) {
  background: none;
  transform: translateY(-1px);
}

.location-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 简化的地图列表样式 */
.location-item-simple {
  padding: 8px 12px;
  background: none;
  border: none;
  border-bottom: 1px solid #444;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffd700;
  font-size: 14px;
  text-align: left;
  display: block;
  width: 100%;
}

.location-item-simple:hover:not(.disabled) {
  background: none;
  color: #ffffff;
}

.location-item-simple.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.location-item-simple:last-child {
  border-bottom: none;
}







.no-locations, .loading-locations {
  text-align: center;
  padding: 20px;
  color: #888888;
  font-size: 12px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 四行布局样式 */
.four-row-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 0;
}

.entity-row-container {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  min-height: 20px;
}

.entity-list-horizontal {
  display: flex;
  gap: 15px;
  width: 100%;
  align-items: center;
}

.entity-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.entity-item-compact .entity-name {
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  transition: color 0.3s ease;
  flex: 1;
  line-height: 1.2;
}

.entity-item-compact .entity-name:hover {
  color: #87ceeb;
}

.entity-item-compact .monster-name {
  color: #ff6b6b;
}

.entity-item-compact .monster-name:hover {
  color: #ff8a8a;
}

.action-btn-compact {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.action-btn-compact.npc-action-btn {
  background: linear-gradient(135deg, #87ceeb, #5f9ea0);
  color: white;
  border: 1px solid #87ceeb;
}

.action-btn-compact.npc-action-btn:hover {
  background: linear-gradient(135deg, #5f9ea0, #4682b4);
  box-shadow: 0 0 8px rgba(135, 206, 235, 0.4);
}

.action-btn-compact.battle-btn {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: white;
  border: 1px solid #ff6b6b;
}

.action-btn-compact.battle-btn:hover {
  background: linear-gradient(135deg, #ff3742, #ff2f3a);
  box-shadow: 0 0 8px rgba(255, 71, 87, 0.4);
}

.empty-row {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.empty-text {
  color: #666;
  font-size: 11px;
  font-style: italic;
  line-height: 1.1;
}

.divider-line {
  height: 1px;
  background: #666;
  margin: 0 8px;
  border: none;
}

/* 单行实体显示样式 */
.entity-row-single {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
}

.entity-info {
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.entity-info .entity-name {
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  transition: color 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  display: block;
  line-height: 1.2;
}

.entity-info .entity-name:hover {
  color: #87ceeb;
}

.entity-info .monster-name {
  color: #ff6b6b;
}

.entity-info .monster-name:hover {
  color: #ff8a8a;
}

/* 位置行显示样式 */
.location-row-container {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  min-height: 40px;
}

.location-row-single {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 12px;
}

.location-info {
  flex: 1;
  min-width: 0;
  cursor: pointer;
  text-align: center;
}

.location-info.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.location-info .location-name {
  font-size: 14px;
  font-weight: bold;
  color: #ffd700;
  cursor: pointer;
  transition: color 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.location-info .location-name:hover {
  color: #87ceeb;
}

.location-info.disabled .location-name {
  color: #666;
}

.location-info.disabled .location-name:hover {
  color: #666;
}

</style>

