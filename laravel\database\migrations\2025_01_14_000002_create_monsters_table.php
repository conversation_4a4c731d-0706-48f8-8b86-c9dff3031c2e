<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monsters', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('怪物名称');
            $table->string('title')->nullable()->comment('称号');
            $table->text('description')->nullable()->comment('描述');
            $table->string('avatar')->nullable()->comment('头像路径');
            $table->integer('level')->default(1)->comment('等级');
            $table->enum('type', ['beast', 'demon', 'spirit', 'undead', 'dragon', 'immortal', 'elemental', 'other'])->default('other')->comment('怪物类型');
            $table->enum('element', ['none', 'fire', 'water', 'earth', 'wind', 'thunder', 'ice', 'light', 'dark'])->default('none')->comment('属性');
            $table->enum('size', ['tiny', 'small', 'medium', 'large', 'huge', 'giant'])->default('medium')->comment('体型');
            
            // 位置信息
            $table->foreignId('location_id')->constrained('locations')->onDelete('cascade')->comment('所在位置');
            $table->integer('x')->default(0)->comment('位置X坐标');
            $table->integer('y')->default(0)->comment('位置Y坐标');
            $table->integer('spawn_radius')->default(50)->comment('刷新半径');
            
            // 基础属性
            $table->integer('max_health')->default(100)->comment('最大生命值');
            $table->integer('current_health')->default(100)->comment('当前生命值');
            $table->integer('max_mana')->default(50)->comment('最大法力值');
            $table->integer('current_mana')->default(50)->comment('当前法力值');
            
            // 战斗属性
            $table->integer('attack')->default(10)->comment('攻击力');
            $table->integer('defense')->default(10)->comment('防御力');
            $table->integer('magic_attack')->default(5)->comment('法术攻击');
            $table->integer('magic_defense')->default(5)->comment('法术防御');
            $table->integer('speed')->default(10)->comment('速度');
            $table->integer('accuracy')->default(80)->comment('命中率');
            $table->integer('dodge')->default(10)->comment('闪避率');
            $table->integer('critical')->default(5)->comment('暴击率');
            
            // AI和行为
            $table->enum('ai_type', ['passive', 'aggressive', 'defensive', 'patrol', 'guard'])->default('aggressive')->comment('AI类型');
            $table->integer('aggro_range')->default(100)->comment('仇恨范围');
            $table->integer('chase_range')->default(200)->comment('追击范围');
            $table->json('skills')->nullable()->comment('技能列表');
            $table->json('resistances')->nullable()->comment('抗性');
            
            // 刷新和状态
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->boolean('is_alive')->default(true)->comment('是否存活');
            $table->boolean('is_boss')->default(false)->comment('是否为BOSS');
            $table->integer('respawn_time')->default(300)->comment('重生时间(秒)');
            $table->timestamp('last_death_time')->nullable()->comment('最后死亡时间');
            $table->timestamp('next_respawn_time')->nullable()->comment('下次重生时间');
            
            // 掉落和奖励
            $table->json('drop_items')->nullable()->comment('掉落物品配置');
            $table->integer('exp_reward')->default(10)->comment('经验奖励');
            $table->integer('silver_reward')->default(5)->comment('银两奖励');
            $table->decimal('drop_rate', 5, 2)->default(50.00)->comment('掉落率(%)');
            
            // 特殊属性
            $table->json('special_abilities')->nullable()->comment('特殊能力');
            $table->json('immunities')->nullable()->comment('免疫效果');
            $table->integer('threat_level')->default(1)->comment('威胁等级');
            
            $table->timestamps();
            
            // 索引
            $table->index(['location_id', 'is_active', 'is_alive']);
            $table->index(['level', 'type']);
            $table->index(['is_boss', 'level']);
            $table->index(['next_respawn_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monsters');
    }
};
