/**
 * 任务系统API服务
 * 提供任务相关的接口调用
 */
import { get, post } from '../request.js';
import logger from '../../utils/logger.js';

/**
 * 任务服务
 */
const questService = {
    /**
     * 获取角色任务列表
     * @param {string} characterId - 角色ID
     * @param {Object} params - 查询参数
     * @param {string} params.status - 任务状态筛选 (available/in_progress/completed/finished)
     * @param {string} params.type - 任务类型筛选
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise<Object>} - 任务列表
     */
    getQuests(characterId, params = {}) {
        logger.debug('[QuestService] 获取任务列表, characterId:', characterId, 'params:', params);
        
        return get(`/characters/${characterId}/quests`, params, {
            loading: true,
            loadingText: '加载任务列表...'
        }).then(res => {
            logger.debug('[QuestService] 任务列表响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 获取任务列表失败:', error);
            throw error;
        });
    },

    /**
     * 获取任务详情
     * @param {string} questId - 任务ID
     * @returns {Promise<Object>} - 任务详情
     */
    getQuestDetails(questId) {
        logger.debug('[QuestService] 获取任务详情, questId:', questId);
        
        return get(`/quests/${questId}`, {}, {
            loading: true,
            loadingText: '加载任务详情...'
        }).then(res => {
            logger.debug('[QuestService] 任务详情响应:', res);
            // 缓存结果
            cacheService.set(`${CACHE_KEYS.QUEST_DETAILS}_${questId}`, res.data, 600); // 缓存10分钟
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 获取任务详情失败:', error);
            throw error;
        });
    },

    /**
     * 接受任务
     * @param {string} characterId - 角色ID
     * @param {string} questId - 任务ID
     * @returns {Promise<Object>} - 接受结果
     */
    acceptQuest(characterId, questId) {
        logger.debug('[QuestService] 接受任务, characterId:', characterId, 'questId:', questId);
        
        return post(`/characters/${characterId}/quests/${questId}/accept`, {}, {
            loading: true,
            loadingText: '接受任务中...'
        }).then(res => {
            logger.debug('[QuestService] 接受任务响应:', res);
            // 清除任务列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 接受任务失败:', error);
            throw error;
        });
    },

    /**
     * 完成任务
     * @param {string} characterId - 角色ID
     * @param {string} questId - 任务ID
     * @returns {Promise<Object>} - 完成结果
     */
    completeQuest(characterId, questId) {
        logger.debug('[QuestService] 完成任务, characterId:', characterId, 'questId:', questId);
        
        return post(`/characters/${characterId}/quests/${questId}/complete`, {}, {
            loading: true,
            loadingText: '完成任务中...'
        }).then(res => {
            logger.debug('[QuestService] 完成任务响应:', res);
            // 清除任务列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 完成任务失败:', error);
            throw error;
        });
    },

    /**
     * 放弃任务
     * @param {string} characterId - 角色ID
     * @param {string} questId - 任务ID
     * @returns {Promise<Object>} - 放弃结果
     */
    abandonQuest(characterId, questId) {
        logger.debug('[QuestService] 放弃任务, characterId:', characterId, 'questId:', questId);
        
        return post(`/characters/${characterId}/quests/${questId}/abandon`, {}, {
            loading: true,
            loadingText: '放弃任务中...'
        }).then(res => {
            logger.debug('[QuestService] 放弃任务响应:', res);
            // 清除任务列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 放弃任务失败:', error);
            throw error;
        });
    },

    /**
     * 提交任务物品
     * @param {string} characterId - 角色ID
     * @param {string} questId - 任务ID
     * @param {Array} items - 提交的物品列表
     * @returns {Promise<Object>} - 提交结果
     */
    submitQuestItems(characterId, questId, items) {
        logger.debug('[QuestService] 提交任务物品, characterId:', characterId, 'questId:', questId, 'items:', items);
        
        return post(`/characters/${characterId}/quests/${questId}/submit-items`, {
            items
        }, {
            loading: true,
            loadingText: '提交物品中...'
        }).then(res => {
            logger.debug('[QuestService] 提交任务物品响应:', res);
            // 清除任务列表缓存
            this.clearCache(characterId);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 提交任务物品失败:', error);
            throw error;
        });
    },

    /**
     * 获取可接受的任务列表
     * @param {string} characterId - 角色ID
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} - 可接受任务列表
     */
    getAvailableQuests(characterId, params = {}) {
        logger.debug('[QuestService] 获取可接受任务, characterId:', characterId, 'params:', params);
        
        return get(`/characters/${characterId}/quests/available`, params, {
            loading: true,
            loadingText: '加载可接受任务...'
        }).then(res => {
            logger.debug('[QuestService] 可接受任务响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 获取可接受任务失败:', error);
            throw error;
        });
    },

    /**
     * 获取任务进度
     * @param {string} characterId - 角色ID
     * @param {string} questId - 任务ID
     * @returns {Promise<Object>} - 任务进度
     */
    getQuestProgress(characterId, questId) {
        logger.debug('[QuestService] 获取任务进度, characterId:', characterId, 'questId:', questId);
        
        return get(`/characters/${characterId}/quests/${questId}/progress`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[QuestService] 任务进度响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 获取任务进度失败:', error);
            throw error;
        });
    },

    /**
     * 获取任务奖励预览
     * @param {string} questId - 任务ID
     * @returns {Promise<Object>} - 任务奖励
     */
    getQuestRewards(questId) {
        logger.debug('[QuestService] 获取任务奖励, questId:', questId);
        
        return get(`/quests/${questId}/rewards`, {}, {
            loading: false
        }).then(res => {
            logger.debug('[QuestService] 任务奖励响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 获取任务奖励失败:', error);
            throw error;
        });
    },

    /**
     * 获取每日任务
     * @param {string} characterId - 角色ID
     * @returns {Promise<Object>} - 每日任务列表
     */
    getDailyQuests(characterId) {
        logger.debug('[QuestService] 获取每日任务, characterId:', characterId);
        
        return get(`/characters/${characterId}/quests/daily`, {}, {
            loading: true,
            loadingText: '加载每日任务...'
        }).then(res => {
            logger.debug('[QuestService] 每日任务响应:', res);
            return res.data;
        }).catch(error => {
            logger.error('[QuestService] 获取每日任务失败:', error);
            throw error;
        });
    },

    /**
     * 清除任务相关的缓存
     * @param {string} characterId - 角色ID
     */
    clearCache(characterId) {
        if (characterId) {
            cacheService.removeByPrefix(`${CACHE_KEYS.QUEST_LIST}_${characterId}`);
        } else {
            // 清除所有任务缓存
            cacheService.removeByPrefix(CACHE_KEYS.QUEST_LIST);
            cacheService.removeByPrefix(CACHE_KEYS.QUEST_DETAILS);
        }
    }
};

export default questService;
