<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\CharacterController;
use App\Http\Controllers\Admin\MonsterController;
use App\Http\Controllers\Admin\ItemController;
use App\Http\Controllers\Admin\QuestController;
use App\Http\Controllers\Admin\BattleController;
use App\Http\Controllers\Admin\MapController;
use App\Http\Controllers\Admin\BankController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\SkillController;
use App\Http\Controllers\Admin\ApiController;
use App\Http\Controllers\Admin\LocationConnectionController;

/*
|--------------------------------------------------------------------------
| 管理后台路由
|--------------------------------------------------------------------------
|
| 这里配置所有管理后台的路由
|
*/

// 后台路由前缀
$adminPrefix = config('admin.prefix', 'szsg-admin');

// 管理员认证路由
Route::group(['prefix' => $adminPrefix, 'as' => 'admin.'], function () {
    // 登录相关路由（无需认证）
    Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('login', [AuthController::class, 'login'])->name('login.post');

    // 需要管理员权限的路由
    Route::middleware('admin')->group(function () {
        // 登出
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');

        // 修改密码
        Route::get('change-password', [AuthController::class, 'showChangePasswordForm'])->name('change-password');
        Route::post('change-password', [AuthController::class, 'changePassword'])->name('change-password.post');

        // 后台首页
        Route::get('/', [DashboardController::class, 'index'])->name('admin');
        Route::get('admin', [DashboardController::class, 'index'])->name('admin');
        Route::get('stats', [DashboardController::class, 'getStats'])->name('stats');

        // 用户管理
        Route::resource('users', UserController::class);

        // 角色管理
        Route::resource('characters', CharacterController::class);

        // 怪物管理
        Route::resource('monsters', MonsterController::class);

        // 物品管理
        Route::resource('items', ItemController::class);

        // 技能管理
        Route::resource('skills', SkillController::class);

        // 任务管理
        Route::resource('quests', QuestController::class);

        // 战斗日志
        Route::resource('battles', BattleController::class)->only(['index', 'show']);

        // 地图管理
        Route::resource('maps', MapController::class);
        Route::post('maps/{map}/locations', [MapController::class, 'saveLocations'])->name('maps.locations.save');
        Route::get('maps/{map}/locations/create', [MapController::class, 'createLocation'])->name('maps.locations.create');

        // 位置连接管理
        Route::resource('location-connections', LocationConnectionController::class);
        Route::post('location-connections/batch-create', [LocationConnectionController::class, 'createBidirectional'])->name('location-connections.batch-create');

        // API路由
        Route::prefix('api')->name('api.')->group(function () {
            Route::get('locations', [ApiController::class, 'getLocations'])->name('locations');
        });

        // 钱庄管理
        Route::get('bank', [BankController::class, 'index'])->name('bank');
        Route::get('bank/transactions', [BankController::class, 'transactions'])->name('bank.transactions');

        // 系统设置
        Route::get('settings/basic', [SettingController::class, 'basic'])->name('settings.basic');
        Route::post('settings/basic', [SettingController::class, 'saveBasic'])->name('settings.basic.save');
        Route::get('settings/game', [SettingController::class, 'game'])->name('settings.game');
        Route::post('settings/game', [SettingController::class, 'saveGame'])->name('settings.game.save');

        // 管理员管理
        Route::resource('administrators', AdminController::class);
    });
});
