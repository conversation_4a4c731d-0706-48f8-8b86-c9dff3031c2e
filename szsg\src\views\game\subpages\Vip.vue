<template>
  <GameLayout>
    <div class="vip-page">
      <!-- 返回按钮 -->
      <div class="back-button-container">
        <div class="back-button" @click="goBack">
          <img src="/static/game/UI/anniu/fhui_.png" alt="返回" />
        </div>
      </div>
      
      <!-- 加载状态显示 -->
      <div class="loading-container" v-if="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
      
      <!-- 错误状态显示 -->
      <div class="error-container" v-else-if="hasError">
        <div class="error-icon">!</div>
        <div class="error-text">{{ errorMessage }}</div>
        <button class="pixel-button gold" @click="retryLoading">重新加载</button>
      </div>
      
      <div v-else>
        <!-- 主标签导航 -->
        <div class="main-tabs">
          <div 
            class="main-tab" 
            :class="{ active: currentMainTab === 'vip' }"
            @click="currentMainTab = 'vip'"
          >
            <span>VIP</span>
          </div>
          <div 
            class="main-tab" 
            :class="{ active: currentMainTab === 'benefits' }"
            @click="currentMainTab = 'benefits'"
          >
            <span>VIP福利</span>
          </div>
          <div 
            class="main-tab" 
            :class="{ active: currentMainTab === 'privileges' }"
            @click="currentMainTab = 'privileges'"
          >
            <span>VIP特权</span>
          </div>
        </div>
        
        <!-- VIP信息标签内容 -->
        <div v-if="currentMainTab === 'vip'" class="tab-content">
          <div class="vip-info pixel-border-box">
            <div class="vip-info-header">
              <img :src="vipIcon" class="vip-icon" :alt="'VIP' + vipLevel" />
            </div>
            
            <div class="vip-exp-section">
              <div class="vip-exp">
                当前经验：{{ vipExp }} / {{ vipNextExp }}
                <span v-if="vipLevel < 20" class="vip-next-exp">
                  ，还需 <span class="highlight-text">{{ vipNextExp - vipExp }}</span> 经验升级到 VIP{{ vipLevel + 1 }}
                </span>
                <span v-else class="vip-next-exp">（已满级）</span>
              </div>
              <div class="vip-progress-container">
                <div class="vip-progress-bar">
                  <div class="vip-progress" :style="{width: vipPercent + '%'}"></div>
                  <div class="progress-stars">
                    <span class="progress-star" v-for="n in 5" :key="n" :style="{left: (n * 20 - 10) + '%'}">✦</span>
                  </div>
                </div>
                <div class="vip-progress-text">{{ vipPercent }}%</div>
              </div>
            </div>
            
            <button v-if="canClaimReward" class="pixel-button gold shine-effect" @click="claimVipReward" :disabled="isClaiming">
              <span v-if="isClaiming">领取中...</span>
              <span v-else>领取VIP{{ vipLevel }}奖励</span>
            </button>
          </div>
          
          <div class="vip-reward-history pixel-border-box" v-if="vipRewardHistory.length">
            <div class="section-header">
              <div class="vip-title-with-icon">
                <img :src="vipIcon" class="vip-small-icon" alt="VIP图标" />
                <h3>VIP成长奖励历史</h3>
              </div>
              <div class="section-decor"></div>
            </div>
            <div class="history-list">
              <div v-for="(reward, idx) in vipRewardHistory" :key="idx" class="history-item">
                <div class="history-info">
                  <div class="history-vip-badge">
                    <img :src="getVipIcon(reward.level)" class="history-vip-icon" :alt="'VIP' + reward.level" />
                  </div>
                  <span class="history-date">{{ reward.date || '未领取' }}</span>
                  <span class="history-desc">{{ reward.desc }}</span>
                </div>
                <button 
                  v-if="!reward.date" 
                  class="pixel-button small gold shine-effect" 
                  @click="claimHistoryReward(reward.level)" 
                  :disabled="isClaiming"
                >
                  补领
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- VIP福利标签内容 -->
        <div v-else-if="currentMainTab === 'benefits'" class="tab-content">
          <div class="vip-daily-reward pixel-border-box">
            <div class="section-header">
              <div class="vip-title-with-icon">
                <img :src="vipIcon" class="vip-small-icon" alt="VIP图标" />
                <h3>VIP福利</h3>
              </div>
              <div class="section-decor"></div>
            </div>
            
            <!-- 添加横向选择标签 -->
            <div class="reward-tabs">
              <div 
                class="reward-tab" 
                :class="{ active: currentRewardTab === 'daily' }"
                @click="currentRewardTab = 'daily'"
              >
                每日福利
              </div>
              <div 
                class="reward-tab" 
                :class="{ active: currentRewardTab === 'weekly' }"
                @click="currentRewardTab = 'weekly'"
              >
                每周福利
              </div>
              <div 
                class="reward-tab" 
                :class="{ active: currentRewardTab === 'monthly' }"
                @click="currentRewardTab = 'monthly'"
              >
                每月福利
              </div>
            </div>
            
            <!-- 每日福利内容 -->
            <div class="daily-reward-content" v-if="currentRewardTab === 'daily'">
              <div class="reward-info-card">
                <ul class="reward-list">
                  <li class="reward-list-item">
                    <span class="reward-name">金砖</span>
                    <span class="reward-amount">x{{ vipLevel * 10 }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">银两</span>
                    <span class="reward-amount">x{{ vipLevel * 1000 }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">体力</span>
                    <span class="reward-amount">x{{ vipLevel * 5 }}</span>
                  </li>
                </ul>
              </div>
              <button 
                class="pixel-button" 
                :class="canClaimDaily ? 'gold shine-effect' : 'disabled'"
                @click="claimDailyReward" 
                :disabled="!canClaimDaily || isClaiming"
              >
                <span v-if="isClaiming">领取中...</span>
                <span v-else-if="canClaimDaily">领取每日福利</span>
                <span v-else>今日已领取</span>
              </button>
            </div>
            
            <!-- 每周福利内容 -->
            <div class="daily-reward-content" v-else-if="currentRewardTab === 'weekly'">
              <div class="reward-info-card">
                <ul class="reward-list">
                  <li class="reward-list-item">
                    <span class="reward-name">金砖</span>
                    <span class="reward-amount">x{{ vipLevel * 30 }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">银两</span>
                    <span class="reward-amount">x{{ vipLevel * 3000 }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">高级装备箱</span>
                    <span class="reward-amount">x{{ Math.max(1, Math.floor(vipLevel/2)) }}</span>
                  </li>
                </ul>
              </div>
              <button 
                class="pixel-button" 
                :class="canClaimWeekly ? 'gold shine-effect' : 'disabled'"
                @click="claimWeeklyReward" 
                :disabled="!canClaimWeekly || isClaiming"
              >
                <span v-if="isClaiming">领取中...</span>
                <span v-else-if="canClaimWeekly">领取每周福利</span>
                <span v-else>本周已领取</span>
              </button>
            </div>
            
            <!-- 每月福利内容 -->
            <div class="daily-reward-content" v-else>
              <div class="reward-info-card">
                <ul class="reward-list">
                  <li class="reward-list-item">
                    <span class="reward-name">金砖</span>
                    <span class="reward-amount">x{{ vipLevel * 100 }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">银两</span>
                    <span class="reward-amount">x{{ vipLevel * 10000 }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">稀有装备箱</span>
                    <span class="reward-amount">x{{ Math.max(1, Math.floor(vipLevel/3)) }}</span>
                  </li>
                  <li class="reward-list-item">
                    <span class="reward-name">仙将碎片</span>
                    <span class="reward-amount">x{{ vipLevel * 5 }}</span>
                  </li>
                </ul>
              </div>
              <button 
                class="pixel-button" 
                :class="canClaimMonthly ? 'gold shine-effect' : 'disabled'"
                @click="claimMonthlyReward" 
                :disabled="!canClaimMonthly || isClaiming"
              >
                <span v-if="isClaiming">领取中...</span>
                <span v-else-if="canClaimMonthly">领取每月福利</span>
                <span v-else>本月已领取</span>
              </button>
            </div>
          </div>
        </div>
        
        <!-- VIP特权标签内容 -->
        <div v-else class="tab-content">
          <div class="vip-privileges pixel-border-box">
            <div class="section-header">
              <div class="vip-title-with-icon">
                <img :src="vipIcon" class="vip-small-icon" alt="VIP图标" />
                <h3>VIP{{ vipLevel }}特权说明</h3>
              </div>
              <div class="section-decor"></div>
            </div>
            <ul class="privilege-list">
              <li v-for="(desc, idx) in vipPrivileges" :key="idx" class="privilege-item">
                <i class="privilege-icon">✓</i>
                <span>{{ desc }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/layouts/GameLayout.vue'
import { getVipInfo, claimVipReward, claimHistoryReward, claimDailyReward, claimWeeklyReward, claimMonthlyReward } from '@/api/services/vipService'
import logger from '@/utils/logger'

export default {
  name: 'Vip',
  components: { GameLayout },
  data() {
    return {
      vipLevel: 0,
      vipExp: 0,
      vipNextExp: 100,
      isClaiming: false,
      canClaimReward: false,
      vipPrivileges: [],
      vipRewardHistory: [],
      canClaimDaily: false,
      canClaimWeekly: false,
      canClaimMonthly: false,
      selectedVipLevel: null,
      loading: true,
      hasError: false,
      errorMessage: '加载数据失败，请稍后重试',
      currentRewardTab: 'daily',
      currentMainTab: 'vip'
    }
  },
  computed: {
    vipIcon() {
      return `/static/game/UI/vip/${this.vipLevel}.png`
    },
    vipPercent() {
      return Math.min(100, Math.round((this.vipExp / this.vipNextExp) * 100))
    }
  },
  methods: {
    goBack() {
      this.$router.push('/game/main');
    },
    getVipIcon(level) {
      return `/static/game/UI/vip/${level}.png`
    },
    async fetchVipData() {
      this.loading = true
      this.hasError = false
      
      try {
        const data = await getVipInfo()
        this.vipLevel = data.level
        this.vipExp = data.exp
        this.vipNextExp = data.next_exp
        this.canClaimReward = data.can_claim_reward
        this.vipPrivileges = data.privileges || []
        this.vipRewardHistory = data.reward_history || []
        this.canClaimDaily = data.can_claim_daily
        this.canClaimWeekly = data.can_claim_weekly
        this.canClaimMonthly = data.can_claim_monthly
        logger.info('VIP信息加载成功', { level: this.vipLevel, exp: this.vipExp })
      } catch (e) {
        this.$toast && this.$toast('获取VIP信息失败')
        logger.error('获取VIP信息失败', e)
        this.hasError = true
        this.errorMessage = '获取VIP信息失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    async retryLoading() {
      this.hasError = false
      this.loading = true
      
      try {
        // 不再需要并行加载VIP等级数据
        await this.fetchVipData()
      } catch (e) {
        logger.error('VIP页面重新加载失败', e)
        this.hasError = true
        this.errorMessage = '加载数据失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    async claimVipReward() {
      if (this.isClaiming) return
      
      this.isClaiming = true
      try {
        await claimVipReward()
        this.$toast && this.$toast('领取成功', 'success')
        await this.fetchVipData()
      } catch (e) {
        this.$toast && this.$toast('领取失败')
        logger.error('领取VIP奖励失败', e)
      } finally {
        this.isClaiming = false
      }
    },
    async claimHistoryReward(level) {
      if (this.isClaiming) return
      
      this.isClaiming = true
      try {
        await claimHistoryReward(level)
        this.$toast && this.$toast('补领成功', 'success')
        await this.fetchVipData()
      } catch (e) {
        this.$toast && this.$toast('补领失败')
        logger.error('补领历史奖励失败', e)
      } finally {
        this.isClaiming = false
      }
    },
    async claimDailyReward() {
      if (this.isClaiming || !this.canClaimDaily) return
      
      this.isClaiming = true
      try {
        await claimDailyReward()
        this.$toast && this.$toast('领取成功', 'success')
        await this.fetchVipData()
      } catch (e) {
        this.$toast && this.$toast('领取失败')
        logger.error('领取每日奖励失败', e)
      } finally {
        this.isClaiming = false
      }
    },
    async claimWeeklyReward() {
      if (this.isClaiming || !this.canClaimWeekly) return
      
      this.isClaiming = true
      try {
        await claimWeeklyReward()
        this.$toast && this.$toast('领取成功', 'success')
        await this.fetchVipData()
      } catch (e) {
        this.$toast && this.$toast('领取失败')
        logger.error('领取每周奖励失败', e)
      } finally {
        this.isClaiming = false
      }
    },
    async claimMonthlyReward() {
      if (this.isClaiming || !this.canClaimMonthly) return
      
      this.isClaiming = true
      try {
        await claimMonthlyReward()
        this.$toast && this.$toast('领取成功', 'success')
        await this.fetchVipData()
      } catch (e) {
        this.$toast && this.$toast('领取失败')
        logger.error('领取每月奖励失败', e)
      } finally {
        this.isClaiming = false
      }
    },
    showVipDetails(level) {
      this.selectedVipLevel = level
      // 找到对应等级的VIP信息
      const levelInfo = this.vipLevels.find(item => item.level === level)
      if (levelInfo) {
        this.$toast && this.$toast(`VIP${level}特权: ${levelInfo.privileges.join('、')}`, 'info')
      } else {
        this.$toast && this.$toast(`VIP${level}特权详情`, 'info')
      }
    }
  },
  async mounted() {
    try {
      // 并行加载数据以提高性能
      await Promise.all([
        this.fetchVipData()
      ])
    } catch (e) {
      logger.error('VIP页面初始化失败', e)
      // 设置错误状态
      this.hasError = true
      this.errorMessage = '加载数据失败，请稍后重试'
      // 确保无论如何都结束加载状态
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.vip-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  color: #ffd700;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  position: relative;
}

.back-button-container {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 100%;
  max-width: 800px;
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.back-button {
  cursor: pointer;
  transition: all 0.2s ease;
  width: 140px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  &:hover {
    transform: scale(1.05);
    filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// 添加设备适配变量
:root {
  --main-tab-padding: 12px 20px;
  --main-tab-margin: 0 10px;
  --main-tab-min-width: 100px;
  --main-tab-font-size: 16px;
  --section-padding: 20px;
  --vip-icon-size: 120px;
  --reward-tab-padding: 10px 20px;
  --reward-tab-min-width: 90px;
}

.main-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 10px;
  border: 2px solid #444;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.main-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--main-tab-padding);
  margin: var(--main-tab-margin);
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid #555;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: var(--main-tab-min-width);
  position: relative;
  
  span {
    font-weight: bold;
    font-size: var(--main-tab-font-size);
    color: #ccc;
    transition: color 0.3s ease;
  }
  
  &:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: translateY(-3px);
    
    span {
      color: #fff;
    }
  }
  
  &.active {
    background: linear-gradient(to bottom, rgba(255, 215, 0, 0.2), rgba(184, 134, 11, 0.2));
    border-color: #ffd700;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 25%;
      right: 25%;
      height: 3px;
      background: #ffd700;
      border-radius: 3px;
    }
    
    span {
      color: #ffd700;
    }
  }
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.pixel-border-box {
  border: 2px solid #ffd700;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: var(--section-padding);
  margin-bottom: 25px;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 1px solid #ff8800;
    border-radius: 5px;
    pointer-events: none;
    z-index: 1;
  }
}

.section-header {
  margin-bottom: 20px;
  position: relative;
  text-align: center;
  
  .vip-title-with-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    
    .vip-small-icon {
      width: 30px;
      height: 30px;
    }
  }
  
  h3 {
    color: #ffd700;
    font-size: 20px;
    margin: 0;
    padding-bottom: 10px;
    display: inline-block;
  }
  
  .section-decor {
    height: 2px;
    background: linear-gradient(to right, transparent, #ffd700, transparent);
    margin-top: 5px;
  }
}

.vip-info {
  text-align: center;
  
  .vip-info-header {
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
  }
  
  .vip-icon {
    width: var(--vip-icon-size);
    height: var(--vip-icon-size);
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
    transition: transform 0.3s, filter 0.3s;
    
    &:hover {
      transform: scale(1.05);
      filter: drop-shadow(0 0 15px rgba(255, 215, 0, 1));
    }
  }
}

.vip-exp-section {
  margin: 20px 0;
}

.vip-exp {
  color: #ccc;
  margin-bottom: 15px;
  font-size: 15px;
  
  .vip-next-exp {
    color: #ff8800;
  }
  
  .highlight-text {
    color: #ffd700;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
  }
}

.vip-progress-container {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  .vip-progress-bar {
    flex: 1;
    height: 18px;
    background: #222;
    border: 1px solid #444;
    border-radius: 9px;
    overflow: hidden;
    margin-right: 10px;
    position: relative;
    box-shadow: inset 0 2px 5px rgba(0,0,0,0.5);
  }
  
  .vip-progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ff8800);
    border-radius: 9px;
    transition: width 0.8s cubic-bezier(0.22, 1, 0.36, 1);
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
  }
  
  .progress-stars {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }
  
  .progress-star {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
  }
  
  .vip-progress-text {
    width: 45px;
    text-align: right;
    font-size: 16px;
    color: #ffd700;
    font-weight: bold;
  }
}

.privilege-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.privilege-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px 15px;
  border-radius: 4px;
  border-left: 3px solid #4caf50;
  transition: transform 0.2s, background 0.2s;
  
  &:hover {
    background: rgba(0, 0, 0, 0.4);
    transform: translateX(5px);
  }
  
  .privilege-icon {
    color: #4caf50;
    margin-right: 12px;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(76, 175, 80, 0.2);
    border-radius: 50%;
  }
}

.reward-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  padding: 5px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid #444;
}

.reward-tab {
  padding: var(--reward-tab-padding);
  cursor: pointer;
  background: #222;
  border: 2px solid #444;
  margin: 0 5px;
  transition: all 0.3s ease;
  position: relative;
  font-weight: bold;
  text-align: center;
  min-width: var(--reward-tab-min-width);
  border-radius: 4px;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: transparent;
  }
  
  &:hover {
    border-color: #666;
    transform: translateY(-2px);
  }
  
  &.active {
    background: linear-gradient(to bottom, #ffd700, #b8860b);
    color: #000;
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    
    &::after {
      background: #ffd700;
    }
  }
}

.daily-reward-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.reward-info-card {
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #444;
}

.reward-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.reward-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.2s;
  border-left: 3px solid #ffd700;
  
  &:hover {
    background: rgba(0, 0, 0, 0.4);
  }
  
  .reward-name {
    font-weight: bold;
    color: #ffd700;
  }
  
  .reward-amount {
    color: #ccc;
    font-weight: bold;
  }
}

.history-list {
  max-height: 250px;
  overflow-y: auto;
  padding-right: 5px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
    
    &:hover {
      background: #666;
    }
  }
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.2s;
  
  &:hover {
    background: rgba(0, 0, 0, 0.4);
  }
  
  .history-info {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .history-vip-badge {
      display: flex;
      align-items: center;
      
      .history-vip-icon {
        width: 30px;
        height: 30px;
        filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
      }
    }
    
    .history-date {
      color: #888;
    }
    
    .history-desc {
      flex-basis: 100%;
      color: #ccc;
      margin-top: 5px;
    }
  }
}

.pixel-button {
  background: #333;
  color: #fff;
  border: 2px solid #555;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(1px);
  }
  
  &.gold {
    background: linear-gradient(to bottom, #ffd700, #b8860b);
    border-color: #ffd700;
    color: #000;
    
    &:hover {
      box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
    }
  }
  
  &.disabled {
    background: #444;
    border-color: #555;
    color: #888;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
  
  &.small {
    padding: 5px 12px;
    font-size: 13px;
  }
  
  &.shine-effect::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: shine 3s infinite;
  }
}

@keyframes shine {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}

// 加载和错误状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 5px solid rgba(255, 215, 0, 0.3);
  border-radius: 50%;
  border-top-color: #ffd700;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  color: #ffd700;
  font-size: 18px;
  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  
  .error-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(255, 0, 0, 0.2);
    border: 3px solid #ff6b6b;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    font-weight: bold;
    color: #ff6b6b;
    margin-bottom: 20px;
  }
  
  .error-text {
    color: #ff6b6b;
    font-size: 18px;
    margin-bottom: 25px;
    text-align: center;
  }
}

// 响应式调整 - 大屏幕设备 (1200px以上)
@media (min-width: 1201px) {
  .vip-page {
    max-width: 900px;
    padding: 30px;
  }
  
  .main-tabs {
    padding: 15px;
  }
  
  .main-tab {
    padding: 15px 30px;
    min-width: 120px;
    
    span {
      font-size: 18px;
    }
  }
  
  .vip-info .vip-icon {
    width: 150px;
    height: 150px;
  }
  
  .section-header h3 {
    font-size: 24px;
  }
  
  .pixel-border-box {
    padding: 25px;
  }
}

// 响应式调整 - 平板设备 (768px - 1200px)
@media (min-width: 768px) and (max-width: 1200px) {
  .vip-page {
    padding: 20px;
  }
  
  .main-tabs {
    padding: 12px;
  }
  
  .main-tab {
    padding: 12px 25px;
    min-width: 110px;
  }
  
  .vip-info .vip-icon {
    width: 100px;
    height: 100px;
  }
  
  .pixel-border-box {
    padding: 20px;
  }
}

// 响应式调整 - 手机设备 (600px - 767px)
@media (min-width: 601px) and (max-width: 767px) {
  .vip-page {
    padding: 15px;
  }
  
  .main-tabs {
    padding: 10px;
  }
  
  .main-tab {
    padding: 10px 20px;
    min-width: 90px;
    margin: 0 5px;
    
    span {
      font-size: 15px;
    }
  }
  
  .vip-info .vip-icon {
    width: 90px;
    height: 90px;
  }
  
  .section-header h3 {
    font-size: 19px;
  }
  
  .pixel-border-box {
    padding: 18px;
  }
  
  .reward-tab {
    padding: 8px 15px;
    min-width: 85px;
  }
}

// 响应式调整 - 小屏幕手机设备 (600px以下)
@media (max-width: 600px) {
  .vip-page {
    padding: 12px;
  }
  
  .main-tabs {
    flex-wrap: wrap;
    padding: 8px;
  }
  
  .main-tab {
    padding: 10px;
    margin: 5px;
    min-width: 80px;
    
    span {
      font-size: 14px;
    }
  }
  
  .pixel-border-box {
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .section-header h3 {
    font-size: 18px;
  }
  
  .vip-info .vip-icon {
    width: 70px;
    height: 70px;
  }
  
  .reward-tabs {
    flex-wrap: wrap;
  }
  
  .reward-tab {
    padding: 8px 15px;
    margin: 3px;
    min-width: 80px;
    font-size: 14px;
  }
  
  .reward-list-item {
    padding: 8px 12px;
    
    .reward-name, .reward-amount {
      font-size: 14px;
    }
  }
  
  .privilege-item {
    font-size: 14px;
    padding: 8px 12px;
  }
  
  .vip-exp {
    font-size: 13px;
  }
  
  .vip-progress-container {
    .vip-progress-bar {
      height: 15px;
    }
    
    .vip-progress-text {
      width: 40px;
      font-size: 14px;
    }
  }
  
  .history-item {
    padding: 10px;
    
    .history-info {
      gap: 8px;
      
      .history-vip-icon {
        width: 25px;
        height: 25px;
      }
      
      .history-date, .history-desc {
        font-size: 13px;
      }
    }
  }
  
  .back-button-container {
    bottom: 30px;
  }
  
  .back-button {
    width: 120px;
    height: 45px;
  }
}

// 极小屏幕设备 (375px以下)
@media (max-width: 375px) {
  .vip-page {
    padding: 10px;
  }
  
  .main-tabs {
    padding: 5px;
  }
  
  .main-tab {
    padding: 8px;
    margin: 3px;
    min-width: 70px;
    
    span {
      font-size: 13px;
    }
  }
  
  .pixel-border-box {
    padding: 12px;
  }
  
  .section-header h3 {
    font-size: 16px;
  }
  
  .vip-info .vip-icon {
    width: 60px;
    height: 60px;
  }
  
  .reward-tab {
    padding: 6px 10px;
    min-width: 70px;
    font-size: 13px;
  }
  
  .pixel-button {
    padding: 8px 15px;
    font-size: 13px;
    
    &.small {
      padding: 4px 10px;
      font-size: 12px;
    }
  }
  
  .back-button-container {
    bottom: 25px;
  }
  
  .back-button {
    width: 110px;
    height: 40px;
  }
}
</style>