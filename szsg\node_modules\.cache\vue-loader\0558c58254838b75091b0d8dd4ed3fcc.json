{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue?vue&type=style&index=0&id=f28434cc&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\src\\components\\GameChat.vue", "mtime": 1750348047473}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749535534991}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749535541018}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749535537294}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749535533550}, {"path": "C:\\Users\\<USER>\\Desktop\\szxy\\szsg\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749535538600}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GameChat.vue"], "names": [], "mappings": ";AAgl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file": "GameChat.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n    <div class=\"chat-container\" :class=\"{ 'minimized': minimized, 'compact': compactMode }\">\r\n        <div class=\"chat-header\" @click=\"toggleMinimize\">\r\n            <div class=\"chat-title-section\">\r\n                <span class=\"chat-title\">聊天</span>\r\n                <div class=\"chat-status\" :class=\"{ 'connected': isConnected }\" :title=\"isConnected ? '已连接' : '未连接'\"></div>\r\n            </div>\r\n            <div class=\"chat-controls\">\r\n                <button v-if=\"!minimized\" class=\"control-btn\" @click.stop=\"clearMessages\" title=\"清空消息\">\r\n                    <span class=\"icon\">🗑</span>\r\n                </button>\r\n                <button v-if=\"!minimized\" class=\"control-btn\" @click.stop=\"toggleCompactMode\" title=\"紧凑模式\">\r\n                    <span class=\"icon\">{{ compactMode ? '📖' : '📄' }}</span>\r\n                </button>\r\n                <button class=\"control-btn minimize-btn\" :title=\"minimized ? '展开聊天' : '收起聊天'\">\r\n                    <span class=\"icon\">{{ minimized ? '⬆' : '⬇' }}</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n\r\n        <div v-if=\"!minimized\" class=\"chat-body\">\r\n            <div class=\"chat-channels\">\r\n                <button\r\n                    v-for=\"(channel, index) in channels\"\r\n                    :key=\"index\"\r\n                    class=\"channel-tab\"\r\n                    :class=\"{ active: currentChannelIndex === index }\"\r\n                    :data-icon=\"getChannelIcon(channel.id)\"\r\n                    @click=\"switchChannel(index)\"\r\n                >\r\n                    <span class=\"channel-icon\">{{ getChannelIcon(channel.id) }}</span>\r\n                    <span class=\"channel-name\">{{ channel.name }}</span>\r\n                    <span v-if=\"channel.unread > 0\" class=\"channel-badge\">{{ channel.unread > 99 ? '99+' : channel.unread }}</span>\r\n                </button>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"chatMessagesContainer\">\r\n                <template v-if=\"filteredMessages.length > 0\">\r\n                    <div\r\n                        v-for=\"(msg, index) in filteredMessages\"\r\n                        :key=\"index\"\r\n                        class=\"chat-message\"\r\n                        :class=\"{\r\n                            'system-message': msg.type === 'system',\r\n                            'npc-message': msg.type === 'npc',\r\n                            'player-message': msg.type === 'player',\r\n                            'self-message': msg.isSelf,\r\n                            'compact': compactMode\r\n                        }\"\r\n                    >\r\n                        <div v-if=\"msg.type !== 'system'\" class=\"message-header\">\r\n                            <span class=\"message-sender\" @click=\"handleSenderClick(msg)\">{{ msg.sender }}</span>\r\n                            <span v-if=\"msg.timestamp && !compactMode\" class=\"message-time\">{{ formatTime(msg.timestamp) }}</span>\r\n                        </div>\r\n                        <div class=\"message-content\">{{ msg.content }}</div>\r\n                        <div v-if=\"msg.timestamp && compactMode\" class=\"message-time-compact\">{{ formatTime(msg.timestamp) }}</div>\r\n                    </div>\r\n                </template>\r\n                <div v-else class=\"empty-messages\">\r\n                    <div class=\"empty-icon\">💬</div>\r\n                    <div class=\"empty-text\">暂无消息</div>\r\n                    <div class=\"empty-hint\">在下方输入框发送消息开始聊天</div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input-container\">\r\n                <div class=\"input-wrapper\">\r\n                    <input\r\n                        class=\"chat-input\"\r\n                        v-model=\"newMessage\"\r\n                        :placeholder=\"getInputPlaceholder()\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        @focus=\"onInputFocus\"\r\n                        @blur=\"onInputBlur\"\r\n                        :disabled=\"!isConnected\"\r\n                        maxlength=\"200\"\r\n                    />\r\n                    <div class=\"input-counter\" v-if=\"newMessage.length > 150\">{{ newMessage.length }}/200</div>\r\n                </div>\r\n                <button\r\n                    class=\"send-btn\"\r\n                    @click=\"sendMessage\"\r\n                    :disabled=\"!isConnected || !newMessage.trim()\"\r\n                    :title=\"!isConnected ? '未连接到聊天服务器' : '发送消息'\"\r\n                >\r\n                    <span class=\"send-icon\">📤</span>\r\n                    <span class=\"send-text\">发送</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport socketManager from '../utils/socketManager';\r\n\r\n// 聊天频道配置\r\nconst CHANNELS = [\r\n    { id: 'world', name: '世界', unread: 0 },\r\n    { id: 'trade', name: '交易', unread: 0 },\r\n    { id: 'team', name: '队伍', unread: 0 },\r\n    { id: 'private', name: '私聊', unread: 0 }\r\n];\r\n\r\nexport default {\r\n    name: 'GameChat',\r\n    props: {\r\n        characterInfo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        autoConnect: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        initialMinimized: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            isConnected: false,\r\n            channels: [...CHANNELS],\r\n            currentChannelIndex: 0,\r\n            messages: [],\r\n            newMessage: '',\r\n            minimized: this.initialMinimized,\r\n            reconnecting: false,\r\n            compactMode: false,\r\n            inputFocused: false,\r\n            lastMessageTime: null,\r\n            messageHistory: [], // 消息历史记录，用于上下键切换\r\n            historyIndex: -1\r\n        };\r\n    },\r\n    computed: {\r\n        currentChannel() {\r\n            return this.channels[this.currentChannelIndex]?.id || 'world';\r\n        },\r\n        filteredMessages() {\r\n            // 如果是私聊频道，显示所有私聊消息\r\n            if (this.currentChannel === 'private') {\r\n                return this.messages.filter(msg => msg.channel === 'private');\r\n            }\r\n            \r\n            // 如果是队伍频道，根据characterInfo中的teamId过滤队伍消息\r\n            if (this.currentChannel === 'team' && this.characterInfo.teamId) {\r\n                return this.messages.filter(msg => \r\n                    msg.channel === 'team' && \r\n                    msg.teamId === this.characterInfo.teamId\r\n                );\r\n            }\r\n            \r\n            // 否则只显示当前频道的消息\r\n            return this.messages.filter(msg => msg.channel === this.currentChannel);\r\n        }\r\n    },\r\n    watch: {\r\n        characterInfo: {\r\n            handler(newInfo) {\r\n                if (newInfo && newInfo.id && this.autoConnect) {\r\n                    this.connectChat();\r\n                }\r\n            },\r\n            immediate: true\r\n        },\r\n        currentChannelIndex() {\r\n            // 切换频道时，重置未读消息数\r\n            if (this.channels[this.currentChannelIndex]) {\r\n                this.channels[this.currentChannelIndex].unread = 0;\r\n            }\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        }\r\n    },\r\n    mounted() {\r\n        // 当前组件已挂载\r\n        if (this.characterInfo?.id && this.autoConnect) {\r\n            this.connectChat();\r\n        }\r\n    },\r\n    beforeUnmount() {\r\n        // 组件销毁前，移除所有事件监听器\r\n        this.disconnectChat();\r\n    },\r\n    methods: {\r\n        async connectChat() {\r\n            if (!this.characterInfo?.id) {\r\n                console.error('[GameChat] 无法连接聊天，缺少角色信息');\r\n                return;\r\n            }\r\n            \r\n            try {\r\n                // 初始化Socket连接\r\n                await socketManager.init();\r\n                \r\n                // 添加事件监听\r\n                this.setupEventListeners();\r\n                \r\n                // 加入相关频道\r\n                this.joinChannels();\r\n                \r\n                // 设置连接状态\r\n                this.isConnected = true;\r\n                this.reconnecting = false;\r\n                \r\n                // 添加系统消息\r\n                this.addSystemMessage('已连接到聊天服务器');\r\n                \r\n            } catch (error) {\r\n                console.error('[GameChat] 连接失败:', error);\r\n                \r\n                this.isConnected = false;\r\n                if (!this.reconnecting) {\r\n                    this.reconnecting = true;\r\n                    this.addSystemMessage('连接失败，正在尝试重新连接...');\r\n                }\r\n            }\r\n        },\r\n        \r\n        disconnectChat() {\r\n            // 移除事件监听\r\n            if (this.unsubscribers) {\r\n                this.unsubscribers.forEach(unsubscribe => unsubscribe());\r\n                this.unsubscribers = [];\r\n            }\r\n            \r\n            // 断开socket连接\r\n            socketManager.disconnect();\r\n            \r\n            // 更新状态\r\n            this.isConnected = false;\r\n        },\r\n        \r\n        setupEventListeners() {\r\n            // 存储所有取消订阅的函数\r\n            this.unsubscribers = [];\r\n            \r\n            // 监听连接事件\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('connect', () => {\r\n                    this.isConnected = true;\r\n                    this.reconnecting = false;\r\n                })\r\n            );\r\n            \r\n            // 监听断开连接事件\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('disconnect', (reason) => {\r\n                    this.isConnected = false;\r\n                    this.addSystemMessage(`连接已断开 (${reason})`);\r\n                })\r\n            );\r\n            \r\n            // 世界消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('world_message', (data) => {\r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'world',\r\n                        sender: data.sender.name,\r\n                        content: data.message,\r\n                        timestamp: data.timestamp,\r\n                        sender_id: data.sender.id,\r\n                        isSelf: data.sender.id === this.characterInfo?.id\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 队伍消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('team_message', (data) => {\r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'team',\r\n                        teamId: data.team_id,\r\n                        sender: data.message.sender.name,\r\n                        content: data.message.message,\r\n                        timestamp: data.message.timestamp,\r\n                        sender_id: data.message.sender.id,\r\n                        isSelf: data.message.sender.id === this.characterInfo?.id\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 私聊消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('private_message', (data) => {\r\n                    const isSelf = data.sender_id === this.characterInfo?.id;\r\n                    const sender = isSelf ? this.characterInfo?.name : data.message.sender.name;\r\n                    const receiverId = isSelf ? data.receiver_id : data.sender_id;\r\n                    \r\n                    this.handleChatMessage({\r\n                        type: 'player',\r\n                        channel: 'private',\r\n                        sender,\r\n                        content: data.message.message,\r\n                        timestamp: data.message.timestamp,\r\n                        sender_id: data.message.sender.id,\r\n                        receiver_id: receiverId,\r\n                        isSelf\r\n                    });\r\n                })\r\n            );\r\n            \r\n            // 系统消息\r\n            this.unsubscribers.push(\r\n                socketManager.subscribe('system_message', (data) => {\r\n                    this.addSystemMessage(data.message || '系统通知');\r\n                })\r\n            );\r\n        },\r\n        \r\n        joinChannels() {\r\n            // 加入世界频道\r\n            socketManager.joinChannel('chat.world');\r\n            \r\n            // 如果角色有队伍，加入队伍频道\r\n            if (this.characterInfo.teamId) {\r\n                socketManager.joinChannel(`team.${this.characterInfo.teamId}`);\r\n            }\r\n            \r\n            // 加入角色私聊频道\r\n            socketManager.joinChannel(`chat.user.${this.characterInfo.id}`);\r\n        },\r\n        \r\n        handleChatMessage(messageData) {\r\n            // 添加到消息列表\r\n            this.messages.push(messageData);\r\n            \r\n            // 控制消息列表大小，防止过长\r\n            if (this.messages.length > 200) {\r\n                this.messages = this.messages.slice(-150);\r\n            }\r\n            \r\n            // 如果消息不是当前频道，更新未读计数\r\n            if (messageData.channel !== this.currentChannel) {\r\n                const channelIndex = this.channels.findIndex(c => c.id === messageData.channel);\r\n                if (channelIndex !== -1) {\r\n                    this.channels[channelIndex].unread++;\r\n                }\r\n            }\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n        \r\n        addSystemMessage(content, timestamp = null) {\r\n            this.messages.push({\r\n                type: 'system',\r\n                channel: 'system',\r\n                sender: '系统',\r\n                content,\r\n                timestamp: timestamp || Date.now()\r\n            });\r\n            \r\n            // 滚动到底部\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n        \r\n        switchChannel(index) {\r\n            this.currentChannelIndex = index;\r\n        },\r\n        \r\n        sendMessage() {\r\n            if (!this.isConnected) {\r\n                this.addSystemMessage('未连接到聊天服务器，无法发送消息');\r\n                return;\r\n            }\r\n            \r\n            const message = this.newMessage.trim();\r\n            if (!message) return;\r\n            \r\n            // 获取当前频道\r\n            const channel = this.currentChannel;\r\n            \r\n            // 构建消息数据\r\n            let messageOptions = {\r\n                channel: channel,\r\n                message: message,\r\n                character_id: this.characterInfo.id\r\n            };\r\n            \r\n            switch (channel) {\r\n                case 'team':\r\n                    if (!this.characterInfo.teamId) {\r\n                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');\r\n                        return;\r\n                    }\r\n                    messageOptions.team_id = this.characterInfo.teamId;\r\n                    break;\r\n                    \r\n                case 'private':\r\n                    // 私聊需要指定接收者\r\n                    // TODO: 添加私聊目标选择功能\r\n                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');\r\n                    return;\r\n            }\r\n            \r\n            // 使用socketManager的sendChatMessage方法\r\n            socketManager.sendChatMessage(messageOptions);\r\n            \r\n            // 清空输入框\r\n            this.newMessage = '';\r\n        },\r\n        \r\n        handleSenderClick(msg) {\r\n            // 点击发送者名称\r\n            if (msg.type === 'player' && !msg.isSelf) {\r\n                this.$emit('player-click', {\r\n                    id: msg.sender_id,\r\n                    name: msg.sender\r\n                });\r\n            }\r\n        },\r\n        \r\n        scrollToBottom() {\r\n            const container = this.$refs.chatMessagesContainer;\r\n            if (container) {\r\n                // 注意: uni-app环境中可能需要特殊处理\r\n                if (container.$el) {\r\n                    container.$el.scrollTop = container.$el.scrollHeight;\r\n                } else if (container.scrollTop !== undefined) {\r\n                    container.scrollTop = container.scrollHeight;\r\n                }\r\n            }\r\n        },\r\n        \r\n        toggleMinimize() {\r\n            this.minimized = !this.minimized;\r\n            \r\n            if (!this.minimized) {\r\n                // 展开时，滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n            }\r\n        },\r\n        \r\n        formatTime(timestamp) {\r\n            if (!timestamp) return '';\r\n\r\n            const date = new Date(timestamp);\r\n            const now = new Date();\r\n            const hours = date.getHours().toString().padStart(2, '0');\r\n            const minutes = date.getMinutes().toString().padStart(2, '0');\r\n\r\n            // 如果是今天，只显示时间\r\n            if (date.toDateString() === now.toDateString()) {\r\n                return `${hours}:${minutes}`;\r\n            }\r\n\r\n            // 如果不是今天，显示月日和时间\r\n            const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n            const day = date.getDate().toString().padStart(2, '0');\r\n            return `${month}-${day} ${hours}:${minutes}`;\r\n        },\r\n\r\n        // 新增方法\r\n        toggleCompactMode() {\r\n            this.compactMode = !this.compactMode;\r\n            this.$nextTick(() => {\r\n                this.scrollToBottom();\r\n            });\r\n        },\r\n\r\n        clearMessages() {\r\n            this.messages = [];\r\n            this.addSystemMessage('消息已清空');\r\n        },\r\n\r\n        getInputPlaceholder() {\r\n            const channel = this.currentChannel;\r\n            const placeholders = {\r\n                'world': '对所有人说...',\r\n                'trade': '发布交易信息...',\r\n                'team': '对队友说...',\r\n                'private': '私聊消息...'\r\n            };\r\n            return placeholders[channel] || '输入消息...';\r\n        },\r\n\r\n        onInputFocus() {\r\n            this.inputFocused = true;\r\n        },\r\n\r\n        onInputBlur() {\r\n            this.inputFocused = false;\r\n        },\r\n\r\n        // 处理消息历史记录\r\n        addToHistory(message) {\r\n            if (message && message.trim()) {\r\n                // 移除重复的消息\r\n                const index = this.messageHistory.indexOf(message);\r\n                if (index > -1) {\r\n                    this.messageHistory.splice(index, 1);\r\n                }\r\n\r\n                // 添加到历史记录开头\r\n                this.messageHistory.unshift(message);\r\n\r\n                // 限制历史记录数量\r\n                if (this.messageHistory.length > 20) {\r\n                    this.messageHistory = this.messageHistory.slice(0, 20);\r\n                }\r\n            }\r\n            this.historyIndex = -1;\r\n        },\r\n\r\n        // 获取频道图标\r\n        getChannelIcon(channelId) {\r\n            const icons = {\r\n                'world': '🌍',\r\n                'trade': '💰',\r\n                'team': '👥',\r\n                'private': '💬'\r\n            };\r\n            return icons[channelId] || '💬';\r\n        },\r\n\r\n        // 增强的消息发送\r\n        sendMessage() {\r\n            if (!this.isConnected) {\r\n                this.addSystemMessage('未连接到聊天服务器，无法发送消息');\r\n                return;\r\n            }\r\n\r\n            const message = this.newMessage.trim();\r\n            if (!message) return;\r\n\r\n            // 检查消息长度\r\n            if (message.length > 200) {\r\n                this.addSystemMessage('消息长度不能超过200个字符');\r\n                return;\r\n            }\r\n\r\n            // 防止刷屏（限制发送频率）\r\n            const now = Date.now();\r\n            if (this.lastMessageTime && (now - this.lastMessageTime) < 1000) {\r\n                this.addSystemMessage('发送消息过于频繁，请稍后再试');\r\n                return;\r\n            }\r\n            this.lastMessageTime = now;\r\n\r\n            // 添加到历史记录\r\n            this.addToHistory(message);\r\n\r\n            // 获取当前频道\r\n            const channel = this.currentChannel;\r\n\r\n            // 构建消息数据\r\n            let messageOptions = {\r\n                channel: channel,\r\n                message: message,\r\n                character_id: this.characterInfo.id\r\n            };\r\n\r\n            switch (channel) {\r\n                case 'team':\r\n                    if (!this.characterInfo.teamId) {\r\n                        this.addSystemMessage('你不在队伍中，无法发送队伍消息');\r\n                        return;\r\n                    }\r\n                    messageOptions.team_id = this.characterInfo.teamId;\r\n                    break;\r\n\r\n                case 'private':\r\n                    // 私聊需要指定接收者\r\n                    this.addSystemMessage('私聊功能尚未完全实现，请稍后再试');\r\n                    return;\r\n            }\r\n\r\n            // 使用socketManager的sendChatMessage方法\r\n            socketManager.sendChatMessage(messageOptions);\r\n\r\n            // 清空输入框\r\n            this.newMessage = '';\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.chat-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(20,20,30,0.9) 100%);\r\n    backdrop-filter: blur(15px);\r\n    border-radius: 12px 12px 0 0;\r\n    overflow: hidden;\r\n    height: 420px;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: 1px solid rgba(255,255,255,0.1);\r\n    box-shadow: 0 8px 32px rgba(0,0,0,0.3);\r\n}\r\n\r\n.chat-container.minimized {\r\n    height: 48px;\r\n}\r\n\r\n.chat-container.compact {\r\n    height: 320px;\r\n}\r\n\r\n.chat-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 8px 12px;\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(30,30,40,0.8) 100%);\r\n    border-bottom: 1px solid rgba(255,255,255,0.15);\r\n    height: 48px;\r\n    box-sizing: border-box;\r\n    cursor: pointer;\r\n    user-select: none;\r\n}\r\n\r\n.chat-header:hover {\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(30,30,40,0.9) 100%);\r\n}\r\n\r\n.chat-title-section {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.chat-title {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    font-weight: 600;\r\n    text-shadow: 0 1px 2px rgba(0,0,0,0.5);\r\n}\r\n\r\n.chat-controls {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.control-btn {\r\n    background: rgba(255,255,255,0.1);\r\n    border: none;\r\n    border-radius: 6px;\r\n    width: 28px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    color: #ccc;\r\n}\r\n\r\n.control-btn:hover {\r\n    background: rgba(255,255,255,0.2);\r\n    color: #fff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.control-btn .icon {\r\n    font-size: 12px;\r\n}\r\n\r\n.chat-status {\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #ff4444;\r\n    border-radius: 50%;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 0 8px rgba(255,68,68,0.5);\r\n}\r\n\r\n.chat-status.connected {\r\n    background-color: #4CAF50;\r\n    box-shadow: 0 0 8px rgba(76,175,80,0.5);\r\n}\r\n\r\n.chat-body {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n}\r\n\r\n.chat-channels {\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    background: linear-gradient(90deg, rgba(0,0,0,0.3) 0%, rgba(20,20,30,0.4) 100%);\r\n    border-bottom: 1px solid rgba(255,255,255,0.1);\r\n    padding: 4px;\r\n    gap: 2px;\r\n}\r\n\r\n.channel-tab {\r\n    background: none;\r\n    border: none;\r\n    padding: 8px 16px;\r\n    font-size: 12px;\r\n    color: #aaa;\r\n    border-radius: 6px;\r\n    position: relative;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    min-width: 0;\r\n}\r\n\r\n.channel-tab:hover {\r\n    background: rgba(255,255,255,0.1);\r\n    color: #ddd;\r\n}\r\n\r\n.channel-tab.active {\r\n    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);\r\n    color: #fff;\r\n    box-shadow: 0 2px 8px rgba(232,81,28,0.3);\r\n}\r\n\r\n.channel-name {\r\n    white-space: nowrap;\r\n}\r\n\r\n.channel-badge {\r\n    background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);\r\n    color: white;\r\n    border-radius: 12px;\r\n    padding: 2px 6px;\r\n    font-size: 10px;\r\n    font-weight: 600;\r\n    min-width: 16px;\r\n    height: 16px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    line-height: 1;\r\n    box-shadow: 0 2px 4px rgba(255,68,68,0.3);\r\n}\r\n\r\n.chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 8px 12px;\r\n    background: rgba(0,0,0,0.1);\r\n    scrollbar-width: thin;\r\n    scrollbar-color: rgba(255,255,255,0.3) transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 4px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(255,255,255,0.3);\r\n    border-radius: 2px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(255,255,255,0.5);\r\n}\r\n\r\n.chat-message {\r\n    margin-bottom: 8px;\r\n    padding: 6px 8px;\r\n    border-radius: 8px;\r\n    background: rgba(255,255,255,0.02);\r\n    transition: all 0.2s ease;\r\n    border-left: 3px solid transparent;\r\n}\r\n\r\n.chat-message:hover {\r\n    background: rgba(255,255,255,0.05);\r\n}\r\n\r\n.chat-message.compact {\r\n    margin-bottom: 4px;\r\n    padding: 4px 6px;\r\n}\r\n\r\n.message-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 2px;\r\n}\r\n\r\n.message-sender {\r\n    font-size: 11px;\r\n    color: #4fc3f7;\r\n    cursor: pointer;\r\n    font-weight: 600;\r\n    transition: color 0.2s ease;\r\n}\r\n\r\n.message-sender:hover {\r\n    color: #29b6f6;\r\n    text-decoration: underline;\r\n}\r\n\r\n.self-message .message-sender {\r\n    color: #90CAF9;\r\n}\r\n\r\n.npc-message .message-sender {\r\n    color: #a5d6a7;\r\n}\r\n\r\n.player-message {\r\n    border-left-color: #4fc3f7;\r\n}\r\n\r\n.self-message {\r\n    border-left-color: #90CAF9;\r\n    background: rgba(144,202,249,0.05);\r\n}\r\n\r\n.npc-message {\r\n    border-left-color: #a5d6a7;\r\n}\r\n\r\n.message-content {\r\n    font-size: 12px;\r\n    color: #f0f0f0;\r\n    line-height: 1.4;\r\n    word-break: break-word;\r\n    margin: 2px 0;\r\n}\r\n\r\n.message-time {\r\n    font-size: 10px;\r\n    color: #888;\r\n    opacity: 0.7;\r\n}\r\n\r\n.message-time-compact {\r\n    font-size: 9px;\r\n    color: #666;\r\n    text-align: right;\r\n    margin-top: 2px;\r\n}\r\n\r\n.system-message {\r\n    border-left-color: #90caf9;\r\n    background: rgba(144,202,249,0.08);\r\n    font-style: italic;\r\n}\r\n\r\n.system-message .message-content {\r\n    color: #90caf9;\r\n    font-size: 11px;\r\n}\r\n\r\n.empty-messages {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    opacity: 0.6;\r\n    color: #aaa;\r\n    text-align: center;\r\n    padding: 20px;\r\n}\r\n\r\n.empty-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n    opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.empty-hint {\r\n    font-size: 11px;\r\n    opacity: 0.7;\r\n}\r\n\r\n.chat-input-container {\r\n    display: flex;\r\n    padding: 8px 12px;\r\n    border-top: 1px solid rgba(255,255,255,0.15);\r\n    gap: 8px;\r\n    flex-shrink: 0;\r\n    background: rgba(0,0,0,0.2);\r\n}\r\n\r\n.input-wrapper {\r\n    flex: 1;\r\n    position: relative;\r\n}\r\n\r\n.chat-input {\r\n    width: 100%;\r\n    background: rgba(255,255,255,0.1);\r\n    border: 1px solid rgba(255,255,255,0.2);\r\n    border-radius: 8px;\r\n    padding: 8px 12px;\r\n    color: #fff;\r\n    font-size: 12px;\r\n    height: 36px;\r\n    box-sizing: border-box;\r\n    transition: all 0.2s ease;\r\n    outline: none;\r\n}\r\n\r\n.chat-input:focus {\r\n    background: rgba(255,255,255,0.15);\r\n    border-color: #e8511c;\r\n    box-shadow: 0 0 0 2px rgba(232,81,28,0.2);\r\n}\r\n\r\n.chat-input:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n}\r\n\r\n.chat-input::placeholder {\r\n    color: #aaa;\r\n}\r\n\r\n.input-counter {\r\n    position: absolute;\r\n    right: 8px;\r\n    bottom: -16px;\r\n    font-size: 9px;\r\n    color: #888;\r\n}\r\n\r\n.send-btn {\r\n    background: linear-gradient(135deg, #e8511c 0%, #ff6b35 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 0 16px;\r\n    border-radius: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 4px;\r\n    height: 36px;\r\n    font-size: 11px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    min-width: 60px;\r\n}\r\n\r\n.send-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d64516 0%, #e55a2b 100%);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(232,81,28,0.3);\r\n}\r\n\r\n.send-btn:active:not(:disabled) {\r\n    transform: translateY(0);\r\n}\r\n\r\n.send-btn:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.send-icon {\r\n    font-size: 12px;\r\n}\r\n\r\n.send-text {\r\n    font-size: 11px;\r\n}\r\n\r\n/* 响应式设计 */\r\n/* 超小屏幕 (手机竖屏) */\r\n@media (max-width: 480px) {\r\n    .chat-container {\r\n        height: 280px;\r\n        border-radius: 8px 8px 0 0;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 220px;\r\n    }\r\n\r\n    .chat-container.minimized {\r\n        height: 40px;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 4px 8px;\r\n        height: 40px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 20px;\r\n        height: 20px;\r\n        gap: 2px;\r\n    }\r\n\r\n    .control-btn .icon {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 4px 8px;\r\n        font-size: 10px;\r\n        gap: 3px;\r\n    }\r\n\r\n    .channel-name {\r\n        display: none; /* 超小屏幕隐藏频道名称，只显示图标 */\r\n    }\r\n\r\n    .channel-tab::before {\r\n        content: attr(data-icon);\r\n        font-size: 12px;\r\n    }\r\n\r\n    .chat-messages {\r\n        padding: 4px 6px;\r\n    }\r\n\r\n    .chat-message {\r\n        margin-bottom: 6px;\r\n        padding: 4px 6px;\r\n    }\r\n\r\n    .message-sender {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 9px;\r\n    }\r\n\r\n    .chat-input-container {\r\n        padding: 4px 6px;\r\n        gap: 6px;\r\n    }\r\n\r\n    .chat-input {\r\n        height: 32px;\r\n        font-size: 11px;\r\n        padding: 6px 10px;\r\n    }\r\n\r\n    .send-btn {\r\n        min-width: 45px;\r\n        padding: 0 8px;\r\n        height: 32px;\r\n    }\r\n\r\n    .send-text {\r\n        display: none; /* 超小屏幕只显示图标 */\r\n    }\r\n\r\n    .empty-icon {\r\n        font-size: 24px;\r\n    }\r\n\r\n    .empty-text {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .empty-hint {\r\n        font-size: 10px;\r\n    }\r\n}\r\n\r\n/* 小屏幕 (手机横屏/小平板) */\r\n@media (min-width: 481px) and (max-width: 768px) {\r\n    .chat-container {\r\n        height: 320px;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 260px;\r\n    }\r\n\r\n    .chat-container.minimized {\r\n        height: 44px;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 6px 10px;\r\n        height: 44px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 24px;\r\n        height: 24px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 6px 12px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .chat-messages {\r\n        padding: 6px 8px;\r\n    }\r\n\r\n    .message-sender {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .chat-input-container {\r\n        padding: 6px 8px;\r\n    }\r\n\r\n    .chat-input {\r\n        height: 34px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .send-btn {\r\n        min-width: 50px;\r\n        padding: 0 12px;\r\n        height: 34px;\r\n    }\r\n}\r\n\r\n/* 中等屏幕 (平板) */\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n    .chat-container {\r\n        height: 380px;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 300px;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 7px 11px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 13px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 26px;\r\n        height: 26px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 7px 14px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .chat-messages {\r\n        padding: 7px 10px;\r\n    }\r\n\r\n    .chat-input {\r\n        height: 35px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .send-btn {\r\n        height: 35px;\r\n    }\r\n}\r\n\r\n/* 大屏幕 (桌面) */\r\n@media (min-width: 1025px) {\r\n    .chat-container {\r\n        height: 420px;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 320px;\r\n    }\r\n}\r\n\r\n/* 横屏适配 */\r\n@media (orientation: landscape) and (max-height: 600px) {\r\n    .chat-container {\r\n        height: 240px !important;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 200px !important;\r\n    }\r\n\r\n    .chat-container.minimized {\r\n        height: 36px !important;\r\n    }\r\n\r\n    .chat-header {\r\n        height: 36px;\r\n        padding: 4px 8px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 22px;\r\n        height: 22px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 4px 10px;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .chat-messages {\r\n        padding: 4px 6px;\r\n    }\r\n\r\n    .chat-input-container {\r\n        padding: 4px 6px;\r\n    }\r\n\r\n    .chat-input {\r\n        height: 30px;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .send-btn {\r\n        height: 30px;\r\n        min-width: 40px;\r\n        padding: 0 8px;\r\n    }\r\n}\r\n\r\n/* 高分辨率屏幕适配 */\r\n@media (min-width: 1440px) {\r\n    .chat-container {\r\n        height: 480px;\r\n        max-width: 400px;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .chat-container.compact {\r\n        height: 380px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 15px;\r\n    }\r\n\r\n    .control-btn {\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n\r\n    .channel-tab {\r\n        padding: 10px 18px;\r\n        font-size: 13px;\r\n    }\r\n\r\n    .message-sender {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .message-content {\r\n        font-size: 13px;\r\n    }\r\n\r\n    .chat-input {\r\n        font-size: 13px;\r\n        height: 38px;\r\n    }\r\n\r\n    .send-btn {\r\n        height: 38px;\r\n        font-size: 12px;\r\n    }\r\n}\r\n\r\n/* 触摸设备优化 */\r\n@media (hover: none) and (pointer: coarse) {\r\n    .control-btn,\r\n    .channel-tab,\r\n    .send-btn {\r\n        min-height: 44px; /* iOS建议的最小触摸目标 */\r\n    }\r\n\r\n    .control-btn:active {\r\n        transform: scale(0.95);\r\n        background: rgba(255,255,255,0.3);\r\n    }\r\n\r\n    .channel-tab:active {\r\n        background: rgba(232,81,28,0.8);\r\n    }\r\n\r\n    .send-btn:active {\r\n        transform: scale(0.98);\r\n    }\r\n\r\n    .message-sender {\r\n        min-height: 32px;\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n}\r\n\r\n/* 深色模式适配 */\r\n@media (prefers-color-scheme: dark) {\r\n    .chat-container {\r\n        background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(15,15,25,0.95) 100%);\r\n        border-color: rgba(255,255,255,0.15);\r\n    }\r\n\r\n    .chat-header {\r\n        background: linear-gradient(90deg, rgba(0,0,0,0.8) 0%, rgba(20,20,30,0.9) 100%);\r\n    }\r\n\r\n    .chat-status.connected {\r\n        box-shadow: 0 0 12px rgba(76,175,80,0.8);\r\n    }\r\n}\r\n\r\n/* 减少动画的用户偏好 */\r\n@media (prefers-reduced-motion: reduce) {\r\n    .chat-container,\r\n    .control-btn,\r\n    .channel-tab,\r\n    .chat-message,\r\n    .send-btn {\r\n        transition: none;\r\n    }\r\n\r\n    .chat-status {\r\n        animation: none;\r\n    }\r\n}\r\n</style> "]}]}