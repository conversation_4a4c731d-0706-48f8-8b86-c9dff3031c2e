<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamInvite extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'inviter_id',
        'character_id',
        'status',
        'expires_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取邀请所属的队伍
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 获取邀请发送者
     */
    public function inviter()
    {
        return $this->belongsTo(Character::class, 'inviter_id');
    }

    /**
     * 获取被邀请角色
     */
    public function character()
    {
        return $this->belongsTo(Character::class, 'character_id');
    }
}
