<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('位置名称');
            $table->string('type')->comment('位置类型：town, forest, mountain, dungeon等');
            $table->text('description')->nullable()->comment('位置描述');
            $table->integer('x')->default(0)->comment('X坐标');
            $table->integer('y')->default(0)->comment('Y坐标');
            $table->integer('level_requirement')->default(1)->comment('等级要求');
            $table->boolean('is_safe')->default(true)->comment('是否安全区域');
            $table->json('facilities')->nullable()->comment('设施列表');
            $table->json('npcs')->nullable()->comment('NPC列表');
            $table->json('monsters')->nullable()->comment('怪物列表');
            $table->timestamps();
            
            $table->index(['type']);
            $table->index(['level_requirement']);
            $table->index(['x', 'y']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
