@extends('admin.layouts.app')

@section('title', '用户管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        用户列表
        <a href="{{ route('admin.users.create') }}" class="layui-btn layui-btn-xs layui-btn-normal" style="float: right;">添加用户</a>
    </div>
    <div class="layui-card-body">
        @if(session('success'))
        <div class="layui-alert layui-alert-success">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="layui-alert layui-alert-danger">
            {{ session('error') }}
        </div>
        @endif

        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>注册时间</th>
                    <th>最后登录</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                @forelse($users as $user)
                <tr>
                    <td>{{ $user->id }}</td>
                    <td>{{ $user->username ?? $user->name ?? '未命名' }}</td>
                    <td>{{ $user->created_at }}</td>
                    <td>{{ $user->last_login_at ?? '从未登录' }}</td>
                    <td>
                        @if(isset($user->status) && $user->status == 1)
                        <span class="layui-badge layui-bg-green">正常</span>
                        @else
                        <span class="layui-badge">禁用</span>
                        @endif
                    </td>
                    <td>
                        <div class="layui-btn-group">
                            <a href="{{ route('admin.users.edit', $user->id) }}" class="layui-btn layui-btn-xs">编辑</a>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteUser({{ $user->id }}, '{{ $user->username ?? $user->name ?? '未命名用户' }}')">删除</button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="layui-center">暂无用户数据</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        {{ $users->links('admin.layouts.pagination') }}
    </div>
</div>

<!-- 删除确认表单 -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@section('scripts')
<script>
function deleteUser(id, username) {
    layer.confirm('确定要删除用户 "' + username + '" 吗？', {
        btn: ['确定', '取消']
    }, function() {
        var form = document.getElementById('deleteForm');
        form.action = "{{ route('admin.users.destroy', '') }}/" + id;
        form.submit();
    });
}

layui.use(['table'], function(){
    var table = layui.table;
});
</script>
@endsection
