<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Character;

class SkillController extends Controller
{
    /**
     * 获取角色技能列表
     */
    public function getSkills(Request $request, Character $character)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟技能数据
        $skills = [
            [
                'id' => 1,
                'name' => '基础攻击',
                'description' => '最基本的攻击技能，造成100%武器伤害。',
                'type' => 'attack',
                'level' => 1,
                'max_level' => 10,
                'learned' => true,
                'mp_cost' => 0,
                'cooldown' => 0,
                'damage_multiplier' => 1.0,
                'icon' => '/static/game/skills/basic_attack.png'
            ],
            [
                'id' => 2,
                'name' => '重击',
                'description' => '蓄力攻击，造成150%武器伤害。',
                'type' => 'attack',
                'level' => 3,
                'max_level' => 10,
                'learned' => true,
                'mp_cost' => 10,
                'cooldown' => 3,
                'damage_multiplier' => 1.5,
                'icon' => '/static/game/skills/heavy_strike.png'
            ],
            [
                'id' => 3,
                'name' => '治疗术',
                'description' => '恢复自身生命值。',
                'type' => 'support',
                'level' => 2,
                'max_level' => 10,
                'learned' => true,
                'mp_cost' => 15,
                'cooldown' => 5,
                'heal_amount' => 50,
                'icon' => '/static/game/skills/heal.png'
            ],
            [
                'id' => 4,
                'name' => '火球术',
                'description' => '发射火球攻击敌人，造成魔法伤害。',
                'type' => 'attack',
                'level' => 0,
                'max_level' => 10,
                'learned' => false,
                'mp_cost' => 20,
                'cooldown' => 4,
                'damage_multiplier' => 1.8,
                'icon' => '/static/game/skills/fireball.png'
            ],
            [
                'id' => 5,
                'name' => '防御姿态',
                'description' => '提高防御力，减少受到的伤害。',
                'type' => 'defense',
                'level' => 1,
                'max_level' => 10,
                'learned' => true,
                'mp_cost' => 5,
                'cooldown' => 0,
                'defense_bonus' => 0.2,
                'icon' => '/static/game/skills/defense_stance.png'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'skills' => $skills,
                'skill_points' => 5,
                'total_skill_points' => 10
            ]
        ]);
    }

    /**
     * 升级技能
     */
    public function levelUpSkill(Request $request, Character $character, $skillId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        // 模拟升级技能逻辑
        return response()->json([
            'success' => true,
            'message' => '技能升级成功'
        ]);
    }

    /**
     * 设置技能快捷键
     */
    public function setSkillShortcut(Request $request, Character $character, $skillId)
    {
        // 检查角色是否属于当前用户
        if ($character->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FORBIDDEN',
                    'message' => '无权访问该角色',
                ]
            ], 403);
        }

        $shortcut = $request->input('shortcut');

        // 模拟设置快捷键逻辑
        return response()->json([
            'success' => true,
            'message' => '快捷键设置成功'
        ]);
    }
}
