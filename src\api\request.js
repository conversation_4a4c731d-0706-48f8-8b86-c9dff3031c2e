  // 执行fetch请求
  fetch(requestUrl, requestOptions)
    .then(async (response) => {
      clearTimeout(timeoutId);

      // 尝试解析响应体为JSON
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        try {
          responseData = await response.json();
        } catch (error) {
          // console.error('[Request] 解析JSON响应失败:', error);
          responseData = {};
        }
      } else {
        try {
          responseData = await response.text();
        } catch (error) {
          // console.error('[Request] 解析文本响应失败:', error);
          responseData = '';
        }
      }
    }) 