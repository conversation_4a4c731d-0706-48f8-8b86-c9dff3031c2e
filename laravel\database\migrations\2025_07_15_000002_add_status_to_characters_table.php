<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatusToCharactersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('characters', function (Blueprint $table) {
            // 检查表中是否已经存在status字段
            if (!Schema::hasColumn('characters', 'status')) {
                $table->tinyInteger('status')->default(1)->after('level')->comment('角色状态：1-正常，0-禁用');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('characters', function (Blueprint $table) {
            // 如果存在status字段，则删除
            if (Schema::hasColumn('characters', 'status')) {
                $table->dropColumn('status');
            }
        });
    }
}
